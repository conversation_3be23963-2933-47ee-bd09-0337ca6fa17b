// admin_panel_web/functions/index.js

// استيراد نسخة v1 لإعادة دعم واجهة Firestore Trigger
const functions = require("firebase-functions/v1");
const admin = require("firebase-admin");
admin.initializeApp();


// ── 1) عند إنشاء بطاقة جديدة في cards/{cardId} ──
exports.notifyNewCard = functions.firestore
    .document("cards/{cardId}")
    .onCreate(async (snap, ctx) => {
      const data = snap.data() || {};
      const cardId = ctx.params.cardId;

      // تحضير Notification payload
      const notification = {
        title: "بطاقة جديدة 🎉",
        body: data.title || "اضغط لعرضها",
        click_action: "FLUTTER_NOTIFICATION_CLICK",
      };
      const payload = {
        notification,
        data: {route: "notifications", cardId},
      };

      // 1.a) إرسال Push عبر HTTP v1 API
      await admin.messaging().send({
        topic: "allUsers",
        notification: payload.notification,
        data: payload.data,
      });

      // 1.b) Batch write: تسجيل إشعار في Firestore لكل مستخدم
      const usersSnap = await admin.firestore().collection("users").get();
      const batch = admin.firestore().batch();
      usersSnap.docs.forEach((u) => {
        const ref = admin.firestore()
            .collection("users").doc(u.id)
            .collection("notifications").doc();
        batch.set(ref, {
          title: payload.notification.title,
          body: payload.notification.body,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          isRead: false,
          cardId,
        });
      });
      await batch.commit();
    });


// ── 2) Callable Function للإرسال من لوحة التحكم ──
exports.sendNotification = functions.https
    .onCall(async (data, context) => {
      const isBroadcast = data.isBroadcast === true;
      const token = (data.token || "").trim();
      const title = (data.title || "").trim();
      const body = (data.body || "").trim();
      const uid = (data.uid || "").trim();

      if (!title || !body) {
        throw new functions.https.HttpsError(
            "invalid-argument",
            "العنوان والنص لا يمكن أن يكونا فارغين",
        );
      }

      // تحضير payload
      const notification = {
        title, body,
        click_action: "FLUTTER_NOTIFICATION_CLICK",
      };
      const payload = {
        notification,
        data: {route: "notifications", uid},
      };

      if (isBroadcast) {
      // 2.a) بث عام
        await admin.messaging().send({
          topic: "allUsers",
          notification: payload.notification,
          data: payload.data,
        });
      } else {
      // 2.b) إرسال لجهاز محدد
        if (!token) {
          throw new functions.https.HttpsError(
              "invalid-argument",
              "توكن المستخدم غير صالح",
          );
        }
        await admin.messaging().send({
          token: token,
          notification: payload.notification,
          data: payload.data,
        });
      }

      return {success: true};
    });
