@echo off
echo ========================================
echo    تحليل حجم APK لتطبيق مشاعر
echo ========================================

echo.
echo 1. تحليل حجم APK...
call flutter build apk --release --analyze-size

echo.
echo 2. إنشاء تقرير مفصل عن الحجم...
call flutter build apk --release --analyze-size --target-platform android-arm64

echo.
echo 3. تحليل App Bundle...
call flutter build appbundle --release --analyze-size

echo.
echo ========================================
echo تم الانتهاء من التحليل!
echo ========================================
echo.
echo يمكنك مراجعة التقرير المفصل في:
echo - Terminal output أعلاه
echo - Flutter DevTools (Size Analysis)
echo.
echo لفتح Flutter DevTools:
echo flutter pub global activate devtools
echo flutter pub global run devtools
echo ========================================

pause
