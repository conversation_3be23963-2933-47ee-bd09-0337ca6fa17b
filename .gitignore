# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# Web related
lib/generated_plugin_registrant.dart

# Firebase related
**/firebase_options.dart
**/.firebaserc
**/firebase.json
**/functions/node_modules/
**/functions/.env

# Environment files
.env
.env.local
.env.development
.env.production
**/.env

# API Keys and sensitive data
**/google-services.json
**/GoogleService-Info.plist
**/firebase-adminsdk-*.json

# Build outputs
**/build/
**/dist/
**/out/

# Temporary files
**/temp/
**/tmp/
**/*.tmp
**/*.temp

# Log files
**/logs/
**/*.log

# Cache directories
**/.cache/
**/cache/

# Node modules (for Cloud Functions)
**/node_modules/
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# Coverage reports
**/coverage/
**/.nyc_output/

# APK files
**/*.apk
**/*.aab

# Generated files
**/generated/
**/.generated/
**/l10n/generated/

# Analysis files
**/analysis_options.yaml.bak

# Backup files
**/*.bak
**/*.backup

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Archive files
**/*.zip
**/*.tar.gz
**/*.rar

# IDE files
**/.project
**/.classpath
**/.settings/

# Flutter specific
flutter_*.log
.flutter-plugins-dependencies

# Dart specific
.dart_tool/
.packages
pubspec.lock

# Testing
**/test_driver/
**/integration_test/driver/

# Documentation
**/doc/

# Database files
**/*.db
**/*.sqlite
**/*.sqlite3

# Certificate files
**/*.p12
**/*.jks
**/*.keystore

# Gradle files
**/.gradle/
**/gradle-wrapper.properties

# Maven files
**/target/

# Xcode files
**/project.xcworkspace/
**/xcuserdata/

# CocoaPods
**/Pods/
**/Podfile.lock

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Local configuration files
**/local.properties
**/keystore.properties
