@echo off
echo ========================================
echo    بناء APK محسن لتطبيق مشاعر
echo ========================================

echo.
echo 1. تنظيف المشروع...
call flutter clean

echo.
echo 2. تحديث الحزم...
call flutter pub get

echo.
echo 3. تحليل التطبيق...
call flutter analyze

echo.
echo 4. بناء APK محسن (مقسم حسب المعمارية)...
call flutter build apk --release --split-per-abi --no-tree-shake-icons

echo.
echo 5. بناء App Bundle (الأفضل لـ Google Play)...
call flutter build appbundle --release --no-tree-shake-icons

echo.
echo ========================================
echo تم الانتهاء من البناء!
echo ========================================
echo.
echo ملفات APK المقسمة (أصغر حجماً):
echo - build\app\outputs\flutter-apk\app-arm64-v8a-release.apk
echo - build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk
echo.
echo ملف App Bundle (للنشر على Google Play):
echo - build\app\outputs\bundle\release\app-release.aab
echo.
echo ملاحظة: استخدم ملفات APK المقسمة لتوزيع أصغر حجماً
echo أو استخدم App Bundle للنشر على Google Play Store
echo ========================================

pause
