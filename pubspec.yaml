name: mashair
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  # Professional drag, resize, rotate controls
  flutter_box_transform: ^0.4.7

  # Firebase packages with fixed versions
  firebase_core: ^2.24.2
  firebase_auth: ^4.16.0
  cloud_firestore: ^4.14.0
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.7.9

  # Other packages
  equatable: ^2.0.5
  dartz: ^0.10.1
  intl: ^0.19.0
  google_sign_in: ^6.1.6
  flutter_facebook_auth: ^6.0.4
  flutter_bloc: ^8.1.3
  get_it: ^7.6.4
  provider: ^6.1.1
  flutter_localization: ^0.1.14
  flutter_localizations:
    sdk: flutter
  flutter_carousel_widget: ^2.2.0
  google_fonts: ^5.1.0
  shimmer: ^3.0.0
  image_picker: ^1.0.4
  uuid: ^4.2.1
  pdf: ^3.10.4
  printing: ^5.11.0
  share_plus: ^7.2.1
  signature: ^5.4.0
  qr_flutter: ^4.1.0
  flutter_pdfview: ^1.3.1
  url_launcher: ^6.1.14
  http: ^1.1.0
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  package_info_plus: ^4.2.0
  path: ^1.8.3
  vector_math: ^2.1.4
  cached_network_image: ^3.3.0
  flutter_blurhash: ^0.8.2
  flutter_cache_manager: ^3.3.1
  octo_image: ^2.0.0
  flutter_image_compress: ^2.1.0
  image: ^4.1.3
  collection: ^1.18.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    # تحسين تنظيم الأصول لتقليل الحجم
    - assets/logos/
    - assets/icons/
    - assets/banners/
    - assets/stickers/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: TrajanPro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
