plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

android {
    namespace = "com.devx.mashair"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.devx.mashair"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    // تفعيل تقسيم APK (بدون ndk abiFilters لتجنب التضارب)
    splits {
        abi {
            enable true
            reset()
            include 'arm64-v8a', 'armeabi-v7a'
            universalApk true  // تفعيل APK عالمي
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug

            // تفعيل تحسينات تقليل حجم APK (مع تعطيل shrinkResources مؤقتاً)
            minifyEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            // تحسين الكود وإزالة الكود غير المستخدم
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            zipAlignEnabled true
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // تحديث إصدار Firebase Auth لحل مشكلة PigeonUserDetails
    implementation 'com.google.firebase:firebase-auth:22.3.1'
    implementation 'com.google.firebase:firebase-auth-interop:20.0.0'
    implementation 'com.google.firebase:firebase-common:20.4.2'
    implementation 'com.google.firebase:firebase-components:17.1.5'
    implementation 'com.google.firebase:firebase-annotations:16.2.0'

    // إضافة Google Play Core لحل مشكلة الفئات المفقودة
    implementation 'com.google.android.play:core:1.10.3'
    implementation 'com.google.android.play:core-ktx:1.8.1'

    // Kotlin standard library
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}
