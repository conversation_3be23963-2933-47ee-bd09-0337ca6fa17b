# 🎨 تطبيق بطاقات المشاعر (<PERSON><PERSON><PERSON> Cards)

[![Flutter](https://img.shields.io/badge/Flutter-02569B?style=for-the-badge&logo=flutter&logoColor=white)](https://flutter.dev)
[![Dart](https://img.shields.io/badge/Dart-0175C2?style=for-the-badge&logo=dart&logoColor=white)](https://dart.dev)
[![Firebase](https://img.shields.io/badge/Firebase-039BE5?style=for-the-badge&logo=Firebase&logoColor=white)](https://firebase.google.com)

## 📱 نظرة عامة
تطبيق بطاقات المشاعر هو تطبيق Flutter متكامل يتيح للمستخدمين إنشاء وتخصيص بطاقات تهنئة رقمية للمناسبات المختلفة مع ذكاء اصطناعي ومحرر متقدم، بالإضافة إلى لوحة تحكم إدارية شاملة.

## المكونات الرئيسية

### 1. التطبيق المحمول (Mashair Cards)
- **المنصة**: Flutter
- **المميزات**:
  - إنشاء بطاقات تهنئة مخصصة
  - محرر بطاقات متقدم مع عناصر قابلة للتحرير
  - ذكاء اصطناعي لتوليد النصوص والصور
  - دعم متعدد اللغات (العربية والإنجليزية)
  - نظام إشعارات متقدم
  - مشاركة البطاقات وطباعتها
  - واجهة مستخدم حديثة ومتجاوبة

### 2. لوحة التحكم الإدارية (Admin Panel)
- **المنصة**: Flutter Web
- **المميزات**:
  - إدارة المستخدمين والبطاقات
  - رفع وإدارة الملصقات والبانرات
  - نظام إشعارات للمستخدمين
  - إحصائيات وتقارير شاملة
  - إدارة طلبات الطباعة
  - واجهة إدارية حديثة ومتجاوبة

## التقنيات المستخدمة

### Frontend
- **Flutter**: إطار العمل الرئيسي
- **Dart**: لغة البرمجة
- **BLoC Pattern**: لإدارة الحالة
- **Clean Architecture**: هيكلة المشروع

### Backend & Services
- **Firebase**: قاعدة البيانات والمصادقة
- **Cloud Functions**: الخدمات السحابية
- **Firebase Storage**: تخزين الملفات
- **Firebase Messaging**: الإشعارات

### المكتبات الرئيسية
- `flutter_bloc`: إدارة الحالة
- `firebase_core`: خدمات Firebase الأساسية
- `cloud_firestore`: قاعدة البيانات
- `firebase_auth`: المصادقة
- `firebase_storage`: تخزين الملفات
- `image_picker`: اختيار الصور
- `google_fonts`: الخطوط
- `provider`: إدارة الحالة المحلية

## هيكل المشروع

```
Mashair Cards/
├── lib/
│   ├── core/                 # الوظائف الأساسية
│   ├── features/             # المميزات الرئيسية
│   │   ├── auth/            # المصادقة
│   │   ├── home/            # الصفحة الرئيسية
│   │   ├── create_card/     # إنشاء البطاقات
│   │   ├── ai/              # الذكاء الاصطناعي
│   │   └── notifications/   # الإشعارات
│   └── main.dart
├── assets/                   # الموارد (صور، خطوط)
├── android/                  # إعدادات Android
├── ios/                      # إعدادات iOS
└── functions/               # Cloud Functions

admin_panel_web/
├── lib/
│   ├── core/                # الوظائف الأساسية
│   ├── features/            # المميزات الإدارية
│   │   ├── dashboard/       # لوحة التحكم
│   │   └── admin_auth/      # مصادقة الإدارة
│   └── main.dart
├── web/                     # إعدادات الويب
└── functions/              # Cloud Functions
```

## المتطلبات

### للتطوير
- Flutter SDK (3.0+)
- Dart SDK (2.17+)
- Android Studio / VS Code
- Firebase CLI
- Git

### للتشغيل
- Android 5.0+ / iOS 11.0+
- اتصال بالإنترنت
- حساب Firebase

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/YOUR_USERNAME/mashair-cards-app.git
git clone https://github.com/YOUR_USERNAME/mashair-admin-panel.git
```

### 2. تثبيت التبعيات
```bash
# للتطبيق
cd mashair-cards-app
flutter pub get

# للوحة التحكم
cd ../mashair-admin-panel
flutter pub get
```

### 3. إعداد Firebase
- إنشاء مشروع Firebase جديد
- إضافة التطبيق للمشروع
- تحميل ملفات التكوين
- تفعيل الخدمات المطلوبة

### 4. تشغيل المشروع
```bash
# التطبيق
flutter run

# لوحة التحكم (الويب)
flutter run -d chrome
```

## المميزات الرئيسية

### للمستخدمين
- ✅ إنشاء بطاقات مخصصة
- ✅ محرر متقدم للبطاقات
- ✅ ذكاء اصطناعي للنصوص
- ✅ مكتبة ملصقات متنوعة
- ✅ مشاركة وطباعة البطاقات
- ✅ حفظ المسودات
- ✅ واجهة عربية/إنجليزية

### للإدارة
- ✅ إدارة شاملة للمستخدمين
- ✅ رفع وإدارة المحتوى
- ✅ إحصائيات مفصلة
- ✅ نظام إشعارات
- ✅ إدارة طلبات الطباعة
- ✅ واجهة إدارية حديثة

## الأمان والخصوصية
- 🔒 مصادقة آمنة عبر Firebase
- 🔒 تشفير البيانات
- 🔒 حماية API endpoints
- 🔒 التحقق من صحة المدخلات
- 🔒 إدارة الصلاحيات

## المساهمة
نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request

## الترخيص
هذا المشروع محمي بحقوق الطبع والنشر لشركة Craft Solutions 2025.

## التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: www.craftsolutions.com
- **واتساب**: +1234567890

---
**جميع الحقوق محفوظة لشركة Craft Solutions 2025**
