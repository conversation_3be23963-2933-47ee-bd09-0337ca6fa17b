@echo off
echo ========================================
echo    نسخ ملفات APK لتطبيق مشاعر
echo ========================================

echo.
echo إنشاء مجلد APK Files...
if not exist "APK Files" mkdir "APK Files"

echo.
echo نسخ ملفات APK...
if exist "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" (
    copy "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" "APK Files\Mashair-arm64-v8a-release.apk"
    echo تم نسخ: Mashair-arm64-v8a-release.apk
) else (
    echo لم يتم العثور على: app-arm64-v8a-release.apk
)

if exist "build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk" (
    copy "build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk" "APK Files\Mashair-armeabi-v7a-release.apk"
    echo تم نسخ: Mashair-armeabi-v7a-release.apk
) else (
    echo لم يتم العثور على: app-armeabi-v7a-release.apk
)

if exist "build\app\outputs\bundle\release\app-release.aab" (
    copy "build\app\outputs\bundle\release\app-release.aab" "APK Files\Mashair-release.aab"
    echo تم نسخ: Mashair-release.aab
) else (
    echo لم يتم العثور على: app-release.aab
)

echo.
echo ========================================
echo تم الانتهاء من النسخ!
echo ========================================
echo.
echo ملفات APK متوفرة في مجلد: APK Files
echo.
echo أحجام الملفات:
if exist "APK Files\Mashair-arm64-v8a-release.apk" (
    for %%I in ("APK Files\Mashair-arm64-v8a-release.apk") do echo - Mashair-arm64-v8a-release.apk: %%~zI bytes
)
if exist "APK Files\Mashair-armeabi-v7a-release.apk" (
    for %%I in ("APK Files\Mashair-armeabi-v7a-release.apk") do echo - Mashair-armeabi-v7a-release.apk: %%~zI bytes
)
if exist "APK Files\Mashair-release.aab" (
    for %%I in ("APK Files\Mashair-release.aab") do echo - Mashair-release.aab: %%~zI bytes
)

echo.
echo ملاحظات:
echo - استخدم arm64-v8a للأجهزة الحديثة (64-bit)
echo - استخدم armeabi-v7a للأجهزة القديمة (32-bit)
echo - استخدم .aab للنشر على Google Play Store
echo ========================================

pause
