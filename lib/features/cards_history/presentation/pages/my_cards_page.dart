// lib/features/cards_history/presentation/pages/my_cards_page.dart

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../core/constants/app_colors.dart';

/// Página para mostrar las tarjetas guardadas por el usuario
class MyCardsPage extends StatefulWidget {
  const MyCardsPage({super.key});

  @override
  State<MyCardsPage> createState() => _MyCardsPageState();
}

class _MyCardsPageState extends State<MyCardsPage>
    with SingleTickerProviderStateMixin {
  List<File> _allCards = [];
  List<File> _filteredCards = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedFilter = 'all'; // 'all', 'recent', 'oldest'
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Controlador para el campo de búsqueda
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSavedCards();

    // Configurar animación para elementos de la cuadrícula
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// Carga las tarjetas guardadas desde el almacenamiento local
  Future<void> _loadSavedCards() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final directory = await getApplicationDocumentsDirectory();
      final cardsDir = Directory('${directory.path}/my_cards');

      if (await cardsDir.exists()) {
        final files = await cardsDir.list().toList();

        // Filtrar solo archivos PNG
        _allCards = files
            .whereType<File>()
            .where((file) => file.path.toLowerCase().endsWith('.png'))
            .toList();

        // Aplicar filtro inicial
        _applyFilters();
      } else {
        _allCards = [];
        _filteredCards = [];
      }
    } catch (e) {
      debugPrint('Error loading saved cards: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Aplica filtros a las tarjetas
  void _applyFilters() {
    // Primero aplicamos el filtro de búsqueda
    var filtered = _allCards;

    if (_searchQuery.isNotEmpty) {
      // Filtrar por nombre de archivo (podría mejorarse con metadatos)
      filtered = filtered.where((card) {
        final fileName = card.path.split('/').last.toLowerCase();
        return fileName.contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Luego aplicamos el filtro de ordenamiento
    switch (_selectedFilter) {
      case 'recent':
        filtered.sort(
            (a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
        break;
      case 'oldest':
        filtered.sort(
            (a, b) => a.lastModifiedSync().compareTo(b.lastModifiedSync()));
        break;
      case 'all':
      default:
        // Por defecto, mostrar más recientes primero
        filtered.sort(
            (a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
        break;
    }

    setState(() {
      _filteredCards = filtered;
    });
  }

  /// Elimina una tarjeta guardada
  Future<void> _deleteCard(File card) async {
    try {
      await card.delete();

      // Mostrar snackbar con animación
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 16),
                Text(
                  'تم حذف البطاقة',
                  style: GoogleFonts.cairo(),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }

      _loadSavedCards(); // Recargar la lista
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف البطاقة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Comparte una tarjeta guardada usando la API moderna
  Future<void> _shareCard(File card) async {
    try {
      final files = <XFile>[XFile(card.path)];
      await Share.shareXFiles(
        files,
        text: 'بطاقة من تطبيق مشاعر',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في مشاركة البطاقة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Imprime una tarjeta usando la biblioteca printing
  Future<void> _printCard(File card) async {
    try {
      // Leer la imagen como bytes
      final Uint8List imageBytes = await card.readAsBytes();

      // Mostrar la vista previa de impresión
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async {
          // Crear un documento PDF
          final pdf = pw.Document();

          // Crear una imagen para el PDF desde los bytes
          final image = pw.MemoryImage(imageBytes);

          // Añadir una página con la imagen
          pdf.addPage(
            pw.Page(
              pageFormat: format,
              build: (pw.Context context) {
                return pw.Center(
                  child: pw.Image(image, fit: pw.BoxFit.contain),
                );
              },
            ),
          );

          // Devolver el PDF como bytes
          return pdf.save();
        },
        name: 'بطاقة مشاعر.pdf',
        // Personalizar la interfaz de impresión
        dynamicLayout: true,
        usePrinterSettings: true,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طباعة البطاقة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        title: Text(
          AppLocalizations.of(context).myCards,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          // Botón de actualizar
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            tooltip: AppLocalizations.of(context).refresh,
            onPressed: _loadSavedCards,
          ),
        ],
      ),
      body: Column(
        children: [
          // Barra de búsqueda y filtros
          _buildSearchAndFilterBar(),

          // Contenido principal
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : _filteredCards.isEmpty
                    ? _buildEmptyState()
                    : _buildCardsGrid(),
          ),
        ],
      ),
    );
  }

  /// Construye la barra de búsqueda y filtros con diseño mejorado
  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryColor,
            AppColors.primaryColor.withOpacity(0.85),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Título de la sección de filtros
          Padding(
            padding: const EdgeInsets.only(bottom: 12, left: 4),
            child: Text(
              AppLocalizations.of(context).filterCards,
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),

          // Campo de búsqueda con diseño mejorado
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _searchController,
              style: GoogleFonts.cairo(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
                fontSize: 16,
              ),
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context).searchForCard,
                hintStyle: GoogleFonts.cairo(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[400]
                      : Colors.grey[600],
                  fontSize: 16,
                ),
                prefixIcon:
                    Icon(Icons.search, color: Colors.white.withOpacity(0.8)),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear,
                            color: Colors.white.withOpacity(0.8)),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          _applyFilters();
                        },
                      )
                    : null,
                filled: false,
                border: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _applyFilters();
              },
            ),
          ),

          const SizedBox(height: 20),

          // Filtros con diseño mejorado
          SizedBox(
            height: 44,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildFilterButton(AppLocalizations.of(context).all, 'all',
                    Icons.grid_view_rounded),
                const SizedBox(width: 12),
                _buildFilterButton(AppLocalizations.of(context).newest,
                    'recent', Icons.access_time),
                const SizedBox(width: 12),
                _buildFilterButton(AppLocalizations.of(context).oldest,
                    'oldest', Icons.history),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Construye un botón de filtro con icono
  Widget _buildFilterButton(String label, String value, IconData icon) {
    final isSelected = _selectedFilter == value;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isSelected ? Colors.transparent : Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            setState(() {
              _selectedFilter = value;
            });
            _applyFilters();
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 18,
                  color: isSelected ? AppColors.primaryColor : Colors.white,
                ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: GoogleFonts.cairo(
                    color: isSelected ? AppColors.primaryColor : Colors.white,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Construye el estado vacío (sin tarjetas)
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_not_supported,
            size: 100,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 24),
          Text(
            AppLocalizations.of(context).noSavedCards,
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).createCardToShowHere,
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Navegar a la página de creación de tarjetas
              Navigator.pop(context);
              // Aquí iría la navegación a la página de creación
            },
            icon: const Icon(Icons.add),
            label: Text(
              AppLocalizations.of(context).createNewCard,
              style: GoogleFonts.cairo(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Construye la cuadrícula de tarjetas
  Widget _buildCardsGrid() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _filteredCards.length,
      itemBuilder: (context, index) {
        final card = _filteredCards[index];
        // Usar animación para cada elemento
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            final delay = index * 0.1;
            final start = delay;
            final end = delay + 0.4;

            final animationValue = _animation.value;
            final opacity = animationValue < start
                ? 0.0
                : animationValue > end
                    ? 1.0
                    : (animationValue - start) / (end - start);

            final slideValue = animationValue < start
                ? 0.0
                : animationValue > end
                    ? 1.0
                    : (animationValue - start) / (end - start);

            return Opacity(
              opacity: opacity,
              child: Transform.translate(
                offset: Offset(0, 20 * (1 - slideValue)),
                child: child,
              ),
            );
          },
          child: _buildCardItem(card, index),
        );
      },
    );
  }

  /// Construye un elemento de tarjeta para la cuadrícula con diseño mejorado
  Widget _buildCardItem(File card, int index) {
    // Obtener fecha de modificación para mostrarla
    final lastModified = card.lastModifiedSync();
    final formattedDate = DateFormat('dd/MM/yyyy').format(lastModified);

    return GestureDetector(
      onTap: () => _showCardDetails(card),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(20),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Imagen de la tarjeta
              Positioned.fill(
                child: Hero(
                  tag: 'card_${card.path}',
                  child: Image.file(
                    card,
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              // Gradiente inferior para mejorar la legibilidad
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                height: 80,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withAlpha(160),
                      ],
                    ),
                  ),
                ),
              ),

              // Fecha de creación
              Positioned(
                bottom: 8,
                right: 12,
                child: Text(
                  formattedDate,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              // Barra de acciones
              Positioned(
                bottom: 4,
                left: 4,
                child: Row(
                  children: [
                    // Botón de compartir
                    _buildCardActionButton(
                      icon: Icons.share_rounded,
                      tooltip: 'مشاركة',
                      onPressed: () => _shareCard(card),
                    ),

                    // Botón de imprimir
                    _buildCardActionButton(
                      icon: Icons.print_rounded,
                      tooltip: 'طباعة',
                      onPressed: () => _printCard(card),
                    ),

                    // Botón de eliminar
                    _buildCardActionButton(
                      icon: Icons.delete_rounded,
                      tooltip: 'حذف',
                      color: Colors.red.shade300,
                      onPressed: () => _showDeleteConfirmation(card),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Construye un botón de acción para la tarjeta
  Widget _buildCardActionButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: onPressed,
        child: Tooltip(
          message: tooltip,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Icon(
              icon,
              size: 22,
              color: color ?? Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// Muestra un diálogo de confirmación para eliminar una tarjeta
  void _showDeleteConfirmation(File card) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'حذف البطاقة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'هل أنت متأكد من حذف هذه البطاقة؟',
              style: GoogleFonts.cairo(),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'لا يمكن التراجع عن هذا الإجراء.',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteCard(card);
            },
            icon: const Icon(Icons.delete),
            label: Text(
              'حذف',
              style: GoogleFonts.cairo(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// Muestra los detalles de la tarjeta en una ventana emergente
  void _showCardDetails(File card) {
    // Obtener fecha de modificación
    final lastModified = card.lastModifiedSync();
    final formattedDate = DateFormat('dd/MM/yyyy HH:mm').format(lastModified);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Barra superior con botón de cerrar
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'تفاصيل البطاقة',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),

              // Imagen de la tarjeta
              Flexible(
                child: Hero(
                  tag: 'card_${card.path}',
                  child: Image.file(
                    card,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              // Información de la tarjeta
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تاريخ الإنشاء: $formattedDate',
                      style: GoogleFonts.cairo(),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'المسار: ${card.path}',
                      style: GoogleFonts.cairo(fontSize: 12),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Botones de acción
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.share,
                      label: 'مشاركة',
                      color: Colors.blue,
                      onPressed: () => _shareCard(card),
                    ),
                    _buildActionButton(
                      icon: Icons.print,
                      label: 'طباعة',
                      color: AppColors.primaryColor,
                      onPressed: () {
                        Navigator.of(context).pop();
                        _printCard(card);
                      },
                    ),
                    _buildActionButton(
                      icon: Icons.delete,
                      label: 'حذف',
                      color: Colors.red,
                      onPressed: () {
                        Navigator.of(context).pop();
                        _showDeleteConfirmation(card);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Construye un botón de acción para el diálogo de detalles
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(
        label,
        style: GoogleFonts.cairo(),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }
}
