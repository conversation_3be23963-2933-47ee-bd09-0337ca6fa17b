import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

// FontManager removed
import '../../../../injection_container.dart';
import '../../domain/entities/profile_entity.dart';
import '../../domain/usecases/upload_profile_image_usecase.dart';
import '../blocs/profile/profile_bloc.dart';
import '../blocs/profile/profile_event.dart';

class ProfileForm extends StatefulWidget {
  final ProfileEntity profile;
  const ProfileForm({super.key, required this.profile});

  @override
  State<ProfileForm> createState() => _ProfileFormState();
}

class _ProfileFormState extends State<ProfileForm> {
  // متحكمات النص
  late TextEditingController _nameController;
  late TextEditingController _phoneController;

  // رابط الصورة الحالية + ملف الصورة المختار (إن وجد)
  late String _currentImageUrl;
  File? _pickedImageFile;

  // مؤشر التحميل لزر التحديث
  bool _isUploadingImage = false;

  // رسائل الخطأ
  String? _nameError;
  String? _phoneError;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.profile.name);
    _phoneController = TextEditingController(text: widget.profile.phone);
    _currentImageUrl = widget.profile.imageUrl;

    // إضافة مستمعين لإزالة رسائل الخطأ عند بدء الكتابة وتحديث حالة الزر
    _nameController.addListener(() {
      setState(() {
        if (_nameError != null) {
          _nameError = null;
        }
      });
    });

    _phoneController.addListener(() {
      setState(() {
        if (_phoneError != null) {
          _phoneError = null;
        }
      });
    });
  }

  // في حال تغيرت بيانات البروفايل من الخارج، لا نفقد قيمنا
  @override
  void didUpdateWidget(covariant ProfileForm oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.profile != widget.profile) {
      _nameController.text = widget.profile.name;
      _phoneController.text = widget.profile.phone;
      _currentImageUrl = widget.profile.imageUrl;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  /// اختيار الصورة من المعرض
  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile == null) return;
    setState(() {
      _pickedImageFile = File(pickedFile.path);
    });
  }

  /// التحقق من صحة البيانات بدون عرض رسائل خطأ (للتحقق من حالة الزر)
  bool _isFormValid() {
    // التحقق من الاسم
    if (_nameController.text.trim().isEmpty || _nameController.text.trim().length < 2) {
      return false;
    }

    // التحقق من رقم الهاتف (إجباري)
    if (_phoneController.text.trim().isEmpty) {
      return false;
    }

    final phoneRegex = RegExp(r'^[+]?[0-9]{10,15}$');
    if (!phoneRegex.hasMatch(_phoneController.text.trim())) {
      return false;
    }

    return true;
  }

  /// التحقق من صحة البيانات مع عرض رسائل الخطأ
  bool _validateForm() {
    setState(() {
      _nameError = null;
      _phoneError = null;
    });

    bool isValid = true;

    // التحقق من الاسم
    if (_nameController.text.trim().isEmpty) {
      setState(() {
        _nameError = AppLocalizations.of(context).fieldRequired;
      });
      isValid = false;
    } else if (_nameController.text.trim().length < 2) {
      setState(() {
        _nameError = 'يجب أن يكون الاسم أكثر من حرفين';
      });
      isValid = false;
    }

    // التحقق من رقم الهاتف (إجباري)
    if (_phoneController.text.trim().isEmpty) {
      setState(() {
        _phoneError = AppLocalizations.of(context).fieldRequired;
      });
      isValid = false;
    } else {
      final phoneRegex = RegExp(r'^[+]?[0-9]{10,15}$');
      if (!phoneRegex.hasMatch(_phoneController.text.trim())) {
        setState(() {
          _phoneError = 'رقم الهاتف غير صحيح';
        });
        isValid = false;
      }
    }

    return isValid;
  }

  /// تحديث الملف الشخصي بعد رفع الصورة (إذا وُجدت صورة جديدة)
  Future<void> _onUpdatePressed() async {
    // التحقق من صحة البيانات أولاً
    if (!_validateForm()) {
      return;
    }

    setState(() => _isUploadingImage = true);

    try {
      // رفع الصورة إذا اختار المستخدم صورة جديدة
      if (_pickedImageFile != null) {
        final uploadUseCase = sl<UploadProfileImageUseCase>();
        final result = await uploadUseCase(
          UploadProfileImageParams(
            uid: widget.profile.uid,
            imageFile: _pickedImageFile!,
          ),
        );

        bool uploadFailed = false;
        result.fold(
          (failure) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content:
                        Text('${AppLocalizations.of(context).error}: $failure')),
              );
            }
            uploadFailed = true;
          },
          (downloadUrl) {
            _currentImageUrl = downloadUrl;
          },
        );

        if (uploadFailed) {
          if (mounted) {
            setState(() => _isUploadingImage = false);
          }
          return;
        }
      }

      // إنشاء نسخة محدثة من الملف الشخصي
      final updatedProfile = widget.profile.copyWith(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        imageUrl: _currentImageUrl,
      );

      // إرسال الحدث لتحديث البيانات
      if (mounted) {
        context.read<ProfileBloc>().add(UpdateUserProfileEvent(updatedProfile));
      }
    } finally {
      if (mounted) {
        setState(() => _isUploadingImage = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // إذا كانت لدينا صورة مختارة، نعرضها، وإلا نستخدم رابط الصورة أو الأيقونة الافتراضية
    ImageProvider? imageProvider;
    if (_pickedImageFile != null) {
      imageProvider = FileImage(_pickedImageFile!);
    } else if (_currentImageUrl.isNotEmpty) {
      imageProvider = NetworkImage(_currentImageUrl);
    }

    // استخدام ألوان التطبيق الأساسية
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color primaryColor =
        Theme.of(context).primaryColor; // استخدام اللون الأساسي للتطبيق
    final Color accentColor = Theme.of(context)
        .colorScheme
        .secondary; // استخدام اللون الثانوي للتطبيق
    final Color cardColor = isDarkMode
        ? const Color(0xFF424242) // رمادي داكن للوضع الليلي
        : Colors.white; // أبيض للوضع النهاري
    final Color textColor = isDarkMode
        ? Colors.white // أبيض للوضع الليلي
        : Colors.black87; // أسود للوضع النهاري
    final Color textFieldBgColor = isDarkMode
        ? const Color(0xFF303030) // رمادي داكن للوضع الليلي
        : Colors.grey[100]!; // رمادي فاتح للوضع النهاري

    // تحديد الخط حسب اللغة

    return Scaffold(
      // استخدام خلفية متدرجة عصرية
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              primaryColor,
              accentColor,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // شريط علوي مع عنوان وأيقونة رجوع
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // عنوان الصفحة
                      Text(
                        AppLocalizations.of(context).personalInfo,
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      // زر الرجوع
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(51),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          icon: const Icon(Icons.arrow_forward_ios),
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),

                // صورة الملف الشخصي مع تأثيرات ظل
                const SizedBox(height: 24),
                Stack(
                  alignment: Alignment.center,
                  clipBehavior: Clip.none,
                  children: [
                    // الصورة الدائرية مع ظل
                    Container(
                      width: 140,
                      height: 140,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: cardColor,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(51),
                            blurRadius: 15,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(4.0), // إطار داخلي أبيض
                        child: ClipOval(
                          child: imageProvider != null
                              ? Image(
                                  image: imageProvider,
                                  fit: BoxFit.cover,
                                )
                              : Icon(
                                  Icons.person,
                                  size: 70,
                                  color: Colors.grey[400],
                                ),
                        ),
                      ),
                    ),

                    // أيقونة تعديل الصورة بتصميم عصري
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: _pickImage,
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: primaryColor,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(51),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(10),
                          child: const Icon(
                            Icons.camera_alt_rounded,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                // اسم المستخدم تحت الصورة مع تأثير ظل للنص
                const SizedBox(height: 16),
                Text(
                  _nameController.text.isNotEmpty
                      ? _nameController.text
                      : AppLocalizations.of(context).username,
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),

                // المسافة بين الجزء العلوي وحقول الإدخال
                const SizedBox(height: 40),

                // حاوية لعرض حقول الإدخال بتصميم عصري
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 24),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
                  decoration: BoxDecoration(
                    color: cardColor,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(26),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان القسم
                      Text(
                        AppLocalizations.of(context).personalInfo,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // الحقل الأول: الاسم + أيقونة المستخدم
                      _buildModernTextField(
                        controller: _nameController,
                        label: AppLocalizations.of(context).name,
                        icon: Icons.person_outline_rounded,
                        readOnly: false,
                        textColor: textColor,
                        bgColor: textFieldBgColor,
                        fontFamily: 'Cairo',
                        errorText: _nameError,
                      ),
                      const SizedBox(height: 20),

                      // الحقل الثاني: البريد الإلكتروني + أيقونة البريد
                      _buildModernTextField(
                        controller:
                            TextEditingController(text: widget.profile.email),
                        label: AppLocalizations.of(context).email,
                        icon: Icons.email_outlined,
                        readOnly: true,
                        textColor: textColor,
                        bgColor: textFieldBgColor,
                        fontFamily: 'Cairo',
                      ),
                      const SizedBox(height: 20),

                      // الحقل الثالث: رقم الهاتف + أيقونة الهاتف
                      _buildModernTextField(
                        controller: _phoneController,
                        label: AppLocalizations.of(context).phoneNumber,
                        icon: Icons.phone_outlined,
                        readOnly: false,
                        textColor: textColor,
                        bgColor: textFieldBgColor,
                        fontFamily: 'Cairo',
                        errorText: _phoneError,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 40),

                // زر التحديث بتصميم عصري
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: (_isUploadingImage || !_isFormValid()) ? null : _onUpdatePressed,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: Colors.white,
                        elevation: 8,
                        shadowColor: primaryColor.withAlpha(128),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: _isUploadingImage
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.save_rounded),
                                const SizedBox(width: 10),
                                Text(
                                  AppLocalizations.of(context).updateProfile,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ),

                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// دالة لبناء حقول الإدخال بتصميم عصري
  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required Color textColor,
    required Color bgColor,
    required String fontFamily,
    bool readOnly = false,
    String? errorText,
  }) {
    // استخدام الخط المناسب حسب اللغة
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    final hasError = errorText != null && errorText.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(15),
            border: hasError
                ? Border.all(color: Colors.red, width: 1.5)
                : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: controller,
            readOnly: readOnly,
            textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
            style: TextStyle(
              fontSize: 16,
              color: textColor,
            ),
            decoration: InputDecoration(
              filled: false,
              labelText: label,
              labelStyle: TextStyle(
                fontSize: 14,
                color: hasError ? Colors.red : textColor.withAlpha(180),
              ),
              prefixIcon: isArabic
                  ? null
                  : Icon(icon,
                      color: hasError ? Colors.red : textColor.withAlpha(150)),
              suffixIcon: isArabic
                  ? Icon(icon,
                      color: hasError ? Colors.red : textColor.withAlpha(150))
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide.none,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            ),
          ),
        ),
        if (hasError) ...[
          const SizedBox(height: 6),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              errorText,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
