import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/usecases/get_profile_usecase.dart';
import '../../../domain/usecases/update_profile_usecase.dart';
import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final GetProfileUseCase? getProfileUseCase;
  final UpdateProfileUseCase? updateProfileUseCase;

  ProfileBloc({
    this.getProfileUseCase,
    this.updateProfileUseCase,
  }) : super(ProfileInitial()) {
    on<LoadUserProfileEvent>(_onLoadProfile);
    on<UpdateUserProfileEvent>(_onUpdateProfile);
  }

  Future<void> _onLoadProfile(
      LoadUserProfileEvent event, Emitter<ProfileState> emit) async {
    emit(ProfileLoading());
    if (getProfileUseCase == null) {
      emit(ProfileError('لا يوجد UseCase لجلب البيانات'));
      return;
    }
    final result = await getProfileUseCase!(ProfileParams(uid: event.uid));
    result.fold(
      (failure) => emit(ProfileError(failure.message)),
      (profile) => emit(ProfileLoaded(profile)),
    );
  }

  Future<void> _onUpdateProfile(
      UpdateUserProfileEvent event, Emitter<ProfileState> emit) async {
    emit(ProfileLoading());
    if (updateProfileUseCase == null) {
      emit(ProfileError('لا يوجد UseCase لتحديث البيانات'));
      return;
    }
    final result = await updateProfileUseCase!(event.profile);
    result.fold(
      (failure) => emit(ProfileError(failure.message)),
      (updated) => emit(ProfileUpdateSuccess(updated)),
    );
  }
}
