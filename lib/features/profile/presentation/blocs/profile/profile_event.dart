import 'package:equatable/equatable.dart';

import '../../../domain/entities/profile_entity.dart';

abstract class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

class LoadUserProfileEvent extends ProfileEvent {
  final String uid;
  const LoadUserProfileEvent(this.uid);

  @override
  List<Object?> get props => [uid];
}

class UpdateUserProfileEvent extends ProfileEvent {
  final ProfileEntity profile;
  const UpdateUserProfileEvent(this.profile);

  @override
  List<Object?> get props => [profile];
}
