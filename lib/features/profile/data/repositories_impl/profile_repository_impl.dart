// data/repositories/profile_repository_impl.dart
import 'dart:io';

import 'package:dartz/dartz.dart';

import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/profile_entity.dart';
import '../../domain/repositories/profile_repository.dart';
import '../datasources/profile_remote_data_source.dart';
import '../models/profile_model.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  final ProfileRemoteDataSource remoteDataSource;
  ProfileRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, ProfileEntity>> getUserProfile(String uid) async {
    try {
      final model = await remoteDataSource.getUserProfile(uid);
      return Right(model);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, ProfileEntity>> updateUserProfile(
      ProfileEntity profile) async {
    try {
      final model = ProfileModel(
        uid: profile.uid,
        name: profile.name,
        email: profile.email,
        phone: profile.phone,
        imageUrl: profile.imageUrl,
      );
      final updated = await remoteDataSource.updateUserProfile(model);
      return Right(updated);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, String>> uploadProfileImage(
      String uid, File imageFile) async {
    try {
      final imageUrl =
          await remoteDataSource.uploadProfileImage(uid, imageFile);
      return Right(imageUrl);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}
