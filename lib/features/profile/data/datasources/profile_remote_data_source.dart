// data/datasources/profile_remote_data_source.dart
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../../../core/errors/exceptions.dart';
import '../models/profile_model.dart';

abstract class ProfileRemoteDataSource {
  Future<ProfileModel> getUserProfile(String uid);
  Future<ProfileModel> updateUserProfile(ProfileModel profile);
  Future<String> uploadProfileImage(String uid, File imageFile);
}

class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  final FirebaseFirestore firestore;
  ProfileRemoteDataSourceImpl({required this.firestore});

  @override
  Future<ProfileModel> getUserProfile(String uid) async {
    try {
      final doc = await firestore.collection('users').doc(uid).get();
      if (!doc.exists) {
        throw ServerException(message: 'المستخدم غير موجود');
      }
      return ProfileModel.fromMap(uid, doc.data()!);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<ProfileModel> updateUserProfile(ProfileModel profile) async {
    try {
      await firestore
          .collection('users')
          .doc(profile.uid)
          .update(profile.toMap());
      return profile;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<String> uploadProfileImage(String uid, File imageFile) async {
    try {
      String fileName =
          'profile_${uid}_${DateTime.now().millisecondsSinceEpoch}';
      Reference storageRef = FirebaseStorage.instance
          .ref()
          .child('profile_images')
          .child(fileName);
      UploadTask uploadTask = storageRef.putFile(imageFile);
      TaskSnapshot snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
