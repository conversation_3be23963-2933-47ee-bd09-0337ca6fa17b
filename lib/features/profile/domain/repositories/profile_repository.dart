// domain/repositories/profile_repository.dart
import 'dart:io';

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../entities/profile_entity.dart';

abstract class ProfileRepository {
  Future<Either<Failure, ProfileEntity>> getUserProfile(String uid);
  Future<Either<Failure, ProfileEntity>> updateUserProfile(
      ProfileEntity profile);
  Future<Either<Failure, String>> uploadProfileImage(
      String uid, File imageFile);
}
