class ProfileEntity {
  final String uid;
  final String name;
  final String email;
  final String phone;
  final String imageUrl;

  const ProfileEntity({
    required this.uid,
    required this.name,
    required this.email,
    required this.phone,
    required this.imageUrl,
  });

  ProfileEntity copyWith({
    String? uid,
    String? name,
    String? email,
    String? phone,
    String? imageUrl,
  }) {
    return ProfileEntity(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }
}
