// domain/usecases/upload_profile_image_usecase.dart
import 'dart:io';

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/profile_repository.dart';

class UploadProfileImageUseCase
    implements UseCase<String, UploadProfileImageParams> {
  final ProfileRepository repository;
  UploadProfileImageUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(UploadProfileImageParams params) async {
    return await repository.uploadProfileImage(params.uid, params.imageFile);
  }
}

class UploadProfileImageParams {
  final String uid;
  final File imageFile;

  UploadProfileImageParams({required this.uid, required this.imageFile});
}
