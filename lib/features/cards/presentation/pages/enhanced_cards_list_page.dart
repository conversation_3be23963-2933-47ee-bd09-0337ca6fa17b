// lib/features/cards/presentation/pages/enhanced_cards_list_page.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/constants/app_colors.dart';
import '../widgets/filter_bottom_sheet.dart';

/// كيان بطاقة التهنئة المبسط للاستخدام في صفحة قائمة البطاقات
class GreetingCardEntity extends Equatable {
  final String id;
  final String title;
  final String thumbnailUrl;
  final String? occasionName;
  final dynamic occasionId; // استخدام dynamic للتعامل مع كل من int و string
  final DateTime createdAt;
  final DateTime lastModified;

  const GreetingCardEntity({
    required this.id,
    required this.title,
    required this.thumbnailUrl,
    this.occasionName,
    this.occasionId, // يمكن أن يكون int أو string أو null
    required this.createdAt,
    required this.lastModified,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        thumbnailUrl,
        occasionName,
        occasionId,
        createdAt,
        lastModified,
      ];
}

/// صفحة عرض قائمة البطاقات المحسنة
class EnhancedCardsListPage extends StatefulWidget {
  /// معرف المناسبة (اختياري)
  /// يجب أن يكون String لضمان اتساق نوع البيانات
  final String? occasionId;

  /// اسم المناسبة (اختياري)
  final String? occasionName;

  /// المنشئ
  const EnhancedCardsListPage({
    super.key,
    this.occasionId,
    this.occasionName,
  });

  @override
  State<EnhancedCardsListPage> createState() => _EnhancedCardsListPageState();
}

class _EnhancedCardsListPageState extends State<EnhancedCardsListPage> {
  /// قائمة البطاقات
  List<GreetingCardEntity> _cards = [];

  /// قائمة البطاقات المفلترة
  List<GreetingCardEntity> _filteredCards = [];

  /// حالة التحميل
  bool _isLoading = false;

  /// رسالة الخطأ
  String _errorMessage = '';

  /// تحكم البحث
  final TextEditingController _searchController = TextEditingController();

  /// معايير الفلترة
  FilterCriteria _filterCriteria = FilterCriteria();

  /// قائمة الفلاتر النشطة
  List<String> _activeFilters = [];

  @override
  void initState() {
    super.initState();
    _loadCards();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البطاقات حسب المناسبة
  Future<void> _loadCards() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      if (kDebugMode) {
        debugPrint('🔍 البحث عن البطاقات:');
        debugPrint('🔍 معرف المناسبة: ${widget.occasionId}');
        debugPrint('🔍 اسم المناسبة: ${widget.occasionName}');
      }

      // جلب جميع البطاقات من Firestore
      final snapshot =
          await FirebaseFirestore.instance.collection('cards').get();
      if (kDebugMode) {
        debugPrint('🔍 تم العثور على ${snapshot.docs.length} بطاقة في المجموع');
      }

      // تحويل المستندات إلى كيانات
      List<GreetingCardEntity> allCards = [];

      // استخدام حلقة for بدلاً من map لمعالجة الاستثناءات بشكل أفضل
      for (var doc in snapshot.docs) {
        try {
          final data = doc.data();

          // استخراج معرف المناسبة من البيانات
          dynamic occasionId;
          try {
            occasionId = data['occasionId'];
          } catch (error) {
            // إذا حدث خطأ في استخراج معرف المناسبة، نستخدم قيمة افتراضية
            occasionId = null;
            if (kDebugMode) {
              debugPrint('⚠️ خطأ في استخراج معرف المناسبة: $error');
            }
          }

          if (kDebugMode) {
            debugPrint(
                '🔍 معرف المناسبة: $occasionId (النوع: ${occasionId?.runtimeType}) - البطاقة: ${data['title']}');

            // طباعة قائمة المناسبات للتشخيص
            debugPrint('🔍 قائمة المناسبات المتاحة:');
            for (var occasion in occasionsList) {
              debugPrint(
                  '   - معرف: ${occasion['id']} (${occasion['id'].runtimeType}), اسم: ${occasion['name']}, كود: ${occasion['code']}');
            }
          }

          // معالجة تاريخ الإنشاء
          DateTime createdAt;
          try {
            if (data['createdAt'] != null) {
              if (data['createdAt'] is Timestamp) {
                createdAt = (data['createdAt'] as Timestamp).toDate();
              } else {
                createdAt = DateTime.parse(data['createdAt'].toString());
              }
            } else {
              createdAt = DateTime.now();
            }
          } catch (e) {
            if (kDebugMode) {
              debugPrint('⚠️ خطأ في معالجة تاريخ الإنشاء: $e');
            }
            createdAt = DateTime.now();
          }

          // معالجة تاريخ التعديل
          DateTime lastModified;
          try {
            if (data['lastModified'] != null) {
              if (data['lastModified'] is Timestamp) {
                lastModified = (data['lastModified'] as Timestamp).toDate();
              } else {
                lastModified = DateTime.parse(data['lastModified'].toString());
              }
            } else {
              lastModified = DateTime.now();
            }
          } catch (e) {
            if (kDebugMode) {
              debugPrint('⚠️ خطأ في معالجة تاريخ التعديل: $e');
            }
            lastModified = DateTime.now();
          }

          // إنشاء كيان البطاقة وإضافته إلى القائمة
          final card = GreetingCardEntity(
            id: doc.id,
            title: data['title'] ?? '',
            thumbnailUrl: data['imageUrl'] ?? '',
            occasionName: data['occasionName'] ?? '',
            occasionId: occasionId, // استخدام القيمة كما هي (dynamic)
            createdAt: createdAt,
            lastModified: lastModified,
          );

          // إضافة البطاقة إلى القائمة
          allCards.add(card);
        } catch (error) {
          // تجاهل البطاقات التي تسبب أخطاء وطباعة معلومات التصحيح
          if (kDebugMode) {
            debugPrint('⚠️ خطأ في معالجة البطاقة: ${doc.id} - الخطأ: $error');
          }
          continue;
        }
      }

      // تصفية البطاقات حسب المعايير
      List<GreetingCardEntity> filteredCards = [];

      // 1. إذا كان معرف المناسبة متوفراً، نبحث عن البطاقات التي تطابق هذا المعرف
      if (widget.occasionId != null && widget.occasionId!.isNotEmpty) {
        // التأكد من أن معرف المناسبة هو نص (String) للمقارنة
        final String safeOccasionId = widget.occasionId.toString();

        if (kDebugMode) {
          debugPrint('🔍 البحث عن البطاقات بمعرف المناسبة: $safeOccasionId');
          debugPrint('🔍 عدد البطاقات الكلي: ${allCards.length}');
        }

        // البحث عن البطاقات بمعرف المناسبة
        filteredCards = allCards.where((card) {
          // طباعة معلومات تشخيصية عن كل بطاقة
          if (kDebugMode) {
            debugPrint(
                '🔍 مقارنة: card.occasionId (${card.occasionId.runtimeType}): ${card.occasionId} مع safeOccasionId (String): $safeOccasionId');
          }

          // إذا كان معرف المناسبة null في البطاقة، نتخطاها
          if (card.occasionId == null) {
            return false;
          }

          // إذا كان معرف المناسبة المطلوب فارغًا، نعرض جميع البطاقات
          if (safeOccasionId.isEmpty) {
            return true;
          }

          // تحويل معرف المناسبة المطلوب إلى int للمقارنة مع البطاقات القديمة
          final int? requestedOccasionIdInt = int.tryParse(safeOccasionId);

          if (kDebugMode) {
            debugPrint('🔍 مقارنة البطاقة: ${card.title}');
            debugPrint(
                '   - معرف المناسبة المطلوب: $safeOccasionId (${safeOccasionId.runtimeType})');
            debugPrint(
                '   - معرف المناسبة المطلوب كرقم: $requestedOccasionIdInt');
            debugPrint(
                '   - معرف المناسبة في البطاقة: ${card.occasionId} (${card.occasionId.runtimeType})');
          }

          // مقارنة حسب نوع البيانات
          bool match = false;

          // 1. مقارنة مباشرة (قد تنجح إذا كان النوعان متطابقين)
          if (card.occasionId == requestedOccasionIdInt) {
            match = true;
            if (kDebugMode) {
              debugPrint(
                  '   - ✅ تطابق مباشر بين ${card.occasionId} و $requestedOccasionIdInt');
            }
          }
          // 2. مقارنة بعد تحويل معرف المناسبة في البطاقة إلى رقم
          else if (requestedOccasionIdInt != null) {
            final cardOccasionIdInt = card.occasionId is int
                ? card.occasionId
                : int.tryParse(card.occasionId.toString());

            if (cardOccasionIdInt == requestedOccasionIdInt) {
              match = true;
              if (kDebugMode) {
                debugPrint(
                    '   - ✅ تطابق بعد التحويل إلى رقم: $cardOccasionIdInt == $requestedOccasionIdInt');
              }
            } else {
              if (kDebugMode) {
                debugPrint(
                    '   - ❌ عدم تطابق الأرقام: $cardOccasionIdInt != $requestedOccasionIdInt');
              }
            }
          }
          // 3. مقارنة كنصوص
          else if (card.occasionId.toString() == safeOccasionId) {
            match = true;
            if (kDebugMode) {
              debugPrint(
                  '   - ✅ تطابق النصوص: ${card.occasionId} == $safeOccasionId');
            }
          } else {
            if (kDebugMode) {
              debugPrint(
                  '   - ❌ عدم تطابق النصوص: ${card.occasionId} != $safeOccasionId');
            }
          }

          // 4. مقارنة مع كود المناسبة (للتوافق مع البطاقات القديمة)
          if (!match) {
            // البحث عن المناسبة المقابلة للمعرف المطلوب
            Map<String, dynamic> requestedOccasion;

            if (requestedOccasionIdInt != null) {
              // البحث عن المناسبة بالمعرف الرقمي
              requestedOccasion = occasionsList.firstWhere(
                (o) => o['id'] == requestedOccasionIdInt,
                orElse: () => {'id': 0, 'name': '', 'code': ''},
              );
            } else {
              // البحث عن المناسبة بالكود
              requestedOccasion = occasionsList.firstWhere(
                (o) => o['code'] == safeOccasionId,
                orElse: () => {'id': 0, 'name': '', 'code': ''},
              );
            }

            if (requestedOccasion['code'] != '') {
              // 4.1 مقارنة كود المناسبة مع معرف المناسبة في البطاقة
              if (card.occasionId.toString() == requestedOccasion['code']) {
                match = true;
                if (kDebugMode) {
                  debugPrint(
                      '   - ✅ تطابق كود المناسبة مع معرف المناسبة في البطاقة: ${requestedOccasion['code']} == ${card.occasionId}');
                }
              }
              // 4.2 مقارنة معرف المناسبة الرقمي مع معرف المناسبة في البطاقة
              else if (card.occasionId is int &&
                  requestedOccasion['id'] is int &&
                  card.occasionId == requestedOccasion['id']) {
                match = true;
                if (kDebugMode) {
                  debugPrint(
                      '   - ✅ تطابق معرف المناسبة الرقمي: ${requestedOccasion['id']} == ${card.occasionId}');
                }
              }
              // 4.3 مقارنة معرف المناسبة الرقمي مع معرف المناسبة في البطاقة بعد التحويل
              else if (int.tryParse(card.occasionId.toString()) ==
                  requestedOccasion['id']) {
                match = true;
                if (kDebugMode) {
                  debugPrint(
                      '   - ✅ تطابق معرف المناسبة الرقمي بعد التحويل: ${requestedOccasion['id']} == ${int.tryParse(card.occasionId.toString())}');
                }
              } else {
                if (kDebugMode) {
                  debugPrint(
                      '   - ❌ عدم تطابق كود المناسبة مع معرف المناسبة في البطاقة: ${requestedOccasion['code']} != ${card.occasionId}');
                }
              }
            }
          }

          if (match && kDebugMode) {
            debugPrint('✅ النتيجة النهائية: تطابق البطاقة ${card.title}');
          } else if (kDebugMode) {
            debugPrint('❌ النتيجة النهائية: عدم تطابق البطاقة ${card.title}');
          }
          return match;
        }).toList();
      }
      // 2. إذا كان اسم المناسبة متوفراً، نبحث عن البطاقات التي تتضمن هذا الاسم
      else if (widget.occasionName != null && widget.occasionName!.isNotEmpty) {
        // استخدام طريقة بحث محسنة للنصوص العربية
        filteredCards = allCards.where((card) {
          // البحث في اسم المناسبة
          final occasionNameMatch = card.occasionName != null &&
              card.occasionName!.contains(widget.occasionName!);

          // البحث في عنوان البطاقة
          final titleMatch = card.title.contains(widget.occasionName!);

          return occasionNameMatch || titleMatch;
        }).toList();
      }
      // 3. إذا لم يتم توفير أي معايير، نعرض جميع البطاقات
      else {
        if (kDebugMode) {
          debugPrint('🔍 عرض جميع البطاقات (${allCards.length})');
        }
        filteredCards = allCards;
      }

      setState(() {
        _cards = filteredCards;
        _filteredCards = filteredCards;
        _isLoading = false;

        // إضافة الفلتر النشط إذا كان هناك مناسبة محددة
        _activeFilters = [];
        if (widget.occasionName != null && widget.occasionName!.isNotEmpty) {
          _activeFilters.add('المناسبة: ${widget.occasionName}');
        } else if (widget.occasionId != null && widget.occasionId!.isNotEmpty) {
          // البحث عن اسم المناسبة المقابل للمعرف
          // التأكد من أن معرف المناسبة هو نص (String) للمقارنة
          final String safeOccasionId = widget.occasionId.toString();

          // تحويل معرف المناسبة إلى رقم للبحث
          final int? occasionIdInt = int.tryParse(safeOccasionId);

          final occasion = occasionsList.firstWhere(
            (o) => o['id'] == occasionIdInt,
            orElse: () => {'id': 0, 'name': ''},
          );
          if (occasion['name']!.isNotEmpty) {
            _activeFilters.add('المناسبة: ${occasion['name']}');
          }
        }
      });
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ: $e');
      }
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل البطاقات: $e';
        _isLoading = false;
      });
    }
  }

  /// فلترة البطاقات حسب معايير البحث
  void _filterCards() {
    if (_cards.isEmpty) return;

    final query = _searchController.text.toLowerCase();

    setState(() {
      if (query.isEmpty && _filterCriteria.occasionId == null) {
        // إذا لم يكن هناك بحث أو فلتر، نعرض جميع البطاقات
        _filteredCards = _cards;
        _activeFilters = [];
      } else {
        // فلترة البطاقات حسب البحث
        _filteredCards = _cards.where((card) {
          bool matchesSearch = true;
          if (query.isNotEmpty) {
            matchesSearch = card.title.toLowerCase().contains(query) ||
                (card.occasionName != null &&
                    card.occasionName!.toLowerCase().contains(query));
          }

          bool matchesOccasion = true;
          if (_filterCriteria.occasionId != null) {
            // التأكد من أن معرف المناسبة هو نص (String) للمقارنة
            final String safeOccasionId = _filterCriteria.occasionId.toString();

            // إذا كان معرف المناسبة null في البطاقة، نتخطاها
            if (card.occasionId == null) {
              matchesOccasion = false;
            } else {
              // تحويل معرف المناسبة المطلوب إلى int للمقارنة مع البطاقات القديمة
              final int? filterOccasionIdInt = int.tryParse(safeOccasionId);

              if (kDebugMode) {
                debugPrint('🔍 فلترة البطاقة: ${card.title}');
                debugPrint(
                    '   - معرف المناسبة المطلوب: $safeOccasionId (${safeOccasionId.runtimeType})');
                debugPrint(
                    '   - معرف المناسبة المطلوب كرقم: $filterOccasionIdInt');
                debugPrint(
                    '   - معرف المناسبة في البطاقة: ${card.occasionId} (${card.occasionId.runtimeType})');
              }

              // 1. مقارنة مباشرة (قد تنجح إذا كان النوعان متطابقين)
              if (card.occasionId == filterOccasionIdInt) {
                matchesOccasion = true;
                if (kDebugMode) {
                  debugPrint(
                      '   - ✅ تطابق مباشر بين ${card.occasionId} و $filterOccasionIdInt');
                }
              }
              // 2. مقارنة بعد تحويل معرف المناسبة في البطاقة إلى رقم
              else if (filterOccasionIdInt != null) {
                final cardOccasionIdInt = card.occasionId is int
                    ? card.occasionId
                    : int.tryParse(card.occasionId.toString());

                if (cardOccasionIdInt == filterOccasionIdInt) {
                  matchesOccasion = true;
                  if (kDebugMode) {
                    debugPrint(
                        '   - ✅ تطابق بعد التحويل إلى رقم: $cardOccasionIdInt == $filterOccasionIdInt');
                  }
                } else {
                  if (kDebugMode) {
                    debugPrint(
                        '   - ❌ عدم تطابق الأرقام: $cardOccasionIdInt != $filterOccasionIdInt');
                  }
                }
              }
              // 3. مقارنة كنصوص
              else if (card.occasionId.toString() == safeOccasionId) {
                matchesOccasion = true;
                if (kDebugMode) {
                  debugPrint(
                      '   - ✅ تطابق النصوص: ${card.occasionId} == $safeOccasionId');
                }
              } else {
                if (kDebugMode) {
                  debugPrint(
                      '   - ❌ عدم تطابق النصوص: ${card.occasionId} != $safeOccasionId');
                }
              }

              // 4. مقارنة مع كود المناسبة (للتوافق مع البطاقات القديمة)
              if (!matchesOccasion && filterOccasionIdInt != null) {
                // البحث عن المناسبة المقابلة للمعرف المطلوب
                final requestedOccasion = occasionsList.firstWhere(
                  (o) => o['id'] == filterOccasionIdInt,
                  orElse: () => {'id': 0, 'name': '', 'code': ''},
                );

                if (requestedOccasion['code'] != '') {
                  // مقارنة كود المناسبة مع معرف المناسبة في البطاقة
                  if (card.occasionId.toString() == requestedOccasion['code']) {
                    matchesOccasion = true;
                    if (kDebugMode) {
                      debugPrint(
                          '   - ✅ تطابق كود المناسبة مع معرف المناسبة في البطاقة: ${requestedOccasion['code']} == ${card.occasionId}');
                    }
                  } else {
                    if (kDebugMode) {
                      debugPrint(
                          '   - ❌ عدم تطابق كود المناسبة مع معرف المناسبة في البطاقة: ${requestedOccasion['code']} != ${card.occasionId}');
                    }
                  }
                }
              }
            }

            if (kDebugMode && matchesOccasion) {
              debugPrint(
                  '✅ تطابق: card.occasionId: ${card.occasionId} مع safeOccasionId: $safeOccasionId');
            }
          }

          return matchesSearch && matchesOccasion;
        }).toList();

        // ترتيب البطاقات
        if (_filterCriteria.popularitySort) {
          // ترتيب حسب الشعبية (نفترض أن البطاقات الأحدث هي الأكثر شعبية)
          _filteredCards.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        } else if (_filterCriteria.newestFirst) {
          // ترتيب حسب الأحدث
          _filteredCards.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        } else {
          // ترتيب حسب الأقدم
          _filteredCards.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        }

        // تحديث الفلاتر النشطة
        _activeFilters = [];
        if (query.isNotEmpty) {
          _activeFilters.add('بحث: $query');
        }
        if (_filterCriteria.occasionId != null) {
          // التأكد من أن معرف المناسبة هو نص (String) للمقارنة
          final String safeOccasionId = _filterCriteria.occasionId.toString();

          // تحويل معرف المناسبة إلى رقم للبحث
          final int? occasionIdInt = int.tryParse(safeOccasionId);

          final occasion = occasionsList.firstWhere(
            (o) => o['id'] == occasionIdInt,
            orElse: () => {'id': 0, 'name': ''},
          );
          if (occasion['name']!.isNotEmpty) {
            _activeFilters.add('المناسبة: ${occasion['name']}');
          }
        }
        if (_filterCriteria.popularitySort) {
          _activeFilters.add('الترتيب: الأكثر شعبية');
        } else if (_filterCriteria.newestFirst) {
          _activeFilters.add('الترتيب: الأحدث أولاً');
        } else {
          _activeFilters.add('الترتيب: الأقدم أولاً');
        }
      }
    });
  }

  /// عرض نافذة الفلترة
  Future<void> _showFilterBottomSheet() async {
    final result = await showFilterBottomSheet(context, _filterCriteria);
    if (result != null) {
      setState(() {
        _filterCriteria = result;
        _filterCards();
      });
    }
  }

  /// بناء قائمة الفلاتر النشطة
  List<Widget> _buildActiveFilters() {
    return _activeFilters.map((filter) {
      return Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: Chip(
          label: Text(
            filter,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
          backgroundColor: AppColors.primaryColor,
          deleteIconColor: Colors.white,
          onDeleted: () {
            setState(() {
              if (filter.startsWith('بحث:')) {
                _searchController.clear();
              } else if (filter.startsWith('المناسبة:')) {
                _filterCriteria = _filterCriteria.copyWith(occasionId: null);
              } else if (filter.startsWith('الترتيب:')) {
                _filterCriteria = _filterCriteria.copyWith(
                  newestFirst: true,
                  popularitySort: false,
                );
              }
              _filterCards();
            });
          },
        ),
      );
    }).toList();
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل البطاقات...',
            style: GoogleFonts.cairo(),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage,
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadCards,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(
              'إعادة المحاولة',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بطاقات متطابقة',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          if (_activeFilters.isNotEmpty)
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _searchController.clear();
                  _filterCriteria = FilterCriteria();
                  _activeFilters = [];
                  _filteredCards = _cards;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: Text(
                'إزالة الفلاتر',
                style: GoogleFonts.cairo(color: Colors.white),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء شبكة البطاقات
  Widget _buildCardsGrid() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _filteredCards.length,
      itemBuilder: (context, index) {
        final card = _filteredCards[index];
        return _buildCardItem(card);
      },
    );
  }

  /// بناء عنصر البطاقة
  Widget _buildCardItem(GreetingCardEntity card) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26), // equivalent to opacity 0.1
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: () {
            Navigator.pushNamed(
              context,
              '/createCard',
              arguments: {'cardId': card.id, 'isAiMode': false},
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة البطاقة
              Expanded(
                child: Hero(
                  tag: 'card_${card.id}',
                  child: card.thumbnailUrl.isNotEmpty
                      ? Image.network(
                          card.thumbnailUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[200],
                              child: const Icon(
                                Icons.image_not_supported,
                                size: 50,
                                color: Colors.grey,
                              ),
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              color: Colors.grey[200],
                              child: Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes !=
                                          null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                                  strokeWidth: 2,
                                ),
                              ),
                            );
                          },
                        )
                      : Container(
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.image_not_supported,
                            size: 50,
                            color: Colors.grey,
                          ),
                        ),
                ),
              ),
              // معلومات البطاقة
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      card.title,
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (card.occasionName != null &&
                        card.occasionName!.isNotEmpty)
                      Text(
                        card.occasionName!,
                        style: GoogleFonts.cairo(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        title: Text(
          widget.occasionName ?? 'البطاقات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          // زر الفلترة
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            tooltip: 'فلترة',
            onPressed: _showFilterBottomSheet,
          ),
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            tooltip: 'تحديث',
            onPressed: _loadCards,
          ),
          // زر التشخيص (مخفي في الإصدار النهائي)
          if (kDebugMode)
            IconButton(
              icon: const Icon(Icons.bug_report, color: Colors.white),
              tooltip: 'تشخيص',
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text('معلومات التشخيص', style: GoogleFonts.cairo()),
                    content: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                              'معرف المناسبة: ${widget.occasionId ?? "غير محدد"}',
                              style: GoogleFonts.cairo()),
                          Text(
                              'اسم المناسبة: ${widget.occasionName ?? "غير محدد"}',
                              style: GoogleFonts.cairo()),
                          Text('عدد البطاقات: ${_filteredCards.length}',
                              style: GoogleFonts.cairo()),
                          const SizedBox(height: 10),
                          Text('البطاقات:',
                              style: GoogleFonts.cairo(
                                  fontWeight: FontWeight.bold)),
                          ..._filteredCards.map((card) => Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  '- ${card.title} (المناسبة: ${card.occasionName ?? "غير محدد"})',
                                  style: GoogleFonts.cairo(),
                                ),
                              )),
                        ],
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('إغلاق', style: GoogleFonts.cairo()),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن بطاقة...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _filterCards();
                          });
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _filterCards();
                });
              },
            ),
          ),

          // عرض الفلاتر النشطة
          if (_activeFilters.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: _buildActiveFilters(),
                ),
              ),
            ),

          // محتوى البطاقات
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _errorMessage.isNotEmpty
                    ? _buildErrorState()
                    : _filteredCards.isEmpty
                        ? _buildEmptyState()
                        : _buildCardsGrid(),
          ),
        ],
      ),
    );
  }
}
