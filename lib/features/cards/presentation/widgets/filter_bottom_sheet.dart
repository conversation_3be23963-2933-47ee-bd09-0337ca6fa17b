// lib/features/cards/presentation/widgets/filter_bottom_sheet.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../core/constants/app_colors.dart';

/// نموذج لتخزين معايير الفلترة
class FilterCriteria {
  final dynamic occasionId; // استخدام dynamic للتعامل مع كل من int و string
  final String? searchQuery;
  final bool newestFirst;
  final bool popularitySort;

  FilterCriteria({
    this.occasionId,
    this.searchQuery,
    this.newestFirst = true,
    this.popularitySort = false,
  });

  /// إنشاء نسخة جديدة مع تحديث بعض الحقول
  FilterCriteria copyWith({
    dynamic occasionId,
    String? searchQuery,
    bool? newestFirst,
    bool? popularitySort,
  }) {
    return FilterCriteria(
      occasionId: occasionId ?? this.occasionId,
      searchQuery: searchQuery ?? this.searchQuery,
      newestFirst: newestFirst ?? this.newestFirst,
      popularitySort: popularitySort ?? this.popularitySort,
    );
  }

  /// التحقق من تطابق معرف المناسبة
  bool isOccasionIdMatching(String? otherId) {
    if (occasionId == null || otherId == null) {
      return occasionId == otherId;
    }

    // محاولة تحويل معرف المناسبة إلى رقم للمقارنة
    final int? occasionIdInt =
        occasionId is int ? occasionId : int.tryParse(occasionId.toString());
    final int? otherIdInt = int.tryParse(otherId);

    // إذا كان كلاهما رقمًا، نقارن كأرقام
    if (occasionIdInt != null && otherIdInt != null) {
      return occasionIdInt == otherIdInt;
    }

    // وإلا نقارن كنصوص
    return occasionId.toString() == otherId;
  }
}

/// قائمة المناسبات المتاحة للفلترة (مرتبة حسب الصورة المطلوبة)
/// الترتيب: مبروك التخرج - سنة حلوة - ذكرى غالية - غلا العمر - نور البيت - غالية وجت - أبوي الغالي - أمي الحبيبة - كفو دايم - بداية جديدة - منزل مبارك - أجر وشفاء
final List<Map<String, dynamic>> occasionsList = [
  {'id': 1, 'name': 'مبروك التخرج', 'code': 'sticker_category_graduation'},
  {'id': 2, 'name': 'سنة حلوة', 'code': 'sticker_category_birthday'},
  {'id': 3, 'name': 'ذكرى غالية', 'code': 'sticker_category_anniversary'},
  {'id': 4, 'name': 'غلا العمر', 'code': 'sticker_category_love'},
  {'id': 5, 'name': 'نور البيت', 'code': 'sticker_category_new_baby'},
  {'id': 6, 'name': 'غالية وجت', 'code': 'sticker_category_wedding'},
  {'id': 7, 'name': 'أبوي الغالي', 'code': 'sticker_category_father'},
  {'id': 8, 'name': 'أمي الحبيبة', 'code': 'sticker_category_mother'},
  {'id': 9, 'name': 'كفو دايم', 'code': 'sticker_category_congratulations'},
  {'id': 10, 'name': 'بداية جديدة', 'code': 'sticker_category_new_beginning'},
  {'id': 11, 'name': 'منزل مبارك', 'code': 'sticker_category_new_home'},
  {'id': 12, 'name': 'أجر وشفاء', 'code': 'sticker_category_get_well'},
];

/// عرض نافذة الفلترة السفلية
Future<FilterCriteria?> showFilterBottomSheet(
  BuildContext context,
  FilterCriteria currentCriteria,
) async {
  return await showModalBottomSheet<FilterCriteria>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Theme.of(context).brightness == Brightness.dark
        ? Colors.black
        : Colors.white,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) => FilterBottomSheet(currentCriteria: currentCriteria),
  );
}

/// نافذة الفلترة السفلية
class FilterBottomSheet extends StatefulWidget {
  final FilterCriteria currentCriteria;

  const FilterBottomSheet({
    super.key,
    required this.currentCriteria,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late FilterCriteria _criteria;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _criteria = widget.currentCriteria;
    _searchController.text = _criteria.searchQuery ?? '';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان النافذة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'فلترة البطاقات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const Divider(),

            // حقل البحث
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن بطاقة...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _criteria = _criteria.copyWith(searchQuery: value);
                });
              },
            ),
            const SizedBox(height: 16),

            // قسم المناسبات
            Text(
              'المناسبة',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // قائمة المناسبات
            SizedBox(
              height: 40,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  // خيار "الكل"
                  _buildOccasionChip(null, AppLocalizations.of(context).all),
                  ...occasionsList.map((occasion) =>
                      _buildOccasionChip(occasion['id'], occasion['name']!)),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // قسم الترتيب
            Text(
              'ترتيب حسب',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // خيارات الترتيب
            Row(
              children: [
                _buildSortOption(
                  'الأحدث أولاً',
                  Icons.access_time,
                  _criteria.newestFirst && !_criteria.popularitySort,
                  () {
                    setState(() {
                      _criteria = _criteria.copyWith(
                        newestFirst: true,
                        popularitySort: false,
                      );
                    });
                  },
                ),
                const SizedBox(width: 12),
                _buildSortOption(
                  'الأقدم أولاً',
                  Icons.history,
                  !_criteria.newestFirst && !_criteria.popularitySort,
                  () {
                    setState(() {
                      _criteria = _criteria.copyWith(
                        newestFirst: false,
                        popularitySort: false,
                      );
                    });
                  },
                ),
                const SizedBox(width: 12),
                _buildSortOption(
                  'الأكثر شعبية',
                  Icons.favorite,
                  _criteria.popularitySort,
                  () {
                    setState(() {
                      _criteria = _criteria.copyWith(
                        popularitySort: true,
                      );
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            // أزرار التطبيق وإعادة الضبط
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _criteria = FilterCriteria();
                        _searchController.clear();
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      side: BorderSide(color: AppColors.primaryColor),
                    ),
                    child: Text(
                      'إعادة ضبط',
                      style: GoogleFonts.cairo(color: AppColors.primaryColor),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context, _criteria);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      'تطبيق',
                      style: GoogleFonts.cairo(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريحة المناسبة
  Widget _buildOccasionChip(dynamic id, String name) {
    // التحقق من تطابق معرف المناسبة
    bool isSelected = false;
    if (_criteria.occasionId == null && id == null) {
      isSelected = true;
    } else if (_criteria.occasionId != null && id != null) {
      // مقارنة الأرقام مباشرة
      isSelected = _criteria.occasionId == id;
    }

    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: ChoiceChip(
        label: Text(
          name,
          style: GoogleFonts.cairo(
            color: isSelected ? Colors.white : Colors.black,
            fontSize: 14,
          ),
        ),
        selected: isSelected,
        selectedColor: AppColors.primaryColor,
        backgroundColor: Colors.grey[200],
        onSelected: (selected) {
          setState(() {
            _criteria = _criteria.copyWith(occasionId: selected ? id : null);
          });
        },
      ),
    );
  }

  /// بناء خيار الترتيب
  Widget _buildSortOption(
      String title, IconData icon, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primaryColor.withValues(
                    alpha: 26,
                    red: AppColors.primaryColor.r.toDouble(),
                    green: AppColors.primaryColor.g.toDouble(),
                    blue: AppColors.primaryColor.b.toDouble())
                : Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? AppColors.primaryColor : Colors.grey[300]!,
            ),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? AppColors.primaryColor : Colors.grey[600],
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: isSelected ? AppColors.primaryColor : Colors.grey[800],
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
