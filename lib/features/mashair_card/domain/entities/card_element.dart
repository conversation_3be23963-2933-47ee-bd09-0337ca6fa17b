// lib/features/mashair_card/domain/entities/card_element.dart

import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

/// عنصر البطاقة الأساسي
abstract class CardElement extends Equatable {
  /// معرف العنصر
  final String id;
  
  /// موضع العنصر
  final Offset position;
  
  /// إنشاء عنصر بطاقة
  const CardElement({
    required this.id,
    required this.position,
  });
  
  @override
  List<Object?> get props => [id, position];
}

/// عنصر نص في البطاقة
class CardTextElement extends CardElement {
  /// نص العنصر
  final String text;
  
  /// حجم الخط
  final double fontSize;
  
  /// وزن الخط
  final FontWeight fontWeight;
  
  /// لون النص
  final Color color;
  
  /// عائلة الخط
  final String fontFamily;
  
  /// إنشاء عنصر نص
  const CardTextElement({
    required super.id,
    required super.position,
    required this.text,
    required this.fontSize,
    required this.fontWeight,
    required this.color,
    required this.fontFamily,
  });
  
  @override
  List<Object?> get props => [
    ...super.props,
    text,
    fontSize,
    fontWeight,
    color,
    fontFamily,
  ];
  
  /// إنشاء نسخة معدلة من العنصر
  CardTextElement copyWith({
    String? id,
    Offset? position,
    String? text,
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    String? fontFamily,
  }) {
    return CardTextElement(
      id: id ?? this.id,
      position: position ?? this.position,
      text: text ?? this.text,
      fontSize: fontSize ?? this.fontSize,
      fontWeight: fontWeight ?? this.fontWeight,
      color: color ?? this.color,
      fontFamily: fontFamily ?? this.fontFamily,
    );
  }
}

/// عنصر صورة في البطاقة
class CardImageElement extends CardElement {
  /// مسار الصورة
  final String imagePath;
  
  /// حجم الصورة
  final Size size;
  
  /// إنشاء عنصر صورة
  const CardImageElement({
    required super.id,
    required super.position,
    required this.imagePath,
    required this.size,
  });
  
  @override
  List<Object?> get props => [
    ...super.props,
    imagePath,
    size,
  ];
  
  /// إنشاء نسخة معدلة من العنصر
  CardImageElement copyWith({
    String? id,
    Offset? position,
    String? imagePath,
    Size? size,
  }) {
    return CardImageElement(
      id: id ?? this.id,
      position: position ?? this.position,
      imagePath: imagePath ?? this.imagePath,
      size: size ?? this.size,
    );
  }
}
