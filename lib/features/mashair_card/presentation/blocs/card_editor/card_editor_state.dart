// lib/features/mashair_card/presentation/blocs/card_editor/card_editor_state.dart

import 'package:equatable/equatable.dart';

/// حالة محرر البطاقة الأساسية
abstract class CardEditorState extends Equatable {
  /// إنشاء حالة محرر البطاقة
  const CardEditorState();

  @override
  List<Object?> get props => [];
}

/// حالة محرر البطاقة الأولية
class CardEditorInitial extends CardEditorState {}

/// حالة تحميل محرر البطاقة
class CardEditorLoading extends CardEditorState {}

/// حالة محرر البطاقة المحملة
class CardEditorLoaded extends CardEditorState {
  /// قائمة العناصر في البطاقة
  final List<dynamic> elements;
  
  /// معرف العنصر المحدد
  final String? selectedElementId;
  
  /// ما إذا كان يمكن التراجع
  final bool canUndo;
  
  /// ما إذا كان يمكن الإعادة
  final bool canRedo;

  /// إنشاء حالة محرر البطاقة المحملة
  const CardEditorLoaded({
    required this.elements,
    this.selectedElementId,
    this.canUndo = false,
    this.canRedo = false,
  });

  @override
  List<Object?> get props => [elements, selectedElementId, canUndo, canRedo];
  
  /// إنشاء نسخة معدلة من الحالة
  CardEditorLoaded copyWith({
    List<dynamic>? elements,
    String? selectedElementId,
    bool? canUndo,
    bool? canRedo,
  }) {
    return CardEditorLoaded(
      elements: elements ?? this.elements,
      selectedElementId: selectedElementId ?? this.selectedElementId,
      canUndo: canUndo ?? this.canUndo,
      canRedo: canRedo ?? this.canRedo,
    );
  }
}

/// حالة خطأ محرر البطاقة
class CardEditorError extends CardEditorState {
  /// رسالة الخطأ
  final String message;

  /// إنشاء حالة خطأ محرر البطاقة
  const CardEditorError(this.message);

  @override
  List<Object?> get props => [message];
}
