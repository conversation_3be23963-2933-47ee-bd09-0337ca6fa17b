// lib/features/mashair_card/presentation/blocs/card_editor/card_editor_bloc.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/entities/card_element.dart';
import 'card_editor_event.dart';
import 'card_editor_state.dart';

/// بلوك محرر البطاقة
/// يدير حالة محرر البطاقة والعناصر المضافة إليها
class CardEditorBloc extends Bloc<CardEditorEvent, CardEditorState> {
  /// إنشاء بلوك محرر البطاقة
  CardEditorBloc() : super(CardEditorInitial()) {
    on<InitializeCardEditor>(_onInitializeCardEditor);
    on<AddTextEvent>(_onAddText);
    on<AddImageEvent>(_onAddImage);
    on<UpdateElementEvent>(_onUpdateElement);
    on<DeleteElementEvent>(_onDeleteElement);
    on<ReorderElementsEvent>(_onReorderElements);
    on<SaveCardEvent>(_onSaveCard);
    on<LoadCardEvent>(_onLoadCard);
    on<ClearCardEvent>(_onClearCard);
    on<UndoEvent>(_onUndo);
    on<RedoEvent>(_onRedo);
  }

  /// معالجة حدث تهيئة محرر البطاقة
  void _onInitializeCardEditor(
    InitializeCardEditor event,
    Emitter<CardEditorState> emit,
  ) {
    emit(CardEditorLoaded(
      elements: [],
      selectedElementId: null,
      canUndo: false,
      canRedo: false,
    ));
  }

  /// معالجة حدث إضافة نص
  void _onAddText(
    AddTextEvent event,
    Emitter<CardEditorState> emit,
  ) {
    if (state is CardEditorLoaded) {
      final currentState = state as CardEditorLoaded;

      // إنشاء عنصر نص جديد
      final newTextElement = CardTextElement(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        text: event.text,
        position: const Offset(100, 100),
        fontSize: 16,
        fontWeight: FontWeight.normal,
        color: Colors.black,
        fontFamily: 'Cairo',
      );

      // إضافة العنصر إلى القائمة
      final updatedElements = List<CardElement>.from(currentState.elements)
        ..add(newTextElement);

      // تحديث الحالة
      emit(CardEditorLoaded(
        elements: updatedElements,
        selectedElementId: newTextElement.id,
        canUndo: true,
        canRedo: false,
      ));
    }
  }

  /// معالجة حدث إضافة صورة
  void _onAddImage(
    AddImageEvent event,
    Emitter<CardEditorState> emit,
  ) {
    if (state is CardEditorLoaded) {
      final currentState = state as CardEditorLoaded;

      // إنشاء عنصر صورة جديد
      final newImageElement = CardImageElement(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        imagePath: event.imagePath,
        position: const Offset(100, 100),
        size: const Size(200, 200),
      );

      // إضافة العنصر إلى القائمة
      final updatedElements = List<CardElement>.from(currentState.elements)
        ..add(newImageElement);

      // تحديث الحالة
      emit(CardEditorLoaded(
        elements: updatedElements,
        selectedElementId: newImageElement.id,
        canUndo: true,
        canRedo: false,
      ));
    }
  }

  /// معالجة حدث تحديث عنصر
  void _onUpdateElement(
    UpdateElementEvent event,
    Emitter<CardEditorState> emit,
  ) {
    if (state is CardEditorLoaded) {
      final currentState = state as CardEditorLoaded;

      // تحديث العنصر في القائمة
      final updatedElements = currentState.elements.map((element) {
        if (element.id == event.elementId) {
          return event.updatedElement;
        }
        return element;
      }).toList();

      // تحديث الحالة
      emit(CardEditorLoaded(
        elements: updatedElements,
        selectedElementId: currentState.selectedElementId,
        canUndo: true,
        canRedo: false,
      ));
    }
  }

  /// معالجة حدث حذف عنصر
  void _onDeleteElement(
    DeleteElementEvent event,
    Emitter<CardEditorState> emit,
  ) {
    if (state is CardEditorLoaded) {
      final currentState = state as CardEditorLoaded;

      // حذف العنصر من القائمة
      final updatedElements = currentState.elements
          .where((element) => element.id != event.elementId)
          .toList();

      // تحديث الحالة
      emit(CardEditorLoaded(
        elements: updatedElements,
        selectedElementId: null,
        canUndo: true,
        canRedo: false,
      ));
    }
  }

  /// معالجة حدث إعادة ترتيب العناصر
  void _onReorderElements(
    ReorderElementsEvent event,
    Emitter<CardEditorState> emit,
  ) {
    if (state is CardEditorLoaded) {
      final currentState = state as CardEditorLoaded;

      // تحديث الحالة
      emit(CardEditorLoaded(
        elements: event.newOrder,
        selectedElementId: currentState.selectedElementId,
        canUndo: true,
        canRedo: false,
      ));
    }
  }

  /// معالجة حدث حفظ البطاقة
  void _onSaveCard(
    SaveCardEvent event,
    Emitter<CardEditorState> emit,
  ) {
    // سيتم تنفيذ هذه الوظيفة لاحقًا
  }

  /// معالجة حدث تحميل البطاقة
  void _onLoadCard(
    LoadCardEvent event,
    Emitter<CardEditorState> emit,
  ) {
    // سيتم تنفيذ هذه الوظيفة لاحقًا
  }

  /// معالجة حدث مسح البطاقة
  void _onClearCard(
    ClearCardEvent event,
    Emitter<CardEditorState> emit,
  ) {
    emit(CardEditorLoaded(
      elements: [],
      selectedElementId: null,
      canUndo: false,
      canRedo: false,
    ));
  }

  /// معالجة حدث التراجع
  void _onUndo(
    UndoEvent event,
    Emitter<CardEditorState> emit,
  ) {
    // سيتم تنفيذ هذه الوظيفة لاحقًا
  }

  /// معالجة حدث الإعادة
  void _onRedo(
    RedoEvent event,
    Emitter<CardEditorState> emit,
  ) {
    // سيتم تنفيذ هذه الوظيفة لاحقًا
  }
}
