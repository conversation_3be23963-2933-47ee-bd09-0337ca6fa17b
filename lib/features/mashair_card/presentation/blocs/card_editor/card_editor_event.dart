// lib/features/mashair_card/presentation/blocs/card_editor/card_editor_event.dart

import 'package:equatable/equatable.dart';

/// حدث محرر البطاقة الأساسي
abstract class CardEditorEvent extends Equatable {
  /// إنشاء حدث محرر البطاقة
  const CardEditorEvent();

  @override
  List<Object?> get props => [];
}

/// حدث تهيئة محرر البطاقة
class InitializeCardEditor extends CardEditorEvent {
  /// إنشاء حدث تهيئة محرر البطاقة
  const InitializeCardEditor();
}

/// حدث إضافة نص
class AddTextEvent extends CardEditorEvent {
  /// النص المراد إضافته
  final String text;

  /// إنشاء حدث إضافة نص
  const AddTextEvent(this.text);

  @override
  List<Object?> get props => [text];
}

/// حدث إضافة صورة
class AddImageEvent extends CardEditorEvent {
  /// مسار الصورة المراد إضافتها
  final String imagePath;

  /// إنشاء حدث إضافة صورة
  const AddImageEvent(this.imagePath);

  @override
  List<Object?> get props => [imagePath];
}

/// حدث تحديث عنصر
class UpdateElementEvent extends CardEditorEvent {
  /// معرف العنصر المراد تحديثه
  final String elementId;
  
  /// العنصر المحدث
  final dynamic updatedElement;

  /// إنشاء حدث تحديث عنصر
  const UpdateElementEvent({
    required this.elementId,
    required this.updatedElement,
  });

  @override
  List<Object?> get props => [elementId, updatedElement];
}

/// حدث حذف عنصر
class DeleteElementEvent extends CardEditorEvent {
  /// معرف العنصر المراد حذفه
  final String elementId;

  /// إنشاء حدث حذف عنصر
  const DeleteElementEvent(this.elementId);

  @override
  List<Object?> get props => [elementId];
}

/// حدث إعادة ترتيب العناصر
class ReorderElementsEvent extends CardEditorEvent {
  /// الترتيب الجديد للعناصر
  final List<dynamic> newOrder;

  /// إنشاء حدث إعادة ترتيب العناصر
  const ReorderElementsEvent(this.newOrder);

  @override
  List<Object?> get props => [newOrder];
}

/// حدث حفظ البطاقة
class SaveCardEvent extends CardEditorEvent {
  /// اسم البطاقة
  final String cardName;

  /// إنشاء حدث حفظ البطاقة
  const SaveCardEvent(this.cardName);

  @override
  List<Object?> get props => [cardName];
}

/// حدث تحميل البطاقة
class LoadCardEvent extends CardEditorEvent {
  /// معرف البطاقة
  final String cardId;

  /// إنشاء حدث تحميل البطاقة
  const LoadCardEvent(this.cardId);

  @override
  List<Object?> get props => [cardId];
}

/// حدث مسح البطاقة
class ClearCardEvent extends CardEditorEvent {
  /// إنشاء حدث مسح البطاقة
  const ClearCardEvent();
}

/// حدث التراجع
class UndoEvent extends CardEditorEvent {
  /// إنشاء حدث التراجع
  const UndoEvent();
}

/// حدث الإعادة
class RedoEvent extends CardEditorEvent {
  /// إنشاء حدث الإعادة
  const RedoEvent();
}
