// lib/features/home/<USER>/repositories/home_repository_impl.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/greeting_card_entity.dart';
import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/occasion_entity.dart';
import '../../domain/entities/popular_card_entity.dart';
import '../../domain/repositories/home_repository.dart';
import '../datasources/home_remote_data_source.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDataSource remoteDataSource;

  HomeRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<BannerEntity>>> getBanners() async {
    try {
      final models = await remoteDataSource.getBanners();
      return Right(models);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Stream<Either<Failure, List<BannerEntity>>> getBannersStream() {
    return remoteDataSource.getBannersStream().map((models) {
      return Right<Failure, List<BannerEntity>>(models);
    }).handleError((error) {
      return Left<Failure, List<BannerEntity>>(
        ServerFailure(error.toString()),
      );
    });
  }

  @override
  Future<Either<Failure, List<OccasionEntity>>> getOccasions() async {
    try {
      final models = await remoteDataSource.getOccasions();
      return Right(models);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Stream<Either<Failure, List<OccasionEntity>>> getOccasionsStream() {
    return remoteDataSource.getOccasionsStream().map((models) {
      return Right<Failure, List<OccasionEntity>>(models);
    }).handleError((error) {
      return Left<Failure, List<OccasionEntity>>(
        ServerFailure(error.toString()),
      );
    });
  }

  @override
  Future<Either<Failure, List<PopularCardEntity>>> getPopularCards({
    int limit = 10,
  }) async {
    try {
      final models = await remoteDataSource.getPopularCards(limit: limit);
      return Right(models);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Stream<Either<Failure, List<PopularCardEntity>>> getPopularCardsStream({
    int limit = 10,
  }) {
    return remoteDataSource.getPopularCardsStream(limit: limit).map((models) {
      return Right<Failure, List<PopularCardEntity>>(models);
    }).handleError((error) {
      return Left<Failure, List<PopularCardEntity>>(
        ServerFailure(error.toString()),
      );
    });
  }

  @override
  Future<Either<Failure, GreetingCardEntity>> getCardById(String id) async {
    try {
      final model = await remoteDataSource.getCardById(id);
      // تحويل النموذج إلى كيان
      final entity = GreetingCardEntity(
        id: model.id,
        title: model.title,
        description: model.description,
        imageUrl: model.imageUrl,
        occasion: model.occasion,
        popularity: model.popularity,
        backgroundColor: model.backgroundColor,
        elements: model.elements,
        visits: model.visits,
        createdAt: model.createdAt,
      );
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}
