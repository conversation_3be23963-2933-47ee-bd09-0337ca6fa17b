// lib/features/home/<USER>/blocs/search/search_event.dart

import 'package:equatable/equatable.dart';

/// أحداث البحث
abstract class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object?> get props => [];
}

/// حدث البحث عن البطاقات
class SearchCardsEvent extends SearchEvent {
  /// استعلام البحث
  final String query;

  /// إنشاء حدث البحث عن البطاقات
  const SearchCardsEvent({required this.query});

  @override
  List<Object?> get props => [query];
}

/// حدث مسح نتائج البحث
class ClearSearchEvent extends SearchEvent {
  /// إنشاء حدث مسح نتائج البحث
  const ClearSearchEvent();
}
