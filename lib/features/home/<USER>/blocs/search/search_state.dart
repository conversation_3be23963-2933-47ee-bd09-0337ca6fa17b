// lib/features/home/<USER>/blocs/search/search_state.dart

import 'package:equatable/equatable.dart';

import '../../../domain/entities/greeting_card_entity.dart';

/// حالات البحث
abstract class SearchState extends Equatable {
  const SearchState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية للبحث
class SearchInitial extends SearchState {
  /// إنشاء الحالة الأولية للبحث
  const SearchInitial();
}

/// حالة جاري البحث
class SearchLoading extends SearchState {
  /// إنشاء حالة جاري البحث
  const SearchLoading();
}

/// حالة نتائج البحث
class SearchResults extends SearchState {
  /// قائمة البطاقات
  final List<GreetingCardEntity> cards;

  /// استعلام البحث
  final String query;

  /// إنشاء حالة نتائج البحث
  const SearchResults({
    required this.cards,
    required this.query,
  });

  @override
  List<Object?> get props => [cards, query];
}

/// حالة خطأ في البحث
class SearchError extends SearchState {
  /// رسالة الخطأ
  final String message;

  /// إنشاء حالة خطأ في البحث
  const SearchError({required this.message});

  @override
  List<Object?> get props => [message];
}
