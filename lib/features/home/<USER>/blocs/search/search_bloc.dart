// lib/features/home/<USER>/blocs/search/search_bloc.dart

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';

import '../../../domain/entities/greeting_card_entity.dart';
import 'search_event.dart';
import 'search_state.dart';

/// بلوك البحث
class SearchBloc extends Bloc<SearchEvent, SearchState> {
  /// إنشاء بلوك البحث
  SearchBloc() : super(const SearchInitial()) {
    on<SearchCardsEvent>(_onSearchCards);
    on<ClearSearchEvent>(_onClearSearch);
  }

  /// معالجة حدث البحث عن البطاقات
  Future<void> _onSearchCards(
    SearchCardsEvent event,
    Emitter<SearchState> emit,
  ) async {
    emit(const SearchLoading());

    try {
      // في الواقع، يجب استدعاء واجهة برمجة التطبيقات للبحث
      // هنا نقوم بمحاكاة البحث باستخدام البيانات المحلية
      final cards = await _searchCards(event.query);
      emit(SearchResults(cards: cards, query: event.query));
    } catch (e) {
      emit(SearchError(message: e.toString()));
    }
  }

  /// معالجة حدث مسح نتائج البحث
  void _onClearSearch(
    ClearSearchEvent event,
    Emitter<SearchState> emit,
  ) {
    emit(const SearchInitial());
  }

  /// البحث عن البطاقات (محاكاة)
  Future<List<GreetingCardEntity>> _searchCards(String query) async {
    try {
      // في الواقع، يجب استدعاء واجهة برمجة التطبيقات للبحث
      // هنا نقوم بمحاكاة البحث باستخدام البيانات المحلية
      final allCards = _getMockCards();

      // تصفية البطاقات حسب الاستعلام
      return allCards.where((card) {
        final title = card.title.toLowerCase();
        final occasion = card.occasion.toLowerCase();
        final description = card.description.toLowerCase();
        final searchQuery = query.toLowerCase();

        return title.contains(searchQuery) ||
            occasion.contains(searchQuery) ||
            description.contains(searchQuery);
      }).toList();
    } catch (e) {
      throw Exception('فشل في البحث عن البطاقات: $e');
    }
  }

  /// الحصول على بيانات وهمية للبطاقات
  List<GreetingCardEntity> _getMockCards() {
    return [
      GreetingCardEntity(
        id: '1',
        title: 'بطاقة عيد الفطر',
        description: 'بطاقة تهنئة بمناسبة عيد الفطر',
        imageUrl: 'https://example.com/card1.jpg',
        occasion: 'عيد الفطر',
        popularity: 100,
        backgroundColor: const Color(0xFFFFFFFF),
        elements: [],
      ),
      GreetingCardEntity(
        id: '2',
        title: 'بطاقة عيد الأضحى',
        description: 'بطاقة تهنئة بمناسبة عيد الأضحى',
        imageUrl: 'https://example.com/card2.jpg',
        occasion: 'عيد الأضحى',
        popularity: 90,
        backgroundColor: const Color(0xFFFFFFFF),
        elements: [],
      ),
      GreetingCardEntity(
        id: '3',
        title: 'بطاقة المولد النبوي',
        description: 'بطاقة تهنئة بمناسبة المولد النبوي',
        imageUrl: 'https://example.com/card3.jpg',
        occasion: 'المولد النبوي',
        popularity: 80,
        backgroundColor: const Color(0xFFFFFFFF),
        elements: [],
      ),
    ];
  }
}
