// lib/features/home/<USER>/blocs/home/<USER>

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/usecases/usecase.dart';
import '../../../domain/entities/banner_entity.dart';
import '../../../domain/entities/occasion_entity.dart';
import '../../../domain/entities/popular_card_entity.dart';
import '../../../domain/usecases/get_banners_usecase.dart';
import '../../../domain/usecases/get_banners_stream_usecase.dart';
import '../../../domain/usecases/get_occasions_usecase.dart';
import '../../../domain/usecases/get_occasions_stream_usecase.dart';
import '../../../domain/usecases/get_popular_cards_usecase.dart';
import '../../../domain/usecases/get_popular_cards_stream_usecase.dart';
import 'home_event.dart';
import 'home_state.dart';

/// BLoC that manages the home screen data and state.
///
/// This BLoC is responsible for loading all the data needed for the home screen,
/// including banners, occasions (categories), and popular cards.
class HomeBloc extends Bloc<HomeEvent, HomeState> {
  /// Use case for retrieving banners
  final GetBannersUseCase getBannersUseCase;

  /// Use case for retrieving occasions/categories
  final GetOccasionsUseCase getOccasionsUseCase;

  /// Use case for retrieving popular cards
  final GetPopularCardsUseCase getPopularCardsUseCase;

  /// Use case for streaming banners
  final GetBannersStreamUseCase getBannersStreamUseCase;

  /// Use case for streaming occasions
  final GetOccasionsStreamUseCase getOccasionsStreamUseCase;

  /// Use case for streaming popular cards
  final GetPopularCardsStreamUseCase getPopularCardsStreamUseCase;

  /// Subscriptions to streams
  final List<StreamSubscription> _subscriptions = [];

  /// Creates a new [HomeBloc] with the required use cases.
  HomeBloc({
    required this.getBannersUseCase,
    required this.getOccasionsUseCase,
    required this.getPopularCardsUseCase,
    required this.getBannersStreamUseCase,
    required this.getOccasionsStreamUseCase,
    required this.getPopularCardsStreamUseCase,
  }) : super(HomeInitial()) {
    on<LoadHomeDataEvent>(_onLoadHomeData);
    on<UpdateBannersEvent>(_onUpdateBanners);
    on<UpdateOccasionsEvent>(_onUpdateOccasions);
    on<UpdatePopularCardsEvent>(_onUpdatePopularCards);

    // Start listening to streams
    _startListeningToStreams();
  }

  /// Start listening to all streams for real-time updates
  void _startListeningToStreams() {
    // Listen to banners stream
    final bannersSubscription =
        getBannersStreamUseCase(const NoParams()).listen(
      (result) {
        result.fold(
          (failure) => add(HomeErrorEvent(failure.message)),
          (banners) => add(UpdateBannersEvent(banners)),
        );
      },
      onError: (error) => add(HomeErrorEvent(error.toString())),
    );

    // Listen to occasions stream
    final occasionsSubscription =
        getOccasionsStreamUseCase(const NoParams()).listen(
      (result) {
        result.fold(
          (failure) => add(HomeErrorEvent(failure.message)),
          (occasions) => add(UpdateOccasionsEvent(occasions)),
        );
      },
      onError: (error) => add(HomeErrorEvent(error.toString())),
    );

    // Listen to popular cards stream
    final popularCardsSubscription = getPopularCardsStreamUseCase(
      const PopularCardsParams(limit: 10),
    ).listen(
      (result) {
        result.fold(
          (failure) => add(HomeErrorEvent(failure.message)),
          (cards) => add(UpdatePopularCardsEvent(cards)),
        );
      },
      onError: (error) => add(HomeErrorEvent(error.toString())),
    );

    // Add subscriptions to the list
    _subscriptions.addAll([
      bannersSubscription,
      occasionsSubscription,
      popularCardsSubscription,
    ]);
  }

  @override
  Future<void> close() {
    // Cancel all subscriptions when the bloc is closed
    for (var subscription in _subscriptions) {
      subscription.cancel();
    }
    return super.close();
  }

  /// Handles the [LoadHomeDataEvent] by loading all home screen data.
  ///
  /// This method fetches banners, occasions, and popular cards in parallel,
  /// then combines the results into a [HomeLoaded] state.
  ///
  /// Emits:
  /// - [HomeLoading] while fetching data
  /// - [HomeError] if any of the data fetching operations fail
  /// - [HomeLoaded] with the fetched data on success
  Future<void> _onLoadHomeData(
    LoadHomeDataEvent event,
    Emitter<HomeState> emit,
  ) async {
    emit(HomeLoading());

    // Fetch all data in parallel
    final bRes = await getBannersUseCase(const NoParams());
    final oRes = await getOccasionsUseCase(const NoParams());
    final pRes = await getPopularCardsUseCase(const NoParams());

    // Initialize result containers
    List<BannerEntity> banners = [];
    List<OccasionEntity> occasions = [];
    List<PopularCardEntity> popularCards = [];

    // Process banner results
    bRes.fold(
      (failure) => emit(HomeError(failure.message)),
      (list) {
        banners = list;
      },
    );

    // Process occasions results
    oRes.fold(
      (failure) => emit(HomeError(failure.message)),
      (list) => occasions = list,
    );

    // Process popular cards results
    pRes.fold(
      (failure) => emit(HomeError(failure.message)),
      (list) => popularCards = list,
    );

    // Emit loaded state with all data
    emit(HomeLoaded(
      banners: banners,
      popularCards: popularCards,
      occasions: occasions,
    ));
  }

  /// Handles the [UpdateBannersEvent] by updating the banners in the current state.
  void _onUpdateBanners(
    UpdateBannersEvent event,
    Emitter<HomeState> emit,
  ) {
    final currentState = state;
    if (currentState is HomeLoaded) {
      emit(currentState.copyWith(banners: event.banners));
    }
  }

  /// Handles the [UpdateOccasionsEvent] by updating the occasions in the current state.
  void _onUpdateOccasions(
    UpdateOccasionsEvent event,
    Emitter<HomeState> emit,
  ) {
    final currentState = state;
    if (currentState is HomeLoaded) {
      emit(currentState.copyWith(occasions: event.occasions));
    }
  }

  /// Handles the [UpdatePopularCardsEvent] by updating the popular cards in the current state.
  void _onUpdatePopularCards(
    UpdatePopularCardsEvent event,
    Emitter<HomeState> emit,
  ) {
    final currentState = state;
    if (currentState is HomeLoaded) {
      emit(currentState.copyWith(popularCards: event.popularCards));
    }
  }
}
