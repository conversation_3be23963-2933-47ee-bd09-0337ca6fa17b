import 'package:equatable/equatable.dart';

import '../../../domain/entities/banner_entity.dart';
import '../../../domain/entities/occasion_entity.dart';
import '../../../domain/entities/popular_card_entity.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object?> get props => [];
}

/// عند تحميل الصفحة الرئيسية لجلب البيانات المطلوبة.
class LoadHomeDataEvent extends HomeEvent {}

/// حدث يتم إطلاقه عند حدوث خطأ في الـ HomeBloc
class HomeErrorEvent extends HomeEvent {
  final String message;

  const HomeErrorEvent(this.message);

  @override
  List<Object?> get props => [message];
}

/// حدث يتم إطلاقه عند تحديث البانرات
class UpdateBannersEvent extends HomeEvent {
  final List<BannerEntity> banners;

  const UpdateBannersEvent(this.banners);

  @override
  List<Object?> get props => [banners];
}

/// حدث يتم إطلاقه عند تحديث المناسبات
class UpdateOccasionsEvent extends HomeEvent {
  final List<OccasionEntity> occasions;

  const UpdateOccasionsEvent(this.occasions);

  @override
  List<Object?> get props => [occasions];
}

/// حدث يتم إطلاقه عند تحديث البطاقات الشائعة
class UpdatePopularCardsEvent extends HomeEvent {
  final List<PopularCardEntity> popularCards;

  const UpdatePopularCardsEvent(this.popularCards);

  @override
  List<Object?> get props => [popularCards];
}
