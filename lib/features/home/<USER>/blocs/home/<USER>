// lib/features/home/<USER>/blocs/home_state.dart

import 'package:equatable/equatable.dart';
import 'package:mashair/features/home/<USER>/entities/occasion_entity.dart';

import '../../../domain/entities/banner_entity.dart';
import '../../../domain/entities/popular_card_entity.dart';

abstract class HomeState extends Equatable {
  const HomeState();
  @override
  List<Object?> get props => [];
}

class HomeInitial extends HomeState {}

class HomeLoading extends HomeState {}

class HomeLoaded extends HomeState {
  final List<BannerEntity> banners;
  final List<PopularCardEntity>? popularCards;
  final List<OccasionEntity> occasions;

  const HomeLoaded({
    required this.banners,
    required this.popularCards,
    required this.occasions,
  });

  /// إنشاء نسخة جديدة من الحالة مع تحديث بعض الخصائص
  HomeLoaded copyWith({
    List<BannerEntity>? banners,
    List<PopularCardEntity>? popularCards,
    List<OccasionEntity>? occasions,
  }) {
    return HomeLoaded(
      banners: banners ?? this.banners,
      popularCards: popularCards ?? this.popularCards,
      occasions: occasions ?? this.occasions,
    );
  }

  @override
  List<Object?> get props => [banners, popularCards ?? [], occasions];
}

class HomeError extends HomeState {
  final String message;
  const HomeError(this.message);
  @override
  List<Object?> get props => [message];
}
