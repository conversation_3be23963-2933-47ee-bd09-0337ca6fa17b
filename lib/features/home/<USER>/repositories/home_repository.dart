// lib/features/home/<USER>/repositories/home_repository.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../entities/greeting_card_entity.dart';
import '../entities/banner_entity.dart';
import '../entities/occasion_entity.dart';
import '../entities/popular_card_entity.dart';

/// Repository interface for the Home feature.
///
/// This repository handles fetching data for the home screen including:
/// - Banners for promotional content
/// - Occasions (categories) for greeting cards
/// - Popular greeting cards
/// - Individual greeting card details
abstract class HomeRepository {
  /// Retrieves a list of banners for display on the home screen.
  ///
  /// @return An [Either] with either a [Failure] or a list of [BannerEntity]
  Future<Either<Failure, List<BannerEntity>>> getBanners();

  /// Provides a stream of banner updates for real-time display.
  ///
  /// @return A [Stream] of [Either] with either a [Failure] or a list of [BannerEntity]
  Stream<Either<Failure, List<BannerEntity>>> getBannersStream();

  /// Retrieves a list of occasions (categories) for greeting cards.
  ///
  /// @return An [Either] with either a [Failure] or a list of [OccasionEntity]
  Future<Either<Failure, List<OccasionEntity>>> getOccasions();

  /// Provides a stream of occasion updates for real-time display.
  ///
  /// @return A [Stream] of [Either] with either a [Failure] or a list of [OccasionEntity]
  Stream<Either<Failure, List<OccasionEntity>>> getOccasionsStream();

  /// Retrieves a list of popular greeting cards.
  ///
  /// @param limit Optional parameter to limit the number of results
  /// @return An [Either] with either a [Failure] or a list of [PopularCardEntity]
  Future<Either<Failure, List<PopularCardEntity>>> getPopularCards({int limit});

  /// Provides a stream of popular card updates for real-time display.
  ///
  /// @param limit Optional parameter to limit the number of results
  /// @return A [Stream] of [Either] with either a [Failure] or a list of [PopularCardEntity]
  Stream<Either<Failure, List<PopularCardEntity>>> getPopularCardsStream(
      {int limit});

  /// Retrieves a specific greeting card by its ID.
  ///
  /// @param id The unique identifier of the greeting card
  /// @return An [Either] with either a [Failure] or a [GreetingCardEntity]
  Future<Either<Failure, GreetingCardEntity>> getCardById(String id);
}
