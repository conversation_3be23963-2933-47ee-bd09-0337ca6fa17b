import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../routes.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../auth/presentation/blocs/auth/auth_bloc.dart';
import '../../../auth/presentation/blocs/auth/auth_event.dart';
import '../../../auth/presentation/blocs/auth/auth_state.dart';

class HomeDrawer extends StatelessWidget {
  const HomeDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    // نقرأ الحالة الحالية من AuthBloc
    final authState = context.watch<AuthBloc>().state;

    // إضافة رسالة تصحيح للمساعدة في تشخيص المشكلة
    debugPrint('AuthState: ${authState.runtimeType}');

    // تحديد الخط المناسب حسب اللغة (تم حذف المتغير لأنه غير مستخدم حالياً)

    // قيم افتراضية
    String userName = '--';
    String userEmail = '--';
    String? userUid;
    String? userPhotoUrl; // سيحوي رابط الصورة إن وجد

    // عند تسجيل الدخول, نعكس القيم من الحالة
    if (authState is AuthLoggedIn) {
      debugPrint('User is logged in: ${authState.user.uid}');
      userName = authState.user.name;
      userEmail = authState.user.email;
      userUid = authState.user.uid;
      userPhotoUrl = authState.user.imageUrl;
    } else {
      debugPrint('User is not logged in. State: ${authState.runtimeType}');
      // إذا كان المستخدم غير مسجل الدخول، نقوم بإعادة التحقق من حالة المصادقة
      if (authState is! AuthLoading) {
        // نتحقق من حالة المصادقة فقط إذا لم نكن بالفعل في حالة التحميل
        Future.microtask(() {
          if (context.mounted) {
            context
                .read<AuthBloc>()
                .add(const CheckAuthStatusEvent(silent: true));
          }
        });
      }
    }

    return Drawer(
      backgroundColor: Theme.of(context).drawerTheme.backgroundColor ??
                      Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.zero,
      ),
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // الهيدر العلوي يحوي صورة المستخدم واسمه وبريده
          UserAccountsDrawerHeader(
            accountName: Text(
              userName,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            accountEmail: Text(
              userEmail,
              style: GoogleFonts.cairo(fontSize: 14),
            ),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Colors.white,
              backgroundImage: (userPhotoUrl != null && userPhotoUrl.isNotEmpty)
                  ? NetworkImage(userPhotoUrl)
                  : null,
              child: (userPhotoUrl == null || userPhotoUrl.isEmpty)
                  ? const Icon(
                      Icons.person,
                      size: 40,
                      color: Color(0xFF986D9E),
                    )
                  : null,
            ),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF986D9E), Color(0xFF986D9E)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),

          // البند الأول: الملف الشخصي أو تسجيل الدخول
          if (authState is AuthLoggedIn)
            // إذا كان المستخدم مسجل الدخول، نعرض زر الملف الشخصي
            ListTile(
              leading: Icon(Icons.person,
                color: Theme.of(context).iconTheme.color ??
                       Theme.of(context).colorScheme.onSurface),
              title: Text(
                AppLocalizations.of(context).profile,
                style: GoogleFonts.cairo(
                  color: Theme.of(context).textTheme.bodyLarge?.color ??
                         Theme.of(context).colorScheme.onSurface,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(
                  context,
                  AppRoutes.profile,
                  arguments: userUid,
                );
              },
            )
          else
            // إذا كان المستخدم غير مسجل الدخول، نعرض زر تسجيل الدخول
            ListTile(
              leading: Icon(Icons.login,
                color: Theme.of(context).iconTheme.color ??
                       Theme.of(context).colorScheme.onSurface),
              title: Text(
                AppLocalizations.of(context).login,
                style: GoogleFonts.cairo(
                  color: Theme.of(context).textTheme.bodyLarge?.color ??
                         Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, AppRoutes.login);
              },
            ),

          // البند الثاني: صفحة "المقترحات"
          ListTile(
            leading: Icon(Icons.feedback,
              color: Theme.of(context).iconTheme.color ??
                     Theme.of(context).colorScheme.onSurface),
            title: Text(
              AppLocalizations.of(context).suggestions,
              style: GoogleFonts.cairo(
                color: Theme.of(context).textTheme.bodyLarge?.color ??
                       Theme.of(context).colorScheme.onSurface,
              ),
            ),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, AppRoutes.suggestions);
            },
          ),

          // البند الثالث: صفحة "الإعدادات"
          ListTile(
            leading: Icon(Icons.settings,
              color: Theme.of(context).iconTheme.color ??
                     Theme.of(context).colorScheme.onSurface),
            title: Text(
              AppLocalizations.of(context).settings,
              style: GoogleFonts.cairo(
                color: Theme.of(context).textTheme.bodyLarge?.color ??
                       Theme.of(context).colorScheme.onSurface,
              ),
            ),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, AppRoutes.settings);
            },
          ),

          // البند الرابع: صفحة "تاريخ البطاقات"
          ListTile(
            leading: Icon(Icons.history,
              color: Theme.of(context).iconTheme.color ??
                     Theme.of(context).colorScheme.onSurface),
            title: Text(
              AppLocalizations.of(context).cardsHistory,
              style: GoogleFonts.cairo(
                color: Theme.of(context).textTheme.bodyLarge?.color ??
                       Theme.of(context).colorScheme.onSurface,
              ),
            ),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, AppRoutes.cardsHistory);
            },
          ),

          // البند السادس: صفحة "دليل المستخدم" - معلق مؤقتاً للتحديث المستقبلي
          // ListTile(
          //   leading: const Icon(Icons.help_outline, color: Colors.black),
          //   title: Text(
          //     'دليل المستخدم',
          //     style: GoogleFonts.cairo(color: Colors.black),
          //   ),
          //   onTap: () {
          //     Navigator.pop(context);
          //     Navigator.pushNamed(context, AppRoutes.userGuide);
          //   },
          // ),

          // توسيع لمسار "تواصل معنا" مع التعديلات على الأيقونات وحجم النص للعناصر الفرعية
          Theme(
            data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
            child: ExpansionTile(
              tilePadding: const EdgeInsets.symmetric(horizontal: 16.0),
              leading: Image.asset(
                'assets/icons/contact.png',
                width: 24,
                height: 24,
              ),
              title: Text(
                AppLocalizations.of(context).contactUs,
                style: GoogleFonts.cairo(
                  color: Theme.of(context).textTheme.bodyLarge?.color ??
                         Theme.of(context).colorScheme.onSurface,
                  fontSize: 16,
                ),
              ),
              children: [
                ListTile(
                  leading: Image.asset(
                    'assets/icons/mail.png',
                    width: 20,
                    height: 20,
                  ),
                  title: Text(
                    AppLocalizations.of(context).viaEmail,
                    style: GoogleFonts.cairo(
                      color: Theme.of(context).textTheme.bodyMedium?.color ??
                             Theme.of(context).colorScheme.onSurface,
                      fontSize: 14,
                    ),
                  ),
                  onTap: () async {
                    final currentContext = context;
                    final Uri emailUri = Uri(
                      scheme: 'mailto',
                      path: '<EMAIL>',
                    );
                    if (await canLaunchUrl(emailUri)) {
                      if (!await launchUrl(
                        emailUri,
                        mode: LaunchMode.externalApplication,
                      )) {
                        if (currentContext.mounted) {
                          ScaffoldMessenger.of(currentContext).showSnackBar(
                            SnackBar(
                              content: Text(AppLocalizations.of(currentContext)
                                  .emailError),
                            ),
                          );
                        }
                      }
                    } else {
                      if (currentContext.mounted) {
                        ScaffoldMessenger.of(currentContext).showSnackBar(
                          SnackBar(
                            content: Text(
                                AppLocalizations.of(currentContext).emailError),
                          ),
                        );
                      }
                    }
                  },
                ),
                ListTile(
                  leading: Image.asset(
                    'assets/icons/link.png',
                    width: 20,
                    height: 20,
                  ),
                  title: Text(
                    AppLocalizations.of(context).visitWebsite,
                    style: GoogleFonts.cairo(
                      color: Theme.of(context).textTheme.bodyMedium?.color ??
                             Theme.of(context).colorScheme.onSurface,
                      fontSize: 14,
                    ),
                  ),
                  onTap: () async {
                    final currentContext = context;
                    final Uri websiteUri =
                        Uri.parse("http://www.craftsolutions.sa");
                    if (await canLaunchUrl(websiteUri)) {
                      if (!await launchUrl(
                        websiteUri,
                        mode: LaunchMode.externalApplication,
                      )) {
                        if (currentContext.mounted) {
                          ScaffoldMessenger.of(currentContext).showSnackBar(
                            SnackBar(
                              content: Text(AppLocalizations.of(currentContext)
                                  .websiteError),
                            ),
                          );
                        }
                      }
                    } else {
                      if (currentContext.mounted) {
                        ScaffoldMessenger.of(currentContext).showSnackBar(
                          SnackBar(
                            content: Text(AppLocalizations.of(currentContext)
                                .websiteError),
                          ),
                        );
                      }
                    }
                  },
                ),
                ListTile(
                  leading: Image.asset(
                    'assets/icons/whatsapp.png',
                    width: 20,
                    height: 20,
                  ),
                  title: Text(
                    AppLocalizations.of(context).whatsappMessage,
                    style: GoogleFonts.cairo(
                      color: Theme.of(context).textTheme.bodyMedium?.color ??
                             Theme.of(context).colorScheme.onSurface,
                      fontSize: 14,
                    ),
                  ),
                  onTap: () async {
                    final currentContext = context;
                    const String phoneNumber = "966573412331";
                    final String welcomeMessage =
                        AppLocalizations.of(context).whatsappWelcomeMessage;
                    final Uri whatsappUri = Uri.parse(
                      "https://wa.me/$phoneNumber?text=${Uri.encodeComponent(welcomeMessage)}",
                    );
                    if (await canLaunchUrl(whatsappUri)) {
                      if (!await launchUrl(
                        whatsappUri,
                        mode: LaunchMode.externalApplication,
                      )) {
                        if (currentContext.mounted) {
                          ScaffoldMessenger.of(currentContext).showSnackBar(
                            SnackBar(
                              content: Text(AppLocalizations.of(currentContext)
                                  .whatsappError),
                            ),
                          );
                        }
                      }
                    } else {
                      if (currentContext.mounted) {
                        ScaffoldMessenger.of(currentContext).showSnackBar(
                          SnackBar(
                            content: Text(AppLocalizations.of(currentContext)
                                .whatsappError),
                          ),
                        );
                      }
                    }
                  },
                ),
              ],
            ),
          ),

          // زر تسجيل الخروج مع نافذة تأكيد
          ListTile(
            leading: Icon(Icons.logout,
              color: Theme.of(context).iconTheme.color ??
                     Theme.of(context).colorScheme.onSurface),
            title: Text(
              AppLocalizations.of(context).logout,
              style: GoogleFonts.cairo(
                color: Theme.of(context).textTheme.bodyLarge?.color ??
                       Theme.of(context).colorScheme.onSurface,
              ),
            ),
            onTap: () async {
              final currentContext = context;
              final confirm = await showDialog<bool>(
                context: currentContext,
                builder: (context) => AlertDialog(
                  backgroundColor: Theme.of(context).dialogBackgroundColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  title: Text(
                    AppLocalizations.of(context).confirm,
                    style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                  ),
                  content: Text(
                    AppLocalizations.of(context).logoutConfirm,
                    style: GoogleFonts.cairo(),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: Text(
                        AppLocalizations.of(context).cancel,
                        style: GoogleFonts.cairo(),
                      ),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      child: Text(
                        AppLocalizations.of(context).ok,
                        style: GoogleFonts.cairo(),
                      ),
                    ),
                  ],
                ),
              );

              if (confirm == true && currentContext.mounted) {
                // إغلاق القائمة الجانبية
                Navigator.pop(currentContext);

                // تسجيل الخروج عبر AuthBloc - سيتم التعامل مع التوجيه في التطبيق الرئيسي
                if (currentContext.mounted) {
                  currentContext.read<AuthBloc>().add(LogoutEvent());
                }
              }
            },
          ),
          const Divider(),

          // النصوص التوضيحية في الأسفل
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Directionality(
                  textDirection: TextDirection.rtl,
                  child: Text(
                    AppLocalizations.of(context).allRightsReserved,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
