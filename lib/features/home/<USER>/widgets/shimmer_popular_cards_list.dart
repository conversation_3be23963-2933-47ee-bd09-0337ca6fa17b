// lib/features/home/<USER>/widgets/shimmer_popular_cards_list.dart

import 'package:flutter/material.dart';
import '../../../../core/widgets/shimmer_loading.dart';

/// Widget para mostrar un efecto de carga Shimmer para las tarjetas populares
class ShimmerPopularCardsList extends StatelessWidget {
  /// Número de tarjetas a mostrar
  final int itemCount;

  /// Ancho de cada tarjeta
  final double cardWidth;

  /// Altura de cada tarjeta
  final double cardHeight;

  /// Radio de borde de las tarjetas
  final double borderRadius;

  /// Constructor
  const ShimmerPopularCardsList({
    super.key,
    this.itemCount = 5,
    this.cardWidth = 160,
    this.cardHeight = 220,
    this.borderRadius = 12,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: cardHeight,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: itemCount,
        itemBuilder: (context, index) {
          return _buildShimmerCard(context);
        },
      ),
    );
  }

  /// Construye una tarjeta con efecto Shimmer
  Widget _buildShimmerCard(BuildContext context) {
    return Container(
      width: cardWidth,
      margin: const EdgeInsets.only(right: 12),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          clipBehavior: Clip.antiAlias,
          child: Stack(
            children: [
              // Efecto Shimmer para la imagen de la tarjeta
              ShimmerBox(
                width: cardWidth,
                height: cardHeight,
                borderRadius: borderRadius,
                baseColor: const Color(0xFFF5F5F5),
                highlightColor: Colors.white,
              ),

              // Efecto Shimmer para el contador de popularidad
              Positioned(
                top: 8,
                right: 8,
                child: ShimmerBox(
                  width: 50,
                  height: 24,
                  borderRadius: 12,
                  baseColor: const Color(0xFFF5F5F5),
                  highlightColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
