// lib/features/home/<USER>/widgets/card_item.dart
// Archivo creado por Augment Agent

import 'package:flutter/material.dart';
import 'package:mashair/features/create_card/presentation/pages/card_editor_page_wrapper.dart';
import 'package:mashair/features/home/<USER>/entities/popular_card_entity.dart';

/// عنصر بطاقة في الصفحة الرئيسية
class CardItem extends StatelessWidget {
  /// نموذج البطاقة
  final PopularCardEntity card;

  /// هل يستخدم المحرر الموحد الجديد
  final bool useUnifiedEditor;

  /// إنشاء عنصر بطاقة
  const CardItem({
    super.key,
    required this.card,
    this.useUnifiedEditor = true, // استخدام المحرر الموحد الجديد افتراضيًا
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _openCardEditor(context),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة البطاقة
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              child: AspectRatio(
                aspectRatio: 0.7, // نسبة العرض إلى الارتفاع
                child: _buildCardImage(),
              ),
            ),

            // معلومات البطاقة
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان البطاقة
                  Text(
                    card.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // وصف البطاقة
                  Text(
                    card.description ?? '',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // عدد الزيارات
                  Row(
                    children: [
                      const Icon(
                        Icons.visibility,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${card.popularity} زيارة',
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صورة البطاقة
  Widget _buildCardImage() {
    if (card.imageUrl != null && card.imageUrl!.isNotEmpty) {
      return Image.network(
        card.imageUrl!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            return child;
          }
          return _buildLoadingIndicator(loadingProgress);
        },
      );
    }

    return _buildPlaceholder();
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator(ImageChunkEvent loadingProgress) {
    return Center(
      child: CircularProgressIndicator(
        value: loadingProgress.expectedTotalBytes != null
            ? loadingProgress.cumulativeBytesLoaded /
                loadingProgress.expectedTotalBytes!
            : null,
      ),
    );
  }

  /// بناء صورة بديلة
  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[300],
      child: const Center(
        child: Icon(
          Icons.image,
          size: 48,
          color: Colors.grey,
        ),
      ),
    );
  }

  /// فتح محرر البطاقات
  void _openCardEditor(BuildContext context) {
    // استخدام مدخل محرر البطاقات لفتح المحرر
    CardEditorEntry.openCardEditor(
      context,
      cardId: card.id,
      useUnifiedEditor: useUnifiedEditor,
    );
  }

  /// تحليل لون من سلسلة نصية
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return null;
    }

    try {
      // إزالة علامة # إذا وجدت
      final hex =
          colorString.startsWith('#') ? colorString.substring(1) : colorString;

      // تحويل سلسلة اللون إلى عدد صحيح
      int colorValue;

      // إذا كان الرمز يحتوي على قناة ألفا بالفعل
      if (hex.length == 8) {
        colorValue = int.parse(hex, radix: 16);
      } else {
        // إذا كان الرمز بدون قناة ألفا
        colorValue = int.parse('FF$hex', radix: 16);
      }

      return Color(colorValue);
    } catch (e) {
      debugPrint('خطأ في تحليل اللون: $e');
      return null;
    }
  }
}
