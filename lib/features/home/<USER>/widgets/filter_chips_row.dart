// lib/features/home/<USER>/widgets/filter_chips_row.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// صف رقائق التصفية
class FilterChipsRow extends StatelessWidget {
  /// التصفية المحددة حالياً
  final String selectedFilter;

  /// دالة تستدعى عند تغيير التصفية
  final Function(String) onFilterChanged;

  /// إنشاء صف رقائق التصفية
  const FilterChipsRow({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          _buildFilterChip(
            context,
            'all',
            'الكل',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'featured',
            'مميزة',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'recent',
            'الأحدث',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'popular',
            'الأكثر شعبية',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'religious',
            'دينية',
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            context,
            'social',
            'اجتماعية',
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة تصفية
  Widget _buildFilterChip(
    BuildContext context,
    String value,
    String label,
  ) {
    final isSelected = selectedFilter == value;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        boxShadow: isSelected ? [
          BoxShadow(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: FilterChip(
        label: Text(
          label,
          style: GoogleFonts.cairo(
            color: isSelected
                ? Colors.white
                : Theme.of(context).textTheme.bodyMedium?.color,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 13,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            onFilterChanged(value);
          }
        },
        backgroundColor: Theme.of(context).cardColor,
        selectedColor: Theme.of(context).primaryColor,
        checkmarkColor: Colors.white,
        elevation: isSelected ? 2 : 0,
        pressElevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(25),
          side: BorderSide(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }
}
