// lib/features/home/<USER>/widgets/upcoming_events_card.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../../../routes.dart';
import '../../domain/entities/calendar_event_entity.dart';

/// بطاقة الأحداث القادمة
class UpcomingEventsCard extends StatelessWidget {
  /// قائمة الأحداث
  final List<CalendarEventEntity> events;

  /// إنشاء بطاقة الأحداث القادمة
  const UpcomingEventsCard({
    super.key,
    required this.events,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.event,
                    color: Colors.indigo,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'المناسبات القادمة',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Divider(),
              if (events.isEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Center(
                    child: Text(
                      'لا توجد مناسبات قادمة',
                      style: GoogleFonts.cairo(
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: events.length > 3 ? 3 : events.length,
                  itemBuilder: (context, index) {
                    final event = events[index];
                    return _buildEventItem(context, event);
                  },
                ),
              if (events.length > 3)
                Align(
                  alignment: Alignment.center,
                  child: TextButton(
                    onPressed: () {
                      Navigator.pushNamed(context, AppRoutes.cardsHistory);
                    },
                    child: Text(
                      'عرض المزيد',
                      style: GoogleFonts.cairo(),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر الحدث
  Widget _buildEventItem(BuildContext context, CalendarEventEntity event) {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final formattedDate = dateFormat.format(event.date);

    // حساب الأيام المتبقية
    final daysRemaining = event.date.difference(DateTime.now()).inDays;

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: _getEventColor(event.type),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            event.date.day.toString(),
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ),
      ),
      title: Text(
        event.title,
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text(
        '$formattedDate (متبقي $daysRemaining يوم)',
        style: GoogleFonts.cairo(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
      trailing: TextButton(
        onPressed: () {
          Navigator.pushNamed(
            context,
            AppRoutes.createCard,
            arguments: {'occasionId': event.occasionId, 'isAiMode': false},
          );
        },
        child: Text(
          'إنشاء بطاقة',
          style: GoogleFonts.cairo(),
        ),
      ),
    );
  }

  /// الحصول على لون الحدث
  Color _getEventColor(String type) {
    switch (type) {
      case 'religious':
        return Colors.green;
      case 'national':
        return Colors.red;
      case 'personal':
        return Colors.blue;
      case 'social':
        return Colors.purple;
      default:
        return Colors.indigo;
    }
  }
}
