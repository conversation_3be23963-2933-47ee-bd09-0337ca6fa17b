// lib/features/home/<USER>/widgets/recent_cards_list.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';


import '../../../../core/widgets/optimized/index.dart';
import '../../../../routes.dart';
import '../../domain/entities/greeting_card_entity.dart';

/// قائمة البطاقات الأخيرة
class RecentCardsList extends StatelessWidget {
  /// قائمة البطاقات
  final List<GreetingCardEntity> cards;

  /// إنشاء قائمة البطاقات الأخيرة
  const RecentCardsList({
    super.key,
    required this.cards,
  });

  @override
  Widget build(BuildContext context) {
    // ترتيب البطاقات حسب الأحدث
    final sortedCards = List<GreetingCardEntity>.from(cards)
      ..sort((a, b) {
        // ترتيب حسب التاريخ (الأحدث أولاً)
        return b.createdAt.compareTo(a.createdAt);
      });

    return SizedBox(
      height: 200,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: sortedCards.length,
        itemBuilder: (context, index) {
          final card = sortedCards[index];
          return _buildCardItem(context, card);
        },
      ),
    );
  }

  /// بناء عنصر البطاقة
  Widget _buildCardItem(BuildContext context, GreetingCardEntity card) {
    // تنسيق التاريخ
    final formattedDate = DateFormat('yyyy/MM/dd').format(card.createdAt);

    return Container(
      width: 150,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: () {
            Navigator.pushNamed(
              context,
              AppRoutes.createCard,
              arguments: {'card': card, 'cardId': card.id, 'isAiMode': false},
            );
          },
          child: Stack(
            children: [
              // صورة البطاقة
              SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: card.imageUrl.isNotEmpty
                    ? OptimizedImage(
                        imageUrl: card.imageUrl,
                        fit: BoxFit.cover,
                        width: double.infinity,
                      )
                    : Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.image,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
              ),

              // تاريخ الإنشاء في الزاوية العلوية اليسرى
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    formattedDate,
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),

              // تأثير التظليل الخفيف في الأسفل
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.6),
                      ],
                    ),
                  ),
                ),
              ),

              // عنوان البطاقة
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      card.title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.8),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      card.occasion,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.9),
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.8),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
