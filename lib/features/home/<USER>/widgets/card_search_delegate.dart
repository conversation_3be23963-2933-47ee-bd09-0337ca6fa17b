// lib/features/home/<USER>/widgets/card_search_delegate.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../routes.dart';
import '../../domain/entities/greeting_card_entity.dart';
import '../blocs/search/search_bloc.dart';
import '../blocs/search/search_event.dart';
import '../blocs/search/search_state.dart';

/// مندوب البحث عن البطاقات
class CardSearchDelegate extends SearchDelegate<String> {
  @override
  String get searchFieldLabel => 'البحث عن بطاقات...';

  @override
  TextStyle get searchFieldStyle => GoogleFonts.cairo(
        fontSize: 16,
        color: Colors.black87, // سيتم تجاوزه بواسطة الثيم
      );

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            query = '';
            showSuggestions(context);
          },
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    if (query.trim().isEmpty) {
      return Center(
        child: Text(
          'أدخل نص للبحث',
          style: GoogleFonts.cairo(fontSize: 16),
        ),
      );
    }

    // تشغيل حدث البحث
    context.read<SearchBloc>().add(SearchCardsEvent(query: query.trim()));

    return BlocBuilder<SearchBloc, SearchState>(
      builder: (context, state) {
        if (state is SearchLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is SearchError) {
          return Center(
            child: Text(
              'حدث خطأ: ${state.message}',
              style: GoogleFonts.cairo(fontSize: 16),
            ),
          );
        }

        if (state is SearchResults) {
          if (state.cards.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.search_off, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد نتائج لـ "$query"',
                    style: GoogleFonts.cairo(fontSize: 16),
                  ),
                ],
              ),
            );
          }

          return _buildSearchResults(context, state.cards);
        }

        return Center(
          child: Text(
            'ابدأ البحث عن البطاقات',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    // يمكن إضافة اقتراحات البحث هنا
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.search, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            'ابحث عن البطاقات حسب الاسم أو المناسبة',
            style: GoogleFonts.cairo(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// بناء نتائج البحث
  Widget _buildSearchResults(
      BuildContext context, List<GreetingCardEntity> cards) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) {
        final card = cards[index];
        return _buildCardItem(context, card);
      },
    );
  }

  /// بناء عنصر البطاقة
  Widget _buildCardItem(BuildContext context, GreetingCardEntity card) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () {
          close(context, card.id);
          Navigator.pushNamed(
            context,
            AppRoutes.createCard,
            arguments: {'cardId': card.id, 'isAiMode': false},
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة البطاقة
            Expanded(
              child: card.imageUrl.isNotEmpty
                  ? Image.network(
                      card.imageUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: const Icon(
                            Icons.image_not_supported,
                            size: 50,
                            color: Colors.grey,
                          ),
                        );
                      },
                    )
                  : Container(
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.image,
                        size: 50,
                        color: Colors.grey,
                      ),
                    ),
            ),
            // معلومات البطاقة
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    card.title,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    card.occasion,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
