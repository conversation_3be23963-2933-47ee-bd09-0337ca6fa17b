import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/optimized/optimized_image.dart';
import '../../../../routes.dart';
import '../../../auth/presentation/blocs/auth/auth_bloc.dart';
import '../../../auth/presentation/blocs/auth/auth_event.dart';
import '../../../auth/presentation/blocs/auth/auth_state.dart';

/// درج جانبي مخصص للصفحة الرئيسية الجديدة
class NewHomeDrawer extends StatelessWidget {
  const NewHomeDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, authState) {
        debugPrint('AuthBloc state in Drawer: ${authState.runtimeType}');
        if (authState is AuthLoggedOut) {
          debugPrint('User logged out in Drawer, navigating to login');
          // إغلاق الدرج أولاً إذا كان مفتوحاً
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          }
          // التوجه لصفحة تسجيل الدخول
          Navigator.of(context).pushNamedAndRemoveUntil(
            AppRoutes.login,
            (route) => false,
          );
        }
      },
      builder: (context, authState) {
        // قيم افتراضية
        String userName = '--';
        String userEmail = '--';
        String? userUid;
        String? userPhotoUrl; // سيحوي رابط الصورة إن وجد

        // عند تسجيل الدخول, نعكس القيم من الحالة
        if (authState is AuthLoggedIn) {
          userName = authState.user.name;
          userEmail = authState.user.email;
          userUid = authState.user.uid;
          userPhotoUrl = authState.user.imageUrl;
          debugPrint('Drawer: User data loaded - UID: $userUid, Name: $userName, Email: $userEmail');
        } else {
          debugPrint('Drawer: User not logged in, authState: ${authState.runtimeType}');
        }

        return _buildDrawerContent(context, userName, userEmail, userUid, userPhotoUrl, authState);
      },
    );
  }

  /// بناء محتوى الدرج الجانبي
  Widget _buildDrawerContent(
    BuildContext context,
    String userName,
    String userEmail,
    String? userUid,
    String? userPhotoUrl,
    AuthState authState,
  ) {
    return Drawer(
        width: 280, // Ancho reducido para el drawer
        backgroundColor: Theme.of(context).drawerTheme.backgroundColor ??
                        Theme.of(context).scaffoldBackgroundColor,
        child: Column(
        children: [
          // Header con información del usuario
          Container(
            width: double.infinity, // Asegura que ocupe todo el ancho
            padding: const EdgeInsets.only(top: 40, bottom: 16),
            decoration: BoxDecoration(
              color:
                  AppColors.primaryColor, // Color sólido en lugar de gradiente
            ),
            child: Column(
              children: [
                // Foto de perfil
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(26),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: (userPhotoUrl != null && userPhotoUrl.isNotEmpty)
                      ? ClipOval(
                          child: OptimizedImage(
                            imageUrl: userPhotoUrl,
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                            useProgressiveLoading: true,
                            freezeWhenInactive: false,
                          ),
                        )
                      : Icon(
                          Icons.person,
                          size: 40,
                          color: AppColors.primaryColor,
                        ),
                ),
                const SizedBox(height: 12),
                // Nombre de usuario
                Text(
                  userName,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                // Email
                Text(
                  userEmail,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white.withAlpha(230),
                  ),
                ),
              ],
            ),
          ),

          // Lista de opciones del menú
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildMenuItem(
                  context,
                  icon: Icons.home,
                  title: AppLocalizations.of(context).homeScreen,
                  onTap: () => Navigator.pop(context),
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.person,
                  title: AppLocalizations.of(context).profile,
                  onTap: () {
                    Navigator.pop(context);
                    debugPrint('Profile button pressed - userUid: $userUid');
                    debugPrint('AuthState type: ${authState.runtimeType}');

                    if (userUid != null && userUid.isNotEmpty) {
                      debugPrint('Navigating to profile with UID: $userUid');
                      Navigator.pushNamed(
                        context,
                        AppRoutes.profile,
                        arguments: userUid,
                      );
                    } else {
                      debugPrint('No valid UID found, redirecting to login');
                      // بدلاً من التوجه لصفحة تسجيل الدخول، نطلب من المستخدم تسجيل الدخول أولاً
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى تسجيل الدخول أولاً للوصول إلى الملف الشخصي'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    }
                  },
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.card_giftcard,
                  title: AppLocalizations.of(context).myCards,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.cardsHistory);
                  },
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.notifications,
                  title: AppLocalizations.of(context).notifications,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.notifications);
                  },
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.feedback,
                  title: AppLocalizations.of(context).suggestions,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.suggestions);
                  },
                ),
                // دليل المستخدم - معلق مؤقتاً للتحديث المستقبلي
                // _buildMenuItem(
                //   context,
                //   icon: Icons.help_outline,
                //   title: AppLocalizations.of(context).userGuide,
                //   onTap: () {
                //     Navigator.pop(context);
                //     Navigator.pushNamed(context, AppRoutes.userGuide);
                //   },
                // ),
                // قسم تواصل معنا مع قائمة منسدلة
                _buildExpandableMenuItem(
                  context,
                  icon: Icons.contact_support,
                  title: AppLocalizations.of(context).contactUs,
                  children: [
                    _buildSubmenuItem(
                      context,
                      icon: Icons.email,
                      title: AppLocalizations.of(context).viaEmail,
                      onTap: () async {
                        Navigator.pop(context);
                        final Uri emailUri = Uri(
                          scheme: 'mailto',
                          path: '<EMAIL>',
                        );
                        if (await canLaunchUrl(emailUri)) {
                          await launchUrl(
                            emailUri,
                            mode: LaunchMode.externalApplication,
                          );
                        } else {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    AppLocalizations.of(context).emailError),
                              ),
                            );
                          }
                        }
                      },
                    ),
                    _buildSubmenuItem(
                      context,
                      icon: Icons.language,
                      title: AppLocalizations.of(context).visitWebsite,
                      onTap: () async {
                        Navigator.pop(context);
                        final Uri websiteUri =
                            Uri.parse("http://www.craftsolutions.sa");
                        if (await canLaunchUrl(websiteUri)) {
                          await launchUrl(
                            websiteUri,
                            mode: LaunchMode.externalApplication,
                          );
                        } else {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    AppLocalizations.of(context).websiteError),
                              ),
                            );
                          }
                        }
                      },
                    ),
                    _buildSubmenuItem(
                      context,
                      icon: Icons.message,
                      title: AppLocalizations.of(context).whatsappMessage,
                      onTap: () async {
                        Navigator.pop(context);
                        const String phoneNumber = "966573412331";
                        final String welcomeMessage =
                            "مرحباً، أريد الاستفسار عن تطبيق Mashair";
                        final Uri whatsappUri = Uri.parse(
                          "https://wa.me/$phoneNumber?text=${Uri.encodeComponent(welcomeMessage)}",
                        );
                        if (await canLaunchUrl(whatsappUri)) {
                          await launchUrl(
                            whatsappUri,
                            mode: LaunchMode.externalApplication,
                          );
                        } else {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    AppLocalizations.of(context).whatsappError),
                              ),
                            );
                          }
                        }
                      },
                    ),
                  ],
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  child: Divider(height: 1),
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.settings,
                  title: AppLocalizations.of(context).settings,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.settings);
                  },
                ),
                _buildMenuItem(
                  context,
                  icon: Icons.logout,
                  title: AppLocalizations.of(context).logout,
                  iconColor: Colors.red,
                  onTap: () async {
                    // Guardar una referencia al contexto actual
                    final currentContext = context;

                    final confirm = await showDialog<bool>(
                      context: currentContext,
                      builder: (dialogContext) => AlertDialog(
                        backgroundColor: Theme.of(context).dialogBackgroundColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        title: Text(
                          AppLocalizations.of(context).confirm,
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).textTheme.headlineSmall?.color,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        content: Text(
                          AppLocalizations.of(context).logoutConfirmation,
                          style: GoogleFonts.cairo(
                            color: Theme.of(context).textTheme.bodyMedium?.color,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        actions: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              TextButton(
                                onPressed: () =>
                                    Navigator.pop(dialogContext, false),
                                style: TextButton.styleFrom(
                                  foregroundColor: Colors.grey[700],
                                ),
                                child: Text(
                                  AppLocalizations.of(context).cancel,
                                  style: GoogleFonts.cairo(),
                                ),
                              ),
                              const SizedBox(width: 16),
                              TextButton(
                                onPressed: () =>
                                    Navigator.pop(dialogContext, true),
                                style: TextButton.styleFrom(
                                  foregroundColor: Colors.red,
                                ),
                                child: Text(
                                  AppLocalizations.of(context).ok,
                                  style: GoogleFonts.cairo(
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );

                    // Verificar si el widget todavía está montado antes de usar el contexto
                    if (confirm == true && currentContext.mounted) {
                      Navigator.pop(currentContext); // Cerrar el drawer

                      // تسجيل الخروج مباشرة بدون حوار تحميل
                      debugPrint('Logout button pressed in drawer, triggering logout event');
                      currentContext.read<AuthBloc>().add(LogoutEvent());
                    }
                  },
                ),
              ],
            ),
          ),

          // Footer con copyright
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Text(
              AppLocalizations.of(context).copyright,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Theme.of(context).textTheme.bodySmall?.color ??
                       Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
        ),
    );
  }

  /// بناء عنصر قائمة مخصص
  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return RepaintBoundary(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          splashColor: AppColors.primaryColor.withValues(alpha: 0.1),
          highlightColor: AppColors.primaryColor.withValues(alpha: 0.05),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 22,
                  color: iconColor ?? AppColors.primaryColor,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodyLarge?.color ??
                             Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء عنصر قائمة قابل للتوسيع
  Widget _buildExpandableMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required List<Widget> children,
  }) {
    return RepaintBoundary(
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
        child: ExpansionTile(
          leading: Icon(
            icon,
            size: 22,
            color: AppColors.primaryColor,
          ),
          title: Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).textTheme.bodyLarge?.color ??
                     Theme.of(context).colorScheme.onSurface,
            ),
          ),
          tilePadding: const EdgeInsets.symmetric(horizontal: 16),
          childrenPadding: const EdgeInsets.only(left: 16, right: 16),
          maintainState: true,
          children: children,
        ),
      ),
    );
  }

  /// بناء عنصر قائمة فرعية
  Widget _buildSubmenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return RepaintBoundary(
      child: Material(
        color: Colors.transparent,
        child: ListTile(
          leading: Icon(
            icon,
            size: 20,
            color: AppColors.primaryColor,
          ),
          title: Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color ??
                     Theme.of(context).colorScheme.onSurface,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          dense: true,
          splashColor: AppColors.primaryColor.withValues(alpha: 0.1),
          onTap: onTap,
        ),
      ),
    );
  }
}
