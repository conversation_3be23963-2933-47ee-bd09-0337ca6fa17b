// lib/features/home/<USER>/widgets/popular_cards_list.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/widgets/shimmer_loading.dart';
import '../../../../routes.dart';
import '../../domain/entities/popular_card_entity.dart';

/// قائمة البطاقات الشائعة
class PopularCardsList extends StatelessWidget {
  /// قائمة البطاقات
  final List<PopularCardEntity> cards;

  /// إنشاء قائمة البطاقات الشائعة
  const PopularCardsList({
    super.key,
    required this.cards,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 200,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: cards.length,
        itemBuilder: (context, index) {
          final card = cards[index];
          return _buildCardItem(context, card);
        },
      ),
    );
  }

  /// بناء عنصر البطاقة
  Widget _buildCardItem(BuildContext context, PopularCardEntity card) {
    return Container(
      width: 150,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: () {
            Navigator.pushNamed(
              context,
              AppRoutes.createCard,
              arguments: {'cardId': card.id, 'isAiMode': false},
            );
          },
          child: Stack(
            children: [
              // صورة البطاقة
              SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Hero(
                  tag: 'popular_card_${card.id}',
                  child: card.thumbnailUrl.isNotEmpty
                      ? Image.network(
                          card.thumbnailUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Theme.of(context).colorScheme.surfaceContainerHighest,
                              child: Icon(
                                Icons.image_not_supported,
                                size: 50,
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            // Usar un efecto Shimmer para la carga de imágenes
                            return Container(
                              color: Theme.of(context).colorScheme.surfaceContainerHighest,
                              child: ShimmerBox(
                                width: double.infinity,
                                height: double.infinity,
                                borderRadius: 12,
                                baseColor: const Color(0xFFF5F5F5),
                                highlightColor: Colors.white,
                              ),
                            );
                          },
                        )
                      : Container(
                          color: Theme.of(context).colorScheme.surfaceContainerHighest,
                          child: Icon(
                            Icons.image,
                            size: 50,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                ),
              ),
              // تأثير التظليل الخفيف في الأسفل
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.6),
                      ],
                    ),
                  ),
                ),
              ),
              // عنوان البطاقة
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Text(
                  card.title,
                  style: GoogleFonts.cairo(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.8),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
