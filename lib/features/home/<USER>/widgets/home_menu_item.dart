// lib/features/home/<USER>/widgets/home_menu_item.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/widgets/enhanced_tooltip.dart';

/// عنصر قائمة في الصفحة الرئيسية مع تلميح
class HomeMenuItem extends StatelessWidget {
  /// عنوان العنصر
  final String title;
  
  /// وصف العنصر
  final String description;
  
  /// أيقونة العنصر
  final IconData icon;
  
  /// لون العنصر
  final Color color;
  
  /// دالة يتم استدعاؤها عند النقر على العنصر
  final VoidCallback? onTap;
  
  /// نص التلميح
  final String? tooltip;
  
  /// إنشاء عنصر قائمة في الصفحة الرئيسية
  const HomeMenuItem({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    this.onTap,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    final Widget menuItem = Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48.0,
                color: color,
              ),
              const SizedBox(height: 12.0),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4.0),
              Text(
                description,
                style: GoogleFonts.cairo(
                  fontSize: 12.0,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
    
    // إذا كان هناك تلميح، قم بتغليف العنصر بتلميح محسن
    if (tooltip != null) {
      return EnhancedTooltip(
        message: tooltip!,
        icon: icon,
        position: TooltipPosition.top,
        child: menuItem,
      );
    }
    
    return menuItem;
  }
}
