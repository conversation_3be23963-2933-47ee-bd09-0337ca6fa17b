import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';

import '../../../../routes.dart';
import '../../domain/entities/banner_entity.dart';

/// Widget to display a list of banners in a slider.
class BannerSlider extends StatelessWidget {
  final List<BannerEntity> banners;

  const BannerSlider({super.key, required this.banners});

  @override
  Widget build(BuildContext context) {
    if (banners.isEmpty) {
      // If there are no banners, show a default banner
      return _buildDefaultBanner(context);
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: FlutterCarousel(
        options: CarouselOptions(
          height: 180,
          aspectRatio: 16 / 9,
          viewportFraction: 1.0,
          initialPage: 0,
          enableInfiniteScroll: true,
          reverse: false,
          autoPlay: true,
          autoPlayInterval: const Duration(seconds: 4),
          autoPlayAnimationDuration: const Duration(milliseconds: 600),
          autoPlayCurve: Curves.easeInOut,
          enlargeCenterPage: false,
          scrollDirection: Axis.horizontal,
        ),
      items: banners.map((banner) {
        return Builder(
          builder: (context) {
            return GestureDetector(
              onTap: () {
                // Open cards page related to the occasion
                // If there is an occasion ID linked to the banner, use it
                if (banner.occasionId != null &&
                    banner.occasionId.toString().isNotEmpty) {
                  // Convert occasion ID to String explicitly
                  final String safeOccasionId = banner.occasionId.toString();

                  debugPrint(
                    'Opening cards page with occasion ID: $safeOccasionId',
                  );
                  debugPrint(
                    'Occasion ID type: ${banner.occasionId.runtimeType}',
                  );

                  Navigator.pushNamed(
                    context,
                    AppRoutes.cardsList,
                    arguments: {
                      'occasionId': safeOccasionId,
                      'occasionName': banner.occasionName ?? banner.title,
                    },
                  );
                }
                // Extract occasion ID from banner link (if in format occasion:ID)
                else if (banner.link.startsWith('occasion:')) {
                  // Extract occasion ID and convert to String explicitly
                  final String safeOccasionId = banner.link.substring(9);

                  debugPrint(
                    'Opening cards page with occasion ID from link: $safeOccasionId',
                  );
                  Navigator.pushNamed(
                    context,
                    AppRoutes.cardsList,
                    arguments: {
                      'occasionId': safeOccasionId,
                      'occasionName': banner.title,
                    },
                  );
                }
                // If there is an occasion name linked to the banner, use it
                else if (banner.occasionName != null &&
                    banner.occasionName!.isNotEmpty) {
                  Navigator.pushNamed(
                    context,
                    AppRoutes.cardsList,
                    arguments: {'occasionName': banner.occasionName},
                  );
                }
                // If the link contains only the occasion name
                else if (banner.link.isNotEmpty) {
                  Navigator.pushNamed(
                    context,
                    AppRoutes.cardsList,
                    arguments: {'occasionName': banner.link},
                  );
                }
                // If there is no link, use the banner title as the occasion name
                else if (banner.title.isNotEmpty) {
                  Navigator.pushNamed(
                    context,
                    AppRoutes.cardsList,
                    arguments: {'occasionName': banner.title},
                  );
                }
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 12,
                      spreadRadius: 0,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: banner.imageUrl.startsWith('assets/')
                      ? Image.asset(
                          banner.imageUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                        )
                      : Image.network(
                          banner.imageUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultBannerContent();
                          },
                        ),
                ),
              ),
            );
          },
        );
      }).toList(),
      ),
    );
  }

  /// Build a default banner
  Widget _buildDefaultBanner(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.7),
            Theme.of(context).primaryColor,
          ],
        ),
      ),
      child: _buildDefaultBannerContent(),
    );
  }

  /// Build default banner content
  Widget _buildDefaultBannerContent() {
    return Stack(
      children: [
        // Gift images in background
        Positioned(
          left: 10,
          top: 40,
          child: Image.asset(
            'assets/icons/gift.png',
            width: 60,
            height: 60,
            color: Colors.purple[200],
            errorBuilder: (context, error, stackTrace) {
              return Icon(
                Icons.card_giftcard,
                size: 60,
                color: Colors.purple[200],
              );
            },
          ),
        ),
        Positioned(
          right: 10,
          bottom: 40,
          child: Image.asset(
            'assets/icons/gift.png',
            width: 60,
            height: 60,
            color: Colors.purple[200],
            errorBuilder: (context, error, stackTrace) {
              return Icon(
                Icons.card_giftcard,
                size: 60,
                color: Colors.purple[200],
              );
            },
          ),
        ),
        Positioned(
          right: 80,
          top: 20,
          child: Image.asset(
            'assets/icons/gift.png',
            width: 40,
            height: 40,
            color: Colors.purple[100],
            errorBuilder: (context, error, stackTrace) {
              return Icon(
                Icons.card_giftcard,
                size: 40,
                color: Colors.purple[100],
              );
            },
          ),
        ),

        // Center content
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.card_giftcard,
                size: 48,
                color: Colors.white,
              ),
              const SizedBox(height: 12),
              Text(
                'Explore our beautiful cards',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
