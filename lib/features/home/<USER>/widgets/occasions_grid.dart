// lib/features/occasions/presentation/widgets/occasions_grid.dart

import 'package:flutter/material.dart';

import '../../../../routes.dart';
import '../../domain/entities/occasion_entity.dart';

class OccasionsGrid extends StatelessWidget {
  final List<OccasionEntity> occasions;

  const OccasionsGrid({super.key, required this.occasions});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      itemCount: occasions.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3, // ثلاث مناسبات في كل صف
        crossAxisSpacing: 12, // تقليل المسافة الأفقية
        mainAxisSpacing: 16, // تقليل المسافة العمودية
        childAspectRatio: 0.85, // تحسين نسبة العرض للارتفاع
      ),
      itemBuilder: (context, index) {
        final occasion = occasions[index];
        return GestureDetector(
          onTap: () {
            // تحويل معرف المناسبة إلى String بشكل صريح
            final String safeOccasionId = occasion.id.toString();

            debugPrint('فتح صفحة البطاقات بمعرف المناسبة: $safeOccasionId');
            debugPrint('نوع معرف المناسبة: ${occasion.id.runtimeType}');

            Navigator.pushNamed(context, AppRoutes.cardsList, arguments: {
              'occasionId': safeOccasionId,
              'occasionName': occasion.name
            });
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة المناسبة مع خلفية دائرية
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Image.asset(
                      occasion.iconAsset,
                      width: 30,
                      height: 30,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.favorite,
                          size: 30,
                          color: Theme.of(context).primaryColor,
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  occasion.name,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
