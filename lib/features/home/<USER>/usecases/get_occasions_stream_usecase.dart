import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/occasion_entity.dart';
import '../repositories/home_repository.dart';
import 'get_banners_stream_usecase.dart';

/// UseCase لجلب تدفق المناسبات من الـ HomeRepository.
/// يستخدم هذا الـ UseCase للحصول على تحديثات المناسبات في الوقت الحقيقي.
class GetOccasionsStreamUseCase implements StreamUseCase<List<OccasionEntity>, NoParams> {
  final HomeRepository repository;

  GetOccasionsStreamUseCase(this.repository);

  @override
  Stream<Either<Failure, List<OccasionEntity>>> call(NoParams params) {
    return repository.getOccasionsStream();
  }
}
