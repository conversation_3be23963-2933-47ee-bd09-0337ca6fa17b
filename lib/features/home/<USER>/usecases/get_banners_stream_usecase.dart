import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/banner_entity.dart';
import '../repositories/home_repository.dart';

/// UseCase لجلب تدفق البانرات من الـ HomeRepository.
/// يستخدم هذا الـ UseCase للحصول على تحديثات البانرات في الوقت الحقيقي.
class GetBannersStreamUseCase implements StreamUseCase<List<BannerEntity>, NoParams> {
  final HomeRepository repository;

  GetBannersStreamUseCase(this.repository);

  @override
  Stream<Either<Failure, List<BannerEntity>>> call(NoParams params) {
    return repository.getBannersStream();
  }
}

/// واجهة لـ UseCase يعيد Stream بدلاً من Future
abstract class StreamUseCase<Type, Params> {
  Stream<Either<Failure, Type>> call(Params params);
}
