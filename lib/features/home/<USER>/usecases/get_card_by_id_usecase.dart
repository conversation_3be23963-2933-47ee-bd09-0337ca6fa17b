// lib/features/home/<USER>/usecases/get_card_by_id_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/greeting_card_entity.dart';
import '../repositories/home_repository.dart';

/// Parameters for fetching a card by its ID.
class CardParams {
  final String id;

  CardParams({required this.id});
}

/// UseCase to fetch a single greeting card by ID.
class GetCardByIdUseCase implements UseCase<GreetingCardEntity, CardParams> {
  final HomeRepository repository;

  GetCardByIdUseCase(this.repository);

  @override
  Future<Either<Failure, GreetingCardEntity>> call(CardParams params) {
    return repository.getCardById(params.id);
  }
}
