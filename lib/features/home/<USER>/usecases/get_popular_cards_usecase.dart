// lib/features/home/<USER>/usecases/get_popular_cards_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/popular_card_entity.dart';
import '../repositories/home_repository.dart';

class GetPopularCardsUseCase
    implements UseCase<List<PopularCardEntity>, NoParams> {
  final HomeRepository repository;

  GetPopularCardsUseCase(this.repository);

  @override
  Future<Either<Failure, List<PopularCardEntity>>> call(NoParams _) {
    return repository.getPopularCards(limit: 10);
  }
}
