import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/errors/failures.dart';
import '../entities/popular_card_entity.dart';
import '../repositories/home_repository.dart';
import 'get_banners_stream_usecase.dart';

/// UseCase لجلب تدفق البطاقات الشائعة من الـ HomeRepository.
/// يستخدم هذا الـ UseCase للحصول على تحديثات البطاقات الشائعة في الوقت الحقيقي.
class GetPopularCardsStreamUseCase implements StreamUseCase<List<PopularCardEntity>, PopularCardsParams> {
  final HomeRepository repository;

  GetPopularCardsStreamUseCase(this.repository);

  @override
  Stream<Either<Failure, List<PopularCardEntity>>> call(PopularCardsParams params) {
    return repository.getPopularCardsStream(limit: params.limit);
  }
}

/// معلمات لـ UseCase جلب البطاقات الشائعة
class PopularCardsParams extends Equatable {
  final int limit;

  const PopularCardsParams({this.limit = 10});

  @override
  List<Object?> get props => [limit];
}
