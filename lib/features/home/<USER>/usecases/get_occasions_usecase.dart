import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/occasion_entity.dart';
import '../repositories/home_repository.dart';

/// UseCase لجلب المناسبات من الـ HomeRepository.
class GetOccasionsUseCase implements UseCase<List<OccasionEntity>, NoParams> {
  final HomeRepository repository;

  GetOccasionsUseCase(this.repository);

  @override
  Future<Either<Failure, List<OccasionEntity>>> call(NoParams params) async {
    return await repository.getOccasions();
  }
}
