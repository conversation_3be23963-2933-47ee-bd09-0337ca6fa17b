// lib/features/home/<USER>/pages/unified_home_page.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/widgets/custom_snackbar.dart';
import '../../../../core/widgets/optimized/index.dart';
import '../../../../routes.dart';

import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/occasion_entity.dart';
import '../../domain/entities/popular_card_entity.dart';
import '../../domain/entities/greeting_card_entity.dart';
import '../blocs/home/<USER>';
import '../blocs/home/<USER>';
import '../blocs/home/<USER>';
import '../widgets/banner_slider.dart';
import '../widgets/card_search_delegate.dart';
import '../widgets/shimmer_popular_cards_list.dart';
import '../widgets/filter_chips_row.dart';
import '../widgets/home_drawer.dart';
import '../widgets/occasions_grid.dart';
import '../widgets/popular_cards_list.dart';
import '../widgets/recent_cards_list.dart';

/// صفحة الرئيسية الموحدة
/// تجمع بين أفضل ميزات الإصدارات السابقة مع تحسينات إضافية
class UnifiedHomePage extends StatefulWidget {
  /// إنشاء صفحة الرئيسية الموحدة
  const UnifiedHomePage({super.key});

  @override
  State<UnifiedHomePage> createState() => _UnifiedHomePageState();
}

class _UnifiedHomePageState extends State<UnifiedHomePage> {
  // مفاتيح للوصول إلى عناصر الواجهة
  final GlobalKey _settingsKey = GlobalKey();
  final GlobalKey _createCardKey = GlobalKey();
  final GlobalKey _aiButtonKey = GlobalKey();
  final GlobalKey _occasionsKey = GlobalKey();
  final GlobalKey _popularCardsKey = GlobalKey();
  final GlobalKey _recentCardsKey = GlobalKey();

  // التصفية المحددة حالياً
  String _selectedFilter = 'all';

  // قائمة البطاقات الأخيرة (محاكاة)
  List<GreetingCardEntity> _recentCards = [];

  // تم حذف قائمة الأحداث القادمة

  @override
  void initState() {
    super.initState();
    // تحميل بيانات الصفحة الرئيسية
    context.read<HomeBloc>().add(LoadHomeDataEvent());

    // محاكاة تحميل البطاقات الأخيرة
    _loadMockRecentCards();
  }

  /// تحميل بيانات البطاقات الأخيرة (محاكاة)
  void _loadMockRecentCards() {
    final now = DateTime.now();

    _recentCards = [
      GreetingCardEntity(
        id: 'recent1',
        title: 'بطاقة سنة حلوة',
        description: 'بطاقة تهنئة بمناسبة سنة حلوة',
        imageUrl: 'https://via.placeholder.com/150',
        occasion: 'سنة حلوة',
        popularity: 100,
        backgroundColor: Colors.white,
        elements: [],

        createdAt: now.subtract(
            const Duration(days: 2)), // إضافة تاريخ الإنشاء (قبل يومين)
      ),
      GreetingCardEntity(
        id: 'recent2',
        title: 'بطاقة مبروك التخرج',
        description: 'بطاقة تهنئة بمناسبة التخرج',
        imageUrl: 'https://via.placeholder.com/150',
        occasion: 'مبروك التخرج',
        popularity: 90,
        backgroundColor: Colors.white,
        elements: [],

        createdAt: now.subtract(
            const Duration(days: 5)), // إضافة تاريخ الإنشاء (قبل 5 أيام)
      ),
      GreetingCardEntity(
        id: 'recent3',
        title: 'بطاقة عيد الفطر',
        description: 'بطاقة تهنئة بمناسبة عيد الفطر',
        imageUrl: 'https://via.placeholder.com/150',
        occasion: 'عيد الفطر',
        popularity: 80,
        backgroundColor: Colors.white,
        elements: [],

        createdAt: now.subtract(
            const Duration(days: 1)), // إضافة تاريخ الإنشاء (قبل يوم واحد)
      ),
    ];
  }

  /// تم حذف دالة تحميل بيانات الأحداث القادمة

  /// تحديث المحتوى عند تغيير التصفية
  void _refreshContent() {
    setState(() {
      // في التطبيق الحقيقي، يجب استدعاء واجهة برمجة التطبيقات للحصول على البيانات المصفاة
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق
      appBar: AppBar(
        toolbarHeight: 65,
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        title: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Image.asset(
            'assets/logos/logo.png',
            height: 35,
            fit: BoxFit.contain,
          ),
        ),
        actions: [
          // زر البحث
          Container(
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                Icons.search,
                size: 22,
                color: Theme.of(context).primaryColor,
              ),
              tooltip: 'بحث',
              onPressed: () {
                showSearch(
                  context: context,
                  delegate: CardSearchDelegate(),
                );
              },
            ),
          ),
          // زر الإعدادات
          Container(
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              key: _settingsKey,
              icon: Icon(
                Icons.settings,
                size: 22,
                color: Theme.of(context).primaryColor,
              ),
              tooltip: 'الإعدادات',
              onPressed: () {
                Navigator.pushNamed(context, AppRoutes.settings);
              },
            ),
          ),
          // زر الإشعارات
          Container(
            margin: const EdgeInsets.only(right: 8, left: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.8), // خلفية ملونة لتبرز الأيقونة البيضاء
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(
                Icons.notifications,
                size: 22,
                color: Colors.white, // أبيض في جميع الأوضاع
              ),
              tooltip: 'الإشعارات',
              onPressed: () {
                Navigator.pushNamed(context, AppRoutes.notifications);
              },
            ),
          ),
        ],
      ),

      // الدرج الجانبي
      drawer: const HomeDrawer(),

      // محتوى الصفحة
      body: BlocConsumer<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is HomeError) {
            CustomSnackbar.show(
              context: context,
              message: state.message,
              type: NotificationType.error,
            );
          }
        },
        builder: (context, state) {
          if (state is HomeLoading) {
            // Mostrar contenido con efecto Shimmer durante la carga
            return _buildHomeContent(
              context,
              banners: const [],
              occasions: const [],
              popularCards: const [],
              isLoading: true,
            );
          }

          if (state is HomeLoaded) {
            return _buildHomeContent(
              context,
              banners: state.banners,
              occasions: state.occasions,
              popularCards: state.popularCards ?? [],
              isLoading: false,
            );
          }

          return Center(
            child: Text(
              'لا توجد بيانات',
              style: GoogleFonts.cairo(),
            ),
          );
        },
      ),

      // زر الإنشاء العائم
      floatingActionButton: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: FloatingActionButton.extended(
          key: _createCardKey,
          onPressed: () {
            Navigator.pushNamed(context, AppRoutes.createCard,
                arguments: {'card': null, 'isAiMode': false});
          },
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          icon: const Icon(Icons.add, size: 24),
          label: Text(
            'إنشاء بطاقة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء محتوى الصفحة الرئيسية
  Widget _buildHomeContent(
    BuildContext context, {
    required List<BannerEntity> banners,
    required List<OccasionEntity> occasions,
    required List<PopularCardEntity> popularCards,
    bool isLoading = false,
  }) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<HomeBloc>().add(LoadHomeDataEvent());
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // البانر العلوي
            if (banners.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: OptimizedWidget(
                  child: BannerSlider(
                    banners: banners,
                  ),
                ),
              ),

            // رقائق التصفية
            FilterChipsRow(
              selectedFilter: _selectedFilter,
              onFilterChanged: (filter) {
                setState(() {
                  _selectedFilter = filter;
                });
                _refreshContent();
              },
            ),

            // قسم الذكاء الاصطناعي
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      Theme.of(context).primaryColor.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: InkWell(
                  key: _aiButtonKey,
                  onTap: () {
                    Navigator.pushNamed(context, AppRoutes.createCard,
                        arguments: {'card': null, 'isAiMode': true});
                  },
                  borderRadius: BorderRadius.circular(16),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      children: [
                        // أيقونة الذكاء الاصطناعي
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.auto_awesome,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'إنشاء بطاقة بالذكاء الاصطناعي',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).textTheme.titleLarge?.color,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'أنشئ بطاقات مميزة باستخدام الذكاء الاصطناعي',
                                style: GoogleFonts.cairo(
                                  fontSize: 13,
                                  color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Theme.of(context).primaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // قسم المناسبات الخاصة بالمستخدم - تم تعطيله مؤقتًا
            // BlocProvider(
            //   create: (context) => sl<UserOccasionsBloc>(),
            //   child: const UpcomingUserOccasionsCard(),
            // ),

            // قسم المناسبات
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المناسبات',
                    key: _occasionsKey,
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleLarge?.color,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/occasions');
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).primaryColor,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    child: Text(
                      'عرض الكل',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // شبكة المناسبات
            if (occasions.isNotEmpty)
              OptimizedWidget(
                child: OccasionsGrid(occasions: occasions),
              )
            else
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'لا توجد مناسبات',
                    style: GoogleFonts.cairo(),
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // قسم البطاقات الأخيرة
            if (_recentCards.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'بطاقاتك الأخيرة',
                          key: _recentCardsKey,
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).textTheme.titleLarge?.color,
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.pushNamed(
                                context, AppRoutes.cardsHistory);
                          },
                          style: TextButton.styleFrom(
                            foregroundColor: Theme.of(context).primaryColor,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          child: Text(
                            'عرض الكل',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  RecentCardsList(cards: _recentCards),
                ],
              ),

            // قسم البطاقات الشائعة
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'البطاقات الشائعة',
                    key: _popularCardsKey,
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleLarge?.color,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // تمرير قيمة فارغة لـ occasionId لعرض جميع البطاقات
                      Navigator.pushNamed(
                        context,
                        AppRoutes.cardsList,
                        arguments: {
                          'occasionId': '',
                          'occasionName': 'جميع البطاقات'
                        },
                      );
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).primaryColor,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    child: Text(
                      'عرض الكل',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // قائمة البطاقات الشائعة
            if (isLoading)
              // Mostrar efecto Shimmer durante la carga
              const ShimmerPopularCardsList()
            else if (popularCards.isNotEmpty)
              OptimizedWidget(
                child: PopularCardsList(cards: popularCards),
              )
            else
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'لا توجد بطاقات شائعة',
                    style: GoogleFonts.cairo(),
                  ),
                ),
              ),

            const SizedBox(height: 80), // مساحة للزر العائم
          ],
        ),
      ),
    );
  }
}
