// lib/features/home/<USER>/pages/new_home_page.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/providers/language_provider.dart';
import '../../../../core/widgets/custom_snackbar.dart';
import '../../../../routes.dart';
import '../../../notifications/presentation/bloc/notifications_bloc.dart';
import '../../../notifications/presentation/bloc/notifications_event.dart';
import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/occasion_entity.dart';
import '../../domain/entities/popular_card_entity.dart';
import '../blocs/home/<USER>';
import '../blocs/home/<USER>';
import '../blocs/home/<USER>';
import '../widgets/banner_slider.dart';
import '../widgets/new_home_drawer.dart';
import '../widgets/popular_cards_list.dart';
import '../widgets/shimmer_popular_cards_list.dart';

/// صفحة الرئيسية الجديدة بتصميم محسن
class NewHomePage extends StatefulWidget {
  /// إنشاء صفحة الرئيسية الجديدة
  const NewHomePage({super.key});

  @override
  State<NewHomePage> createState() => _NewHomePageState();
}

class _NewHomePageState extends State<NewHomePage> {
  @override
  void initState() {
    super.initState();
    // تحميل بيانات الصفحة الرئيسية
    context.read<HomeBloc>().add(LoadHomeDataEvent());

    // Cargar notificaciones si el usuario está autenticado
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      context.read<NotificationsBloc>().add(LoadNotificationsEvent(user.uid));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70), // الطول الافتراضي
        child: Stack(
          children: [
              AppBar(
                backgroundColor: Theme.of(context).appBarTheme.backgroundColor ??
                                Theme.of(context).colorScheme.surface,
                elevation: 0,
                toolbarHeight: 70, // الطول الافتراضي
                title: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Image.asset(
                    'assets/logos/logo.png',
                    height: 45,
                    fit: BoxFit.contain,
                  ),
                ),
                centerTitle: true,
                leading: Builder(
                  builder: (BuildContext context) {
                    return IconButton(
                      icon: Icon(Icons.menu,
                        color: Theme.of(context).appBarTheme.iconTheme?.color ??
                               AppColors.primaryColor),
                      onPressed: () {
                        Scaffold.of(context).openDrawer();
                      },
                    );
                  },
                ),
                actions: [
                  // Botón de notificaciones con contador en tiempo real
                  StreamBuilder<User?>(
                    stream: FirebaseAuth.instance.authStateChanges(),
                    builder: (context, authSnapshot) {
                      final user = authSnapshot.data;
                      if (user == null) {
                        return IconButton(
                          icon: Icon(
                            Icons.notifications_outlined,
                            color: Colors.white, // أبيض في جميع الأوضاع
                            size: 28,
                          ),
                          onPressed: () {
                            Navigator.pushNamed(context, AppRoutes.notifications);
                          },
                        );
                      }

                      return StreamBuilder<QuerySnapshot>(
                        stream: FirebaseFirestore.instance
                            .collection('users')
                            .doc(user.uid)
                            .collection('notifications')
                            .where('isRead', isEqualTo: false)
                            .snapshots(),
                        builder: (context, notificationSnapshot) {
                          // حساب عدد الإشعارات غير المقروءة من الـ Stream
                          int unreadCount = 0;
                          if (notificationSnapshot.hasData) {
                            unreadCount = notificationSnapshot.data!.docs.length;
                          }

                          return Stack(
                            alignment: Alignment.center,
                            children: [
                              IconButton(
                                icon: Icon(
                                  Icons.notifications_outlined,
                                  color: Colors.white, // أبيض في جميع الأوضاع
                                  size: 28,
                                ),
                                iconSize: 28,
                                onPressed: () {
                                  // تحديث الإشعارات قبل الانتقال
                                  context
                                      .read<NotificationsBloc>()
                                      .add(LoadNotificationsEvent(user.uid));

                                  // الانتقال لصفحة الإشعارات
                                  Navigator.pushNamed(context, AppRoutes.notifications)
                                      .then((_) {
                                    // تحديث الإشعارات عند العودة
                                    if (mounted && context.mounted) {
                                      context
                                          .read<NotificationsBloc>()
                                          .add(LoadNotificationsEvent(user.uid));
                                    }
                                  });
                                },
                              ),
                              // عداد الإشعارات غير المقروءة مع تحديث فوري
                              if (unreadCount > 0)
                                Positioned(
                                  top: 6,
                                  right: 6,
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.elasticOut,
                                    padding: const EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFDA291C),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withAlpha(51),
                                          blurRadius: 2,
                                          spreadRadius: 0,
                                          offset: const Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                    constraints: const BoxConstraints(
                                      minWidth: 20,
                                      minHeight: 20,
                                    ),
                                    child: Center(
                                      child: Text(
                                        unreadCount > 99 ? '99+' : unreadCount.toString(),
                                        style: GoogleFonts.cairo(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          );
                        },
                      );
                    },
                  ),
                ],
              ),


            ],
          ),
        ),

      // الدرج الجانبي
      drawer: const NewHomeDrawer(),

      // محتوى الصفحة
      body: Stack(
        children: [
          // المحتوى الرئيسي
          BlocConsumer<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is HomeError) {
            CustomSnackbar.show(
              context: context,
              message: state.message,
              type: NotificationType.error,
            );
          }
        },
        builder: (context, state) {
          if (state is HomeLoading) {
            // Mostrar contenido con efecto Shimmer durante la carga
            return _buildHomeContent(
              context,
              banners: const [],
              occasions: const [],
              popularCards: const [],
              isLoading: true,
            );
          }

          if (state is HomeLoaded) {
            return _buildHomeContent(
              context,
              banners: state.banners,
              occasions: state.occasions,
              popularCards: state.popularCards ?? [],
              isLoading: false,
            );
          }

          return Center(
            child: Text(
              AppLocalizations.of(context).noData,
              style: GoogleFonts.cairo(),
            ),
          );
        },
      ),

          // زر السويتش خارج الشريط العلوي تماماً
          Positioned(
            top: 10, // أعلى الصفحة مباشرة
            right: 16, // على اليمين
            child: _buildLanguageSwitch(),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى الصفحة الرئيسية
  Widget _buildHomeContent(
    BuildContext context, {
    required List<BannerEntity> banners,
    required List<OccasionEntity> occasions,
    List<PopularCardEntity> popularCards = const [],
    bool isLoading = false,
  }) {
    // إضافة بانرات محلية إذا كانت القائمة فارغة
    final List<BannerEntity> displayBanners = banners.isNotEmpty
        ? banners
        : [
            BannerEntity(
              id: 'local-1',
              imageUrl: 'assets/banners/ai-banner.png',
              title: AppLocalizations.of(context).appropriateWords,
              link: '',
            ),
            BannerEntity(
              id: 'local-2',
              imageUrl: 'assets/banners/builder-card.png',
              title: AppLocalizations.of(context).expressBeautifulFeelings,
              link: '',
            ),
          ];

    // إضافة بطاقات شائعة محلية إذا كانت القائمة فارغة
    final List<PopularCardEntity> displayPopularCards = popularCards.isNotEmpty
        ? popularCards
        : [
            PopularCardEntity(
              id: 'mock-1',
              title:
                  '${AppLocalizations.of(context).birthday} ${AppLocalizations.of(context).card}',
              thumbnailUrl: 'https://via.placeholder.com/150',
              popularity: 120, // هذا الحقل يستخدم الآن كعداد للزيارات
            ),
            PopularCardEntity(
              id: 'mock-2',
              title:
                  '${AppLocalizations.of(context).congratulations} ${AppLocalizations.of(context).card}',
              thumbnailUrl: 'https://via.placeholder.com/150',
              popularity: 98, // هذا الحقل يستخدم الآن كعداد للزيارات
            ),
            PopularCardEntity(
              id: 'mock-3',
              title:
                  '${AppLocalizations.of(context).wedding} ${AppLocalizations.of(context).card}',
              thumbnailUrl: 'https://via.placeholder.com/150',
              popularity: 85, // هذا الحقل يستخدم الآن كعداد للزيارات
            ),
          ];

    return RefreshIndicator(
      onRefresh: () async {
        context.read<HomeBloc>().add(LoadHomeDataEvent());
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مسافة لتجنب تداخل السويتش مع البانر
            const SizedBox(height: 60),

            // البانر العلوي
            Padding(
              padding: const EdgeInsets.only(top: 8, bottom: 8),
              child: BannerSlider(
                banners: banners.isNotEmpty ? banners : displayBanners,
              ),
            ),

            // قسم "بطاقات لك"
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocalizations.of(context).cardsForYou,
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pushNamed(context, AppRoutes.cardsList);
                    },
                    child: Text(
                      AppLocalizations.of(context).viewAll,
                      style: GoogleFonts.cairo(),
                    ),
                  ),
                ],
              ),
            ),

            // قائمة البطاقات الشائعة
            isLoading
                ? const ShimmerPopularCardsList() // Mostrar efecto Shimmer durante la carga
                : PopularCardsList(cards: displayPopularCards),

            // مسافة إضافية قبل بانر الذكاء الاصطناعي
            const SizedBox(height: 24),

            // قسم "إنشاء بطاقة بالذكاء الاصطناعي"
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.createCard,
                      arguments: {'card': null, 'isAiMode': true});
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.asset(
                      'assets/banners/ai-banner.png',
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),

            // عنوان قسم المناسبات
            Padding(
              padding: const EdgeInsets.only(
                  left: 16, right: 16, top: 24, bottom: 8),
              child: Text(
                AppLocalizations.of(context).occasions,
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ),

            // شبكة المناسبات
            _buildOccasionsGrid(occasions),

            // عنوان قسم "إنشاء بطاقة"
            Padding(
              padding: const EdgeInsets.only(
                  left: 16, right: 16, top: 24, bottom: 8),
              child: Text(
                AppLocalizations.of(context).createCard,
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ),

            // قسم "إنشاء بطاقة"
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.createCard,
                      arguments: {'card': null, 'isAiMode': false});
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.asset(
                      'assets/banners/builder-card.png',
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// بناء شبكة المناسبات
  Widget _buildOccasionsGrid(List<OccasionEntity> occasions) {
    // قائمة المناسبات المخصصة حسب الترتيب في الصورة مع معرفاتها الصحيحة
    final customOccasions = [
      // الصف الأول: مبروك التخرج - سنة حلوة - ذكرى غالية
      _buildCustomOccasion(AppLocalizations.of(context).graduation,
          'assets/stickers/mabrouk.png', 'sticker_category_graduation'),
      _buildCustomOccasion(AppLocalizations.of(context).birthday,
          'assets/stickers/sana.png', 'sticker_category_birthday'),
      _buildCustomOccasion(AppLocalizations.of(context).anniversary,
          'assets/stickers/dikra.png', 'sticker_category_anniversary'),

      // الصف الثاني: غلا العمر - نور البيت - غالية وجت
      _buildCustomOccasion(AppLocalizations.of(context).valentinesDay,
          'assets/stickers/ghala.png', 'sticker_category_love'),
      _buildCustomOccasion(AppLocalizations.of(context).babyShower,
          'assets/stickers/noor.png', 'sticker_category_new_baby'),
      _buildCustomOccasion(AppLocalizations.of(context).wedding,
          'assets/stickers/ghalia.png', 'sticker_category_wedding'),

      // الصف الثالث: أبوي الغالي - أمي الحبيبة - كفو دايم
      _buildCustomOccasion(AppLocalizations.of(context).fathersDay,
          'assets/stickers/dad.png', 'sticker_category_father'),
      _buildCustomOccasion(AppLocalizations.of(context).mothersDay,
          'assets/icons/jaime-maman.png', 'sticker_category_mother'),
      _buildCustomOccasion(AppLocalizations.of(context).congratulations,
          'assets/stickers/kafou.png', 'sticker_category_congratulations'),

      // الصف الرابع: بداية جديدة - منزل مبارك - أجر وشفاء
      _buildCustomOccasion(AppLocalizations.of(context).newYear,
          'assets/stickers/bidaya.png', 'sticker_category_new_beginning'),
      _buildCustomOccasion(AppLocalizations.of(context).newHome,
          'assets/stickers/manzil.png', 'sticker_category_new_home'),
      _buildCustomOccasion(AppLocalizations.of(context).getWell,
          'assets/stickers/ajr.png', 'sticker_category_get_well'),
    ];

    // تقسيم المناسبات إلى صفوف من 3
    final rows = <Widget>[];
    for (var i = 0; i < customOccasions.length; i += 3) {
      final rowItems = <Widget>[];
      for (var j = i; j < i + 3 && j < customOccasions.length; j++) {
        rowItems.add(
          Expanded(
            child: customOccasions[j],
          ),
        );
      }
      rows.add(
        Padding(
          padding: const EdgeInsets.symmetric(
              vertical: 16), // Aumentar el espaciado vertical
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: rowItems,
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: rows,
      ),
    );
  }

  /// بناء عنصر مناسبة مخصص
  Widget _buildCustomOccasion(
      String name, String iconAsset, String occasionId) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          AppRoutes.cardsList,
          arguments: {'occasionId': occasionId, 'occasionName': name},
        );
      },
      child: Column(
        children: [
          // Imagen sin fondo púrpura
          Image.asset(
            iconAsset,
            width: 60,
            height: 60,
            errorBuilder: (context, error, stackTrace) {
              return Icon(
                Icons.favorite,
                size: 60,
                color: Colors.grey[600],
              );
            },
          ),
          const SizedBox(height: 12), // Aumentar el espaciado
          Text(
            name,
            style: GoogleFonts.cairo(
              fontSize: 14, // Aumentar el tamaño de la fuente
              fontWeight: FontWeight.w500, // Añadir un poco de peso a la fuente
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// بناء زر تغيير اللغة - Toggle Switch منصهر مع الصفحة
  Widget _buildLanguageSwitch() {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        final isArabic = languageProvider.locale.languageCode == 'ar';

        return Directionality(
          textDirection: TextDirection.ltr, // فرض الاتجاه من اليسار لليمين دائماً
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.95), // شفافية خفيفة
              borderRadius: BorderRadius.circular(25),
              // إزالة الظل والحدود لمظهر منصهر
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // العربية - دائماً على اليسار
                AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 250),
                  style: TextStyle(
                    color: isArabic ? AppColors.primaryColor : Colors.grey[500],
                    fontSize: 13,
                    fontWeight: isArabic ? FontWeight.w600 : FontWeight.w400,
                    letterSpacing: 0.5,
                  ),
                  child: const Text('ع'),
                ),
                const SizedBox(width: 10),

                // Toggle Switch مع تصميم نظيف ومنصهر
                GestureDetector(
                  onTap: languageProvider.isChangingLanguage ? null : () async {
                    try {
                      debugPrint('Toggle switch tapped, current language: ${languageProvider.locale.languageCode}');
                      if (isArabic) {
                        debugPrint('Switching from Arabic to English');
                        await languageProvider.setLocale(const Locale('en'));
                      } else {
                        debugPrint('Switching from English to Arabic');
                        await languageProvider.setLocale(const Locale('ar'));
                      }
                    } catch (e) {
                      debugPrint('Error in toggle switch: $e');
                    }
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.easeInOut,
                    width: 48,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: isArabic
                          ? AppColors.primaryColor.withValues(alpha: 0.15)
                          : Colors.grey.withValues(alpha: 0.15),
                      // إزالة الحدود للمظهر النظيف
                    ),
                    child: Stack(
                      children: [
                        AnimatedPositioned(
                          duration: const Duration(milliseconds: 250),
                          curve: Curves.easeInOut,
                          // العربية: الدائرة على اليسار، الإنجليزية: الدائرة على اليمين
                          left: isArabic ? 2 : 24,
                          top: 2,
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isArabic ? AppColors.primaryColor : Colors.grey[600],
                              // إزالة الظل للمظهر المنصهر
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(width: 10),
                // الإنجليزية - دائماً على اليمين
                AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 250),
                  style: TextStyle(
                    color: !isArabic ? AppColors.primaryColor : Colors.grey[500],
                    fontSize: 13,
                    fontWeight: !isArabic ? FontWeight.w600 : FontWeight.w400,
                    letterSpacing: 0.5,
                  ),
                  child: const Text('EN'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
