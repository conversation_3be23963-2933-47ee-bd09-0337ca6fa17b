// lib/features/home/<USER>/models/popular_card_model.dart

import '../../domain/entities/popular_card_entity.dart';

/// نموذج بيانات للبطاقة الشائعة، يستخدم للتحويل من/إلى JSON في Firestore
class PopularCardModel extends PopularCardEntity {
  /// إنشاء نموذج بيانات للبطاقة الشائعة
  const PopularCardModel({
    required super.id,
    required super.title,
    super.description = '',
    required super.thumbnailUrl,
    required super.popularity,
    super.occasionId,
  });

  /// إنشاء نموذج من JSON في Firestore
  factory PopularCardModel.fromJson(String id, Map<String, dynamic> json) {
    return PopularCardModel(
      id: id,
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      thumbnailUrl: json['imageUrl'] as String? ?? '',
      popularity: (json['popularity'] as num?)?.toInt() ?? 0,
      occasionId: json['occasionId'] as String?,
    );
  }

  /// تحويل النموذج إلى JSON لـ Firestore
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'imageUrl':
          thumbnailUrl, // نستخدم thumbnailUrl ولكن نحفظه كـ imageUrl للتوافق مع الكود القديم
      'popularity': popularity,
      'occasionId': occasionId
          ?.toString(), // تحويل صريح إلى String لضمان اتساق نوع البيانات
    };
  }
}
