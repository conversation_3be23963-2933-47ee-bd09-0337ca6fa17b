import '../../domain/entities/banner_entity.dart';

/// يمثّل البنر على شكل Model كما نخزّنه/نجلبه من Firestore.
/// يمتد (extends) BannerEntity في الدومين.
class BannerModel extends BannerEntity {
  const BannerModel({
    required super.id,
    required super.imageUrl,
    required super.title,
    required super.link,
    super.occasionId,
    super.occasionName,
  });

  factory BannerModel.fromMap(String docId, Map<String, dynamic> data) {
    return BannerModel(
      id: docId,
      imageUrl: data['imageUrl'] ?? '',
      title: data['title'] ?? '',
      link: data['link'] ?? '',
      occasionId: data['occasionId'],
      occasionName: data['occasionName'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'imageUrl': imageUrl,
      'title': title,
      'link': link,
      'occasionId': occasionId,
      'occasionName': occasionName,
    };
  }
}
