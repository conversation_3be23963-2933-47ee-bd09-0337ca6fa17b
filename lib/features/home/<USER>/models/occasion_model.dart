import '../../domain/entities/occasion_entity.dart';

/// يمثّل المناسبة على شكل Model كما قد نخزّنه/نجلبه من مصدر ما (مثل Firestore).
/// يمتد (extends) OccasionEntity في الدومين.
class OccasionModel extends OccasionEntity {
  const OccasionModel({
    required super.id,
    required super.name,
    required super.iconAsset,
  });

  /// إنشاء الموديل من خريطة (مثلاً من وثيقة Firestore)
  factory OccasionModel.fromMap(String docId, Map<String, dynamic> data) {
    return OccasionModel(
      id: docId,
      name: data['name'] ?? '',
      iconAsset: data['iconAsset'] ?? '',
    );
  }

  /// تحويل الموديل إلى خريطة (مثلاً لتخزينه في Firestore)
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'iconAsset': iconAsset,
    };
  }
}
