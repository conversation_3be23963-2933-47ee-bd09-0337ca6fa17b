// lib/features/home/<USER>/datasources/home_remote_data_source.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

import '../../../../core/errors/exceptions.dart';
import '../models/banner_model.dart';
import '../models/occasion_model.dart';
import '../models/popular_card_model.dart';

/// كلاس مؤقت لبطاقة التهنئة
class GreetingCardModel {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final String occasion;
  final int popularity;
  final Color backgroundColor;
  final List<dynamic> elements;
  final int visits;
  final DateTime createdAt;

  GreetingCardModel({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.occasion,
    required this.popularity,
    required this.backgroundColor,
    required this.elements,
    this.visits = 0,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  factory GreetingCardModel.fromMap(String id, Map<String, dynamic> map) {
    // استخراج تاريخ الإنشاء إذا كان موجودًا
    DateTime? createdAt;
    if (map['createdAt'] != null) {
      if (map['createdAt'] is Timestamp) {
        createdAt = (map['createdAt'] as Timestamp).toDate();
      } else if (map['createdAt'] is int) {
        createdAt =
            DateTime.fromMillisecondsSinceEpoch(map['createdAt'] as int);
      }
    }

    return GreetingCardModel(
      id: id,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      imageUrl: map['imageUrl'] ?? '',
      occasion: map['occasion'] ?? '',
      popularity: map['popularity'] ?? 0,
      backgroundColor: map['backgroundColor'] is int
          ? Color(map['backgroundColor'] as int)
          : Colors.white,
      elements: map['elements'] ?? [],
      visits: map['visits'] is num ? (map['visits'] as num).toInt() : 0,
      createdAt: createdAt ?? DateTime.now(),
    );
  }
}

abstract class HomeRemoteDataSource {
  /// جلب البانرات (استعلام لمرة واحدة)
  Future<List<BannerModel>> getBanners();

  /// الاستماع للبانرات في الوقت الحقيقي
  Stream<List<BannerModel>> getBannersStream();

  /// جلب المناسبات (استعلام لمرة واحدة)
  Future<List<OccasionModel>> getOccasions();

  /// الاستماع للمناسبات في الوقت الحقيقي
  Stream<List<OccasionModel>> getOccasionsStream();

  /// جلب البطاقات الشائعة (استعلام لمرة واحدة)
  Future<List<PopularCardModel>> getPopularCards({int limit});

  /// الاستماع للبطاقات الشائعة في الوقت الحقيقي
  Stream<List<PopularCardModel>> getPopularCardsStream({int limit});

  /// جلب بطاقة كاملة (مع عناصرها) بواسطة معرفها
  Future<GreetingCardModel> getCardById(String id);
}

class HomeRemoteDataSourceImpl implements HomeRemoteDataSource {
  final FirebaseFirestore firestore;

  HomeRemoteDataSourceImpl({required this.firestore});

  @override
  Future<List<BannerModel>> getBanners() async {
    try {
      final snap = await firestore.collection('banners').get();
      return snap.docs.map((d) => BannerModel.fromMap(d.id, d.data())).toList();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Stream<List<BannerModel>> getBannersStream() {
    return firestore.collection('banners').snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => BannerModel.fromMap(doc.id, doc.data()))
          .toList();
    });
  }

  @override
  Future<List<OccasionModel>> getOccasions() async {
    try {
      final snap = await firestore.collection('occasions').get();
      return snap.docs
          .map((d) => OccasionModel.fromMap(d.id, d.data()))
          .toList();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Stream<List<OccasionModel>> getOccasionsStream() {
    return firestore.collection('occasions').snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => OccasionModel.fromMap(doc.id, doc.data()))
          .toList();
    });
  }

  @override
  Future<List<PopularCardModel>> getPopularCards({int limit = 10}) async {
    try {
      final snap = await firestore
          .collection('cards')
          .orderBy('popularity', descending: true)
          .limit(limit)
          .get();
      return snap.docs
          .map((d) => PopularCardModel.fromJson(d.id, d.data()))
          .toList();
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Stream<List<PopularCardModel>> getPopularCardsStream({int limit = 10}) {
    return firestore
        .collection('cards')
        .orderBy('popularity', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => PopularCardModel.fromJson(doc.id, doc.data()))
          .toList();
    });
  }

  @override
  Future<GreetingCardModel> getCardById(String id) async {
    try {
      // محاولة جلب البطاقة من مجموعة cards أولاً
      final cardDoc = await firestore.collection('cards').doc(id).get();

      // إذا وجدنا البطاقة في مجموعة cards
      if (cardDoc.exists && cardDoc.data() != null) {
        debugPrint('Found card in cards collection with id: $id');
        debugPrint('Card data: ${cardDoc.data()}');

        // التحقق مما إذا كانت العناصر مخزنة مباشرة في البطاقة
        List<Map<String, dynamic>> elements = [];

        // التحقق مما إذا كانت العناصر مخزنة مباشرة في البطاقة
        if (cardDoc.data()!.containsKey('elements')) {
          debugPrint('Elements found directly in card document');
          final elementsList = cardDoc.data()!['elements'] as List<dynamic>;
          elements =
              elementsList.map((e) => e as Map<String, dynamic>).toList();
          debugPrint(
              'Found ${elements.length} elements directly in card document');
        } else {
          // جلب عناصر البطاقة من مجموعة فرعية
          final elementsSnapshot = await firestore
              .collection('cards')
              .doc(id)
              .collection('elements')
              .get();

          debugPrint(
              'Found ${elementsSnapshot.docs.length} elements in subcollection for card $id');

          // إنشاء قائمة بالعناصر وتحويلها إلى كائنات CardElementModel
          elements = elementsSnapshot.docs.map((e) {
            final data = e.data();
            // إضافة معرف العنصر من معرف الوثيقة إذا لم يكن موجوداً
            if (!data.containsKey('id')) {
              data['id'] = e.id;
            }

            // طباعة بيانات العنصر للتأكد من أنها صحيحة
            debugPrint('Element data from subcollection: $data');

            return data;
          }).toList();
        }

        // محاولة الحصول على عدد الزيارات من مجموعة card_stats
        int visits = 0;
        if (cardDoc.data()!.containsKey('visits') &&
            cardDoc.data()!['visits'] is num) {
          visits = (cardDoc.data()!['visits'] as num).toInt();
        } else {
          // إذا لم يكن موجودًا في وثيقة البطاقة، نحاول من مجموعة الإحصائيات
          try {
            final statsDoc =
                await firestore.collection('card_stats').doc(id).get();
            if (statsDoc.exists &&
                statsDoc.data() != null &&
                statsDoc.data()!.containsKey('visits')) {
              final statsVisits = statsDoc.data()!['visits'];
              if (statsVisits is num) {
                visits = statsVisits.toInt();
                debugPrint('Found visits count in card_stats: $visits');
              }
            }
          } catch (e) {
            debugPrint('Error getting visits from card_stats: $e');
          }
        }

        // دمج البيانات الأساسية مع العناصر وعدد الزيارات
        final Map<String, dynamic> cardData = {
          ...cardDoc.data()!,
          'elements': elements,
          'backgroundColor': cardDoc.data()!['backgroundColor'] ??
              0xFFFFFFFF, // لون أبيض افتراضي
          'visits': visits, // إضافة عدد الزيارات
        };

        return GreetingCardModel.fromMap(cardDoc.id, cardData);
      }

      // إذا لم نجد البطاقة في مجموعة cards، نحاول البحث في مجموعة greeting_cards
      final greetingDoc =
          await firestore.collection('greeting_cards').doc(id).get();
      if (!greetingDoc.exists || greetingDoc.data() == null) {
        throw ServerException(
            message: 'Card with id $id not found in any collection');
      }

      debugPrint('Found card in greeting_cards collection with id: $id');
      debugPrint('Greeting card data: ${greetingDoc.data()}');

      // التحقق مما إذا كانت العناصر مخزنة مباشرة في البطاقة
      List<Map<String, dynamic>> elements = [];

      // التحقق مما إذا كانت العناصر مخزنة مباشرة في البطاقة
      if (greetingDoc.data()!.containsKey('elements')) {
        debugPrint('Elements found directly in greeting_card document');
        final elementsList = greetingDoc.data()!['elements'] as List<dynamic>;
        elements = elementsList.map((e) => e as Map<String, dynamic>).toList();
        debugPrint(
            'Found ${elements.length} elements directly in greeting_card document');
      } else {
        // جلب عناصر البطاقة من مجموعة فرعية
        final elementsSnapshot = await firestore
            .collection('greeting_cards')
            .doc(id)
            .collection('elements')
            .get();

        debugPrint(
            'Found ${elementsSnapshot.docs.length} elements in subcollection for greeting_card $id');

        // إنشاء قائمة بالعناصر وتحويلها إلى كائنات CardElementModel
        elements = elementsSnapshot.docs.map((e) {
          final data = e.data();
          // إضافة معرف العنصر من معرف الوثيقة إذا لم يكن موجوداً
          if (!data.containsKey('id')) {
            data['id'] = e.id;
          }

          // طباعة بيانات العنصر للتأكد من أنها صحيحة
          debugPrint('Element data from greeting_cards subcollection: $data');

          return data;
        }).toList();
      }

      // محاولة الحصول على عدد الزيارات من مجموعة card_stats
      int visits = 0;
      if (greetingDoc.data()!.containsKey('visits') &&
          greetingDoc.data()!['visits'] is num) {
        visits = (greetingDoc.data()!['visits'] as num).toInt();
      } else {
        // إذا لم يكن موجودًا في وثيقة البطاقة، نحاول من مجموعة الإحصائيات
        try {
          final statsDoc =
              await firestore.collection('card_stats').doc(id).get();
          if (statsDoc.exists &&
              statsDoc.data() != null &&
              statsDoc.data()!.containsKey('visits')) {
            final statsVisits = statsDoc.data()!['visits'];
            if (statsVisits is num) {
              visits = statsVisits.toInt();
              debugPrint(
                  'Found visits count in card_stats for greeting_card: $visits');
            }
          }
        } catch (e) {
          debugPrint(
              'Error getting visits from card_stats for greeting_card: $e');
        }
      }

      // دمج البيانات الأساسية مع العناصر وعدد الزيارات
      final Map<String, dynamic> cardData = {
        ...greetingDoc.data()!,
        'elements': elements,
        'backgroundColor': greetingDoc.data()!['backgroundColor'] ??
            0xFFFFFFFF, // لون أبيض افتراضي
        'visits': visits, // إضافة عدد الزيارات
      };

      return GreetingCardModel.fromMap(greetingDoc.id, cardData);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
