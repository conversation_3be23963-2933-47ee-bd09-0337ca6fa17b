/// Represents a banner displayed on the home screen.
///
/// A banner contains an image, title, and optional link for promotional content
/// or special announcements in the application.
class BannerEntity {
  /// Unique identifier for the banner (typically from Firestore or other source)
  final String id;

  /// URL of the banner image
  final String imageUrl;

  /// Title or descriptive text for the banner
  final String title;

  /// URL that the user can be directed to when tapping the banner
  final String link;

  /// ID of the occasion associated with this banner (optional)
  final String? occasionId;

  /// Name of the occasion associated with this banner (optional)
  final String? occasionName;

  /// Creates a new banner entity with the specified properties.
  const BannerEntity({
    required this.id,
    required this.imageUrl,
    required this.title,
    required this.link,
    this.occasionId,
    this.occasionName,
  });
}
