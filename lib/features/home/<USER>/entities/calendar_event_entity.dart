// lib/features/home/<USER>/entities/calendar_event_entity.dart

import 'package:equatable/equatable.dart';

/// كيان حدث التقويم
class CalendarEventEntity extends Equatable {
  /// معرف الحدث
  final String id;

  /// عنوان الحدث
  final String title;

  /// تاريخ الحدث
  final DateTime date;

  /// وصف الحدث
  final String? description;

  /// نوع الحدث
  final String type;

  /// معرف المناسبة المرتبطة
  final String? occasionId;

  /// إنشاء كيان حدث التقويم
  const CalendarEventEntity({
    required this.id,
    required this.title,
    required this.date,
    this.description,
    required this.type,
    this.occasionId,
  });

  @override
  List<Object?> get props => [id, title, date, description, type, occasionId];
}
