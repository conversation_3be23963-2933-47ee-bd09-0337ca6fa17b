/// Represents an occasion or event category for greeting cards.
///
/// Occasions are categories like holidays, graduations, birthdays, etc.,
/// that users can select when creating or browsing greeting cards.
class OccasionEntity {
  /// Unique identifier for the occasion (e.g., "eid_al_fitr", "graduation")
  final String id;

  /// Display name of the occasion
  final String name;

  /// Path to the icon asset representing this occasion
  /// This is typically a relative path in the assets directory
  final String iconAsset;

  /// Creates a new occasion entity with the specified properties.
  const OccasionEntity({
    required this.id,
    required this.name,
    required this.iconAsset,
  });
}
