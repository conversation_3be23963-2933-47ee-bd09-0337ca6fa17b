// lib/features/home/<USER>/entities/popular_card_entity.dart

import 'package:equatable/equatable.dart';

/// كيان البطاقة الشائعة
class PopularCardEntity extends Equatable {
  /// معرف البطاقة
  final String id;

  /// عنوان البطاقة
  final String title;

  /// وصف البطاقة
  final String description;

  /// رابط صورة مصغرة للبطاقة
  final String thumbnailUrl;

  /// شعبية البطاقة (عدد المشاهدات أو الإعجابات)
  final int popularity;

  /// معرف المناسبة التي تنتمي إليها البطاقة
  final String? occasionId;

  /// رابط صورة البطاقة (للتوافق مع الكود القديم)
  String get imageUrl => thumbnailUrl;

  /// إنشاء كيان البطاقة الشائعة
  const PopularCardEntity({
    required this.id,
    required this.title,
    this.description = '',
    required this.thumbnailUrl,
    required this.popularity,
    this.occasionId,
  });

  @override
  List<Object?> get props =>
      [id, title, description, thumbnailUrl, popularity, occasionId];
}
