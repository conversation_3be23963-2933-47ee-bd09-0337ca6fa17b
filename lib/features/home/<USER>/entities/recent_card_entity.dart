// lib/features/home/<USER>/entities/recent_card_entity.dart

import 'package:equatable/equatable.dart';

/// كيان البطاقة الحديثة
class RecentCardEntity extends Equatable {
  /// معرف البطاقة
  final String id;

  /// عنوان البطاقة
  final String title;

  /// وصف البطاقة
  final String description;

  /// رابط صورة مصغرة للبطاقة
  final String thumbnailUrl;

  /// عدد زيارات البطاقة
  final int visits;

  /// تاريخ إنشاء البطاقة
  final DateTime createdAt;

  /// معرف المناسبة التي تنتمي إليها البطاقة
  final String? occasionId;

  /// رابط صورة البطاقة (للتوافق مع الكود القديم)
  String get imageUrl => thumbnailUrl;

  /// إنشاء كيان البطاقة الحديثة
  const RecentCardEntity({
    required this.id,
    required this.title,
    this.description = '',
    required this.thumbnailUrl,
    required this.visits,
    required this.createdAt,
    this.occasionId,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        thumbnailUrl,
        visits,
        createdAt,
        occasionId,
      ];
}
