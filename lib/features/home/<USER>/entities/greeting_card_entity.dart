// lib/features/home/<USER>/entities/greeting_card_entity.dart

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// كيان بطاقة التهنئة
class GreetingCardEntity extends Equatable {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final String occasion;
  final int popularity;
  final Color backgroundColor;
  final List<dynamic> elements;

  /// عدد زيارات البطاقة
  final int visits;

  /// تاريخ إنشاء البطاقة
  final DateTime createdAt;

  GreetingCardEntity({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.occasion,
    required this.popularity,
    required this.backgroundColor,
    required this.elements,
    this.visits = 0,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        imageUrl,
        occasion,
        popularity,
        backgroundColor,
        elements,
        visits,
        createdAt,
      ];
}
