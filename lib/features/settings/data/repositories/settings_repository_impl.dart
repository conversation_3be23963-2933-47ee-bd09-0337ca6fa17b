import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/entities/app_settings.dart';
import '../../domain/repositories/settings_repository.dart';

/// Implementation of the [SettingsRepository] interface.
///
/// This class uses SharedPreferences to persist application settings.
class SettingsRepositoryImpl implements SettingsRepository {
  /// Keys used for storing settings in SharedPreferences
  static const String _darkModeKey = 'dark_mode_enabled';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _cardSizeKey = 'card_size';
  static const String _languageCodeKey = 'language_code';

  /// Loads the application settings.
  @override
  Future<AppSettings> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      return AppSettings(
        isDarkModeEnabled: prefs.getBool(_darkModeKey) ?? false,
        areNotificationsEnabled: prefs.getBool(_notificationsKey) ?? true,
        defaultCardSize: prefs.getString(_cardSizeKey) ?? 'متوسط',
        languageCode: prefs.getString(_languageCodeKey) ?? 'ar',
      );
    } catch (e) {
      // Return default settings if there's an error
      return const AppSettings();
    }
  }

  /// Saves the application settings.
  @override
  Future<bool> saveSettings(AppSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_darkModeKey, settings.isDarkModeEnabled);
      await prefs.setBool(_notificationsKey, settings.areNotificationsEnabled);
      await prefs.setString(_cardSizeKey, settings.defaultCardSize);
      await prefs.setString(_languageCodeKey, settings.languageCode);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Gets the current theme mode.
  @override
  Future<bool> isDarkModeEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_darkModeKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Sets the theme mode.
  @override
  Future<bool> setDarkMode(bool isDarkMode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_darkModeKey, isDarkMode);
    } catch (e) {
      return false;
    }
  }

  /// Gets the current notification setting.
  @override
  Future<bool> areNotificationsEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_notificationsKey) ?? true;
    } catch (e) {
      return true;
    }
  }

  /// Sets the notification setting.
  @override
  Future<bool> setNotifications(bool areEnabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_notificationsKey, areEnabled);
    } catch (e) {
      return false;
    }
  }

  /// Gets the default card size.
  @override
  Future<String> getDefaultCardSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_cardSizeKey) ?? 'متوسط';
    } catch (e) {
      return 'متوسط';
    }
  }

  /// Sets the default card size.
  @override
  Future<bool> setDefaultCardSize(String size) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_cardSizeKey, size);
    } catch (e) {
      return false;
    }
  }

  /// Gets the current language code.
  @override
  Future<String> getLanguageCode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_languageCodeKey) ?? 'ar';
    } catch (e) {
      return 'ar';
    }
  }

  /// Sets the language code.
  @override
  Future<bool> setLanguageCode(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_languageCodeKey, languageCode);
    } catch (e) {
      return false;
    }
  }
}
