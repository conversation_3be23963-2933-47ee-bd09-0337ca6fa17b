import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_sizes.dart';
import '../../../../core/providers/language_provider.dart';
import '../../../../core/widgets/animated_toast.dart';
import '../../../../core/widgets/success_overlay.dart';
import '../../../../routes.dart';
import '../../../auth/presentation/blocs/auth/auth_bloc.dart';
import '../../../auth/presentation/blocs/auth/auth_event.dart';
import '../../../auth/presentation/blocs/auth/auth_state.dart';
import '../../domain/entities/app_settings.dart';
import '../blocs/settings/settings_bloc.dart';
import '../blocs/settings/settings_event.dart';
import '../blocs/settings/settings_state.dart';
import '../widgets/settings_section.dart';
import 'terms_and_conditions_page.dart';

/// Enhanced settings page with improved UI and functionality.
///
/// This page allows users to configure various application settings,
/// manage their account, and access information about the app.
class EnhancedSettingsPage extends StatefulWidget {
  /// Creates a new enhanced settings page.
  const EnhancedSettingsPage({super.key});

  @override
  State<EnhancedSettingsPage> createState() => _EnhancedSettingsPageState();
}

class _EnhancedSettingsPageState extends State<EnhancedSettingsPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _appVersion = '';
  // تم حذف _buildNumber لأنه لم يعد مستخدماً بعد إزالة عرض إصدار التطبيق

  @override
  void initState() {
    super.initState();
    _loadAppInfo();

    // Setup animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Loads application version.
  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
        // لم نعد نحتاج buildNumber بعد إزالة عرض إصدار التطبيق
      });
    } catch (e) {
      debugPrint('Error loading app info: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).settings,
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryColor,
        centerTitle: true,
        elevation: 0,
      ),
      body: BlocConsumer<SettingsBloc, SettingsState>(
        listener: (context, state) {
          if (state is SettingsUpdated) {
            // عرض إشعار نجاح متحرك
            AnimatedToast.show(
              context,
              message: state.message,
              type: ToastType.success,
              position: ToastPosition.top,
            );
          } else if (state is SettingsError) {
            // عرض إشعار خطأ متحرك
            AnimatedToast.show(
              context,
              message: state.message,
              type: ToastType.error,
              position: ToastPosition.top,
            );
          }
        },
        builder: (context, state) {
          if (state is SettingsLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // Get settings from state or use defaults
          final settings =
              state is SettingsLoaded ? state.settings : const AppSettings();

          return FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSizes.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Appearance Section
                  SettingsSection(
                    title: AppLocalizations.of(context).appearance,
                    children: [
                      _buildThemeModeTile(settings),
                      const Divider(),
                      _buildLanguageTile(settings),
                    ],
                  ),

                  const SizedBox(height: AppSizes.paddingMedium),

                  // Notifications Section
                  SettingsSection(
                    title: AppLocalizations.of(context).notifications,
                    children: [
                      _buildNotificationsTile(settings),
                    ],
                  ),

                  const SizedBox(height: AppSizes.paddingMedium),

                  // Account Section
                  SettingsSection(
                    title: AppLocalizations.of(context).account,
                    children: [
                      ListTile(
                        leading: const Icon(Icons.person_outline),
                        title: Text(
                          AppLocalizations.of(context).profile,
                          style: GoogleFonts.cairo(),
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: () {
                          // Navigate to profile page
                          final authState = context.read<AuthBloc>().state;
                          if (authState is AuthLoggedIn) {
                            Navigator.pushNamed(
                              context,
                              AppRoutes.profile,
                              arguments: authState.user.uid,
                            );
                          } else {
                            // Show error message if not authenticated
                            AnimatedToast.show(
                              context,
                              message: "يرجى تسجيل الدخول أولاً",
                              type: ToastType.error,
                              position: ToastPosition.top,
                            );
                          }
                        },
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.logout, color: Colors.red),
                        title: Text(
                          AppLocalizations.of(context).logout,
                          style: GoogleFonts.cairo(color: Colors.red),
                        ),
                        onTap: _showLogoutConfirmationDialog,
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSizes.paddingMedium),

                  // About Section
                  SettingsSection(
                    title: AppLocalizations.of(context).aboutApp,
                    children: [
                      ListTile(
                        leading: const Icon(Icons.info_outline),
                        title: Text(
                          AppLocalizations.of(context).aboutApp,
                          style: GoogleFonts.cairo(),
                        ),
                        onTap: _showAboutDialog,
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.description_outlined),
                        title: Text(
                          AppLocalizations.of(context).termsAndConditions,
                          style: GoogleFonts.cairo(),
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const TermsAndConditionsPage(),
                            ),
                          );
                        },
                      ),
                      // تم حذف عنصر عرض إصدار التطبيق
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Builds the theme mode switch tile.
  Widget _buildThemeModeTile(AppSettings settings) {
    return SwitchListTile(
      secondary: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: Icon(
          settings.isDarkModeEnabled ? Icons.dark_mode : Icons.light_mode,
          key: ValueKey(settings.isDarkModeEnabled),
          color: settings.isDarkModeEnabled
              ? Colors.indigo
              : Colors.amber,
        ),
      ),
      title: Text(
        AppLocalizations.of(context).darkMode,
        style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        AppLocalizations.of(context).enableDarkMode,
        style: GoogleFonts.cairo(fontSize: 12),
      ),
      value: settings.isDarkModeEnabled,
      onChanged: (value) {
        context.read<SettingsBloc>().add(UpdateThemeModeEvent(value));

        // Show success message with theme-appropriate message
        AnimatedToast.show(
          context,
          message: value
              ? 'تم تفعيل الوضع الداكن'
              : 'تم تفعيل الوضع الفاتح',
          type: ToastType.success,
          position: ToastPosition.top,
        );
      },
    );
  }

  /// Builds the language selection tile.
  Widget _buildLanguageTile(AppSettings settings) {
    // Get language name based on language code
    String getLanguageName(String code) {
      switch (code) {
        case 'ar':
          return 'العربية';
        case 'en':
          return 'English';
        case 'es':
          return 'Español';
        case 'fr':
          return 'Français';
        case 'de':
          return 'Deutsch';
        default:
          return code;
      }
    }

    return ListTile(
      leading: const Icon(Icons.language),
      title: Text(
        AppLocalizations.of(context).language,
        style: GoogleFonts.cairo(),
      ),
      subtitle: Text(
        getLanguageName(settings.languageCode),
        style: GoogleFonts.cairo(fontSize: 12),
      ),
      onTap: () {
        _showLanguageSelectionDialog(settings);
      },
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
    );
  }

  /// Shows a dialog to select the application language.
  void _showLanguageSelectionDialog(AppSettings settings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).selectLanguage,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildLanguageOption(
                  'ar', AppLocalizations.of(context).arabic, settings),
              _buildLanguageOption(
                  'en', AppLocalizations.of(context).english, settings),
              _buildLanguageOption(
                  'es', AppLocalizations.of(context).spanish, settings),
              _buildLanguageOption(
                  'fr', AppLocalizations.of(context).french, settings),
              _buildLanguageOption(
                  'de', AppLocalizations.of(context).german, settings),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: GoogleFonts.cairo(),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a language option for the language selection dialog.
  Widget _buildLanguageOption(String code, String name, AppSettings settings) {
    final isSelected = settings.languageCode == code;

    return ListTile(
      leading: isSelected
          ? const Icon(Icons.check_circle, color: AppColors.primaryColor)
          : const Icon(Icons.language),
      title: Text(
        name,
        style: GoogleFonts.cairo(
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? AppColors.primaryColor : null,
        ),
      ),
      onTap: () {
        Navigator.pop(context);

        // Update language in settings
        context.read<SettingsBloc>().add(UpdateLanguageEvent(code));

        // Update language provider
        final languageProvider =
            Provider.of<LanguageProvider>(context, listen: false);
        languageProvider.setLocale(Locale(code));

        // Show success message
        AnimatedToast.show(
          context,
          message: AppLocalizations.of(context).languageChanged,
          type: ToastType.success,
          position: ToastPosition.top,
        );
      },
    );
  }

  /// Builds the notifications switch tile.
  Widget _buildNotificationsTile(AppSettings settings) {
    return SwitchListTile(
      secondary: const Icon(Icons.notifications_outlined),
      title: Text(
        AppLocalizations.of(context).notifications,
        style: GoogleFonts.cairo(),
      ),
      subtitle: Text(
        AppLocalizations.of(context).receiveNotifications,
        style: GoogleFonts.cairo(fontSize: 12),
      ),
      value: settings.areNotificationsEnabled,
      onChanged: (value) {
        context.read<SettingsBloc>().add(UpdateNotificationsEvent(value));

        // Show success message
        AnimatedToast.show(
          context,
          message: value
              ? AppLocalizations.of(context).notificationsEnabled
              : AppLocalizations.of(context).notificationsDisabled,
          type: ToastType.success,
          position: ToastPosition.top,
        );
      },
    );
  }

  /// Shows a confirmation dialog before logging out.
  void _showLogoutConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).logout,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          AppLocalizations.of(context).logoutConfirmation,
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: GoogleFonts.cairo(),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              // عرض إشعار نجاح متحرك قبل تسجيل الخروج
              SuccessOverlay.show(
                context,
                message: AppLocalizations.of(context).loggingOut,
                duration: const Duration(milliseconds: 800),
                onComplete: () {
                  // تسجيل الخروج بعد إظهار الإشعار
                  debugPrint('Logout button pressed, triggering logout event');
                  context.read<AuthBloc>().add(LogoutEvent());
                },
              );
            },
            child: Text(
              AppLocalizations.of(context).logout,
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  /// Shows the about dialog with application information.
  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: null,
        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Image.asset(
                  'assets/logos/logo.png',
                  width: 150,
                  height: 150,
                ),
              ),
              const SizedBox(height: AppSizes.paddingMedium),
              Text(
                '${AppLocalizations.of(context).version} $_appVersion',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSizes.paddingSmall),
              Text(
                AppLocalizations.of(context).appDescription,
                style: GoogleFonts.cairo(),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSizes.paddingMedium),
              Text(
                '© 2025 ${AppLocalizations.of(context).appName}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: GoogleFonts.cairo(),
            ),
          ),
        ],
      ),
    );
  }
}
