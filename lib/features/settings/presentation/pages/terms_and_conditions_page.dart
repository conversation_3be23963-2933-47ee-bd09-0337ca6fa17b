import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_sizes.dart';

/// Page displaying the terms of use and privacy policy.
///
/// This page provides users with information about the application's
/// terms of service, privacy policy, and intellectual property rights.
class TermsAndConditionsPage extends StatelessWidget {
  /// Creates a new terms and conditions page.
  const TermsAndConditionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'شروط الاستخدام والخصوصية',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryColor,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('شروط الاستخدام'),
            _buildParagraph(
              'مرحبًا بك في تطبيق مشاعر! يرجى قراءة شروط الاستخدام هذه بعناية قبل استخدام التطبيق. باستخدامك للتطبيق، فإنك توافق على الالتزام بهذه الشروط.',
            ),
            _buildParagraph(
              '1. يجب أن تكون عمرك 13 عامًا على الأقل لاستخدام هذا التطبيق.',
            ),
            _buildParagraph(
              '2. أنت مسؤول عن الحفاظ على سرية معلومات حسابك وكلمة المرور الخاصة بك.',
            ),
            _buildParagraph(
              '3. يحظر استخدام التطبيق لأي غرض غير قانوني أو غير مصرح به.',
            ),
            _buildParagraph(
              '4. نحتفظ بالحق في تعديل أو إنهاء الخدمة لأي سبب، دون إشعار في أي وقت.',
            ),
            _buildParagraph(
              '5. نحتفظ بالحق في تغيير هذه الشروط من وقت لآخر. ستكون التغييرات سارية فور نشرها على التطبيق.',
            ),
            const SizedBox(height: AppSizes.paddingMedium),
            _buildSectionTitle('سياسة الخصوصية'),
            _buildParagraph(
              'تصف سياسة الخصوصية هذه كيفية جمع واستخدام وحماية المعلومات الشخصية التي تقدمها عند استخدام تطبيقنا.',
            ),
            _buildParagraph(
              '1. المعلومات التي نجمعها: قد نجمع معلومات شخصية مثل اسمك وعنوان بريدك الإلكتروني ورقم هاتفك وموقعك الجغرافي.',
            ),
            _buildParagraph(
              '2. كيفية استخدام المعلومات: نستخدم المعلومات التي نجمعها لتوفير وتحسين خدماتنا، وللتواصل معك، ولأغراض التسويق.',
            ),
            _buildParagraph(
              '3. أمان المعلومات: نتخذ تدابير أمنية مناسبة لحماية معلوماتك الشخصية من الوصول غير المصرح به أو التغيير أو الإفصاح أو الإتلاف.',
            ),
            _buildParagraph(
              '4. الإفصاح لأطراف ثالثة: قد نشارك معلوماتك مع أطراف ثالثة موثوقة تساعدنا في تشغيل تطبيقنا وإدارة أعمالنا، شريطة أن توافق هذه الأطراف على الحفاظ على سرية هذه المعلومات.',
            ),
            _buildParagraph(
              '5. ملفات تعريف الارتباط: قد نستخدم ملفات تعريف الارتباط لتحسين تجربة المستخدم وتحليل استخدام التطبيق.',
            ),
            const SizedBox(height: AppSizes.paddingMedium),
            _buildSectionTitle('حقوق الملكية الفكرية'),
            _buildParagraph(
              'جميع المحتويات المنشورة على التطبيق، بما في ذلك النصوص والرسومات والشعارات والصور ومقاطع الفيديو والأصوات والبرامج وتصميم الموقع، هي ملك لنا أو للمرخصين لنا وهي محمية بموجب قوانين حقوق النشر والعلامات التجارية.',
            ),
            _buildParagraph(
              'لا يجوز لك نسخ أو إعادة إنتاج أو تعديل أو نشر أو عرض علنًا أو إعادة توزيع أو بيع أي محتوى من التطبيق دون إذن كتابي صريح منا.',
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            Center(
              child: Text(
                'جميع الحقوق محفوظة لشركة\ncraft solutions © 2025',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a section title widget.
  ///
  /// @param title The title text to display
  /// @return A styled section title widget
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: AppSizes.paddingSmall,
      ),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.primaryColor,
        ),
      ),
    );
  }

  /// Builds a paragraph text widget.
  ///
  /// @param text The paragraph text to display
  /// @return A styled paragraph widget
  Widget _buildParagraph(String text) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppSizes.paddingSmall,
      ),
      child: Text(
        text,
        style: GoogleFonts.cairo(
          fontSize: 16,
          height: 1.5,
        ),
        textAlign: TextAlign.justify,
      ),
    );
  }
}
