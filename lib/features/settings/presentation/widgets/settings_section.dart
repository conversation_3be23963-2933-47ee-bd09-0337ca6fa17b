import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_sizes.dart';

/// A widget that displays a section of settings with a title and content.
///
/// This widget is used to group related settings together with a common title.
class SettingsSection extends StatelessWidget {
  /// The title of the section
  final String title;
  
  /// The widgets to display in the section
  final List<Widget> children;
  
  /// Whether to animate the section when it appears
  final bool animate;

  /// Creates a new settings section.
  const SettingsSection({
    super.key,
    required this.title,
    required this.children,
    this.animate = true,
  });

  @override
  Widget build(BuildContext context) {
    final Widget content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.only(
            left: AppSizes.paddingSmall,
            right: AppSizes.paddingSmall,
            bottom: AppSizes.paddingSmall,
          ),
          child: Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
        ),
        
        // Section content card
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );

    // Apply animation if requested
    if (animate) {
      return AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 500),
        child: AnimatedPadding(
          padding: const EdgeInsets.all(0),
          duration: const Duration(milliseconds: 500),
          child: content,
        ),
      );
    }

    return content;
  }
}
