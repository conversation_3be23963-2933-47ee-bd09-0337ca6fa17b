import 'package:equatable/equatable.dart';

import '../../../domain/entities/app_settings.dart';

/// Base class for all settings events.
abstract class SettingsEvent extends Equatable {
  const SettingsEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load the application settings.
class LoadSettingsEvent extends SettingsEvent {}

/// Event to save the application settings.
class SaveSettingsEvent extends SettingsEvent {
  /// The settings to save
  final AppSettings settings;

  /// Creates a new save settings event.
  const SaveSettingsEvent(this.settings);

  @override
  List<Object?> get props => [settings];
}

/// Event to update the theme mode.
class UpdateThemeModeEvent extends SettingsEvent {
  /// Whether dark mode should be enabled
  final bool isDarkMode;

  /// Creates a new update theme mode event.
  const UpdateThemeModeEvent(this.isDarkMode);

  @override
  List<Object?> get props => [isDarkMode];
}

/// Event to update the notification setting.
class UpdateNotificationsEvent extends SettingsEvent {
  /// Whether notifications should be enabled
  final bool areEnabled;

  /// Creates a new update notifications event.
  const UpdateNotificationsEvent(this.areEnabled);

  @override
  List<Object?> get props => [areEnabled];
}

/// Event to update the default card size.
class UpdateDefaultCardSizeEvent extends SettingsEvent {
  /// The default card size to set
  final String size;

  /// Creates a new update default card size event.
  const UpdateDefaultCardSizeEvent(this.size);

  @override
  List<Object?> get props => [size];
}

/// Event to update the application language.
class UpdateLanguageEvent extends SettingsEvent {
  /// The language code to set
  final String languageCode;

  /// Creates a new update language event.
  const UpdateLanguageEvent(this.languageCode);

  @override
  List<Object?> get props => [languageCode];
}
