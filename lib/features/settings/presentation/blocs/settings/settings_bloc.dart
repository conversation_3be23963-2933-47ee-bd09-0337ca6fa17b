import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/usecases/usecase.dart';
import '../../../domain/usecases/load_settings_usecase.dart';
import '../../../domain/usecases/save_settings_usecase.dart';
import '../../../domain/usecases/update_language_usecase.dart';
import '../../../domain/usecases/update_theme_mode_usecase.dart';
import 'settings_event.dart';
import 'settings_state.dart';

/// BLoC that manages the application settings.
class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  /// Use case for loading settings
  final LoadSettingsUseCase loadSettingsUseCase;

  /// Use case for saving settings
  final SaveSettingsUseCase saveSettingsUseCase;

  /// Use case for updating the theme mode
  final UpdateThemeModeUseCase updateThemeModeUseCase;

  /// Use case for updating the language
  final UpdateLanguageUseCase updateLanguageUseCase;

  /// Creates a new settings bloc with the required use cases.
  SettingsBloc({
    required this.loadSettingsUseCase,
    required this.saveSettingsUseCase,
    required this.updateThemeModeUseCase,
    required this.updateLanguageUseCase,
  }) : super(SettingsInitial()) {
    on<LoadSettingsEvent>(_onLoadSettings);
    on<SaveSettingsEvent>(_onSaveSettings);
    on<UpdateThemeModeEvent>(_onUpdateThemeMode);
    on<UpdateNotificationsEvent>(_onUpdateNotifications);
    on<UpdateDefaultCardSizeEvent>(_onUpdateDefaultCardSize);
    on<UpdateLanguageEvent>(_onUpdateLanguage);
  }

  /// Handles the [LoadSettingsEvent] by loading the application settings.
  Future<void> _onLoadSettings(
    LoadSettingsEvent event,
    Emitter<SettingsState> emit,
  ) async {
    emit(SettingsLoading());

    final result = await loadSettingsUseCase(const NoParams());

    result.fold(
      (failure) {
        debugPrint('Error loading settings: ${failure.message}');
        emit(SettingsError('Failed to load settings: ${failure.message}'));
      },
      (settings) {
        emit(SettingsLoaded(settings));
      },
    );
  }

  /// Handles the [SaveSettingsEvent] by saving the application settings.
  Future<void> _onSaveSettings(
    SaveSettingsEvent event,
    Emitter<SettingsState> emit,
  ) async {
    emit(SettingsLoading());

    final result = await saveSettingsUseCase(event.settings);

    result.fold(
      (failure) {
        debugPrint('Error saving settings: ${failure.message}');
        emit(SettingsError('Failed to save settings: ${failure.message}'));
      },
      (success) {
        if (success) {
          emit(SettingsUpdated(event.settings, 'Settings saved successfully'));
          emit(SettingsLoaded(event.settings));
        } else {
          emit(SettingsError('Failed to save settings'));
        }
      },
    );
  }

  /// Handles the [UpdateThemeModeEvent] by updating the theme mode.
  Future<void> _onUpdateThemeMode(
    UpdateThemeModeEvent event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSettings = currentState.settings.copyWith(
        isDarkModeEnabled: event.isDarkMode,
      );

      final result = await updateThemeModeUseCase(event.isDarkMode);

      result.fold(
        (failure) {
          debugPrint('Error updating theme mode: ${failure.message}');
          emit(
              SettingsError('Failed to update theme mode: ${failure.message}'));
          emit(currentState);
        },
        (success) {
          if (success) {
            emit(SettingsUpdated(updatedSettings, 'Theme mode updated'));
            emit(currentState.copyWith(settings: updatedSettings));
          } else {
            emit(SettingsError('Failed to update theme mode'));
            emit(currentState);
          }
        },
      );
    }
  }

  /// Handles the [UpdateNotificationsEvent] by updating the notification setting.
  Future<void> _onUpdateNotifications(
    UpdateNotificationsEvent event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSettings = currentState.settings.copyWith(
        areNotificationsEnabled: event.areEnabled,
      );

      final result = await saveSettingsUseCase(updatedSettings);

      result.fold(
        (failure) {
          debugPrint(
              'Error updating notifications setting: ${failure.message}');
          emit(SettingsError(
              'Failed to update notifications setting: ${failure.message}'));
          emit(currentState);
        },
        (success) {
          if (success) {
            emit(SettingsUpdated(
                updatedSettings, 'Notifications setting updated'));
            emit(currentState.copyWith(settings: updatedSettings));
          } else {
            emit(SettingsError('Failed to update notifications setting'));
            emit(currentState);
          }
        },
      );
    }
  }

  /// Handles the [UpdateDefaultCardSizeEvent] by updating the default card size.
  Future<void> _onUpdateDefaultCardSize(
    UpdateDefaultCardSizeEvent event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSettings = currentState.settings.copyWith(
        defaultCardSize: event.size,
      );

      final result = await saveSettingsUseCase(updatedSettings);

      result.fold(
        (failure) {
          debugPrint('Error updating default card size: ${failure.message}');
          emit(SettingsError(
              'Failed to update default card size: ${failure.message}'));
          emit(currentState);
        },
        (success) {
          if (success) {
            emit(SettingsUpdated(updatedSettings, 'Default card size updated'));
            emit(currentState.copyWith(settings: updatedSettings));
          } else {
            emit(SettingsError('Failed to update default card size'));
            emit(currentState);
          }
        },
      );
    }
  }

  /// Handles the [UpdateLanguageEvent] by updating the application language.
  Future<void> _onUpdateLanguage(
    UpdateLanguageEvent event,
    Emitter<SettingsState> emit,
  ) async {
    if (state is SettingsLoaded) {
      final currentState = state as SettingsLoaded;
      final updatedSettings = currentState.settings.copyWith(
        languageCode: event.languageCode,
      );

      final result = await updateLanguageUseCase(event.languageCode);

      result.fold(
        (failure) {
          debugPrint('Error updating language: ${failure.message}');
          emit(SettingsError('Failed to update language: ${failure.message}'));
          emit(currentState);
        },
        (success) {
          if (success) {
            emit(SettingsUpdated(updatedSettings, 'Language updated'));
            emit(currentState.copyWith(settings: updatedSettings));
          } else {
            emit(SettingsError('Failed to update language'));
            emit(currentState);
          }
        },
      );
    }
  }
}
