import 'package:equatable/equatable.dart';

import '../../../domain/entities/app_settings.dart';

/// Base class for all settings states.
abstract class SettingsState extends Equatable {
  const SettingsState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the settings bloc.
class SettingsInitial extends SettingsState {}

/// Loading state during settings operations.
class SettingsLoading extends SettingsState {}

/// State representing loaded settings.
class SettingsLoaded extends SettingsState {
  /// The loaded settings
  final AppSettings settings;

  /// Creates a new loaded state with the settings.
  const SettingsLoaded(this.settings);

  @override
  List<Object?> get props => [settings];

  /// Creates a copy of this state with the given fields replaced with new values.
  SettingsLoaded copyWith({
    AppSettings? settings,
  }) {
    return SettingsLoaded(
      settings ?? this.settings,
    );
  }
}

/// Error state during settings operations.
class SettingsError extends SettingsState {
  /// The error message
  final String message;

  /// Creates a new error state with the specified error message.
  const SettingsError(this.message);

  @override
  List<Object?> get props => [message];
}

/// State representing a successful settings update.
class SettingsUpdated extends SettingsState {
  /// The updated settings
  final AppSettings settings;
  
  /// A message describing the update
  final String message;

  /// Creates a new updated state with the settings and message.
  const SettingsUpdated(this.settings, this.message);

  @override
  List<Object?> get props => [settings, message];
}
