import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../repositories/settings_repository.dart';

/// Use case for updating the application theme mode.
class UpdateThemeModeUseCase {
  /// Repository for managing settings
  final SettingsRepository repository;

  /// Creates a new instance with the required repository.
  UpdateThemeModeUseCase(this.repository);

  /// Executes the use case to update the theme mode.
  ///
  /// @param isDarkMode Whether dark mode should be enabled
  /// @return Either a Failure or a boolean indicating success
  Future<Either<Failure, bool>> call(bool isDarkMode) async {
    try {
      final result = await repository.setDarkMode(isDarkMode);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
