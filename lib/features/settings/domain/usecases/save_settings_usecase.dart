import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../entities/app_settings.dart';
import '../repositories/settings_repository.dart';

/// Use case for saving application settings.
class SaveSettingsUseCase {
  /// Repository for managing settings
  final SettingsRepository repository;

  /// Creates a new instance with the required repository.
  SaveSettingsUseCase(this.repository);

  /// Executes the use case to save settings.
  ///
  /// @param settings The settings to save
  /// @return Either a Failure or a boolean indicating success
  Future<Either<Failure, bool>> call(AppSettings settings) async {
    try {
      final result = await repository.saveSettings(settings);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
