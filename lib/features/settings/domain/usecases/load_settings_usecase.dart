import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/app_settings.dart';
import '../repositories/settings_repository.dart';

/// Use case for loading application settings.
class LoadSettingsUseCase implements UseCase<AppSettings, NoParams> {
  /// Repository for managing settings
  final SettingsRepository repository;

  /// Creates a new instance with the required repository.
  LoadSettingsUseCase(this.repository);

  /// Executes the use case to load settings.
  ///
  /// @param params No parameters needed
  /// @return The current application settings
  @override
  Future<Either<Failure, AppSettings>> call(NoParams params) async {
    try {
      final settings = await repository.loadSettings();
      return Right(settings);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
