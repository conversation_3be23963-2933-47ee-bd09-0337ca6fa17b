import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../repositories/settings_repository.dart';

/// Use case for updating the application language.
class UpdateLanguageUseCase {
  /// Repository for managing settings
  final SettingsRepository repository;

  /// Creates a new instance with the required repository.
  UpdateLanguageUseCase(this.repository);

  /// Executes the use case to update the language.
  ///
  /// @param languageCode The language code to set (ar, en)
  /// @return Either a Failure or a boolean indicating success
  Future<Either<Failure, bool>> call(String languageCode) async {
    try {
      final result = await repository.setLanguageCode(languageCode);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
