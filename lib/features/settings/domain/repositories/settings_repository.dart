import '../entities/app_settings.dart';

/// Repository interface for managing application settings.
abstract class SettingsRepository {
  /// Loads the application settings.
  ///
  /// @return The current application settings
  Future<AppSettings> loadSettings();

  /// Saves the application settings.
  ///
  /// @param settings The settings to save
  /// @return true if successful, false otherwise
  Future<bool> saveSettings(AppSettings settings);

  /// Gets the current theme mode.
  ///
  /// @return true if dark mode is enabled, false otherwise
  Future<bool> isDarkModeEnabled();

  /// Sets the theme mode.
  ///
  /// @param isDarkMode Whether dark mode should be enabled
  /// @return true if successful, false otherwise
  Future<bool> setDarkMode(bool isDarkMode);

  /// Gets the current notification setting.
  ///
  /// @return true if notifications are enabled, false otherwise
  Future<bool> areNotificationsEnabled();

  /// Sets the notification setting.
  ///
  /// @param areEnabled Whether notifications should be enabled
  /// @return true if successful, false otherwise
  Future<bool> setNotifications(bool areEnabled);

  /// Gets the default card size.
  ///
  /// @return The default card size (small, medium, large)
  Future<String> getDefaultCardSize();

  /// Sets the default card size.
  ///
  /// @param size The default card size to set
  /// @return true if successful, false otherwise
  Future<bool> setDefaultCardSize(String size);

  /// Gets the current language code.
  ///
  /// @return The language code (ar, en)
  Future<String> getLanguageCode();

  /// Sets the language code.
  ///
  /// @param languageCode The language code to set
  /// @return true if successful, false otherwise
  Future<bool> setLanguageCode(String languageCode);
}
