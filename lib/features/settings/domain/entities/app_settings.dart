/// Entity representing the application settings.
///
/// This class contains all user-configurable settings for the application.
class AppSettings {
  /// Whether dark mode is enabled
  final bool isDarkModeEnabled;
  
  /// Whether notifications are enabled
  final bool areNotificationsEnabled;
  
  /// The default card size (small, medium, large)
  final String defaultCardSize;
  
  /// The application language code (ar, en)
  final String languageCode;

  /// Creates a new app settings entity.
  const AppSettings({
    this.isDarkModeEnabled = false,
    this.areNotificationsEnabled = true,
    this.defaultCardSize = 'متوسط',
    this.languageCode = 'ar',
  });

  /// Creates a copy of this settings with the given fields replaced with new values.
  AppSettings copyWith({
    bool? isDarkModeEnabled,
    bool? areNotificationsEnabled,
    String? defaultCardSize,
    String? languageCode,
  }) {
    return AppSettings(
      isDarkModeEnabled: isDarkModeEnabled ?? this.isDarkModeEnabled,
      areNotificationsEnabled: areNotificationsEnabled ?? this.areNotificationsEnabled,
      defaultCardSize: defaultCardSize ?? this.defaultCardSize,
      languageCode: languageCode ?? this.languageCode,
    );
  }
}
