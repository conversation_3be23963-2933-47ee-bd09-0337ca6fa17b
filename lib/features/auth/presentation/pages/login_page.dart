// lib/features/auth/presentation/pages/login_page.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/services/auth_persistence_service.dart';
import '../../../../core/widgets/animated_toast.dart';
import '../../../../injection_container.dart';
import '../../domain/usecases/login_with_email_usecase.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_event.dart';
import '../blocs/login/login_bloc.dart';
import '../blocs/login/login_event.dart';
import '../blocs/login/login_state.dart';
import 'forgot_password_page.dart';
import 'register_page.dart';
import '../../../../core/utils/app_routes.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});
  @override
  Widget build(BuildContext context) {
    return BlocProvider<LoginBloc>(
      create: (context) => LoginBloc(
        loginWithEmailUseCase: sl<LoginWithEmailUseCase>(),
      ),
      child: const _LoginPageBody(),
    );
  }
}

class _LoginPageBody extends StatefulWidget {
  const _LoginPageBody();
  @override
  State<_LoginPageBody> createState() => _LoginPageBodyState();
}

class _LoginPageBodyState extends State<_LoginPageBody> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

// Focus nodes to handle keyboard navigation.
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();

  bool _rememberMe = false;
  bool _obscurePassword = true; // متغير للتحكم في إظهار/إخفاء كلمة المرور

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
    // Ya no llamamos a _checkForAutoLogin() aquí porque ahora
    // la verificación se hace en la pantalla splash
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  /// تحميل بيانات المستخدم المحفوظة (البريد الإلكتروني وحالة تذكرني)
  Future<void> _loadSavedCredentials() async {
    final authService = sl<AuthPersistenceService>();
    final isRememberMeEnabled = await authService.isRememberMeEnabled();

    if (isRememberMeEnabled) {
      final credentials = await authService.getSavedCredentials();
      if (credentials != null) {
        setState(() {
          _emailController.text = credentials['email'] ?? '';
          _rememberMe = true;
        });
      }
    }
  }

  // El método _checkForAutoLogin ya no es necesario porque
  // la verificación se hace en la pantalla splash

  /// حفظ بيانات المستخدم إذا تم تفعيل "تذكرني"
  Future<void> _saveCredentialsIfNeeded() async {
    final authService = sl<AuthPersistenceService>();
    if (_rememberMe) {
      await authService.saveCredentials(
        email: _emailController.text.trim(),
        password: _passwordController.text.trim(),
        rememberMe: true,
      );
    } else {
      await authService.clearSavedCredentials();
    }
  }

/* ───────── حفظ FCM token بعد النجاح ───────── */
  Future<void> _saveFcmToken() async {
    final uid = FirebaseAuth.instance.currentUser?.uid;
    final token = await FirebaseMessaging.instance.getToken();
    if (uid == null || token == null) return;
    await FirebaseMessaging.instance.subscribeToTopic('allUsers');
    await FirebaseFirestore.instance
        .collection('users')
        .doc(uid)
        .set({'fcm': token}, SetOptions(merge: true));
  }

  void _onLoginPressed() {
    context.read<LoginBloc>().add(
          LoginWithEmailSubmitted(
            email: _emailController.text.trim(),
            password: _passwordController.text.trim(),
          ),
        );
  }

  void _onForgotPasswordPressed() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const ForgotPasswordPage()),
    );
  }

  void _onRegisterPressed() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const RegisterPage()),
    );
  }

  void _handleLoginSuccess() {
    Navigator.of(context).pushReplacementNamed(AppRoutes.home);
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // الخلفيات (الموجات) متراكبة مع اختلاف الارتفاع
          const Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: WavyContainers(),
          ),
          BlocListener<LoginBloc, LoginState>(
            listener: (context, state) async {
              if (state is LoginSuccess) {
                await _saveCredentialsIfNeeded();
                await _saveFcmToken();
                if (mounted) {
                  // تحديث AuthBloc بحالة المستخدم المسجل
                  context.read<AuthBloc>().add(const CheckAuthStatusEvent(silent: true));
                  _handleLoginSuccess();
                }
              } else if (state is LoginError) {
                // عرض إشعار خطأ متحرك
                if (mounted) {
                  final BuildContext currentContext = context;
                  AnimatedToast.show(
                    currentContext,
                    message: state.message,
                    type: ToastType.error,
                  );
                }
              }
            },
            child: SafeArea(
              child: Container(
                width: double.infinity,
                height: MediaQuery.of(context).size.height,
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // الجزء العلوي: الشعار والنص التوضيحي
                    const _TopSection(),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _emailController,
                      focusNode: _emailFocusNode,
                      textAlign: TextAlign.right,
                      textInputAction: TextInputAction.next,
                      style: GoogleFonts.cairo(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                        fontSize: 16,
                      ),
                      onSubmitted: (_) {
                        FocusScope.of(context).requestFocus(_passwordFocusNode);
                      },
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).email,
                        labelStyle: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[300]
                              : Colors.grey[700],
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _passwordController,
                      focusNode: _passwordFocusNode,
                      obscureText: _obscurePassword,
                      textAlign: TextAlign.right,
                      textInputAction: TextInputAction.done,
                      style: GoogleFonts.cairo(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                        fontSize: 16,
                      ),
                      onSubmitted: (_) => _onLoginPressed(),
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).password,
                        labelStyle: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[300]
                              : Colors.grey[700],
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword
                                ? Icons.visibility_outlined
                                : Icons.visibility_off_outlined,
                            color: Colors.grey.shade600,
                            size: 20,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Checkbox(
                          value: _rememberMe,
                          onChanged: (bool? val) {
                            setState(() {
                              _rememberMe = val ?? false;
                            });
                          },
                        ),
                        Text(
                          AppLocalizations.of(context).rememberMe,
                        ),
                        const Spacer(),
                        TextButton(
                          onPressed: _onForgotPasswordPressed,
                          child:
                              Text(AppLocalizations.of(context).forgotPassword),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
// زر تسجيل الدخول مع مؤشر التحميل داخل الزر عند الضغط
                    BlocBuilder<LoginBloc, LoginState>(
                      builder: (context, state) {
                        return SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed:
                                state is LoginLoading ? null : _onLoginPressed,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF986D9E),
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: state is LoginLoading
                                ? SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          const Color(0xFFFAF9F6)),
                                    ),
                                  )
                                : Text(
                                    AppLocalizations.of(context).login,
                                    style: const TextStyle(
                                      color: Color(0xFFFAF9F6), // الأبيض المكسر
                                    ),
                                  ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                    TextButton(
                      onPressed: _onRegisterPressed,
                      child: Text(AppLocalizations.of(context).noAccount),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// رسم الموجات المتراكبة بتأثير احترافي:
/// - الموجتان الأوليان (WaveClipper) تظهران مع ارتفاعات مختلفة.
/// - الموجة الثالثة (HorizontalInvertedWaveClipper) معكوسة أفقيًا وتكون الأطول لتظهر تأثيرًا بارزًا في الأسفل.
class WavyContainers extends StatelessWidget {
  const WavyContainers({super.key});
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
// الموجة الثالثة: بالأسفل، الأطول، ومعكوسة أفقيًا
        ClipPath(
          clipper: HorizontalInvertedWaveClipper(),
          child: Container(
            height: 200,
            decoration: const BoxDecoration(
              color: Color(0xFFF0E6FF),
            ),
          ),
        ),
// الموجة الثانية: ارتفاع متوسط
        ClipPath(
          clipper: WaveClipper(),
          child: Container(
            height: 140,
            decoration: const BoxDecoration(
              color: Color(0xFFC3A9CF),
            ),
          ),
        ),
// الموجة الأولى: الأقصر
        ClipPath(
          clipper: WaveClipper(),
          child: Container(
            height: 130,
            decoration: const BoxDecoration(
              color: Color(0xFF986D9E),
            ),
          ),
        ),
      ],
    );
  }
}

/// CustomClipper لرسم الموجة الطبيعية (الموجتان الأوليان)
class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
// بدء المسار عند (0,60)
    path.moveTo(0, 60);
    path.quadraticBezierTo(size.width * 0.25, 0, size.width * 0.5, 60);
    path.quadraticBezierTo(size.width * 0.75, 120, size.width, 60);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

/// CustomClipper لرسم الموجة الثالثة معكوسة أفقيًا بالنسبة للموجتين الأوليين
class HorizontalInvertedWaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
// نبدأ من اليسار مع عكس نقاط التحكم:
    path.moveTo(0, 60);
    path.quadraticBezierTo(size.width * 0.25, 120, size.width * 0.5, 60);
    path.quadraticBezierTo(size.width * 0.75, 0, size.width, 60);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

/// الجزء العلوي: شعار التطبيق والنص التوضيحي المحدث
class _TopSection extends StatelessWidget {
  const _TopSection();
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شعار التطبيق
        Image.asset(
          'assets/logos/logo.png',
          width: 140,
          height: 140,
          fit: BoxFit.contain,
        ),
        const SizedBox(height: 8),
        // النص التوضيحي
        Text(
          '${AppLocalizations.of(context).touchHearts}\n'
          '${AppLocalizations.of(context).subscribeNow}',
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }
}