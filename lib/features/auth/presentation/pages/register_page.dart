import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../core/widgets/animated_toast.dart';
import '../../../../core/widgets/success_overlay.dart';
import '../../../../injection_container.dart';
import '../../domain/usecases/register_with_email_usecase.dart';
import '../blocs/auth/auth_bloc.dart';
import '../blocs/auth/auth_event.dart';
import '../blocs/register/register_bloc.dart';
import '../blocs/register/register_event.dart';
import '../blocs/register/register_state.dart';

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});
  @override
  Widget build(BuildContext context) {
    return BlocProvider<RegisterBloc>(
      create: (context) => RegisterBloc(sl<RegisterWithEmailUseCase>()),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.white,
        // إزالة خاصية التمرير بالصفحة؛ المحتوى سيحاكي الارتفاع الكامل للشاشة
        body: DefaultTextStyle(
          style: GoogleFonts.cairo(),
          child: const _RegisterPageBody(),
        ),
      ),
    );
  }
}

class _RegisterPageBody extends StatefulWidget {
  const _RegisterPageBody();
  @override
  State<_RegisterPageBody> createState() => _RegisterPageBodyState();
}

class _RegisterPageBodyState extends State<_RegisterPageBody> {
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();

  // إضافة FocusNodes للتنقل عبر لوحة المفاتيح
  final _nameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _phoneFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  // حالة الموافقة على الشروط
  bool _termsAgreed = false;
  // متغير للتحكم في إظهار/إخفاء كلمة المرور
  bool _obscurePassword = true;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _nameFocusNode.dispose();
    _emailFocusNode.dispose();
    _phoneFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  void _onRegisterPressed() {
    if (!_termsAgreed) {
      // عرض إشعار متحرك للتنبيه بضرورة الموافقة على الشروط
      AnimatedToast.show(
        context,
        message: AppLocalizations.of(context).pleaseAgreeToTerms,
        type: ToastType.warning,
      );
      return;
    }
    context.read<RegisterBloc>().add(
          RegisterWithEmailSubmitted(
            name: _nameController.text.trim(),
            email: _emailController.text.trim(),
            phone: _phoneController.text.trim(),
            password: _passwordController.text.trim(),
          ),
        );
  }

  // Widget لإنشاء حقل إدخال مع أيقونة مع دعم FocusNode والتنقل عبر لوحة المفاتيح
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool isPassword = false,
    FocusNode? focusNode,
    TextInputAction? textInputAction,
    void Function(String)? onSubmitted,
  }) {
    return TextField(
      controller: controller,
      obscureText: isPassword ? _obscurePassword : false,
      focusNode: focusNode,
      textAlign: TextAlign.right,
      textInputAction: textInputAction,
      onSubmitted: onSubmitted,
      style: GoogleFonts.cairo(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white
            : Colors.black87,
        fontSize: 16,
      ),
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        labelText: label,
        labelStyle: GoogleFonts.cairo(fontSize: 12),
        prefixIcon: Icon(icon, color: Colors.purple),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        // إضافة زر إظهار/إخفاء كلمة المرور للحقول المحمية فقط
        suffixIcon: isPassword
            ? IconButton(
                icon: Icon(
                  _obscurePassword
                      ? Icons.visibility_outlined
                      : Icons.visibility_off_outlined,
                  color: Colors.grey.shade600,
                  size: 20,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              )
            : null,
      ),
    );
  }

  // Widget خيار الموافقة على الشروط مع رابط صفحة الشروط
  Widget _buildTermsAgreement() {
    return Row(
      children: [
        Checkbox(
          value: _termsAgreed,
          onChanged: (bool? newValue) {
            setState(() {
              _termsAgreed = newValue ?? false;
            });
          },
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => const TermsAndConditionsPage(),
                ),
              );
            },
            child: RichText(
              text: TextSpan(
                text: '${AppLocalizations.of(context).agreeToThe} ',
                style: GoogleFonts.cairo(
                  color: Colors.black87,
                  fontSize: 14,
                ),
                children: [
                  TextSpan(
                    text: AppLocalizations.of(context).termsAndConditions,
                    style: GoogleFonts.cairo(
                      color: Colors.blue.shade700,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return BlocListener<RegisterBloc, RegisterState>(
        listener: (context, state) {
          if (state is RegisterSuccess) {
            // تحديث AuthBloc بحالة المستخدم المسجل
            context.read<AuthBloc>().add(const CheckAuthStatusEvent(silent: true));

            // عرض إشعار نجاح متحرك
            SuccessOverlay.show(
              context,
              message: 'تم التسجيل بنجاح',
              duration: const Duration(seconds: 1),
              onComplete: () {
                // الانتقال للصفحة الرئيسية بعد التسجيل
                if (mounted) {
                  Navigator.pushReplacementNamed(context, '/login');
                }
              },
            );
          } else if (state is RegisterError) {
            // عرض إشعار خطأ متحرك
            AnimatedToast.show(
              context,
              message: state.message,
              type: ToastType.error,
            );
          }
        },
        child: SafeArea(
          child: Stack(
            children: [
              // التموجات الثلاثة بتنسيق احترافي
              const Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: WavyContainers(),
              ),
              // محتوى الصفحة
              Container(
                width: double.infinity,
                height: screenHeight,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // الجزء العلوي: الشعار والنص التوضيحي
                    const _TopSection(),
                    const SizedBox(height: 8),
                    // حقول الإدخال مع دعم التنقل عبر لوحة المفاتيح
                    _buildTextField(
                      controller: _nameController,
                      label: AppLocalizations.of(context).name,
                      icon: Icons.person_outline,
                      focusNode: _nameFocusNode,
                      textInputAction: TextInputAction.next,
                      onSubmitted: (_) =>
                          FocusScope.of(context).requestFocus(_emailFocusNode),
                    ),
                    const SizedBox(height: 8),
                    _buildTextField(
                      controller: _emailController,
                      label: AppLocalizations.of(context).email,
                      icon: Icons.email_outlined,
                      focusNode: _emailFocusNode,
                      textInputAction: TextInputAction.next,
                      onSubmitted: (_) =>
                          FocusScope.of(context).requestFocus(_phoneFocusNode),
                    ),
                    const SizedBox(height: 8),
                    _buildTextField(
                      controller: _phoneController,
                      label: AppLocalizations.of(context).phoneNumber,
                      icon: Icons.phone_outlined,
                      focusNode: _phoneFocusNode,
                      textInputAction: TextInputAction.next,
                      onSubmitted: (_) => FocusScope.of(context)
                          .requestFocus(_passwordFocusNode),
                    ),
                    const SizedBox(height: 8),
                    _buildTextField(
                      controller: _passwordController,
                      label: AppLocalizations.of(context).password,
                      icon: Icons.lock_outline,
                      isPassword: true,
                      focusNode: _passwordFocusNode,
                      textInputAction: TextInputAction.done,
                      onSubmitted: (_) => _onRegisterPressed(),
                    ),
                    const SizedBox(height: 8),
                    // خيار الموافقة على الشروط
                    _buildTermsAgreement(),
                    const SizedBox(height: 16),
                    // زر التسجيل مع مؤشر التحميل أو أيقونة التحقق عند النجاح
                    BlocBuilder<RegisterBloc, RegisterState>(
                      builder: (context, state) {
                        Widget buttonChild;
                        if (state is RegisterLoading) {
                          buttonChild = const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(0xFFFAF9F6)),
                            ),
                          );
                        } else if (state is RegisterSuccess) {
                          buttonChild = const Icon(
                            Icons.check,
                            color: Color(0xFFFAF9F6),
                            size: 24,
                          );
                        } else {
                          buttonChild = Text(
                            AppLocalizations.of(context).register,
                            style: GoogleFonts.cairo(
                              color: const Color(0xFFFAF9F6),
                              fontSize: 16,
                            ),
                          );
                        }
                        return Column(
                          children: [
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed: state is RegisterLoading
                                    ? null
                                    : _onRegisterPressed,
                                style: ElevatedButton.styleFrom(
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  backgroundColor: const Color(0xFF986D9E),
                                ),
                                child: buttonChild,
                              ),
                            ),
                            if (state is RegisterSuccess) ...[
                              const SizedBox(height: 16),
                              const Text(
                                'يرجى مراجعة بريدك الإلكتروني لاستلام رابط التفعيل',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ],
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    // خيار "لديك حساب بالفعل؟ سجل دخول"
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: RichText(
                        text: TextSpan(
                          text: '${AppLocalizations.of(context).loginHint} ',
                          style: GoogleFonts.cairo(
                            color: Colors.black87,
                          ),
                          children: [
                            TextSpan(
                              text: AppLocalizations.of(context).login,
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.bold,
                                color: Colors.purple.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}

/// صفحة شروط الاستخدام وسياسة الخصوصية
class TermsAndConditionsPage extends StatelessWidget {
  const TermsAndConditionsPage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'شروط الاستخدام وسياسة الخصوصية',
          style: GoogleFonts.cairo(fontSize: 18, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF986D9E),
      ),
      body: DefaultTextStyle(
        style:
            GoogleFonts.cairo(fontSize: 14, height: 1.5, color: Colors.black87),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(12),
          child: const Text(
            '''سياسة الاستخدام والخصوصية لتطبيق "مشاعر"

مرحبًا بك في تطبيق "مشاعر" الحل الأمثل لاختيار عبارات الإهداء الجاهزة لجميع المناسبات. يُرجى قراءة سياسة الاستخدام والخصوصية بعناية، حيث إن استخدام التطبيق يعني موافقتك الكاملة على الشروط والأحكام التالية.

أولًا: سياسة الاستخدام

1. الاستخدام المصرح به:
   - التطبيق متاح للاستخدام الشخصي والتجاري، مما يتيح للأفراد والمحلات التجارية استخدامه لاختيار عبارات الإهداء.
   - يُحظر استخدام التطبيق أو محتوياته بطرق تخالف القوانين أو تسيء لسمعة التطبيق.

2. حقوق الملكية الفكرية:
   - جميع العبارات والتصاميم والبرمجيات الخاصة بالتطبيق محمية بحقوق الملكية الفكرية، ولا يجوز إعادة إنتاجها، نشرها، أو استخدامها خارج التطبيق بأي وسيلة كانت دون إذن مسبق.
   - لا يُسمح بتصوير أو طباعة العبارات من التطبيق لاستخدامها خارج بيئة العمل المتفق عليها.

3. الاستخدام العادل والمناسب:
   - يُمنع استخدام العبارات أو مشاركتها في سياقات غير لائقة، مهينة، أو تخالف الذوق العام.
   - يُحظر إساءة استخدام التطبيق بطريقة تؤثر على عمله أو على تجربة المستخدمين الآخرين.

4. عدم الاستغلال أو التعديل:
   - لا يجوز إعادة برمجة، تعديل، أو التلاعب بالتطبيق أو بمحتواه بأي شكل.
   - يُمنع دمج العبارات أو إعادة استخدامها في تطبيقات أو خدمات أخرى دون تصريح رسمي.

ثانيًا: سياسة الخصوصية

1. حماية بيانات المستخدمين:
   - نحن نحترم خصوصية جميع المستخدمين، سواء كانوا أفرادًا أو محلات تجارية، ونتعامل مع البيانات وفقًا لأعلى معايير الأمان.
   - لا يقوم التطبيق بجمع أي بيانات شخصية دون موافقة المستخدم، كما أنه لا يخزن أي معلومات حساسة دون إذن مسبق.

2. استخدام بيانات المستخدمين:
   - يحق لإدارة "مشاعر" استخدام بيانات الاستخدام لتحسين الخدمة وضمان أفضل تجربة للمستخدمين.
   - لا يتم مشاركة بيانات المستخدمين مع أي طرف ثالث دون إذن مسبق.

3. ملفات تعريف الارتباط (Cookies):
   - قد يستخدم التطبيق ملفات تعريف الارتباط لتحسين الأداء وتجربة المستخدم، مع إمكانية تعديل إعدادات الجهاز للتحكم في ذلك.

4. التعديلات على السياسة:
   - يحق لإدارة التطبيق تحديث سياسة الاستخدام والخصوصية في أي وقت، مع إشعار المستخدمين بأي تغييرات جوهرية عبر البريد الإلكتروني أو داخل التطبيق.

ثالثًا: العقوبات والمساءلة

1. إجراءات الحماية القانونية:
   - يحق لإدارة "مشاعر" اتخاذ الإجراءات القانونية المناسبة ضد أي مستخدم ينتهك هذه الشروط أو يستخدم التطبيق بطرق غير مصرح بها.
   - قد يتم تعليق أو إلغاء حق استخدام التطبيق لأي شخص يسيء استخدامه.

نشكركم على اختيار "مشاعر"، ونتمنى لكم تجربة رائعة تساعدكم في تقديم أرقى عبارات الإهداء.''',
            textAlign: TextAlign.right,
          ),
        ),
      ),
    );
  }
}

/// Widget لرسم التموجات الثلاثة بتنسيق احترافي
class WavyContainers extends StatelessWidget {
  const WavyContainers({super.key});
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        // الموجة الثالثة: بالأسفل، الأطول، ومعكوسة أفقيًا
        ClipPath(
          clipper: HorizontalInvertedWaveClipper(),
          child: Container(
            height: 200,
            decoration: const BoxDecoration(
              color: Color(0xFFF0E6FF),
            ),
          ),
        ),
        // الموجة الثانية: ارتفاع متوسط
        ClipPath(
          clipper: WaveClipper(),
          child: Container(
            height: 140,
            decoration: const BoxDecoration(
              color: Color(0xFFC3A9CF),
            ),
          ),
        ),
        // الموجة الأولى: الأقصر
        ClipPath(
          clipper: WaveClipper(),
          child: Container(
            height: 130,
            decoration: const BoxDecoration(
              color: Color(0xFF986D9E),
            ),
          ),
        ),
      ],
    );
  }
}

/// CustomClipper لرسم الموجة الطبيعية (الموجتان الأوليان)
class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    // بدء المسار عند (0,60)
    path.moveTo(0, 60);
    path.quadraticBezierTo(size.width * 0.25, 0, size.width * 0.5, 60);
    path.quadraticBezierTo(size.width * 0.75, 120, size.width, 60);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

/// CustomClipper لرسم الموجة الثالثة معكوسة أفقيًا
class HorizontalInvertedWaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    // بدء المسار عند (0,60) مع عكس نقاط التحكم
    path.moveTo(0, 60);
    path.quadraticBezierTo(size.width * 0.25, 120, size.width * 0.5, 60);
    path.quadraticBezierTo(size.width * 0.75, 0, size.width, 60);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

/// الجزء العلوي: الشعار والنص التوضيحي
class _TopSection extends StatelessWidget {
  const _TopSection();
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // تكبير الشعار لتقليل الفراغ
          Image.asset(
            'assets/logos/logo.png',
            width: 140,
            height: 140,
          ),
          const SizedBox(height: 4),
          // النص التوضيحي بإجبار سطرين فقط
          Text(
            'اجعل كلماتك تلامس القلوب وتخلد اللحظات الجميلة.\nاشترك الآن وابدأ بإرسال أروع المشاعر',
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.black54,
            ),
          ),
        ],
      ),
    );
  }
}