import 'package:flutter/material.dart';

import '../../../../injection_container.dart';
import '../../domain/usecases/reset_password_usecase.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _emailController = TextEditingController();
  bool _isLoading = false;
  bool _isSuccess = false;

  Future<void> _onSendResetLink() async {
    final email = _emailController.text.trim();
    if (email.isEmpty) return; // يمكن إضافة رسالة خطأ إن رغبت

    setState(() {
      _isLoading = true;
      _isSuccess = false;
    });

    final result = await sl<ResetPasswordUseCase>()(
      ResetPasswordParams(email),
    );

    result.fold(
      (failure) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(failure.message)),
        );
        setState(() {
          _isLoading = false;
        });
      },
      (_) {
        setState(() {
          _isLoading = false;
          _isSuccess = true;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إعادة تعيين كلمة المرور',
          style: TextStyle(fontSize: 16),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'أدخل بريدك الإلكتروني لإرسال رابط إعادة التعيين',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 24),
              TextField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _onSendResetLink,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF986D9E),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                                Color(0xFFFAF9F6)),
                          ),
                        )
                      : _isSuccess
                          ? const Icon(
                              Icons.check,
                              color: Color(0xFFFAF9F6),
                              size: 24,
                            )
                          : const Text(
                              'إرسال رابط',
                              style: TextStyle(
                                color: Color(0xFFFAF9F6),
                                fontSize: 16,
                              ),
                            ),
                ),
              ),
              if (_isSuccess) ...[
                const SizedBox(height: 16),
                const Text(
                  'يرجى مراجعة بريدك الإلكتروني لاستلام رابط إعادة التعيين',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
