import 'package:equatable/equatable.dart';

/// Base class for all authentication events.
///
/// This abstract class is extended by all events that can be dispatched to the [AuthBloc].
/// It implements [Equatable] to allow for easy comparison of events.
abstract class AuthEvent extends Equatable {
  /// Creates a new authentication event.
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event to check the current authentication status.
///
/// This event is typically dispatched when the application starts or
/// when the authentication status needs to be verified.
///
/// If [silent] is true, the bloc will not emit a loading state before checking.
class CheckAuthStatusEvent extends AuthEvent {
  /// Whether to emit a loading state before checking
  final bool silent;

  /// Creates a new check auth status event.
  const CheckAuthStatusEvent({this.silent = false});

  @override
  List<Object?> get props => [silent];
}

/// Event to log out the current user.
///
/// This event is dispatched when the user requests to log out of the application.
class LogoutEvent extends AuthEvent {}
