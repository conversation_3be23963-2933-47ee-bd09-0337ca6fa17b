import 'package:equatable/equatable.dart';

import '../../../domain/entities/user_entity.dart';

/// Base class for all authentication states.
///
/// This abstract class is extended by all possible states of the [AuthBloc].
/// It implements [Equatable] to allow for easy comparison of states.
abstract class AuthState extends Equatable {
  /// Creates a new authentication state.
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the authentication bloc.
///
/// This state is used when the authentication status has not yet been determined.
class AuthInitial extends AuthState {}

/// Loading state during authentication operations.
///
/// This state is emitted when the bloc is performing an authentication operation,
/// such as checking the current user or logging out.
class AuthLoading extends AuthState {}

/// State representing a logged-in user.
///
/// This state contains the authenticated user entity and is emitted
/// when a user is successfully authenticated.
class AuthLoggedIn extends AuthState {
  /// The authenticated user
  final UserEntity user;

  /// Creates a new logged-in state with the authenticated user.
  const AuthLoggedIn(this.user);

  @override
  List<Object?> get props => [user];
}

/// State representing no authenticated user.
///
/// This state is emitted when there is no authenticated user,
/// either because the user has logged out or was never logged in.
class AuthLoggedOut extends AuthState {}

/// Error state during authentication operations.
///
/// This state is emitted when an error occurs during an authentication operation.
/// It contains an error message describing what went wrong.
class AuthError extends AuthState {
  /// The error message
  final String message;

  /// Creates a new error state with the specified error message.
  const AuthError(this.message);

  @override
  List<Object?> get props => [message];
}
