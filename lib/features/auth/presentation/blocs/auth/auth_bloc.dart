import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/services/auth_persistence_service.dart';
import '../../../../../core/usecases/usecase.dart';
import '../../../../../injection_container.dart';
import '../../../domain/usecases/get_current_user_usecase.dart';
import '../../../domain/usecases/logout_usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC that manages authentication state throughout the application.
///
/// This BLoC handles checking the current authentication status and logging out.
/// It uses the [GetCurrentUserUseCase] and [LogoutUseCase] to interact with the
/// authentication repository.
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  /// Use case for retrieving the current authenticated user
  final GetCurrentUserUseCase getCurrentUserUseCase;

  /// Use case for logging out the current user
  final LogoutUseCase logoutUseCase;

  /// Creates a new [AuthBloc] with the required use cases.
  AuthBloc({
    required this.getCurrentUserUseCase,
    required this.logoutUseCase,
  }) : super(AuthInitial()) {
    on<CheckAuthStatusEvent>(_onCheckAuthStatus);
    on<LogoutEvent>(_onLogout);
  }

  /// Handles the [CheckAuthStatusEvent] by checking if a user is currently authenticated.
  ///
  /// Emits [AuthLoading] while checking, then either:
  /// - [AuthLoggedIn] with the user if authenticated
  /// - [AuthLoggedOut] if no user is authenticated
  /// - [AuthError] if an error occurs
  Future<void> _onCheckAuthStatus(
    CheckAuthStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    // طباعة رسالة تصحيح للمساعدة في تشخيص المشكلة
    print('Checking auth status...');

    // لا نصدر حالة التحميل إذا كنا نتحقق من الحالة في الخلفية
    if (event.silent != true) {
      emit(AuthLoading());
    }

    final result = await getCurrentUserUseCase(const NoParams());
    result.fold(
      (failure) {
        print('Auth check failed: ${failure.message}');
        emit(AuthLoggedOut());
      },
      (user) {
        if (user == null) {
          print('User is not logged in (null user)');
          emit(AuthLoggedOut());
        } else {
          print('User is logged in: ${user.uid}');
          emit(AuthLoggedIn(user));
        }
      },
    );
  }

  /// Handles the [LogoutEvent] by logging out the current user.
  ///
  /// Emits [AuthLoading] while logging out, then either:
  /// - [AuthLoggedOut] on successful logout
  /// - [AuthError] if an error occurs
  Future<void> _onLogout(
    LogoutEvent event,
    Emitter<AuthState> emit,
  ) async {
    print('AuthBloc: Starting logout process...');
    emit(AuthLoading());

    // Clear saved credentials when logging out
    final authPersistenceService = sl<AuthPersistenceService>();
    await authPersistenceService.clearSavedCredentials();
    print('AuthBloc: Cleared saved credentials');

    final result = await logoutUseCase(const NoParams());
    result.fold(
      (failure) {
        print('AuthBloc: Logout failed: ${failure.message}');
        emit(AuthError(failure.message));
      },
      (_) {
        print('AuthBloc: Logout successful, emitting AuthLoggedOut');
        emit(AuthLoggedOut());
      },
    );
  }
}
