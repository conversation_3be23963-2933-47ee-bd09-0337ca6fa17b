import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/errors/failures.dart';
import '../../../domain/usecases/register_with_email_usecase.dart';
import 'register_event.dart';
import 'register_state.dart';

/// BLoC that manages the user registration process.
///
/// This BLoC handles the registration of new users with email and password,
/// and manages the state transitions during the registration process.
class RegisterBloc extends Bloc<RegisterEvent, RegisterState> {
  /// Use case for registering a new user with email and password
  final RegisterWithEmailUseCase registerUseCase;

  /// Creates a new [RegisterBloc] with the required use case.
  RegisterBloc(this.registerUseCase) : super(RegisterInitial()) {
    on<RegisterWithEmailSubmitted>(_onRegisterWithEmail);
  }

  /// Handles the [RegisterWithEmailSubmitted] event by attempting to register a new user.
  ///
  /// Emits [RegisterLoading] while registering, then either:
  /// - [RegisterSuccess] with the user on successful registration
  /// - [RegisterError] with an error message if registration fails
  Future<void> _onRegisterWithEmail(
    RegisterWithEmailSubmitted event,
    Emitter<RegisterState> emit,
  ) async {
    emit(RegisterLoading());
    final result = await registerUseCase(
      RegisterParams(
        name: event.name,
        email: event.email,
        phone: event.phone,
        password: event.password,
      ),
    );
    result.fold(
      (failure) => emit(RegisterError(_mapFailureToMessage(failure))),
      (user) => emit(RegisterSuccess(user)),
    );
  }

  /// Maps a [Failure] to a user-friendly error message.
  ///
  /// @param failure The failure to map
  /// @return A user-friendly error message
  String _mapFailureToMessage(Failure failure) {
    return failure.message.isNotEmpty ? failure.message : 'Operation failed';
  }
}
