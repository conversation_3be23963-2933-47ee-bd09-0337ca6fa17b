import 'package:equatable/equatable.dart';

/// Base class for all registration events.
///
/// This abstract class is extended by all events that can be dispatched to the [RegisterBloc].
/// It implements [Equatable] to allow for easy comparison of events.
abstract class RegisterEvent extends Equatable {
  /// Creates a new registration event.
  const RegisterEvent();

  @override
  List<Object?> get props => [];
}

/// Event dispatched when the user submits registration information.
///
/// This event is triggered when the user clicks the register button
/// after entering their information (name, email, phone, password).
class RegisterWithEmailSubmitted extends RegisterEvent {
  /// The user's display name
  final String name;

  /// The user's email address
  final String email;

  /// The user's phone number
  final String phone;

  /// The user's password
  final String password;

  /// Creates a new registration event with the provided user information.
  const RegisterWithEmailSubmitted({
    required this.name,
    required this.email,
    required this.phone,
    required this.password,
  });

  @override
  List<Object?> get props => [name, email, phone, password];
}
