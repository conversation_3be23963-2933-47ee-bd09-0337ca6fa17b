import 'package:equatable/equatable.dart';

import '../../../domain/entities/user_entity.dart';

/// Base class for all registration states.
///
/// This abstract class is extended by all possible states of the [RegisterBloc].
/// It implements [Equatable] to allow for easy comparison of states.
abstract class RegisterState extends Equatable {
  /// Creates a new registration state.
  const RegisterState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the registration bloc.
///
/// This state is used when no registration attempt has been made yet.
class RegisterInitial extends RegisterState {}

/// Loading state during registration operations.
///
/// This state is emitted when the bloc is performing a registration operation.
class RegisterLoading extends RegisterState {}

/// State representing a successful registration.
///
/// This state contains the registered user entity and is emitted
/// when a user is successfully registered.
class RegisterSuccess extends RegisterState {
  /// The registered user
  final UserEntity user;

  /// Creates a new success state with the registered user.
  const RegisterSuccess(this.user);

  @override
  List<Object?> get props => [user];
}

/// Error state during registration operations.
///
/// This state is emitted when an error occurs during a registration operation.
/// It contains an error message describing what went wrong.
class RegisterError extends RegisterState {
  /// The error message
  final String message;

  /// Creates a new error state with the specified error message.
  const RegisterError(this.message);

  @override
  List<Object?> get props => [message];
}
