import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/errors/failures.dart';
import '../../../domain/usecases/login_with_email_usecase.dart';
import 'login_event.dart';
import 'login_state.dart';

/// BLoC that manages the login process.
///
/// This BLoC handles email/password login method
/// and manages the state transitions during the login process.
class LoginBloc extends Bloc<LoginEvent, LoginState> {
  /// Use case for logging in with email and password
  final LoginWithEmailUseCase loginWithEmailUseCase;

  /// Creates a new [LoginBloc] with the required use cases.
  LoginBloc({
    required this.loginWithEmailUseCase,
  }) : super(LoginInitial()) {
    on<LoginWithEmailSubmitted>(_onLoginWithEmail);
  }

  /// Handles the [LoginWithEmailSubmitted] event by attempting to log in with email and password.
  ///
  /// Emits [LoginLoading] while logging in, then either:
  /// - [LoginSuccess] with the user on successful login
  /// - [LoginError] with an error message if login fails
  Future<void> _onLoginWithEmail(
    LoginWithEmailSubmitted event,
    Emitter<LoginState> emit,
  ) async {
    emit(LoginLoading());
    final result = await loginWithEmailUseCase(
      LoginParams(email: event.email, password: event.password),
    );
    result.fold(
      (failure) => emit(LoginError(_mapFailureToMessage(failure))),
      (user) => emit(LoginSuccess(user)),
    );
  }

  /// Maps a [Failure] to a user-friendly error message.
  ///
  /// @param failure The failure to map
  /// @return A user-friendly error message
  String _mapFailureToMessage(Failure failure) {
    return failure.message.isNotEmpty ? failure.message : 'Operation failed';
  }
}
