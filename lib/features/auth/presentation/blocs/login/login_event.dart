import 'package:equatable/equatable.dart';

/// Base class for all login events.
///
/// This abstract class is extended by all events that can be dispatched to the [LoginBloc].
/// It implements [Equatable] to allow for easy comparison of events.
abstract class LoginEvent extends Equatable {
  /// Creates a new login event.
  const LoginEvent();

  @override
  List<Object?> get props => [];
}

/// Event dispatched when the user submits email and password for login.
///
/// This event is triggered when the user clicks the email login button
/// after entering their credentials.
class LoginWithEmailSubmitted extends LoginEvent {
  /// The email address entered by the user
  final String email;

  /// The password entered by the user
  final String password;

  /// Creates a new email login event with the provided credentials.
  const LoginWithEmailSubmitted({required this.email, required this.password});

  @override
  List<Object?> get props => [email, password];
}
