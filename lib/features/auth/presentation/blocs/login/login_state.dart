import 'package:equatable/equatable.dart';

import '../../../domain/entities/user_entity.dart';

/// Base class for all login states.
///
/// This abstract class is extended by all possible states of the [LoginBloc].
/// It implements [Equatable] to allow for easy comparison of states.
abstract class LoginState extends Equatable {
  /// Creates a new login state.
  const LoginState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the login bloc.
///
/// This state is used when no login attempt has been made yet.
class LoginInitial extends LoginState {}

/// Loading state during login operations.
///
/// This state is emitted when the bloc is performing a login operation,
/// such as authenticating with email, Google, or Facebook.
class LoginLoading extends LoginState {}

/// State representing a successful login.
///
/// This state contains the authenticated user entity and is emitted
/// when a user is successfully logged in.
class LoginSuccess extends LoginState {
  /// The authenticated user
  final UserEntity user;

  /// Creates a new success state with the authenticated user.
  const LoginSuccess(this.user);

  @override
  List<Object?> get props => [user];
}

/// Error state during login operations.
///
/// This state is emitted when an error occurs during a login operation.
/// It contains an error message describing what went wrong.
class LoginError extends LoginState {
  /// The error message
  final String message;

  /// Creates a new error state with the specified error message.
  const LoginError(this.message);

  @override
  List<Object?> get props => [message];
}
