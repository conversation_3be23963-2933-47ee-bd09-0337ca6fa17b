import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';

/// Abstract interface representing the authentication contract.
///
/// Following Clean Architecture principles, use cases interact with this interface
/// without knowing the implementation details of the data layer (e.g., Firebase).
abstract class AuthRepository {
  /// Logs in a user with email and password.
  ///
  /// @param email The user's email address
  /// @param password The user's password
  /// @return An [Either] with either a [Failure] or the authenticated [UserEntity]
  Future<Either<Failure, UserEntity>> loginWithEmail(
      String email, String password);

  /// Registers a new user with email, password, name, and phone number.
  ///
  /// @param name The user's display name
  /// @param email The user's email address
  /// @param phone The user's phone number
  /// @param password The user's password
  /// @return An [Either] with either a [Failure] or the registered [UserEntity]
  Future<Either<Failure, UserEntity>> registerWithEmail(
      String name, String email, String phone, String password);

  /// Logs out the current user.
  ///
  /// @return An [Either] with either a [Failure] or void on success
  Future<Either<Failure, void>> logout();

  /// Gets the currently authenticated user, if any.
  ///
  /// @return An [Either] with either a [Failure] or the current [UserEntity] (null if not logged in)
  Future<Either<Failure, UserEntity?>> getCurrentUser();

  /// Sends a password reset email to the specified email address.
  ///
  /// @param email The email address to send the password reset link to
  /// @return An [Either] with either a [Failure] or void on success
  Future<Either<Failure, void>> resetPassword(String email);
}
