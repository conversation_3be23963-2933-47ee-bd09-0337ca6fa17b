/// Represents a user entity within the system.
///
/// This entity contains the core user information needed across the application,
/// including identification, personal details, and profile image.
class UserEntity {
  /// Unique identifier for the user (typically from Firebase Auth)
  final String uid;

  /// User's display name
  final String name;

  /// User's email address
  final String email;

  /// User's phone number
  final String phone;

  /// URL to the user's profile image
  final String imageUrl;

  /// Creates a new user entity with the specified properties.
  const UserEntity({
    required this.uid,
    required this.name,
    required this.email,
    required this.phone,
    required this.imageUrl,
  });
}
