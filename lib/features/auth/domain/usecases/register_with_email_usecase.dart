import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Use case for registering a new user with email and password.
///
/// This use case implements the [UseCase] interface and handles the registration
/// operation by delegating to the auth repository.
class RegisterWithEmailUseCase implements UseCase<UserEntity, RegisterParams> {
  /// Repository for authentication operations
  final AuthRepository repository;

  /// Creates a new instance with the required repository.
  RegisterWithEmailUseCase(this.repository);

  /// Executes the registration operation with the provided parameters.
  ///
  /// @param params The registration parameters (name, email, phone, password)
  /// @return Either a [Failure] or the registered [UserEntity]
  @override
  Future<Either<Failure, UserEntity>> call(RegisterParams params) {
    return repository.registerWithEmail(
      params.name,
      params.email,
      params.phone,
      params.password,
    );
  }
}

/// Parameters for the register with email use case.
///
/// This class encapsulates all the parameters needed for user registration.
class RegisterParams {
  /// The user's display name
  final String name;

  /// The user's email address
  final String email;

  /// The user's phone number
  final String phone;

  /// The user's password
  final String password;

  /// Creates a new instance with all the required registration parameters.
  RegisterParams({
    required this.name,
    required this.email,
    required this.phone,
    required this.password,
  });
}
