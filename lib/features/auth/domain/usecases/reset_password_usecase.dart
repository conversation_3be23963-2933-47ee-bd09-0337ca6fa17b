import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

/// Use case for sending a password reset email.
///
/// This use case implements the [UseCase] interface and handles sending
/// a password reset email by delegating to the auth repository.
class ResetPasswordUseCase implements UseCase<void, ResetPasswordParams> {
  /// Repository for authentication operations
  final AuthRepository repository;

  /// Creates a new instance with the required repository.
  ResetPasswordUseCase(this.repository);

  /// Executes the password reset operation with the provided email.
  ///
  /// @param params The parameters containing the email address
  /// @return Either a [Failure] or void on successful email sending
  @override
  Future<Either<Failure, void>> call(ResetPasswordParams params) {
    return repository.resetPassword(params.email);
  }
}

/// Parameters for the reset password use case.
///
/// This class encapsulates the email parameter needed for the password reset operation.
class ResetPasswordParams {
  /// The email address to send the password reset link to
  final String email;

  /// Creates a new instance with the required email.
  ResetPasswordParams(this.email);
}
