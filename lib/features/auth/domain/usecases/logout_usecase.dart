import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

/// Use case for logging out the current user.
///
/// This use case implements the [UseCase] interface and handles the logout
/// operation by delegating to the auth repository. It doesn't require any parameters.
class LogoutUseCase implements UseCase<void, NoParams> {
  /// Repository for authentication operations
  final AuthRepository repository;

  /// Creates a new instance with the required repository.
  LogoutUseCase(this.repository);

  /// Executes the logout operation.
  ///
  /// @param params No parameters are needed for this operation
  /// @return Either a [Failure] or void on successful logout
  @override
  Future<Either<Failure, void>> call(NoParams params) {
    return repository.logout();
  }
}
