import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Use case for logging in with email and password.
///
/// This use case implements the [UseCase] interface and handles the login
/// operation by delegating to the auth repository.
class LoginWithEmailUseCase implements UseCase<UserEntity, LoginParams> {
  /// Repository for authentication operations
  final AuthRepository repository;

  /// Creates a new instance with the required repository.
  LoginWithEmailUseCase(this.repository);

  /// Executes the login operation with the provided parameters.
  ///
  /// @param params The login parameters (email and password)
  /// @return Either a [Failure] or the authenticated [UserEntity]
  @override
  Future<Either<Failure, UserEntity>> call(LoginParams params) async {
    return await repository.loginWithEmail(params.email, params.password);
  }
}

/// Parameters for the login with email use case.
///
/// This class encapsulates the email and password parameters
/// for easier passing to the use case.
class LoginParams {
  /// The user's email address
  final String email;

  /// The user's password
  final String password;

  /// Creates a new instance with the required email and password.
  LoginParams({required this.email, required this.password});
}
