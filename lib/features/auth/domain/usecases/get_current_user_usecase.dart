import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/user_entity.dart';
import '../repositories/auth_repository.dart';

/// Use case for retrieving the currently authenticated user, if any.
///
/// This use case implements the [UseCase] interface and handles retrieving
/// the current user by delegating to the auth repository. It doesn't require any parameters.
class GetCurrentUserUseCase implements UseCase<UserEntity?, NoParams> {
  /// Repository for authentication operations
  final AuthRepository repository;

  /// Creates a new instance with the required repository.
  GetCurrentUserUseCase(this.repository);

  /// Executes the get current user operation.
  ///
  /// @param params No parameters are needed for this operation
  /// @return Either a [Failure] or the current [UserEntity] (null if not logged in)
  @override
  Future<Either<Failure, UserEntity?>> call(NoParams params) {
    return repository.getCurrentUser();
  }
}
