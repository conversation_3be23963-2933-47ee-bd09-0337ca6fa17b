import '../../domain/entities/user_entity.dart';

/// Model representing a user as received from Firebase.
///
/// This model extends the [UserEntity] from the domain layer and adds
/// serialization/deserialization methods for Firebase operations.
class UserModel extends UserEntity {
  /// Creates a new user model with the specified properties.
  const UserModel({
    required super.uid,
    required super.name,
    required super.email,
    required super.phone,
    required super.imageUrl,
  });

  /// Factory constructor to create a [UserModel] from a map.
  ///
  /// This is used to convert Firestore document data to a user model.
  ///
  /// @param map The map containing user data
  /// @return A new [UserModel] instance
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      imageUrl: map['imageUrl'] ?? '',
    );
  }

  /// Converts this model to a map for Firestore storage.
  ///
  /// @return A map containing the user's data
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'phone': phone,
      'imageUrl': imageUrl,
    };
  }
}
