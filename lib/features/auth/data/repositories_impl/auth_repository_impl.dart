import 'package:dartz/dartz.dart';

import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_local_data_source.dart'; // For local storage if needed
import '../datasources/auth_remote_data_source.dart';

/// Implementation of the [AuthRepository] interface.
///
/// This class serves as a bridge between the domain layer and the data layer,
/// handling the communication with the remote data source and optionally
/// a local data source for caching operations.
class AuthRepositoryImpl implements AuthRepository {
  /// Remote data source for authentication operations
  final AuthRemoteDataSource remoteDataSource;

  /// Optional local data source for caching user data
  final AuthLocalDataSource? localDataSource;

  /// Creates a new instance with the required remote data source
  /// and an optional local data source.
  AuthRepositoryImpl({
    required this.remoteDataSource,
    this.localDataSource,
  });

  /// Logs in a user with email and password.
  @override
  Future<Either<Failure, UserEntity>> loginWithEmail(
      String email, String password) async {
    try {
      final userModel = await remoteDataSource.loginWithEmail(email, password);

      // If "remember me" functionality is needed:
      // localDataSource?.cacheUserEmail(email);

      return Right(userModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  /// Registers a new user with email, password, name, and phone number.
  @override
  Future<Either<Failure, UserEntity>> registerWithEmail(
    String name,
    String email,
    String phone,
    String password,
  ) async {
    try {
      final userModel = await remoteDataSource.registerWithEmail(
          name, email, phone, password);
      return Right(userModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  /// Logs out the current user from all authentication providers.
  @override
  Future<Either<Failure, void>> logout() async {
    try {
      await remoteDataSource.logout();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  /// Gets the currently authenticated user, if any.
  @override
  Future<Either<Failure, UserEntity?>> getCurrentUser() async {
    try {
      final userModel = await remoteDataSource.getCurrentUser();
      return Right(userModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  /// Sends a password reset email to the specified email address.
  @override
  Future<Either<Failure, void>> resetPassword(String email) async {
    try {
      await remoteDataSource.resetPassword(email);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}
