import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as fb;

import '../../../../core/errors/exceptions.dart';
import '../models/user_model.dart';

/// Interface for remote authentication data operations.
///
/// This interface defines methods for all authentication-related operations
/// that interact with remote services like Firebase Authentication.
abstract class AuthRemoteDataSource {
  /// Logs in a user with email and password.
  ///
  /// @param email The user's email address
  /// @param password The user's password
  /// @return A [UserModel] representing the authenticated user
  Future<UserModel> loginWithEmail(String email, String password);

  /// Registers a new user with email, password, name, and phone number.
  ///
  /// @param name The user's display name
  /// @param email The user's email address
  /// @param phone The user's phone number
  /// @param password The user's password
  /// @return A [UserModel] representing the registered user
  Future<UserModel> registerWithEmail(
    String name,
    String email,
    String phone,
    String password,
  );

  /// Logs out the current user from all authentication providers.
  Future<void> logout();

  /// Gets the currently authenticated user, if any.
  ///
  /// @return A [UserModel] representing the current user, or null if not logged in
  Future<UserModel?> getCurrentUser();

  /// Sends a password reset email to the specified email address.
  ///
  /// @param email The email address to send the password reset link to
  Future<void> resetPassword(String email);
}

/// Implementation of [AuthRemoteDataSource] using Firebase services.
///
/// This class handles authentication operations using Firebase Authentication,
/// Google Sign-In, Facebook Auth, and stores user data in Firestore.
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  /// Firebase Authentication instance for auth operations
  final fb.FirebaseAuth firebaseAuth;

  /// Firestore instance for user data storage
  final FirebaseFirestore firestore;

  /// Creates a new instance with the required dependencies.
  AuthRemoteDataSourceImpl({
    required this.firebaseAuth,
    required this.firestore,
  });

  /// Logs in a user with email and password.
  @override
  Future<UserModel> loginWithEmail(String email, String password) async {
    try {
      // Sign in with Firebase Authentication
      final userCredential = await firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = userCredential.user;
      if (user == null) {
        throw ServerException(message: 'Login failed - User does not exist');
      }

      try {
        // Fetch the user document from Firestore to include name/image/phone
        final doc = await firestore.collection('users').doc(user.uid).get();
        if (!doc.exists) {
          throw ServerException(
              message: 'User document not found in Firestore');
        }
        final data = doc.data()!;

        // Build UserModel from document data
        return UserModel(
          uid: user.uid,
          name: data['name'] ?? '',
          email: data['email'] ?? user.email ?? '',
          phone: data['phone'] ?? user.phoneNumber ?? '',
          imageUrl: data['imageUrl'] ?? '',
        );
      } catch (firestoreError) {
        // إذا فشل استرداد بيانات المستخدم من Firestore، استخدم البيانات الأساسية من Firebase Auth
        return UserModel(
          uid: user.uid,
          name: user.displayName ?? '',
          email: user.email ?? '',
          phone: user.phoneNumber ?? '',
          imageUrl: user.photoURL ?? '',
        );
      }
    } catch (e) {
      // معالجة خطأ PigeonUserDetails
      if (e.toString().contains('PigeonUserDetails')) {
        // بدلاً من تسجيل الخروج، نحاول استخدام البيانات الأساسية من Firebase Auth
        final user = firebaseAuth.currentUser;
        if (user != null) {
          return UserModel(
            uid: user.uid,
            name: user.displayName ?? '',
            email: user.email ?? '',
            phone: user.phoneNumber ?? '',
            imageUrl: user.photoURL ?? '',
          );
        }
      }
      throw ServerException(message: e.toString());
    }
  }

  /// Registers a new user with email, password, name, and phone number.
  @override
  Future<UserModel> registerWithEmail(
    String name,
    String email,
    String phone,
    String password,
  ) async {
    try {
      // Create a new user with Firebase Authentication
      final userCredential = await firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = userCredential.user;
      if (user == null) {
        throw ServerException(message: 'Failed to create account');
      }

      try {
        // Save user data in Firestore "users" collection
        final userData = {
          'name': name,
          'email': email,
          'phone': phone,
          'imageUrl': '', // Image URL field is left empty initially
        };
        await firestore.collection('users').doc(user.uid).set(userData);

        // Return the UserModel
        return UserModel(
          uid: user.uid,
          name: name,
          email: email,
          phone: phone,
          imageUrl: '', // Empty at creation
        );
      } catch (firestoreError) {
        // إذا فشل حفظ البيانات في Firestore، استخدم البيانات الأساسية
        return UserModel(
          uid: user.uid,
          name: name,
          email: email,
          phone: phone,
          imageUrl: '',
        );
      }
    } catch (e) {
      // معالجة خطأ PigeonUserDetails
      if (e.toString().contains('PigeonUserDetails')) {
        // بدلاً من تسجيل الخروج، نحاول استخدام البيانات الأساسية من Firebase Auth
        final user = firebaseAuth.currentUser;
        if (user != null) {
          return UserModel(
            uid: user.uid,
            name: name,
            email: email,
            phone: phone,
            imageUrl: '',
          );
        }
      }
      throw ServerException(message: e.toString());
    }
  }

  /// Logs out the current user from all authentication providers.
  @override
  Future<void> logout() async {
    await firebaseAuth.signOut();
  }

  /// Gets the currently authenticated user, if any.
  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      // طباعة رسالة تصحيح للمساعدة في تشخيص المشكلة
      print('Checking current user in Firebase Auth...');

      final user = firebaseAuth.currentUser;
      if (user == null) {
        print('No current user found in Firebase Auth');
        return null;
      }

      print('Current user found in Firebase Auth: ${user.uid}');

      // Fetch the user document from Firestore to get name and imageUrl
      try {
        print('Fetching user data from Firestore...');
        final doc = await firestore.collection('users').doc(user.uid).get();

        if (!doc.exists) {
          print('User document not found in Firestore. Creating one...');

          // إنشاء وثيقة المستخدم إذا لم تكن موجودة
          final userData = {
            'name': user.displayName ?? 'User',
            'email': user.email ?? '',
            'phone': user.phoneNumber ?? '',
            'imageUrl': user.photoURL ?? '',
          };

          await firestore.collection('users').doc(user.uid).set(userData);

          return UserModel(
            uid: user.uid,
            name: userData['name'] ?? '',
            email: userData['email'] ?? '',
            phone: userData['phone'] ?? '',
            imageUrl: userData['imageUrl'] ?? '',
          );
        }

        print('User document found in Firestore');
        final data = doc.data()!;
        return UserModel(
          uid: user.uid,
          name: data['name'] ?? '',
          email: data['email'] ?? user.email ?? '',
          phone: data['phone'] ?? user.phoneNumber ?? '',
          imageUrl: data['imageUrl'] ?? '',
        );
      } catch (firestoreError) {
        print('Error fetching user data from Firestore: $firestoreError');
        // إذا فشل استرداد بيانات المستخدم من Firestore، استخدم البيانات الأساسية من Firebase Auth
        return UserModel(
          uid: user.uid,
          name: user.displayName ?? 'User',
          email: user.email ?? '',
          phone: user.phoneNumber ?? '',
          imageUrl: user.photoURL ?? '',
        );
      }
    } catch (e) {
      print('Error in getCurrentUser: $e');
      // معالجة خطأ PigeonUserDetails
      if (e.toString().contains('PigeonUserDetails')) {
        print('PigeonUserDetails error detected, trying to recover...');
        // بدلاً من تسجيل الخروج، نحاول استخدام البيانات الأساسية من Firebase Auth
        final user = firebaseAuth.currentUser;
        if (user != null) {
          return UserModel(
            uid: user.uid,
            name: user.displayName ?? 'User',
            email: user.email ?? '',
            phone: user.phoneNumber ?? '',
            imageUrl: user.photoURL ?? '',
          );
        }
        return null;
      }

      // لا نرمي استثناء هنا، بل نعيد null لتجنب تعطل التطبيق
      print('Returning null instead of throwing exception');
      return null;
    }
  }

  /// Sends a password reset email to the specified email address.
  @override
  Future<void> resetPassword(String email) async {
    try {
      await firebaseAuth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }
}
