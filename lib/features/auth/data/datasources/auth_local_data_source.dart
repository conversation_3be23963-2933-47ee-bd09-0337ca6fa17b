import 'package:shared_preferences/shared_preferences.dart';

abstract class AuthLocalDataSource {
  Future<void> cacheUserEmail(String email);
  Future<String?> getCachedUserEmail();
  Future<void> clearCachedUserEmail();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  static const String cachedEmailKey = 'CACHED_EMAIL';

  final SharedPreferences sharedPreferences;

  AuthLocalDataSourceImpl(this.sharedPreferences);

  @override
  Future<void> cacheUserEmail(String email) async {
    await sharedPreferences.setString(cachedEmailKey, email);
  }

  @override
  Future<String?> getCachedUserEmail() async {
    return sharedPreferences.getString(cachedEmailKey);
  }

  @override
  Future<void> clearCachedUserEmail() async {
    await sharedPreferences.remove(cachedEmailKey);
  }
}
