import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/entities/notification_entity.dart';

class NotificationModel extends NotificationEntity {
  const NotificationModel({
    required super.id,
    required super.title,
    required super.body,
    required super.isRead,
    required super.timestamp,
  });

  factory NotificationModel.fromMap(String id, Map<String, dynamic> map) {
    // معالجة آمنة للوقت
    DateTime? ts;
    final rawTs = map['timestamp'];
    if (rawTs is Timestamp) ts = rawTs.toDate();

    return NotificationModel(
      id: id,
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      isRead: map['isRead'] ?? false,
      timestamp: ts,
    );
  }

  Map<String, dynamic> toMap() => {
        'title': title,
        'body': body,
        'isRead': isRead,
        'timestamp': timestamp,
      };

  // نسخة معدّلة (لاستخدامها في الـ Bloc)
  NotificationModel copyWith({bool? isRead}) => NotificationModel(
        id: id,
        title: title,
        body: body,
        isRead: isRead ?? this.isRead,
        timestamp: timestamp,
      );
}
