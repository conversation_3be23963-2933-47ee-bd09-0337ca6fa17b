import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/notification_model.dart';

/// واجهة مصدر البيانات البعيد الخاصة بالإشعارات.
abstract class NotificationsRemoteDataSource {
  /// جلب جميع إشعارات المستخدم (مرتبة تنازليًا حسب التاريخ).
  Future<List<NotificationModel>> fetchNotifications(String uid);

  /// وسم إشعار واحد كمقروء.
  Future<void> markNotificationRead(String uid, String notificationId);
}

/// تنفيذ مصدر البيانات باستخدام Cloud Firestore.
class NotificationsRemoteDataSourceImpl
    implements NotificationsRemoteDataSource {
  final FirebaseFirestore firestore;

  NotificationsRemoteDataSourceImpl({required this.firestore});

  @override
  Future<List<NotificationModel>> fetchNotifications(String uid) async {
    final snapshot = await firestore
        .collection('users')
        .doc(uid)
        .collection('notifications')
        .orderBy('timestamp', descending: true)
        .get();

    // ملاحظة: fromMap يتوقع وسيطين (id ثم الخريطة)
    return snapshot.docs.map((doc) {
      final data = doc.data();
      return NotificationModel.fromMap(doc.id, data);
    }).toList();
  }

  @override
  Future<void> markNotificationRead(String uid, String notificationId) {
    return firestore
        .collection('users')
        .doc(uid)
        .collection('notifications')
        .doc(notificationId)
        .update({'isRead': true});
  }
}
