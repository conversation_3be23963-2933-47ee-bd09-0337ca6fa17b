import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/notification_entity.dart';
import '../repositories/notifications_repository.dart';

class NotificationParams {
  final String uid;
  const NotificationParams(this.uid);
}

class GetNotificationsUseCase
    implements UseCase<List<NotificationEntity>, NotificationParams> {
  final NotificationsRepository repository;
  GetNotificationsUseCase(this.repository);

  @override
  Future<Either<Failure, List<NotificationEntity>>> call(
      NotificationParams params) {
    return repository.getNotifications(params.uid);
  }
}
