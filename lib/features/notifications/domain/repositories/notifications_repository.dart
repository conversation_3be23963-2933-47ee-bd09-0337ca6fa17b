import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../data/datasources/notifications_remote_data_source.dart';
import '../entities/notification_entity.dart';

abstract class NotificationsRepository {
  Future<Either<Failure, List<NotificationEntity>>> getNotifications(
      String uid);
  Future<Either<Failure, void>> markAsRead(String uid, String notificationId);
}

class NotificationsRepositoryImpl implements NotificationsRepository {
  final NotificationsRemoteDataSource remote;

  NotificationsRepositoryImpl({required this.remote});

  @override
  Future<Either<Failure, List<NotificationEntity>>> getNotifications(
      String uid) async {
    try {
      final models = await remote.fetchNotifications(uid);
      return Right(models);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> markAsRead(
      String uid, String notificationId) async {
    try {
      await remote.markNotificationRead(uid, notificationId);
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
