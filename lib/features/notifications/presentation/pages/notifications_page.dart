import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// Staggered animations removed
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/animated_toast.dart';
import '../bloc/notifications_bloc.dart';
import '../bloc/notifications_event.dart';
import '../bloc/notifications_state.dart';
import '../../domain/entities/notification_entity.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    with SingleTickerProviderStateMixin {
  // Filtro seleccionado: 'all', 'unread', 'read'
  String _selectedFilter = 'all';

  // Controladores de animación
  late AnimationController _filterAnimationController;
  late Animation<double> _headerAnimation;
  late Animation<double> _searchFocusAnimation;

  // Controlador para la búsqueda
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  String _searchQuery = '';

  // Lista de búsquedas recientes y sugerencias
  final List<String> _recentSearches = ['تحديث', 'بطاقة', 'عرض', 'مهم'];
  List<String> _searchSuggestions = [];

  // Controlador para el desplazamiento
  final ScrollController _scrollController = ScrollController();

  // Estados de UI
  bool _isSearchFocused = false;
  final bool _isSearchVisible = false;
  bool _isRefreshing = false;
  bool _showSuggestions = false;

  // Colores del tema
  late Color _primaryColor;
  late Color _backgroundColor;
  late Color _cardColor;
  late Color _textColor;
  late Color _secondaryTextColor;
  late Color _accentColor;

  @override
  void initState() {
    super.initState();

    // Inicializar controlador de animación
    _filterAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    // Configurar animación del encabezado
    _headerAnimation = CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeOutQuart,
    );

    // Configurar animación para el foco del campo de búsqueda
    _searchFocusAnimation = CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeOutCubic,
    );

    // Configurar listener para el foco del campo de búsqueda
    _searchFocusNode.addListener(_onSearchFocusChange);

    // Configurar listener para el controlador de búsqueda
    _searchController.addListener(_onSearchInputChange);

    // Iniciar animación al cargar la página
    _filterAnimationController.forward();
  }

  @override
  void dispose() {
    _filterAnimationController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Maneja los cambios en el foco del campo de búsqueda
  void _onSearchFocusChange() {
    setState(() {
      _isSearchFocused = _searchFocusNode.hasFocus;
      _showSuggestions = _isSearchFocused && _searchQuery.isNotEmpty;
    });
  }

  /// Maneja los cambios en el texto del campo de búsqueda
  void _onSearchInputChange() {
    final query = _searchController.text;

    if (query != _searchQuery) {
      setState(() {
        _searchQuery = query;
        _showSuggestions = _isSearchFocused && query.isNotEmpty;

        // Generar sugerencias basadas en la consulta actual
        if (query.isNotEmpty) {
          _searchSuggestions = _generateSuggestions(query);
        } else {
          _searchSuggestions = [];
        }
      });
    }
  }

  /// Genera sugerencias basadas en la consulta de búsqueda
  List<String> _generateSuggestions(String query) {
    // Filtrar búsquedas recientes que coincidan con la consulta
    final suggestions = _recentSearches
        .where((search) => search.toLowerCase().contains(query.toLowerCase()))
        .toList();

    // Añadir sugerencias predefinidas basadas en el contexto
    if (query.contains('تحديث')) {
      suggestions.add('تحديث النظام');
      suggestions.add('تحديث التطبيق');
    } else if (query.contains('بطاقة')) {
      suggestions.add('بطاقة تهنئة');
      suggestions.add('بطاقة عيد ميلاد');
    }

    return suggestions;
  }

  /// Construye el panel de sugerencias de búsqueda
  Widget _buildSuggestionsPanel() {
    if (!_showSuggestions || _searchSuggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 4, left: 24, right: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.transparent,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Título de sugerencias
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    Icon(
                      Icons.history_rounded,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context).searchSuggestions,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),

              // Divider
              Divider(height: 1, color: Colors.grey[200]),

              // Lista de sugerencias
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _searchSuggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = _searchSuggestions[index];
                  return ListTile(
                    dense: true,
                    leading: Icon(
                      Icons.search_rounded,
                      size: 18,
                      color: AppColors.primaryColor,
                    ),
                    title: Text(
                      suggestion,
                      style: GoogleFonts.cairo(fontSize: 14),
                    ),
                    onTap: () {
                      setState(() {
                        _searchController.text = suggestion;
                        _searchQuery = suggestion;
                        _showSuggestions = false;
                        _searchFocusNode.unfocus();
                      });
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Inicializar colores del tema basados en el modo claro/oscuro
  void _initThemeColors(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    _primaryColor = AppColors.primaryColor;
    _accentColor = const Color(
        0xFFE91E63); // Color de acento rosa para complementar el púrpura
    _backgroundColor = isDarkMode ? const Color(0xFF121212) : Colors.grey[50]!;
    _cardColor = isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;
    _textColor = isDarkMode ? Colors.white : Colors.grey[850]!;
    _secondaryTextColor = isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
  }

  /// Construye el encabezado moderno con barra de búsqueda y filtros
  Widget _buildHeader() {
    return AnimatedBuilder(
      animation: _filterAnimationController,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.only(top: 100, bottom: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.primaryColor,
                AppColors.primaryColor.withOpacity(0.85),
              ],
            ),
            borderRadius: const BorderRadius.vertical(
              bottom: Radius.circular(32),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 16,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Título con animación
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Transform.translate(
                  offset:
                      Offset(0, 20 * (1 - _filterAnimationController.value)),
                  child: Opacity(
                    opacity: _filterAnimationController.value,
                    child: Row(
                      children: [
                        // زر العودة
                        GestureDetector(
                          onTap: () => Navigator.of(context).pop(),
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.15),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.arrow_back_ios_new_rounded,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.15),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.notifications_active,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          AppLocalizations.of(context).notifications,
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 26,
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Barra de búsqueda con diseño moderno
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Transform.translate(
                  offset:
                      Offset(0, 30 * (1 - _filterAnimationController.value)),
                  child: Opacity(
                    opacity: _filterAnimationController.value,
                    child: Stack(
                      children: [
                        // Campo de búsqueda con diseño mejorado
                        Container(
                          height: 56,
                          decoration: BoxDecoration(
                            // Cambiar el color de fondo a un blanco más opaco para mayor contraste
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white,
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: TextField(
                              controller: _searchController,
                              focusNode: _searchFocusNode,
                              // تحسين لون النص للدارك مود
                              style: GoogleFonts.cairo(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white
                                    : Colors.grey[800],
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              decoration: InputDecoration(
                                hintText: AppLocalizations.of(context)
                                    .searchInNotifications,
                                // تحسين لون النص التوضيحي للدارك مود
                                hintStyle: GoogleFonts.cairo(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.grey[400]
                                      : Colors.grey[600],
                                  fontSize: 16,
                                ),
                                prefixIcon: AnimatedContainer(
                                  duration: const Duration(milliseconds: 300),
                                  padding: const EdgeInsets.all(8),
                                  child: Icon(
                                    Icons.search_rounded,
                                    // Cambiar el color del icono de búsqueda a un color más visible
                                    color: _isSearchFocused
                                        ? AppColors.primaryColor
                                        : Colors.grey[700],
                                    size: 22,
                                  ),
                                ),
                                suffixIcon: _searchQuery.isNotEmpty
                                    ? IconButton(
                                        icon: Container(
                                          padding: const EdgeInsets.all(2),
                                          decoration: BoxDecoration(
                                            // Cambiar el color del fondo del botón de borrar
                                            color: Colors.grey[300],
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            Icons.close_rounded,
                                            // Cambiar el color del icono de borrar
                                            color: Colors.grey[800],
                                            size: 16,
                                          ),
                                        ),
                                        onPressed: () {
                                          setState(() {
                                            _searchController.clear();
                                            _searchQuery = '';
                                            _showSuggestions = false;
                                          });
                                        },
                                      )
                                    : null,
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 16,
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  borderSide: BorderSide(
                                    color: Colors.transparent,
                                    width: 1.5,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  borderSide: BorderSide(
                                    // Cambiar el color del borde cuando está enfocado
                                    color:
                                        AppColors.primaryColor.withOpacity(0.7),
                                    width: 1.5,
                                  ),
                                ),
                              ),
                              onChanged: (value) {
                                setState(() {
                                  _searchQuery = value;
                                  _showSuggestions = value.isNotEmpty;

                                  // Generar sugerencias basadas en la consulta actual
                                  if (value.isNotEmpty) {
                                    _searchSuggestions =
                                        _generateSuggestions(value);
                                  } else {
                                    _searchSuggestions = [];
                                  }
                                });
                              },
                            ),
                          ),
                        ),

                        // Panel de sugerencias
                        if (_showSuggestions && _searchSuggestions.isNotEmpty)
                          Positioned(
                            top: 60,
                            left: 0,
                            right: 0,
                            child: _buildSuggestionsPanel(),
                          ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Filtros con animación
              Transform.translate(
                offset: Offset(0, 40 * (1 - _filterAnimationController.value)),
                child: Opacity(
                  opacity: _filterAnimationController.value,
                  child: SizedBox(
                    height: 40,
                    child: ListView(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      children: [
                        _buildFilterChip(AppLocalizations.of(context).all,
                            'all', Icons.notifications_none_rounded),
                        const SizedBox(width: 12),
                        _buildFilterChip(AppLocalizations.of(context).unread,
                            'unread', Icons.mark_email_unread_rounded),
                        const SizedBox(width: 12),
                        _buildFilterChip(AppLocalizations.of(context).read,
                            'read', Icons.done_all_rounded),
                        const SizedBox(width: 12),
                        _buildFilterChip(AppLocalizations.of(context).today,
                            'today', Icons.today_rounded),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Construye un chip de filtro con diseño moderno y minimalista
  Widget _buildFilterChip(String label, String value, IconData icon) {
    final bool isSelected = _selectedFilter == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = value;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.surface
              : AppColors.primaryColor.withOpacity(0.7),
          borderRadius: BorderRadius.circular(20),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected
                  ? AppColors.primaryColor
                  : Colors.white,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: GoogleFonts.cairo(
                color: isSelected
                    ? AppColors.primaryColor
                    : Colors.white,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Inicializar colores del tema
    _initThemeColors(context);

    // Verificar si hay un usuario autenticado
    final uid = FirebaseAuth.instance.currentUser?.uid;
    if (uid == null) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.primaryColor,
          elevation: 0,
          title: Text(
            AppLocalizations.of(context).notifications,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontSize: 20,
            ),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.notifications_off_outlined,
                  size: 60,
                  color: AppColors.primaryColor.withValues(alpha: 0.5),
                ),
              ),
              const SizedBox(height: 32),
              Text(
                AppLocalizations.of(context).pleaseLogin,
                style: GoogleFonts.cairo(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.headlineSmall?.color ??
                         Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'يجب تسجيل الدخول لعرض الإشعارات',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.pushReplacementNamed(context, '/login');
                },
                icon: const Icon(Icons.login_rounded),
                label: Text(
                  AppLocalizations.of(context).login,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Cargar notificaciones al iniciar
    context.read<NotificationsBloc>().add(LoadNotificationsEvent(uid));

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Stack(
        children: [
          // Contenido principal
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Encabezado con barra de búsqueda y filtros
              SliverToBoxAdapter(
                child: _buildHeader(),
              ),

              // Lista de notificaciones
              SliverToBoxAdapter(
                child: BlocBuilder<NotificationsBloc, NotificationsState>(
                  builder: (context, state) {
                    if (state is NotificationsLoading) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 100),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Indicador de carga con diseño moderno
                              Container(
                                width: 80,
                                height: 80,
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 10,
                                      offset: const Offset(0, 5),
                                    ),
                                  ],
                                ),
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.primaryColor,
                                  ),
                                  strokeWidth: 3,
                                ),
                              ),
                              const SizedBox(height: 24),
                              Text(
                                AppLocalizations.of(context)
                                    .loadingNotifications,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    } else if (state is NotificationsLoaded) {
                      if (state.notifications.isEmpty) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 80),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Contenedor decorativo para el icono
                                Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 20,
                                        spreadRadius: 5,
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.notifications_off_outlined,
                                    size: 60,
                                    color:
                                        AppColors.primaryColor.withOpacity(0.5),
                                  ),
                                ),
                                const SizedBox(height: 32),
                                Text(
                                  AppLocalizations.of(context).noNotifications,
                                  style: GoogleFonts.cairo(
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey[800],
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    AppLocalizations.of(context)
                                        .newNotifications,
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                const SizedBox(height: 40),
                                // Botón para actualizar
                                ElevatedButton.icon(
                                  onPressed: () {
                                    setState(() {
                                      _isRefreshing = true;
                                    });
                                    context
                                        .read<NotificationsBloc>()
                                        .add(LoadNotificationsEvent(uid));
                                    Future.delayed(const Duration(seconds: 1),
                                        () {
                                      if (mounted) {
                                        setState(() {
                                          _isRefreshing = false;
                                        });
                                      }
                                    });
                                  },
                                  icon: _isRefreshing
                                      ? SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                          ),
                                        )
                                      : const Icon(Icons.refresh_rounded),
                                  label: Text(
                                    'تحديث',
                                    style: GoogleFonts.cairo(),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.primaryColor,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 24,
                                      vertical: 12,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      // Filtrar notificaciones según el filtro seleccionado y la búsqueda
                      final filteredNotifications =
                          _filterNotifications(state.notifications);

                      if (filteredNotifications.isEmpty) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 80),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 20,
                                        spreadRadius: 5,
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    _selectedFilter == 'unread'
                                        ? Icons.mark_email_read_rounded
                                        : _selectedFilter == 'read'
                                            ? Icons.done_all_rounded
                                            : _selectedFilter == 'today'
                                                ? Icons.today_rounded
                                                : Icons.filter_alt_off_rounded,
                                    size: 60,
                                    color:
                                        AppColors.primaryColor.withOpacity(0.5),
                                  ),
                                ),
                                const SizedBox(height: 32),
                                Text(
                                  _getEmptyFilterMessage(),
                                  style: GoogleFonts.cairo(
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey[800],
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    _searchQuery.isNotEmpty
                                        ? 'لا توجد إشعارات تطابق بحثك: "$_searchQuery"'
                                        : 'ستظهر الإشعارات هنا عند وصولها',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      // Agrupar notificaciones por fecha
                      final groupedNotifications =
                          _groupNotificationsByDate(filteredNotifications);

                      // Lista de notificaciones con animaciones y agrupadas por fecha
                      return Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.only(bottom: 100),
                          itemCount: groupedNotifications.length,
                          itemBuilder: (context, i) {
                            final dateGroup =
                                groupedNotifications.keys.elementAt(i);
                            final notifications =
                                groupedNotifications[dateGroup]!;

                            return Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Encabezado de la fecha
                                  Padding(
                                    padding:
                                        const EdgeInsets.fromLTRB(16, 8, 16, 8),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryColor
                                            .withOpacity(0.08),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        dateGroup,
                                        style: GoogleFonts.cairo(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                          color: AppColors.primaryColor,
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Notificaciones del grupo
                                  ...notifications.asMap().entries.map((entry) {
                                    final index = entry.key;
                                    final notification = entry.value;

                                    // Animation removed - direct card display
                                    return _buildNotificationCard(
                                      context,
                                      notification,
                                      uid,
                                    );
                                  }),
                                ],
                              ),
                            );
                          },
                        ),
                      );
                    } else if (state is NotificationsError) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 80),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Contenedor decorativo para el icono de error
                              Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  color: Colors.red.shade50,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.red.withOpacity(0.1),
                                      blurRadius: 15,
                                      spreadRadius: 2,
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.error_outline_rounded,
                                  size: 50,
                                  color: Colors.red.shade400,
                                ),
                              ),
                              const SizedBox(height: 32),
                              Text(
                                AppLocalizations.of(context).error,
                                style: GoogleFonts.cairo(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.red.shade700,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 16,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.red.shade200,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  state.message,
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    color: Colors.grey.shade700,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(height: 32),
                              // Botón para reintentar
                              ElevatedButton.icon(
                                onPressed: () {
                                  context
                                      .read<NotificationsBloc>()
                                      .add(LoadNotificationsEvent(uid));
                                },
                                icon: const Icon(Icons.refresh_rounded),
                                label: Text(
                                  AppLocalizations.of(context).tryAgain,
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red.shade500,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 32,
                                    vertical: 16,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),

          // Botón flotante para marcar todas como leídas
          Positioned(
            bottom: 24,
            right: 24,
            child: FloatingActionButton.extended(
              onPressed: () {
                // Mostrar diálogo de confirmación
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    title: Text(
                      'تحديد الكل كمقروء',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    content: Text(
                      'هل تريد تحديد جميع الإشعارات كمقروءة؟',
                      style: GoogleFonts.cairo(),
                      textAlign: TextAlign.center,
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إلغاء',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          AnimatedToast.show(
                            context,
                            message: 'تم تحديد جميع الإشعارات كمقروءة',
                            type: ToastType.success,
                            position: ToastPosition.top,
                          );
                        },
                        child: Text(
                          'تأكيد',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                    ],
                  ),
                );
              },
              icon: const Icon(Icons.done_all_rounded),
              label: Text(
                'تحديد الكل كمقروء',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                ),
              ),
              backgroundColor: AppColors.primaryColor,
              foregroundColor: Colors.white,
              elevation: 4,
            ),
          ),
        ],
      ),
    );
  }

  /// Filtra las notificaciones según el filtro seleccionado y la búsqueda
  List<NotificationEntity> _filterNotifications(
      List<NotificationEntity> notifications) {
    // Primero aplicar el filtro de búsqueda
    var filtered = notifications;

    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((notification) {
        final title = notification.title.toLowerCase();
        final body = notification.body.toLowerCase();
        final query = _searchQuery.toLowerCase();

        return title.contains(query) || body.contains(query);
      }).toList();
    }

    // Luego aplicar el filtro de estado
    if (_selectedFilter == 'unread') {
      filtered =
          filtered.where((notification) => !notification.isRead).toList();
    } else if (_selectedFilter == 'read') {
      filtered = filtered.where((notification) => notification.isRead).toList();
    }

    return filtered;
  }

  /// Obtiene el mensaje para cuando no hay notificaciones según el filtro
  String _getEmptyFilterMessage() {
    if (_searchQuery.isNotEmpty) {
      return 'لا توجد نتائج للبحث';
    }

    switch (_selectedFilter) {
      case 'unread':
        return 'لا توجد إشعارات غير مقروءة';
      case 'read':
        return 'لا توجد إشعارات مقروءة';
      default:
        return 'لا توجد إشعارات';
    }
  }

  /// Agrupa las notificaciones por fecha
  Map<String, List<NotificationEntity>> _groupNotificationsByDate(
      List<NotificationEntity> notifications) {
    final Map<String, List<NotificationEntity>> grouped = {};

    for (final notification in notifications) {
      final DateTime? timestamp = notification.timestamp;
      String dateGroup;

      if (timestamp == null) {
        dateGroup = 'غير محدد';
      } else {
        final now = DateTime.now();
        final difference = now.difference(timestamp);

        if (difference.inHours < 24) {
          dateGroup = 'اليوم';
        } else if (difference.inDays < 2) {
          dateGroup = 'الأمس';
        } else if (difference.inDays < 7) {
          dateGroup = 'هذا الأسبوع';
        } else if (difference.inDays < 30) {
          dateGroup = 'هذا الشهر';
        } else {
          // Formatear la fecha para meses anteriores
          final DateFormat formatter = DateFormat('MMMM yyyy');
          dateGroup = formatter.format(timestamp);
        }
      }

      if (!grouped.containsKey(dateGroup)) {
        grouped[dateGroup] = [];
      }

      grouped[dateGroup]!.add(notification);
    }

    return grouped;
  }

  /// تنسيق التاريخ مع مراعاة إمكانية كونه null.
  String _formatDate(DateTime? dt) {
    if (dt == null) return '';

    // تنسيق التاريخ بشكل أكثر قابلية للقراءة
    final now = DateTime.now();
    final difference = now.difference(dt);

    // نظرًا لأننا لا نستطيع الوصول إلى context هنا، سنستخدم النص العربي مؤقتًا
    // ويمكن تحسين هذا لاحقًا
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      // تنسيق التاريخ بالكامل
      final DateFormat formatter = DateFormat('yyyy/MM/dd - hh:mm a');
      return formatter.format(dt);
    }
  }

  /// Construye una tarjeta de notificación con diseño moderno
  Widget _buildNotificationCard(
    BuildContext context,
    NotificationEntity notification,
    String uid,
  ) {
    // Determinar si la notificación es nueva (menos de 24 horas)
    final bool isNew = notification.timestamp != null &&
        DateTime.now().difference(notification.timestamp!).inHours < 24;

    // Determinar el icono según el contenido
    final IconData notificationIcon = notification.title.contains('بطاقة')
        ? Icons.card_giftcard_rounded
        : notification.title.contains('تحديث')
            ? Icons.system_update_rounded
            : notification.title.contains('عرض')
                ? Icons.local_offer_rounded
                : Icons.notifications_rounded;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        decoration: BoxDecoration(
          color: notification.isRead
              ? Theme.of(context).colorScheme.surfaceContainerHighest
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withValues(alpha: 0.05),
              blurRadius: notification.isRead ? 4 : 8,
              offset: const Offset(0, 2),
              spreadRadius: notification.isRead ? 0 : 1,
            ),
          ],
          border: notification.isRead
              ? Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  width: 1
                )
              : Border.all(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  width: 1.5,
                ),
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              if (!notification.isRead) {
                // Marcar como leída
                context.read<NotificationsBloc>().add(
                      MarkNotificationReadEvent(uid, notification.id),
                    );

                // Recargar las notificaciones para actualizar el contador en la página de inicio
                Future.delayed(const Duration(milliseconds: 300), () {
                  if (mounted) {
                    context
                        .read<NotificationsBloc>()
                        .add(LoadNotificationsEvent(uid));
                  }
                });

                // Mostrar confirmación
                AnimatedToast.show(
                  context,
                  message:
                      AppLocalizations.of(context).notificationMarkedAsRead,
                  type: ToastType.success,
                  position: ToastPosition.bottom,
                );
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Encabezado: icono, título y badge de nuevo
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Icono con fondo
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: notification.isRead
                              ? AppColors.primaryColor.withOpacity(0.08)
                              : AppColors.primaryColor.withOpacity(0.12),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          notificationIcon,
                          color: AppColors.primaryColor,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Título y fecha
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Título con indicador de nuevo
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Text(
                                    notification.title,
                                    style: GoogleFonts.cairo(
                                      fontWeight: notification.isRead
                                          ? FontWeight.w500
                                          : FontWeight.bold,
                                      fontSize: 16,
                                      color: notification.isRead
                                          ? Colors.grey.shade700
                                          : Colors.grey.shade900,
                                    ),
                                  ),
                                ),
                                if (isNew && !notification.isRead)
                                  Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 3,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade500,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      'جديد',
                                      style: GoogleFonts.cairo(
                                        color: Colors.white,
                                        fontSize: 11,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                              ],
                            ),

                            // Fecha
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.access_time_rounded,
                                    size: 14,
                                    color: Colors.grey.shade500,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _formatDate(notification.timestamp),
                                    style: GoogleFonts.cairo(
                                      fontSize: 12,
                                      color: Colors.grey.shade500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Cuerpo de la notificación
                  Padding(
                    padding: const EdgeInsets.only(top: 12, right: 60),
                    child: Text(
                      notification.body,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: notification.isRead
                            ? Colors.grey.shade600
                            : Colors.grey.shade800,
                        height: 1.4,
                      ),
                    ),
                  ),

                  // Acciones
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Botón para eliminar
                        Material(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(8),
                            onTap: () {
                              // Mostrar diálogo de confirmación
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  title: Text(
                                    'حذف الإشعار',
                                    style: GoogleFonts.cairo(
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  content: Text(
                                    'هل أنت متأكد من حذف هذا الإشعار؟',
                                    style: GoogleFonts.cairo(),
                                    textAlign: TextAlign.center,
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: Text(
                                        'إلغاء',
                                        style: GoogleFonts.cairo(),
                                      ),
                                    ),
                                    ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                        foregroundColor: Colors.white,
                                      ),
                                      onPressed: () {
                                        Navigator.pop(context);
                                        AnimatedToast.show(
                                          context,
                                          message: AppLocalizations.of(context)
                                              .featureComingSoon,
                                          type: ToastType.info,
                                          position: ToastPosition.top,
                                        );
                                      },
                                      child: Text(
                                        'حذف',
                                        style: GoogleFonts.cairo(),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.delete_outline_rounded,
                                    size: 18,
                                    color: Colors.red.shade400,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'حذف',
                                    style: GoogleFonts.cairo(
                                      fontSize: 13,
                                      color: Colors.red.shade400,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
