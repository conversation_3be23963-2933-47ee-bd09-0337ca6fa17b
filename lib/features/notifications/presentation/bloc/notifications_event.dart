// lib/features/notifications/presentation/bloc/notifications_event.dart
import 'package:equatable/equatable.dart';

abstract class NotificationsEvent extends Equatable {
  const NotificationsEvent();
  @override
  List<Object?> get props => [];
}

class LoadNotificationsEvent extends NotificationsEvent {
  final String uid;
  const LoadNotificationsEvent(this.uid);
  @override
  List<Object?> get props => [uid];
}

class MarkNotificationReadEvent extends NotificationsEvent {
  final String uid;
  final String notificationId;
  const MarkNotificationReadEvent(this.uid, this.notificationId);
  @override
  List<Object?> get props => [uid, notificationId];
}
