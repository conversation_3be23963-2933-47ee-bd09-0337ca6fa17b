import 'package:flutter_bloc/flutter_bloc.dart';

import '../../data/models/notification_model.dart'; // لاستعمال copyWith
import '../../domain/usecases/get_notifications_usecase.dart';
import '../../domain/usecases/mark_notification_read_usecase.dart';
import 'notifications_event.dart';
import 'notifications_state.dart';

class NotificationsBloc extends Bloc<NotificationsEvent, NotificationsState> {
  final GetNotificationsUseCase _get;
  final MarkNotificationReadUseCase _mark;

  NotificationsBloc({
    required GetNotificationsUseCase getUseCase,
    required MarkNotificationReadUseCase markReadUseCase,
  })  : _get = getUseCase,
        _mark = markReadUseCase,
        super(const NotificationsLoading()) {
    on<LoadNotificationsEvent>(_onLoad);
    on<MarkNotificationReadEvent>(_onMarkRead);
  }

  Future<void> _onLoad(
      LoadNotificationsEvent e, Emitter<NotificationsState> emit) async {
    emit(const NotificationsLoading());
    final res = await _get(NotificationParams(e.uid));
    res.fold(
      (f) => emit(NotificationsError(f.message)),
      (list) => emit(NotificationsLoaded(list)),
    );
  }

  Future<void> _onMarkRead(
      MarkNotificationReadEvent e, Emitter<NotificationsState> emit) async {
    // تحديث الحالة حالياً لتحسين تجربة المستخدم
    if (state is NotificationsLoaded) {
      final current = (state as NotificationsLoaded).notifications;
      final updated = current
          .map((n) => n.id == e.notificationId
              ? (n as NotificationModel).copyWith(isRead: true)
              : n)
          .toList();
      emit(NotificationsLoaded(updated));
    }

    // تحديث قاعدة البيانات (إذا فشل ستبقى الحالة محلية فقط)
    await _mark(MarkNotificationReadParams(
      uid: e.uid,
      notificationId: e.notificationId,
    ));
  }
}
