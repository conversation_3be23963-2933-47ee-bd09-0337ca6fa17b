// lib/features/ai/domain/entities/ai_text_result.dart

/// نتيجة توليد النص بالذكاء الاصطناعي
class AiTextResult {
  /// قائمة النصوص المولدة
  final List<String> generatedTexts;

  /// المطالبة الأصلية التي تم استخدامها لتوليد النصوص
  final String originalPrompt;

  /// تاريخ ووقت التوليد
  final DateTime timestamp;

  /// إنشاء نتيجة توليد النص بالذكاء الاصطناعي
  const AiTextResult({
    required this.generatedTexts,
    required this.originalPrompt,
    required this.timestamp,
  });

  /// الحصول على النص الأول من القائمة (إذا كان موجودًا)
  String? get firstText => generatedTexts.isNotEmpty ? generatedTexts.first : null;

  /// التحقق من وجود نصوص مولدة
  bool get hasTexts => generatedTexts.isNotEmpty;
}
