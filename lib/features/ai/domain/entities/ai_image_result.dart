// lib/features/ai/domain/entities/ai_image_result.dart

/// نتيجة توليد الصورة بالذكاء الاصطناعي
class AiImageResult {
  /// قائمة مسارات الصور المولدة
  final List<String> generatedImagePaths;

  /// قائمة روابط الصور المولدة (إذا كانت متاحة)
  final List<String> generatedImageUrls;

  /// المطالبة الأصلية التي تم استخدامها لتوليد الصور
  final String originalPrompt;

  /// تاريخ ووقت التوليد
  final DateTime timestamp;

  /// إنشاء نتيجة توليد الصورة بالذكاء الاصطناعي
  const AiImageResult({
    required this.generatedImagePaths,
    this.generatedImageUrls = const [],
    required this.originalPrompt,
    required this.timestamp,
  });

  /// الحصول على مسار الصورة الأولى من القائمة (إذا كانت موجودة)
  String? get firstImagePath => 
      generatedImagePaths.isNotEmpty ? generatedImagePaths.first : null;

  /// الحصول على رابط الصورة الأولى من القائمة (إذا كان موجودًا)
  String? get firstImageUrl => 
      generatedImageUrls.isNotEmpty ? generatedImageUrls.first : null;

  /// التحقق من وجود صور مولدة
  bool get hasImages => generatedImagePaths.isNotEmpty || generatedImageUrls.isNotEmpty;

  /// الحصول على عدد الصور المولدة
  int get imageCount => generatedImagePaths.length;
}
