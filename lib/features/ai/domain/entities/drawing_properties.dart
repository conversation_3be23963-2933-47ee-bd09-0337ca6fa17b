// lib/features/ai/domain/entities/drawing_properties.dart

import 'package:flutter/material.dart';

/// نقطة في مسار الرسم
class DrawingPoint {
  /// الإحداثي الأفقي
  final double x;

  /// الإحداثي الرأسي
  final double y;

  /// إنشاء نقطة في مسار الرسم
  const DrawingPoint({
    required this.x,
    required this.y,
  });

  /// تحويل النقطة إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
    };
  }

  /// إنشاء نقطة من JSON
  factory DrawingPoint.fromJson(Map<String, dynamic> json) {
    return DrawingPoint(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is DrawingPoint &&
           other.x == x &&
           other.y == y;
  }

  @override
  int get hashCode => x.hashCode ^ y.hashCode;
}

/// مسار في الرسم
class DrawingPath {
  /// قائمة النقاط في المسار
  final List<DrawingPoint> points;

  /// إنشاء مسار في الرسم
  const DrawingPath({
    required this.points,
  });

  /// تحويل المسار إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'points': points.map((point) => point.toJson()).toList(),
    };
  }

  /// إنشاء مسار من JSON
  factory DrawingPath.fromJson(Map<String, dynamic> json) {
    return DrawingPath(
      points: (json['points'] as List)
          .map((point) => DrawingPoint.fromJson(point as Map<String, dynamic>))
          .toList(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is DrawingPath &&
           other.points.length == points.length &&
           List.generate(points.length, (i) => points[i] == other.points[i])
               .every((element) => element);
  }

  @override
  int get hashCode => points.hashCode;
}

/// خصائص الرسم الحر
class DrawingProperties {
  /// لون القلم
  final int penColorValue;

  /// سمك القلم
  final double penWidth;

  /// قائمة المسارات في الرسم
  final List<DrawingPath> paths;

  /// إنشاء خصائص الرسم الحر
  const DrawingProperties({
    this.penColorValue = 0xFF000000, // أسود
    this.penWidth = 2.0,
    this.paths = const [],
  });

  /// إنشاء نسخة جديدة من الخصائص مع تحديث بعض الخصائص
  DrawingProperties copyWith({
    int? penColorValue,
    double? penWidth,
    List<DrawingPath>? paths,
  }) {
    return DrawingProperties(
      penColorValue: penColorValue ?? this.penColorValue,
      penWidth: penWidth ?? this.penWidth,
      paths: paths ?? this.paths,
    );
  }

  /// الحصول على Paint للرسم
  Paint getPaint() {
    return Paint()
      ..color = Color(penColorValue)
      ..strokeWidth = penWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
  }

  /// تحويل الخصائص إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'penColorValue': penColorValue,
      'penWidth': penWidth,
      'paths': paths.map((path) => path.toJson()).toList(),
    };
  }

  /// إنشاء خصائص من JSON
  factory DrawingProperties.fromJson(Map<String, dynamic> json) {
    return DrawingProperties(
      penColorValue: json['penColorValue'] as int? ?? 0xFF000000,
      penWidth: (json['penWidth'] as num?)?.toDouble() ?? 2.0,
      paths: json['paths'] != null
          ? (json['paths'] as List)
              .map((path) => DrawingPath.fromJson(path as Map<String, dynamic>))
              .toList()
          : const [],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is DrawingProperties &&
           other.penColorValue == penColorValue &&
           other.penWidth == penWidth &&
           other.paths.length == paths.length &&
           List.generate(paths.length, (i) => paths[i] == other.paths[i])
               .every((element) => element);
  }

  @override
  int get hashCode {
    return penColorValue.hashCode ^
           penWidth.hashCode ^
           paths.hashCode;
  }
}
