// lib/features/ai/domain/entities/shape_properties.dart

import 'package:flutter/material.dart';

/// نمط الخط
enum LineStyle {
  /// متصل
  solid,

  /// متقطع
  dashed,

  /// منقط
  dotted,
}

/// خصائص الشكل الهندسي
class ShapeProperties {
  /// لون الحدود
  final int borderColorValue;

  /// سمك الحدود
  final double borderWidth;

  /// نمط الخط
  final LineStyle lineStyle;

  /// لون التعبئة (null إذا كان الشكل بدون تعبئة)
  final int? fillColorValue;

  /// نصف قطر الزوايا (للمستطيل فقط)
  final double cornerRadius;

  /// إنشاء خصائص الشكل الهندسي
  const ShapeProperties({
    this.borderColorValue = 0xFF000000, // أسود
    this.borderWidth = 2.0,
    this.lineStyle = LineStyle.solid,
    this.fillColorValue,
    this.cornerRadius = 0.0,
  });

  /// إنشاء نسخة جديدة من الخصائص مع تحديث بعض الخصائص
  ShapeProperties copyWith({
    int? borderColorValue,
    double? borderWidth,
    LineStyle? lineStyle,
    int? fillColorValue,
    bool clearFillColor = false,
    double? cornerRadius,
  }) {
    return ShapeProperties(
      borderColorValue: borderColorValue ?? this.borderColorValue,
      borderWidth: borderWidth ?? this.borderWidth,
      lineStyle: lineStyle ?? this.lineStyle,
      fillColorValue: clearFillColor ? null : (fillColorValue ?? this.fillColorValue),
      cornerRadius: cornerRadius ?? this.cornerRadius,
    );
  }

  /// الحصول على Paint للحدود
  Paint getBorderPaint() {
    return Paint()
      ..color = Color(borderColorValue)
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;
  }

  /// الحصول على Paint للتعبئة
  Paint? getFillPaint() {
    if (fillColorValue == null) return null;
    
    return Paint()
      ..color = Color(fillColorValue!)
      ..style = PaintingStyle.fill;
  }

  /// تحويل الخصائص إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'borderColorValue': borderColorValue,
      'borderWidth': borderWidth,
      'lineStyle': lineStyle.toString().split('.').last,
      'fillColorValue': fillColorValue,
      'cornerRadius': cornerRadius,
    };
  }

  /// إنشاء خصائص من JSON
  factory ShapeProperties.fromJson(Map<String, dynamic> json) {
    return ShapeProperties(
      borderColorValue: json['borderColorValue'] as int? ?? 0xFF000000,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 2.0,
      lineStyle: _parseLineStyle(json['lineStyle'] as String?),
      fillColorValue: json['fillColorValue'] as int?,
      cornerRadius: (json['cornerRadius'] as num?)?.toDouble() ?? 0.0,
    );
  }

  /// تحليل نمط الخط من النص
  static LineStyle _parseLineStyle(String? value) {
    if (value == null) return LineStyle.solid;
    
    switch (value) {
      case 'dashed':
        return LineStyle.dashed;
      case 'dotted':
        return LineStyle.dotted;
      case 'solid':
      default:
        return LineStyle.solid;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ShapeProperties &&
           other.borderColorValue == borderColorValue &&
           other.borderWidth == borderWidth &&
           other.lineStyle == lineStyle &&
           other.fillColorValue == fillColorValue &&
           other.cornerRadius == cornerRadius;
  }

  @override
  int get hashCode {
    return borderColorValue.hashCode ^
           borderWidth.hashCode ^
           lineStyle.hashCode ^
           fillColorValue.hashCode ^
           cornerRadius.hashCode;
  }
}
