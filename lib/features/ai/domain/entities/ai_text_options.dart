// lib/features/ai/domain/entities/ai_text_options.dart

/// نوع طول النص المطلوب توليده
enum TextLength {
  /// نص قصير (حوالي 50-100 حرف)
  short,

  /// نص متوسط (حوالي 100-200 حرف)
  medium,

  /// نص طويل (حوالي 200-300 حرف)
  long,
}

/// أسلوب النص المطلوب توليده
enum TextStyle {
  /// أسلوب رسمي
  formal,

  /// أسلوب عاطفي
  emotional,

  /// أسلوب فكاهي
  humorous,

  /// أسلوب أدبي
  literary,
}

/// خيارات توليد النص بالذكاء الاصطناعي
class AiTextOptions {
  /// طول النص المطلوب
  final TextLength textLength;

  /// أسلوب النص المطلوب
  final TextStyle textStyle;

  /// عدد النصوص المطلوب توليدها
  final int numberOfTexts;

  /// إنشاء خيارات توليد النص بالذكاء الاصطناعي
  const AiTextOptions({
    this.textLength = TextLength.medium,
    this.textStyle = TextStyle.emotional,
    this.numberOfTexts = 1,
  });

  /// إنشاء نسخة جديدة من الخيارات مع تحديث بعض الخصائص
  AiTextOptions copyWith({
    TextLength? textLength,
    TextStyle? textStyle,
    int? numberOfTexts,
  }) {
    return AiTextOptions(
      textLength: textLength ?? this.textLength,
      textStyle: textStyle ?? this.textStyle,
      numberOfTexts: numberOfTexts ?? this.numberOfTexts,
    );
  }
}
