// lib/features/ai/domain/entities/ai_image_options.dart

/// نوع أسلوب الصورة المطلوب توليدها
enum ImageStyle {
  /// أسلوب واقعي
  realistic,

  /// أسلوب فني
  artistic,

  /// أسلوب كرتوني
  cartoon,

  /// أسلوب مائي
  watercolor,

  /// أسلوب زيتي
  oil,

  /// أسلوب رقمي
  digital,
}

/// نسبة أبعاد الصورة
enum ImageAspectRatio {
  /// مربع (1:1)
  square,

  /// أفقي (16:9)
  landscape,

  /// عمودي (9:16)
  portrait,

  /// أفقي (4:3)
  standard,
}

/// دقة الصورة
enum ImageResolution {
  /// منخفضة (256x256)
  low,

  /// متوسطة (512x512)
  medium,

  /// عالية (1024x1024)
  high,
}

/// خيارات توليد الصورة بالذكاء الاصطناعي
class AiImageOptions {
  /// أسلوب الصورة المطلوب
  final ImageStyle imageStyle;

  /// نسبة أبعاد الصورة
  final ImageAspectRatio aspectRatio;

  /// دقة الصورة
  final ImageResolution resolution;

  /// إنشاء خيارات توليد الصورة بالذكاء الاصطناعي
  const AiImageOptions({
    this.imageStyle = ImageStyle.realistic,
    this.aspectRatio = ImageAspectRatio.square,
    this.resolution = ImageResolution.medium,
  });

  /// إنشاء نسخة جديدة من الخيارات مع تحديث بعض الخصائص
  AiImageOptions copyWith({
    ImageStyle? imageStyle,
    ImageAspectRatio? aspectRatio,
    ImageResolution? resolution,
  }) {
    return AiImageOptions(
      imageStyle: imageStyle ?? this.imageStyle,
      aspectRatio: aspectRatio ?? this.aspectRatio,
      resolution: resolution ?? this.resolution,
    );
  }

  /// الحصول على أبعاد الصورة بناءً على نسبة الأبعاد والدقة
  Map<String, int> getDimensions() {
    int width, height;
    
    // تحديد الدقة الأساسية
    int baseSize;
    switch (resolution) {
      case ImageResolution.low:
        baseSize = 256;
        break;
      case ImageResolution.medium:
        baseSize = 512;
        break;
      case ImageResolution.high:
        baseSize = 1024;
        break;
    }
    
    // تحديد الأبعاد بناءً على نسبة الأبعاد
    switch (aspectRatio) {
      case ImageAspectRatio.square:
        width = baseSize;
        height = baseSize;
        break;
      case ImageAspectRatio.landscape:
        width = baseSize;
        height = (baseSize * 9 / 16).round();
        break;
      case ImageAspectRatio.portrait:
        width = (baseSize * 9 / 16).round();
        height = baseSize;
        break;
      case ImageAspectRatio.standard:
        width = baseSize;
        height = (baseSize * 3 / 4).round();
        break;
    }
    
    return {'width': width, 'height': height};
  }

  /// الحصول على وصف أسلوب الصورة
  String getStyleDescription() {
    switch (imageStyle) {
      case ImageStyle.realistic:
        return 'صورة واقعية بتفاصيل دقيقة';
      case ImageStyle.artistic:
        return 'صورة بأسلوب فني إبداعي';
      case ImageStyle.cartoon:
        return 'صورة كرتونية بألوان زاهية';
      case ImageStyle.watercolor:
        return 'صورة بأسلوب الألوان المائية';
      case ImageStyle.oil:
        return 'صورة بأسلوب الألوان الزيتية';
      case ImageStyle.digital:
        return 'صورة بأسلوب رقمي حديث';
    }
  }
}
