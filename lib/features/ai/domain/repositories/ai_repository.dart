// lib/features/ai/domain/repositories/ai_repository.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../entities/ai_image_options.dart';
import '../entities/ai_image_result.dart';
import '../entities/ai_text_options.dart';
import '../entities/ai_text_result.dart';

/// واجهة مجردة لمستودع الذكاء الاصطناعي.
///
/// هذا المستودع مسؤول عن معالجة العمليات المتعلقة بالذكاء الاصطناعي
/// في طبقة المجال، متبعًا مبادئ البنية النظيفة.
abstract class AiRepository {
  /// توليد نص بالذكاء الاصطناعي بناءً على المطالبة المقدمة.
  ///
  /// @param prompt المطالبة النصية لتوجيه توليد النص بالذكاء الاصطناعي
  /// @return مستقبل يتم حله إما إلى [Failure] أو سلسلة النص المولد
  Future<Either<Failure, String>> generateAiText(String prompt);

  /// توليد نص بالذكاء الاصطناعي بناءً على المطالبة المقدمة مع خيارات متقدمة.
  ///
  /// @param prompt المطالبة النصية لتوجيه توليد النص بالذكاء الاصطناعي
  /// @param options خيارات توليد النص (الطول، الأسلوب، إلخ)
  /// @return مستقبل يتم حله إما إلى [Failure] أو نتيجة النص المولد
  Future<Either<Failure, AiTextResult>> generateAiTextAdvanced(
    String prompt,
    AiTextOptions options,
  );

  /// توليد صورة بالذكاء الاصطناعي بناءً على المطالبة المقدمة.
  ///
  /// @param prompt المطالبة النصية لتوجيه توليد الصورة بالذكاء الاصطناعي
  /// @param options خيارات توليد الصورة (الأسلوب، نسبة الأبعاد، الدقة)
  /// @return مستقبل يتم حله إما إلى [Failure] أو نتيجة الصورة المولدة
  Future<Either<Failure, AiImageResult>> generateAiImage(
    String prompt,
    AiImageOptions options,
  );

  /// استرجاع مطالبات مقترحة للمناسبة المحددة.
  ///
  /// @param occasionId معرف المناسبة للحصول على المطالبات
  /// @return مستقبل يتم حله إما إلى [Failure] أو قائمة من المطالبات المقترحة
  Future<Either<Failure, List<String>>> getSuggestedPrompts(String occasionId);

  /// حفظ نتيجة نص مولد في التاريخ.
  ///
  /// @param result نتيجة النص المولد للحفظ
  /// @return مستقبل يتم حله إما إلى [Failure] أو void
  Future<Either<Failure, void>> saveToHistory(AiTextResult result);

  /// استرجاع تاريخ النصوص المولدة.
  ///
  /// @return مستقبل يتم حله إما إلى [Failure] أو قائمة من نتائج النصوص
  Future<Either<Failure, List<AiTextResult>>> getTextHistory();

  /// حفظ نتيجة صورة مولدة في التاريخ.
  ///
  /// @param result نتيجة الصورة المولدة للحفظ
  /// @return مستقبل يتم حله إما إلى [Failure] أو void
  Future<Either<Failure, void>> saveImageToHistory(AiImageResult result);

  /// استرجاع تاريخ الصور المولدة.
  ///
  /// @return مستقبل يتم حله إما إلى [Failure] أو قائمة من نتائج الصور
  Future<Either<Failure, List<AiImageResult>>> getImageHistory();
}
