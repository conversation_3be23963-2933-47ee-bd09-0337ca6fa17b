// lib/features/ai/domain/usecases/generate_ai_image_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/ai_image_options.dart';
import '../entities/ai_image_result.dart';
import '../repositories/ai_repository.dart';

/// معلمات حالة استخدام توليد الصور بالذكاء الاصطناعي
class GenerateAiImageParams {
  /// المطالبة النصية لتوليد الصورة
  final String prompt;
  
  /// خيارات توليد الصورة
  final AiImageOptions options;
  
  /// إنشاء معلمات جديدة
  const GenerateAiImageParams({
    required this.prompt,
    required this.options,
  });
}

/// حالة استخدام لتوليد صورة باستخدام الذكاء الاصطناعي
class GenerateAiImageUseCase implements UseCase<AiImageResult, GenerateAiImageParams> {
  /// مستودع الذكاء الاصطناعي
  final AiRepository repository;
  
  /// إنشاء حالة استخدام جديدة
  const GenerateAiImageUseCase(this.repository);
  
  @override
  Future<Either<Failure, AiImageResult>> call(GenerateAiImageParams params) {
    return repository.generateAiImage(params.prompt, params.options);
  }
}
