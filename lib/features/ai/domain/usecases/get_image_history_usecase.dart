// lib/features/ai/domain/usecases/get_image_history_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/ai_image_result.dart';
import '../repositories/ai_repository.dart';

/// حالة استخدام للحصول على تاريخ الصور المولدة
class GetImageHistoryUseCase implements UseCase<List<AiImageResult>, NoParams> {
  /// مستودع الذكاء الاصطناعي
  final AiRepository repository;
  
  /// إنشاء حالة استخدام جديدة
  const GetImageHistoryUseCase(this.repository);
  
  @override
  Future<Either<Failure, List<AiImageResult>>> call(NoParams params) {
    return repository.getImageHistory();
  }
}
