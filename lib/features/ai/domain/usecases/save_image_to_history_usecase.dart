// lib/features/ai/domain/usecases/save_image_to_history_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/ai_image_result.dart';
import '../repositories/ai_repository.dart';

/// حالة استخدام لحفظ نتيجة صورة مولدة في التاريخ
class SaveImageToHistoryUseCase implements UseCase<void, AiImageResult> {
  /// مستودع الذكاء الاصطناعي
  final AiRepository repository;
  
  /// إنشاء حالة استخدام جديدة
  const SaveImageToHistoryUseCase(this.repository);
  
  @override
  Future<Either<Failure, void>> call(AiImageResult params) {
    return repository.saveImageToHistory(params);
  }
}
