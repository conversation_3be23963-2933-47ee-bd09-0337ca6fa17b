// lib/features/ai/domain/usecases/generate_ai_text_advanced_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/ai_text_options.dart';
import '../entities/ai_text_result.dart';
import '../repositories/ai_repository.dart';

/// Parameters required for the GenerateAiTextAdvanced use case.
///
/// This class encapsulates the input parameters needed for generating AI text
/// with advanced options.
class GenerateAiTextAdvancedParams {
  /// The text prompt to guide the AI text generation
  final String prompt;
  
  /// The options for text generation (length, style, etc.)
  final AiTextOptions options;

  /// Creates a new instance with the required parameters
  GenerateAiTextAdvancedParams({
    required this.prompt,
    required this.options,
  });
}

/// Use case for generating AI text from a prompt with advanced options.
///
/// This class implements the [UseCase] interface and handles the business logic
/// for generating AI text based on user prompts with advanced options.
class GenerateAiTextAdvancedUseCase
    implements UseCase<AiTextResult, GenerateAiTextAdvancedParams> {
  /// The repository that this use case will interact with
  final AiRepository repository;

  /// Creates a new instance with the required repository
  GenerateAiTextAdvancedUseCase(this.repository);

  @override
  Future<Either<Failure, AiTextResult>> call(GenerateAiTextAdvancedParams params) {
    // Delegate the actual text generation to the repository
    return repository.generateAiTextAdvanced(params.prompt, params.options);
  }
}
