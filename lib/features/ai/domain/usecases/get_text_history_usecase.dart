// lib/features/ai/domain/usecases/get_text_history_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/ai_text_result.dart';
import '../repositories/ai_repository.dart';

/// Use case for retrieving the history of generated AI texts.
///
/// This class implements the [UseCase] interface and handles the business logic
/// for retrieving the history of previously generated AI texts.
class GetTextHistoryUseCase implements UseCase<List<AiTextResult>, NoParams> {
  /// The repository that this use case will interact with
  final AiRepository repository;

  /// Creates a new instance with the required repository
  GetTextHistoryUseCase(this.repository);

  @override
  Future<Either<Failure, List<AiTextResult>>> call(NoParams params) {
    // Delegate the actual retrieval to the repository
    return repository.getTextHistory();
  }
}
