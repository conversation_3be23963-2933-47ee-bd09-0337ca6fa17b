// lib/features/ai/domain/usecases/get_suggested_prompts_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/ai_repository.dart';

/// Parameters required for the GetSuggestedPrompts use case.
///
/// This class encapsulates the input parameters needed for retrieving
/// suggested prompts for a specific occasion.
class GetSuggestedPromptsParams {
  /// The ID of the occasion to get prompts for
  final String occasionId;

  /// Creates a new instance with the required occasion ID
  GetSuggestedPromptsParams(this.occasionId);
}

/// Use case for retrieving suggested prompts for a specific occasion.
///
/// This class implements the [UseCase] interface and handles the business logic
/// for retrieving suggested prompts based on the occasion.
class GetSuggestedPromptsUseCase
    implements UseCase<List<String>, GetSuggestedPromptsParams> {
  /// The repository that this use case will interact with
  final AiRepository repository;

  /// Creates a new instance with the required repository
  GetSuggestedPromptsUseCase(this.repository);

  @override
  Future<Either<Failure, List<String>>> call(GetSuggestedPromptsParams params) {
    // Delegate the actual retrieval to the repository
    return repository.getSuggestedPrompts(params.occasionId);
  }
}
