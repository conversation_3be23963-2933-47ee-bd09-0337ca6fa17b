// lib/features/ai/domain/usecases/save_to_history_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/ai_text_result.dart';
import '../repositories/ai_repository.dart';

/// Parameters required for the SaveToHistory use case.
///
/// This class encapsulates the input parameters needed for saving
/// a generated text result to history.
class SaveToHistoryParams {
  /// The text result to save
  final AiTextResult result;

  /// Creates a new instance with the required result
  SaveToHistoryParams(this.result);
}

/// Use case for saving a generated text result to history.
///
/// This class implements the [UseCase] interface and handles the business logic
/// for saving a generated text result to the history.
class SaveToHistoryUseCase implements UseCase<void, SaveToHistoryParams> {
  /// The repository that this use case will interact with
  final AiRepository repository;

  /// Creates a new instance with the required repository
  SaveToHistoryUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(SaveToHistoryParams params) {
    // Delegate the actual saving to the repository
    return repository.saveToHistory(params.result);
  }
}
