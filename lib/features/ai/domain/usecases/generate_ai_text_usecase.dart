// lib/features/ai/domain/usecases/generate_ai_text_usecase.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/ai_repository.dart';

/// Parameters required for the GenerateAiText use case.
///
/// This class encapsulates the input parameters needed for generating AI text.
class GenerateAiTextParams {
  /// The text prompt to guide the AI text generation
  final String prompt;

  /// Creates a new instance with the required prompt
  GenerateAiTextParams(this.prompt);
}

/// Use case for generating AI text from a prompt.
///
/// This class implements the [UseCase] interface and handles the business logic
/// for generating AI text based on user prompts.
class GenerateAiTextUseCase implements UseCase<String, GenerateAiTextParams> {
  /// The repository that this use case will interact with
  final AiRepository repository;

  /// Creates a new instance with the required repository
  GenerateAiTextUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(GenerateAiTextParams params) {
    // Delegate the actual text generation to the repository
    return repository.generateAiText(params.prompt);
  }
}
