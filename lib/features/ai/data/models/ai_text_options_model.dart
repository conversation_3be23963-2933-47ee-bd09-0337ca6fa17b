// lib/features/ai/data/models/ai_text_options_model.dart

import '../../domain/entities/ai_text_options.dart';

/// نموذج خيارات توليد النص بالذكاء الاصطناعي
class AiTextOptionsModel extends AiTextOptions {
  /// إنشاء نموذج خيارات توليد النص بالذكاء الاصطناعي
  const AiTextOptionsModel({
    super.textLength = TextLength.medium,
    super.textStyle = TextStyle.emotional,
    super.numberOfTexts = 1,
  });

  /// إنشاء نموذج من JSON
  factory AiTextOptionsModel.fromJson(Map<String, dynamic> json) {
    return AiTextOptionsModel(
      textLength: _parseTextLength(json['textLength']),
      textStyle: _parseTextStyle(json['textStyle']),
      numberOfTexts: json['numberOfTexts'] ?? 1,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'textLength': textLength.toString().split('.').last,
      'textStyle': textStyle.toString().split('.').last,
      'numberOfTexts': numberOfTexts,
    };
  }

  /// تحليل طول النص من النص
  static TextLength _parseTextLength(String? value) {
    if (value == null) return TextLength.medium;
    
    switch (value) {
      case 'short':
        return TextLength.short;
      case 'medium':
        return TextLength.medium;
      case 'long':
        return TextLength.long;
      default:
        return TextLength.medium;
    }
  }

  /// تحليل أسلوب النص من النص
  static TextStyle _parseTextStyle(String? value) {
    if (value == null) return TextStyle.emotional;
    
    switch (value) {
      case 'formal':
        return TextStyle.formal;
      case 'emotional':
        return TextStyle.emotional;
      case 'humorous':
        return TextStyle.humorous;
      case 'literary':
        return TextStyle.literary;
      default:
        return TextStyle.emotional;
    }
  }
}
