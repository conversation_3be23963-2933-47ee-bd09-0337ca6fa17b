// lib/features/ai/data/models/ai_image_result_model.dart

import '../../domain/entities/ai_image_result.dart';

/// نموذج نتيجة توليد الصورة بالذكاء الاصطناعي
class AiImageResultModel extends AiImageResult {
  /// إنشاء نموذج نتيجة توليد الصورة بالذكاء الاصطناعي
  const AiImageResultModel({
    required super.generatedImagePaths,
    super.generatedImageUrls = const [],
    required super.originalPrompt,
    required super.timestamp,
  });

  /// إنشاء نموذج من JSON
  factory AiImageResultModel.fromJson(Map<String, dynamic> json) {
    return AiImageResultModel(
      generatedImagePaths: List<String>.from(json['generatedImagePaths'] ?? []),
      generatedImageUrls: List<String>.from(json['generatedImageUrls'] ?? []),
      originalPrompt: json['originalPrompt'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'generatedImagePaths': generatedImagePaths,
      'generatedImageUrls': generatedImageUrls,
      'originalPrompt': originalPrompt,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// إنشاء نسخة جديدة من النموذج مع تحديث بعض الخصائص
  AiImageResultModel copyWith({
    List<String>? generatedImagePaths,
    List<String>? generatedImageUrls,
    String? originalPrompt,
    DateTime? timestamp,
  }) {
    return AiImageResultModel(
      generatedImagePaths: generatedImagePaths ?? this.generatedImagePaths,
      generatedImageUrls: generatedImageUrls ?? this.generatedImageUrls,
      originalPrompt: originalPrompt ?? this.originalPrompt,
      timestamp: timestamp ?? this.timestamp,
    );
  }
}
