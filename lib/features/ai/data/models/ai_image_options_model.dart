// lib/features/ai/data/models/ai_image_options_model.dart

import '../../domain/entities/ai_image_options.dart';

/// نموذج خيارات توليد الصورة بالذكاء الاصطناعي
class AiImageOptionsModel extends AiImageOptions {
  /// إنشاء نموذج خيارات توليد الصورة بالذكاء الاصطناعي
  const AiImageOptionsModel({
    super.imageStyle = ImageStyle.realistic,
    super.aspectRatio = ImageAspectRatio.square,
    super.resolution = ImageResolution.medium,
  });

  /// إنشاء نموذج من JSON
  factory AiImageOptionsModel.fromJson(Map<String, dynamic> json) {
    return AiImageOptionsModel(
      imageStyle: _parseImageStyle(json['imageStyle']),
      aspectRatio: _parseAspectRatio(json['aspectRatio']),
      resolution: _parseResolution(json['resolution']),
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'imageStyle': imageStyle.toString().split('.').last,
      'aspectRatio': aspectRatio.toString().split('.').last,
      'resolution': resolution.toString().split('.').last,
    };
  }

  /// تحليل أسلوب الصورة من النص
  static ImageStyle _parseImageStyle(String? value) {
    if (value == null) return ImageStyle.realistic;
    
    switch (value.toLowerCase()) {
      case 'artistic':
        return ImageStyle.artistic;
      case 'cartoon':
        return ImageStyle.cartoon;
      case 'watercolor':
        return ImageStyle.watercolor;
      case 'oil':
        return ImageStyle.oil;
      case 'digital':
        return ImageStyle.digital;
      case 'realistic':
      default:
        return ImageStyle.realistic;
    }
  }

  /// تحليل نسبة الأبعاد من النص
  static ImageAspectRatio _parseAspectRatio(String? value) {
    if (value == null) return ImageAspectRatio.square;
    
    switch (value.toLowerCase()) {
      case 'landscape':
        return ImageAspectRatio.landscape;
      case 'portrait':
        return ImageAspectRatio.portrait;
      case 'standard':
        return ImageAspectRatio.standard;
      case 'square':
      default:
        return ImageAspectRatio.square;
    }
  }

  /// تحليل الدقة من النص
  static ImageResolution _parseResolution(String? value) {
    if (value == null) return ImageResolution.medium;
    
    switch (value.toLowerCase()) {
      case 'low':
        return ImageResolution.low;
      case 'high':
        return ImageResolution.high;
      case 'medium':
      default:
        return ImageResolution.medium;
    }
  }
}
