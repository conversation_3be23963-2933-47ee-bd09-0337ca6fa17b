// lib/features/ai/data/models/ai_text_result_model.dart

import '../../domain/entities/ai_text_result.dart';

/// نموذج نتيجة توليد النص بالذكاء الاصطناعي
class AiTextResultModel extends AiTextResult {
  /// إنشاء نموذج نتيجة توليد النص بالذكاء الاصطناعي
  const AiTextResultModel({
    required super.generatedTexts,
    required super.originalPrompt,
    required super.timestamp,
  });

  /// إنشاء نموذج من JSON
  factory AiTextResultModel.fromJson(Map<String, dynamic> json) {
    return AiTextResultModel(
      generatedTexts: List<String>.from(json['generatedTexts'] ?? []),
      originalPrompt: json['originalPrompt'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'generatedTexts': generatedTexts,
      'originalPrompt': originalPrompt,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
