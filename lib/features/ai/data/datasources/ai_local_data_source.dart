// lib/features/ai/data/datasources/ai_local_data_source.dart

import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/errors/exceptions.dart';
import '../models/ai_image_result_model.dart';
import '../models/ai_text_result_model.dart';

/// مصدر البيانات المحلي للذكاء الاصطناعي
abstract class AiLocalDataSource {
  /// حفظ نتيجة نص مولد في التاريخ
  ///
  /// @param result نتيجة النص المولد للحفظ
  /// @throws CacheException إذا حدث خطأ أثناء الحفظ
  Future<void> saveToHistory(AiTextResultModel result);

  /// الحصول على تاريخ النصوص المولدة
  ///
  /// @return قائمة بنتائج النصوص المولدة
  /// @throws CacheException إذا حدث خطأ أثناء استرجاع البيانات
  Future<List<AiTextResultModel>> getTextHistory();

  /// حفظ نتيجة صورة مولدة في التاريخ
  ///
  /// @param result نتيجة الصورة المولدة للحفظ
  /// @throws CacheException إذا حدث خطأ أثناء الحفظ
  Future<void> saveImageToHistory(AiImageResultModel result);

  /// الحصول على تاريخ الصور المولدة
  ///
  /// @return قائمة بنتائج الصور المولدة
  /// @throws CacheException إذا حدث خطأ أثناء استرجاع البيانات
  Future<List<AiImageResultModel>> getImageHistory();
}

/// تنفيذ مصدر البيانات المحلي للذكاء الاصطناعي باستخدام SharedPreferences
class AiLocalDataSourceImpl implements AiLocalDataSource {
  /// مفتاح تخزين تاريخ النصوص
  static const String _textHistoryKey = 'ai_text_history';

  /// مفتاح تخزين تاريخ الصور
  static const String _imageHistoryKey = 'ai_image_history';

  /// الحد الأقصى لعدد العناصر في التاريخ
  static const int _maxHistoryItems = 50;

  /// مثيل SharedPreferences
  final SharedPreferences sharedPreferences;

  /// إنشاء مصدر بيانات محلي جديد
  AiLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> saveToHistory(AiTextResultModel result) async {
    try {
      // الحصول على التاريخ الحالي
      final currentHistory = await getTextHistory();

      // إضافة النتيجة الجديدة في بداية القائمة
      currentHistory.insert(0, result);

      // اقتصار القائمة على الحد الأقصى للعناصر
      if (currentHistory.length > _maxHistoryItems) {
        currentHistory.removeRange(_maxHistoryItems, currentHistory.length);
      }

      // تحويل القائمة إلى JSON
      final jsonList = currentHistory.map((item) => item.toJson()).toList();

      // حفظ القائمة في التخزين المحلي
      await sharedPreferences.setString(_textHistoryKey, json.encode(jsonList));
    } catch (e) {
      throw CacheException(message: 'Failed to save to history: $e');
    }
  }

  @override
  Future<List<AiTextResultModel>> getTextHistory() async {
    try {
      // الحصول على سلسلة JSON المخزنة
      final jsonString = sharedPreferences.getString(_textHistoryKey);

      // إذا لم تكن هناك بيانات مخزنة، إرجاع قائمة فارغة
      if (jsonString == null) {
        return [];
      }

      // تحليل سلسلة JSON إلى قائمة من الكائنات
      final jsonList = json.decode(jsonString) as List;

      // تحويل كل كائن JSON إلى نموذج نتيجة نص
      return jsonList.map((item) => AiTextResultModel.fromJson(item)).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get text history: $e');
    }
  }

  @override
  Future<void> saveImageToHistory(AiImageResultModel result) async {
    try {
      // الحصول على التاريخ الحالي
      final currentHistory = await getImageHistory();

      // إضافة النتيجة الجديدة في بداية القائمة
      currentHistory.insert(0, result);

      // اقتصار القائمة على الحد الأقصى للعناصر
      if (currentHistory.length > _maxHistoryItems) {
        currentHistory.removeRange(_maxHistoryItems, currentHistory.length);
      }

      // تحويل القائمة إلى JSON
      final jsonList = currentHistory.map((item) => item.toJson()).toList();

      // حفظ القائمة في التخزين المحلي
      await sharedPreferences.setString(
          _imageHistoryKey, json.encode(jsonList));
    } catch (e) {
      throw CacheException(message: 'Failed to save to image history: $e');
    }
  }

  @override
  Future<List<AiImageResultModel>> getImageHistory() async {
    try {
      // الحصول على سلسلة JSON المخزنة
      final jsonString = sharedPreferences.getString(_imageHistoryKey);

      // إذا لم تكن هناك بيانات مخزنة، إرجاع قائمة فارغة
      if (jsonString == null) {
        return [];
      }

      // تحليل سلسلة JSON إلى قائمة من الكائنات
      final jsonList = json.decode(jsonString) as List;

      // تحويل كل كائن JSON إلى نموذج نتيجة صورة
      return jsonList.map((item) => AiImageResultModel.fromJson(item)).toList();
    } catch (e) {
      throw CacheException(message: 'Failed to get image history: $e');
    }
  }
}
