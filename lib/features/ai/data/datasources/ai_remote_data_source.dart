import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/ai_image_options.dart';
import '../../domain/entities/ai_text_options.dart';
import '../models/ai_image_result_model.dart';
import '../models/ai_text_result_model.dart';

/// واجهة مجردة لمصدر بيانات توليد النصوص والصور بالذكاء الاصطناعي.
///
/// تحدد هذه الواجهة طرقًا لتوليد محتوى نصي وصور بالذكاء الاصطناعي
/// من خلال التواصل مع خدمات الذكاء الاصطناعي الخارجية.
abstract class AiRemoteDataSource {
  /// توليد نص بالذكاء الاصطناعي بناءً على المطالبة المقدمة.
  ///
  /// @param prompt المطالبة النصية لتوجيه توليد النص بالذكاء الاصطناعي
  /// @return مستقبل يتم حله إلى سلسلة النص المولد
  Future<String> generateAiText(String prompt);

  /// توليد نص بالذكاء الاصطناعي بناءً على المطالبة المقدمة مع خيارات متقدمة.
  ///
  /// @param prompt المطالبة النصية لتوجيه توليد النص بالذكاء الاصطناعي
  /// @param options خيارات توليد النص (الطول، الأسلوب، إلخ)
  /// @return مستقبل يتم حله إلى نتيجة النص المولد
  /// @throws ServerException إذا كان هناك خطأ في الاتصال بالخادم
  Future<AiTextResultModel> generateAiTextAdvanced(
    String prompt,
    AiTextOptions options,
  );

  /// توليد صورة بالذكاء الاصطناعي بناءً على المطالبة المقدمة.
  ///
  /// @param prompt المطالبة النصية لتوجيه توليد الصورة بالذكاء الاصطناعي
  /// @param options خيارات توليد الصورة (الأسلوب، نسبة الأبعاد، الدقة)
  /// @return مستقبل يتم حله إلى نتيجة الصورة المولدة
  /// @throws ServerException إذا كان هناك خطأ في الاتصال بالخادم
  Future<AiImageResultModel> generateAiImage(
    String prompt,
    AiImageOptions options,
  );

  /// استرجاع مطالبات مقترحة للمناسبة المحددة.
  ///
  /// @param occasionId معرف المناسبة للحصول على المطالبات
  /// @return مستقبل يتم حله إلى قائمة من المطالبات المقترحة
  /// @throws ServerException إذا كان هناك خطأ في الاتصال بالخادم
  Future<List<String>> getSuggestedPrompts(String occasionId);
}

/// تنفيذ [AiRemoteDataSource] باستخدام واجهة برمجة تطبيقات Gemini و Stability AI.
///
/// تتعامل هذه الفئة مع اتصالات HTTP مع واجهات برمجة التطبيقات لتوليد
/// نصوص وصور بالذكاء الاصطناعي بناءً على مطالبات المستخدم.
class AiRemoteDataSourceImpl implements AiRemoteDataSource {
  /// عميل HTTP لإجراء طلبات API
  final http.Client client;

  /// مفتاح API للوصول إلى Gemini API
  /// ملاحظة: في الإنتاج، يجب تخزين هذا بشكل آمن وليس في كود المصدر
  static const String _geminiApiKey = 'AIzaSyDK4Z96CRtPDzq_5LHBZwXBH74Gk4S2mQo';

  /// مفتاح API للوصول إلى Stability AI API
  /// ملاحظة: في الإنتاج، يجب تخزين هذا بشكل آمن وليس في كود المصدر
  static const String _stabilityApiKey =
      'sk-Nt1aBCdEfGhIjKlMnOpQrStUvWxYz0123456789';

  /// إنشاء مثيل جديد مع عميل HTTP المطلوب
  AiRemoteDataSourceImpl({required this.client});

  @override
  Future<String> generateAiText(String prompt) async {
    // Modify the prompt to request exactly 60 characters
    final modifiedPrompt =
        "$prompt\nPlease generate a complete and correct phrase that is exactly 60 characters long.";

    // Construct the API endpoint URL with the API key
    final url = Uri.parse(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=$_geminiApiKey',
    );

    // Set the required headers for the API request
    final headers = {
      'Content-Type': 'application/json',
    };

    // Construct the request body according to Gemini API format:
    // "contents" -> "parts" -> {"text": "..."}
    final body = jsonEncode({
      "contents": [
        {
          "parts": [
            {"text": modifiedPrompt}
          ]
        }
      ]
    });

    // Send the POST request to the Gemini API
    final response = await client.post(url, headers: headers, body: body);

    if (response.statusCode == 200) {
      final decoded = json.decode(response.body);

      // Extract the "candidates" array from the response
      final candidates = decoded["candidates"];
      if (candidates == null || candidates is! List || candidates.isEmpty) {
        throw ServerException(
          message:
              'Missing or empty "candidates" in response: ${response.body}',
        );
      }

      // Get the first candidate from the response
      final firstCandidate = candidates[0];

      // Extract the content object which should contain "parts" -> [{"text": "..."}]
      final contentObj = firstCandidate["content"];
      if (contentObj == null ||
          contentObj["parts"] == null ||
          contentObj["parts"] is! List ||
          (contentObj["parts"] as List).isEmpty) {
        throw ServerException(
          message:
              'Missing or empty "parts" in first candidate content: ${response.body}',
        );
      }

      // Extract the parts array from the content object
      final parts = contentObj["parts"] as List;

      // Get the generated text from the first part
      final generatedText = parts[0]["text"] as String? ?? '';

      // Optional: Verify the text is exactly 60 characters
      // if (generatedText.length != 60) { ... }

      return generatedText;
    } else {
      throw ServerException(
        message: 'Gemini API Error: ${response.body}',
      );
    }
  }

  @override
  Future<AiTextResultModel> generateAiTextAdvanced(
    String prompt,
    AiTextOptions options,
  ) async {
    // تعديل المطالبة بناءً على الخيارات المقدمة
    String modifiedPrompt = prompt;

    // إضافة تعليمات حول طول النص المطلوب - تحديد أقصى 60 حرف
    switch (options.textLength) {
      case TextLength.short:
        modifiedPrompt += "\nPlease generate a short text (maximum 40 characters).";
        break;
      case TextLength.medium:
        modifiedPrompt +=
            "\nPlease generate a medium-length text (maximum 60 characters).";
        break;
      case TextLength.long:
        modifiedPrompt +=
            "\nPlease generate a longer text (maximum 60 characters).";
        break;
    }

    // إضافة تعليمات حول أسلوب النص المطلوب
    switch (options.textStyle) {
      case TextStyle.formal:
        modifiedPrompt += "\nUse a formal and professional tone.";
        break;
      case TextStyle.emotional:
        modifiedPrompt += "\nUse an emotional and heartfelt tone.";
        break;
      case TextStyle.humorous:
        modifiedPrompt += "\nUse a humorous and light-hearted tone.";
        break;
      case TextStyle.literary:
        modifiedPrompt += "\nUse a literary and poetic style.";
        break;
    }

    // إضافة تعليمات حول عدد النصوص المطلوبة
    if (options.numberOfTexts > 1) {
      modifiedPrompt +=
          "\nPlease generate ${options.numberOfTexts} different variations, separated by '|'.";
    }

    // إنشاء عنوان URL لواجهة برمجة التطبيقات
    final url = Uri.parse(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=$_geminiApiKey',
    );

    // تعيين الرؤوس المطلوبة لطلب واجهة برمجة التطبيقات
    final headers = {
      'Content-Type': 'application/json',
    };

    // إنشاء نص الطلب وفقًا لتنسيق Gemini API
    final body = jsonEncode({
      "contents": [
        {
          "parts": [
            {"text": modifiedPrompt}
          ]
        }
      ]
    });

    // إرسال طلب POST إلى Gemini API
    final response = await client.post(url, headers: headers, body: body);

    if (response.statusCode == 200) {
      final decoded = json.decode(response.body);

      // استخراج مصفوفة "candidates" من الاستجابة
      final candidates = decoded["candidates"];
      if (candidates == null || candidates is! List || candidates.isEmpty) {
        throw ServerException(
          message:
              'Missing or empty "candidates" in response: ${response.body}',
        );
      }

      // الحصول على المرشح الأول من الاستجابة
      final firstCandidate = candidates[0];

      // استخراج كائن المحتوى الذي يجب أن يحتوي على "parts" -> [{"text": "..."}]
      final contentObj = firstCandidate["content"];
      if (contentObj == null ||
          contentObj["parts"] == null ||
          contentObj["parts"] is! List ||
          (contentObj["parts"] as List).isEmpty) {
        throw ServerException(
          message:
              'Missing or empty "parts" in first candidate content: ${response.body}',
        );
      }

      // استخراج مصفوفة الأجزاء من كائن المحتوى
      final parts = contentObj["parts"] as List;

      // الحصول على النص المولد من الجزء الأول
      final generatedText = parts[0]["text"] as String? ?? '';

      // معالجة النص المولد لاستخراج النصوص المختلفة إذا كان هناك أكثر من نص
      List<String> generatedTexts = [];

      if (options.numberOfTexts > 1) {
        // تقسيم النص بناءً على الفاصل '|'
        generatedTexts = generatedText
            .split('|')
            .map((text) => text.trim())
            .where((text) => text.isNotEmpty)
            .map((text) => _limitTextLength(text, 60)) // تحديد النص بـ 60 حرف
            .toList();

        // التأكد من أن لدينا العدد المطلوب من النصوص
        while (generatedTexts.length < options.numberOfTexts) {
          generatedTexts
              .add(''); // إضافة نصوص فارغة إذا لم يتم توليد العدد المطلوب
        }

        // اقتصار القائمة على العدد المطلوب
        if (generatedTexts.length > options.numberOfTexts) {
          generatedTexts = generatedTexts.sublist(0, options.numberOfTexts);
        }
      } else {
        // إذا كان المطلوب نص واحد فقط - تحديد النص بـ 60 حرف
        generatedTexts = [_limitTextLength(generatedText, 60)];
      }

      // إنشاء وإرجاع نموذج نتيجة النص
      return AiTextResultModel(
        generatedTexts: generatedTexts,
        originalPrompt: prompt,
        timestamp: DateTime.now(),
      );
    } else {
      throw ServerException(
        message: 'Gemini API Error: ${response.body}',
      );
    }
  }

  @override
  Future<AiImageResultModel> generateAiImage(
    String prompt,
    AiImageOptions options,
  ) async {
    try {
      // الحصول على أبعاد الصورة بناءً على الخيارات
      final dimensions = options.getDimensions();
      final width = dimensions['width']!;
      final height = dimensions['height']!;

      // تعديل المطالبة بناءً على أسلوب الصورة المطلوب
      String modifiedPrompt = prompt;

      switch (options.imageStyle) {
        case ImageStyle.realistic:
          modifiedPrompt += ", photorealistic, high detail, 8k";
          break;
        case ImageStyle.artistic:
          modifiedPrompt += ", artistic style, creative, vibrant colors";
          break;
        case ImageStyle.cartoon:
          modifiedPrompt += ", cartoon style, colorful, animated look";
          break;
        case ImageStyle.watercolor:
          modifiedPrompt +=
              ", watercolor painting style, soft edges, flowing colors";
          break;
        case ImageStyle.oil:
          modifiedPrompt += ", oil painting style, textured, rich colors";
          break;
        case ImageStyle.digital:
          modifiedPrompt += ", digital art, modern, clean lines";
          break;
      }

      // إنشاء عنوان URL لواجهة برمجة التطبيقات Stability AI
      final url = Uri.parse(
        'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image',
      );

      // تعيين الرؤوس المطلوبة لطلب واجهة برمجة التطبيقات
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $_stabilityApiKey',
      };

      // إنشاء نص الطلب وفقًا لتنسيق Stability AI API
      final body = jsonEncode({
        "text_prompts": [
          {"text": modifiedPrompt, "weight": 1.0}
        ],
        "cfg_scale": 7,
        "height": height,
        "width": width,
        "samples": 1,
        "steps": 30,
      });

      // إرسال طلب POST إلى Stability AI API
      final response = await client.post(url, headers: headers, body: body);

      if (response.statusCode == 200) {
        final decoded = json.decode(response.body);

        // استخراج البيانات المشفرة للصور من الاستجابة
        final artifacts = decoded["artifacts"] as List;
        if (artifacts.isEmpty) {
          throw ServerException(
            message: 'No images generated in response',
          );
        }

        // حفظ الصور المولدة
        final List<String> imagePaths = [];
        final tempDir = await getTemporaryDirectory();

        for (var i = 0; i < artifacts.length; i++) {
          final base64Image = artifacts[i]["base64"];
          final imageBytes = base64Decode(base64Image);

          // إنشاء اسم ملف فريد
          final uuid = const Uuid().v4();
          final imagePath = '${tempDir.path}/ai_generated_$uuid.png';

          // حفظ الصورة في ملف
          final imageFile = File(imagePath);
          await imageFile.writeAsBytes(imageBytes);

          imagePaths.add(imagePath);
        }

        // إنشاء وإرجاع نموذج نتيجة الصورة
        return AiImageResultModel(
          generatedImagePaths: imagePaths,
          originalPrompt: prompt,
          timestamp: DateTime.now(),
        );
      } else {
        throw ServerException(
          message: 'Stability AI API Error: ${response.body}',
        );
      }
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
        message: 'Error generating image: $e',
      );
    }
  }

  @override
  Future<List<String>> getSuggestedPrompts(String occasionId) async {
    // قائمة الاقتراحات المحددة مسبقًا حسب المناسبة
    final Map<String, List<String>> occasionPrompts = {
      'eid_fitr': [
        'تهنئة بمناسبة عيد الفطر',
        'دعاء بمناسبة عيد الفطر',
        'تهنئة للأهل والأصدقاء بعيد الفطر',
        'رسالة تهنئة رسمية بعيد الفطر',
        'عبارات تهنئة بالعيد',
      ],
      'eid_adha': [
        'تهنئة بمناسبة عيد الأضحى',
        'دعاء بمناسبة عيد الأضحى',
        'تهنئة للأهل والأصدقاء بعيد الأضحى',
        'رسالة تهنئة رسمية بعيد الأضحى',
        'عبارات تهنئة بالعيد',
      ],
      'ramadan': [
        'تهنئة بمناسبة شهر رمضان',
        'دعاء بمناسبة شهر رمضان',
        'رسالة تهنئة بحلول شهر رمضان',
        'عبارات تهنئة بشهر رمضان',
        'تهنئة رمضانية للأهل والأصدقاء',
      ],
      'wedding': [
        'تهنئة بمناسبة غالية وجت',
        'دعاء للعروسين',
        'عبارات تهنئة بالزفاف',
        'رسالة تهنئة للعروسين',
        'تهنئة زواج للأصدقاء',
      ],
      'newborn': [
        'تهنئة بمناسبة نور البيت',
        'دعاء للمولود الجديد',
        'عبارات تهنئة بقدوم مولود',
        'رسالة تهنئة بالمولود',
        'تهنئة بالمولود الجديد للأصدقاء',
      ],
      'graduation': [
        'تهنئة بمناسبة مبروك التخرج',
        'عبارات تهنئة بالنجاح والتخرج',
        'رسالة تهنئة للخريج',
        'تهنئة تخرج للأصدقاء',
        'كلمات تشجيعية للخريجين',
      ],
      'birthday': [
        'تهنئة بمناسبة سنة حلوة',
        'عبارات تهنئة بعيد الميلاد',
        'رسالة تهنئة لصديق بعيد ميلاده',
        'تهنئة عيد ميلاد للأحباء',
        'كلمات جميلة لعيد الميلاد',
      ],
      'hajj': [
        'دعاء للحجاج',
        'تهنئة بمناسبة الحج',
        'عبارات تهنئة بأداء فريضة الحج',
        'رسالة تهنئة للحجاج',
        'دعاء للحجاج بالتوفيق',
      ],
      'general': [
        'عبارات شكر وتقدير',
        'كلمات تشجيعية',
        'عبارات تحفيزية',
        'رسالة شكر',
        'دعاء للأحباء',
      ],
    };

    // إرجاع الاقتراحات المناسبة للمناسبة المحددة، أو قائمة عامة إذا لم تكن المناسبة موجودة
    return occasionPrompts[occasionId] ?? occasionPrompts['general']!;
  }

  /// تحديد طول النص بحد أقصى معين
  String _limitTextLength(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }

    // قطع النص عند آخر مسافة قبل الحد الأقصى لتجنب قطع الكلمات
    String truncated = text.substring(0, maxLength);
    int lastSpaceIndex = truncated.lastIndexOf(' ');

    if (lastSpaceIndex > maxLength * 0.7) { // إذا كانت آخر مسافة قريبة من النهاية
      return truncated.substring(0, lastSpaceIndex).trim();
    } else {
      // إذا لم توجد مسافة مناسبة، قطع النص مباشرة
      return truncated.trim();
    }
  }
}
