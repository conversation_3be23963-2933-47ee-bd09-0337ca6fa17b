// lib/features/ai/data/repositories/ai_repository_impl.dart

import 'package:dartz/dartz.dart';

import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/ai_image_options.dart';
import '../../domain/entities/ai_image_result.dart';
import '../../domain/entities/ai_text_options.dart';
import '../../domain/entities/ai_text_result.dart';
import '../../domain/repositories/ai_repository.dart';
import '../datasources/ai_local_data_source.dart';
import '../datasources/ai_remote_data_source.dart';
import '../models/ai_image_result_model.dart';
import '../models/ai_text_result_model.dart';

/// Implementation of the [AiRepository] interface.
///
/// This class serves as a bridge between the domain layer and the data layer,
/// handling the communication with the AI remote data source and converting
/// exceptions to failures according to Clean Architecture principles.
class AiRepositoryImpl implements AiRepository {
  /// The remote data source for AI operations
  final AiRemoteDataSource remoteDataSource;

  /// The local data source for AI operations
  final AiLocalDataSource localDataSource;

  /// Creates a new instance with the required data sources
  AiRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, String>> generateAiText(String prompt) async {
    try {
      // Call the remote data source to generate AI text
      final result = await remoteDataSource.generateAiText(prompt);

      // Return the result wrapped in Right if successful
      return Right(result);
    } on ServerException catch (e) {
      // Convert ServerException to ServerFailure and wrap in Left
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, AiTextResult>> generateAiTextAdvanced(
    String prompt,
    AiTextOptions options,
  ) async {
    try {
      final result =
          await remoteDataSource.generateAiTextAdvanced(prompt, options);

      // حفظ النتيجة في التاريخ
      await localDataSource.saveToHistory(result);

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<String>>> getSuggestedPrompts(
      String occasionId) async {
    try {
      final result = await remoteDataSource.getSuggestedPrompts(occasionId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> saveToHistory(AiTextResult result) async {
    try {
      // تحويل النتيجة إلى نموذج
      final resultModel = result as AiTextResultModel;

      await localDataSource.saveToHistory(resultModel);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<AiTextResult>>> getTextHistory() async {
    try {
      final result = await localDataSource.getTextHistory();
      return Right(result);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, AiImageResult>> generateAiImage(
    String prompt,
    AiImageOptions options,
  ) async {
    try {
      final result = await remoteDataSource.generateAiImage(prompt, options);

      // حفظ النتيجة في التاريخ
      await localDataSource.saveImageToHistory(result);

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> saveImageToHistory(AiImageResult result) async {
    try {
      // تحويل النتيجة إلى نموذج
      final resultModel = result as AiImageResultModel;

      await localDataSource.saveImageToHistory(resultModel);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<AiImageResult>>> getImageHistory() async {
    try {
      final result = await localDataSource.getImageHistory();
      return Right(result);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }
}
