// lib/features/ai/presentation/blocs/ai_bloc_handlers.dart

part of 'ai_bloc.dart';

/// معالجات الأحداث الإضافية لـ AiBloc
extension AiBlocHandlers on AiBloc {
  /// معالجة حدث توليد النص بالذكاء الاصطناعي مع خيارات متقدمة
  Future<void> _onGenerateAiTextAdvanced(
    GenerateAiTextAdvancedEvent event,
    Emitter<AiState> emit,
  ) async {
    if (generateAiTextAdvancedUseCase == null) {
      emit(AiError('Advanced text generation is not available'));
      return;
    }

    // إصدار حالة التحميل
    emit(AiLoading());

    // استدعاء حالة الاستخدام مع المطالبة والخيارات من الحدث
    final params = GenerateAiTextAdvancedParams(
      prompt: event.prompt,
      options: event.options,
    );

    final result = await generateAiTextAdvancedUseCase!(params);

    // معالجة النتيجة باستخدام fold
    result.fold(
      // في حالة الفشل، إصدار حالة خطأ مع رسالة الفشل
      (failure) => emit(AiError(failure.message)),
      // في حالة النجاح، إصدار حالة نجاح مع النص المولد
      (textResult) => emit(AiTextResultGenerated(textResult)),
    );
  }

  /// معالجة حدث الحصول على اقتراحات المطالبات
  Future<void> _onGetSuggestedPrompts(
    GetSuggestedPromptsEvent event,
    Emitter<AiState> emit,
  ) async {
    if (getSuggestedPromptsUseCase == null) {
      emit(AiError('Suggested prompts are not available'));
      return;
    }

    // إصدار حالة التحميل
    emit(AiLoading());

    // استدعاء حالة الاستخدام مع معرف المناسبة من الحدث
    final params = GetSuggestedPromptsParams(event.occasionId);
    final result = await getSuggestedPromptsUseCase!(params);

    // معالجة النتيجة باستخدام fold
    result.fold(
      // في حالة الفشل، إصدار حالة خطأ مع رسالة الفشل
      (failure) => emit(AiError(failure.message)),
      // في حالة النجاح، إصدار حالة نجاح مع الاقتراحات
      (prompts) => emit(AiSuggestedPromptsLoaded(prompts)),
    );
  }

  /// معالجة حدث الحصول على تاريخ النصوص
  Future<void> _onGetTextHistory(
    GetTextHistoryEvent event,
    Emitter<AiState> emit,
  ) async {
    if (getTextHistoryUseCase == null) {
      emit(AiError('Text history is not available'));
      return;
    }

    // إصدار حالة التحميل
    emit(AiLoading());

    // استدعاء حالة الاستخدام
    final result = await getTextHistoryUseCase!(NoParams());

    // معالجة النتيجة باستخدام fold
    result.fold(
      // في حالة الفشل، إصدار حالة خطأ مع رسالة الفشل
      (failure) => emit(AiError(failure.message)),
      // في حالة النجاح، إصدار حالة نجاح مع التاريخ
      (history) => emit(AiTextHistoryLoaded(history)),
    );
  }
}
