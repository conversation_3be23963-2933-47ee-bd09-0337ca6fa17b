// lib/features/ai/presentation/blocs/ai_state.dart

part of 'ai_bloc.dart';

/// Base class for all AI-related states.
///
/// This abstract class serves as the base for all possible states
/// of the [AiBloc].
abstract class AiState extends Equatable {
  const AiState();

  @override
  List<Object?> get props => [];
}

/// Initial state of the AI feature.
///
/// This state represents the initial state before any AI text
/// generation has been requested.
class AiInitial extends AiState {}

/// Loading state during AI text generation.
///
/// This state is active while waiting for the AI to generate text.
class AiLoading extends AiState {}

/// Success state after AI text has been generated.
///
/// This state contains the successfully generated AI text.
class AiSuccess extends AiState {
  /// The text generated by the AI
  final String generatedText;

  /// Creates a new success state with the generated text
  const AiSuccess(this.generatedText);

  @override
  List<Object?> get props => [generatedText];
}

/// Success state after AI text has been generated with advanced options.
///
/// This state is now renamed to AiTextGenerated for consistency.
class AiTextGenerated extends AiState {
  /// The text generated by the AI
  final String generatedText;

  /// Creates a new success state with the generated text
  const AiTextGenerated(this.generatedText);

  @override
  List<Object?> get props => [generatedText];
}

/// Success state after AI text result has been generated.
///
/// This state contains the successfully generated AI text result,
/// which may include multiple text variations.
class AiTextResultGenerated extends AiState {
  /// The text result generated by the AI
  final AiTextResult result;

  /// Creates a new success state with the generated text result
  const AiTextResultGenerated(this.result);

  @override
  List<Object?> get props => [result];
}

/// Success state after suggested prompts have been loaded.
///
/// This state contains the successfully loaded suggested prompts.
class AiSuggestedPromptsLoaded extends AiState {
  /// The suggested prompts
  final List<String> prompts;

  /// Creates a new success state with the suggested prompts
  const AiSuggestedPromptsLoaded(this.prompts);

  @override
  List<Object?> get props => [prompts];
}

/// Success state after text history has been loaded.
///
/// This state contains the successfully loaded text history.
class AiTextHistoryLoaded extends AiState {
  /// The text history
  final List<AiTextResult> history;

  /// Creates a new success state with the text history
  const AiTextHistoryLoaded(this.history);

  @override
  List<Object?> get props => [history];
}

/// Error state when AI text generation fails.
///
/// This state contains an error message explaining what went wrong.
class AiError extends AiState {
  /// The error message describing what went wrong
  final String message;

  /// Creates a new error state with the error message
  const AiError(this.message);

  @override
  List<Object?> get props => [message];
}
