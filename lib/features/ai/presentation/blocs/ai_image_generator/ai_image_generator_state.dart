// lib/features/ai/presentation/blocs/ai_image_generator/ai_image_generator_state.dart

import 'package:equatable/equatable.dart';

import '../../../domain/entities/ai_image_options.dart';
import '../../../domain/entities/ai_image_result.dart';

/// حالة مولد الصور بالذكاء الاصطناعي
class AiImageGeneratorState extends Equatable {
  /// ما إذا كان جاري التحميل
  final bool isLoading;
  
  /// رسالة الخطأ (إذا وجدت)
  final String? errorMessage;
  
  /// نتيجة الصورة المولدة الحالية
  final AiImageResult? currentResult;
  
  /// تاريخ الصور المولدة
  final List<AiImageResult> history;
  
  /// خيارات توليد الصور الحالية
  final AiImageOptions options;
  
  /// إنشاء حالة جديدة
  const AiImageGeneratorState({
    this.isLoading = false,
    this.errorMessage,
    this.currentResult,
    this.history = const [],
    this.options = const AiImageOptions(),
  });
  
  /// إنشاء حالة أولية
  factory AiImageGeneratorState.initial() {
    return const AiImageGeneratorState();
  }
  
  /// إنشاء نسخة جديدة من الحالة مع تحديث بعض الخصائص
  AiImageGeneratorState copyWith({
    bool? isLoading,
    String? errorMessage,
    AiImageResult? currentResult,
    List<AiImageResult>? history,
    AiImageOptions? options,
    bool clearError = false,
    bool clearCurrentResult = false,
  }) {
    return AiImageGeneratorState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      currentResult: clearCurrentResult ? null : (currentResult ?? this.currentResult),
      history: history ?? this.history,
      options: options ?? this.options,
    );
  }
  
  @override
  List<Object?> get props => [
    isLoading,
    errorMessage,
    currentResult,
    history,
    options,
  ];
}
