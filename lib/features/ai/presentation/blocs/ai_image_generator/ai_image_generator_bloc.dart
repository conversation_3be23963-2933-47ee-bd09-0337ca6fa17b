// lib/features/ai/presentation/blocs/ai_image_generator/ai_image_generator_bloc.dart

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/usecases/usecase.dart';
import '../../../domain/usecases/generate_ai_image_usecase.dart';
import '../../../domain/usecases/get_image_history_usecase.dart';
import 'ai_image_generator_event.dart';
import 'ai_image_generator_state.dart';

/// BLoC لمولد الصور بالذكاء الاصطناعي
class AiImageGeneratorBloc
    extends Bloc<AiImageGeneratorEvent, AiImageGeneratorState> {
  /// حالة استخدام توليد الصور
  final GenerateAiImageUseCase generateAiImageUseCase;
  
  /// حالة استخدام الحصول على تاريخ الصور
  final GetImageHistoryUseCase getImageHistoryUseCase;
  
  /// إنشاء BLoC جديد
  AiImageGeneratorBloc({
    required this.generateAiImageUseCase,
    required this.getImageHistoryUseCase,
  }) : super(AiImageGeneratorState.initial()) {
    on<GenerateAiImageEvent>(_onGenerateAiImage);
    on<LoadImageHistoryEvent>(_onLoadImageHistory);
    on<UpdateImageOptionsEvent>(_onUpdateImageOptions);
    on<ResetAiImageGeneratorEvent>(_onResetGenerator);
  }
  
  /// معالجة حدث توليد صورة
  Future<void> _onGenerateAiImage(
    GenerateAiImageEvent event,
    Emitter<AiImageGeneratorState> emit,
  ) async {
    // تحديث الحالة لتعكس بدء التحميل
    emit(state.copyWith(
      isLoading: true,
      clearError: true,
    ));
    
    // استدعاء حالة الاستخدام
    final result = await generateAiImageUseCase(
      GenerateAiImageParams(
        prompt: event.prompt,
        options: event.options,
      ),
    );
    
    // معالجة النتيجة
    result.fold(
      // في حالة الفشل
      (failure) => emit(state.copyWith(
        isLoading: false,
        errorMessage: failure.message,
      )),
      // في حالة النجاح
      (imageResult) => emit(state.copyWith(
        isLoading: false,
        currentResult: imageResult,
        // تحديث التاريخ بإضافة النتيجة الجديدة في البداية
        history: [imageResult, ...state.history],
      )),
    );
  }
  
  /// معالجة حدث تحميل تاريخ الصور
  Future<void> _onLoadImageHistory(
    LoadImageHistoryEvent event,
    Emitter<AiImageGeneratorState> emit,
  ) async {
    // تحديث الحالة لتعكس بدء التحميل
    emit(state.copyWith(isLoading: true));
    
    // استدعاء حالة الاستخدام
    final result = await getImageHistoryUseCase(NoParams());
    
    // معالجة النتيجة
    result.fold(
      // في حالة الفشل
      (failure) => emit(state.copyWith(
        isLoading: false,
        errorMessage: failure.message,
      )),
      // في حالة النجاح
      (history) => emit(state.copyWith(
        isLoading: false,
        history: history,
      )),
    );
  }
  
  /// معالجة حدث تحديث خيارات توليد الصور
  void _onUpdateImageOptions(
    UpdateImageOptionsEvent event,
    Emitter<AiImageGeneratorState> emit,
  ) {
    emit(state.copyWith(options: event.options));
  }
  
  /// معالجة حدث إعادة تعيين المولد
  void _onResetGenerator(
    ResetAiImageGeneratorEvent event,
    Emitter<AiImageGeneratorState> emit,
  ) {
    emit(AiImageGeneratorState.initial());
  }
}
