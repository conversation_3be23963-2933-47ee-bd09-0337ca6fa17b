// lib/features/ai/presentation/blocs/ai_image_generator/ai_image_generator_event.dart

import 'package:equatable/equatable.dart';

import '../../../domain/entities/ai_image_options.dart';

/// حدث أساسي لمولد الصور بالذكاء الاصطناعي
abstract class AiImageGeneratorEvent extends Equatable {
  /// إنشاء حدث جديد
  const AiImageGeneratorEvent();

  @override
  List<Object> get props => [];
}

/// حدث توليد صورة بالذكاء الاصطناعي
class GenerateAiImageEvent extends AiImageGeneratorEvent {
  /// المطالبة النصية لتوليد الصورة
  final String prompt;
  
  /// خيارات توليد الصورة
  final AiImageOptions options;
  
  /// إنشاء حدث جديد
  const GenerateAiImageEvent({
    required this.prompt,
    required this.options,
  });
  
  @override
  List<Object> get props => [prompt, options];
}

/// حدث تحميل تاريخ الصور المولدة
class LoadImageHistoryEvent extends AiImageGeneratorEvent {
  /// إنشاء حدث جديد
  const LoadImageHistoryEvent();
}

/// حدث تحديث خيارات توليد الصور
class UpdateImageOptionsEvent extends AiImageGeneratorEvent {
  /// خيارات توليد الصورة الجديدة
  final AiImageOptions options;
  
  /// إنشاء حدث جديد
  const UpdateImageOptionsEvent({
    required this.options,
  });
  
  @override
  List<Object> get props => [options];
}

/// حدث إعادة تعيين حالة مولد الصور
class ResetAiImageGeneratorEvent extends AiImageGeneratorEvent {
  /// إنشاء حدث جديد
  const ResetAiImageGeneratorEvent();
}
