// lib/features/ai/presentation/blocs/ai_bloc.dart

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/errors/failures.dart';
import '../../../../../core/usecases/usecase.dart';
import '../../domain/entities/ai_text_options.dart';
import '../../domain/entities/ai_text_result.dart';
import '../../domain/usecases/generate_ai_text_usecase.dart';
import '../../domain/usecases/generate_ai_text_advanced_usecase.dart';
import '../../domain/usecases/get_suggested_prompts_usecase.dart';
import '../../domain/usecases/get_text_history_usecase.dart';

part 'ai_event.dart';
part 'ai_state.dart';
part 'ai_bloc_handlers.dart';

/// BLoC that manages AI text generation state and events.
///
/// This BLoC handles the presentation layer logic for generating AI text,
/// including state management during the generation process.
class AiBloc extends Bloc<AiEvent, AiState> {
  /// Use case for generating AI text
  final GenerateAiTextUseCase generateAiTextUseCase;

  /// Use case for generating AI text with advanced options
  final GenerateAiTextAdvancedUseCase? generateAiTextAdvancedUseCase;

  /// Use case for getting suggested prompts
  final GetSuggestedPromptsUseCase? getSuggestedPromptsUseCase;

  /// Use case for getting text history
  final GetTextHistoryUseCase? getTextHistoryUseCase;

  /// Creates a new instance with the required use cases
  AiBloc({
    required this.generateAiTextUseCase,
    this.generateAiTextAdvancedUseCase,
    this.getSuggestedPromptsUseCase,
    this.getTextHistoryUseCase,
  }) : super(AiInitial()) {
    on<GenerateAiTextEvent>(_onGenerateAiText);
    on<GenerateAiTextAdvancedEvent>(_onGenerateAiTextAdvanced);
    on<GetSuggestedPromptsEvent>(_onGetSuggestedPrompts);
    on<GetTextHistoryEvent>(_onGetTextHistory);
  }

  /// Handles the [GenerateAiTextEvent] by generating AI text based on the prompt.
  ///
  /// This method emits different states during the text generation process:
  /// - [AiLoading] while generating text
  /// - [AiSuccess] with the generated text on success
  /// - [AiError] with an error message on failure
  Future<void> _onGenerateAiText(
    GenerateAiTextEvent event,
    Emitter<AiState> emit,
  ) async {
    // Emit loading state
    emit(AiLoading());

    // Call the use case with the prompt from the event
    final result = await generateAiTextUseCase(
      GenerateAiTextParams(event.prompt),
    );

    // Handle the result using fold
    result.fold(
      // On failure, emit error state with mapped failure message
      (failure) => emit(AiError(_mapFailureToMessage(failure))),
      // On success, emit success state with generated text
      (generated) => emit(AiSuccess(generated)),
    );
  }

  /// Maps a [Failure] to a user-friendly error message.
  ///
  /// @param failure The failure to map
  /// @return A user-friendly error message
  String _mapFailureToMessage(Failure failure) {
    return failure.message.isNotEmpty
        ? failure.message
        : 'AI Generation Failed';
  }
}
