// lib/features/ai/presentation/blocs/ai_event.dart

part of 'ai_bloc.dart';

/// Base class for all AI-related events.
///
/// This abstract class serves as the base for all events that can be
/// dispatched to the [AiBloc].
abstract class AiEvent extends Equatable {
  const AiEvent();

  @override
  List<Object?> get props => [];
}

/// Event to request AI text generation.
///
/// This event is dispatched when the user wants to generate AI text
/// based on a specific prompt.
class GenerateAiTextEvent extends AiEvent {
  /// The text prompt to guide the AI text generation
  final String prompt;

  /// Creates a new event with the required prompt
  const GenerateAiTextEvent(this.prompt);

  @override
  List<Object?> get props => [prompt];
}

/// Event to request AI text generation with advanced options.
///
/// This event is dispatched when the user wants to generate AI text
/// with specific options for length, style, etc.
class GenerateAiTextAdvancedEvent extends AiEvent {
  /// The text prompt to guide the AI text generation
  final String prompt;

  /// The options for text generation
  final AiTextOptions options;

  /// Creates a new event with the required parameters
  const GenerateAiTextAdvancedEvent({
    required this.prompt,
    required this.options,
  });

  @override
  List<Object?> get props => [prompt, options];
}

/// Event to request suggested prompts for a specific occasion.
///
/// This event is dispatched when the user wants to get suggested
/// prompts for a specific occasion.
class GetSuggestedPromptsEvent extends AiEvent {
  /// The ID of the occasion to get prompts for
  final String occasionId;

  /// Creates a new event with the required occasion ID
  const GetSuggestedPromptsEvent(this.occasionId);

  @override
  List<Object?> get props => [occasionId];
}

/// Event to request the history of generated texts.
///
/// This event is dispatched when the user wants to view their
/// history of previously generated texts.
class GetTextHistoryEvent extends AiEvent {
  /// Creates a new event to get text history
  const GetTextHistoryEvent();
}
