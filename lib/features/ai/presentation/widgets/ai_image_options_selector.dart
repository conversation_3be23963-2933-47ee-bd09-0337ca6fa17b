// lib/features/ai/presentation/widgets/ai_image_options_selector.dart

import 'package:flutter/material.dart';

import '../../domain/entities/ai_image_options.dart';

/// واجهة مستخدم لاختيار خيارات توليد الصور
class AiImageOptionsSelector extends StatefulWidget {
  /// خيارات توليد الصور الأولية
  final AiImageOptions initialOptions;
  
  /// دالة تستدعى عند تغيير الخيارات
  final Function(AiImageOptions) onOptionsChanged;
  
  /// إنشاء واجهة جديدة
  const AiImageOptionsSelector({
    super.key,
    required this.initialOptions,
    required this.onOptionsChanged,
  });

  @override
  State<AiImageOptionsSelector> createState() => _AiImageOptionsSelectorState();
}

class _AiImageOptionsSelectorState extends State<AiImageOptionsSelector> {
  /// خيارات توليد الصور الحالية
  late AiImageOptions _options;
  
  @override
  void initState() {
    super.initState();
    _options = widget.initialOptions;
  }
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2.0,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'خيارات توليد الصورة',
              style: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16.0),
            _buildStyleSelector(),
            const SizedBox(height: 16.0),
            _buildAspectRatioSelector(),
            const SizedBox(height: 16.0),
            _buildResolutionSelector(),
          ],
        ),
      ),
    );
  }
  
  /// بناء محدد أسلوب الصورة
  Widget _buildStyleSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أسلوب الصورة:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8.0),
        Wrap(
          spacing: 8.0,
          children: [
            _buildStyleChip(ImageStyle.realistic, 'واقعي'),
            _buildStyleChip(ImageStyle.artistic, 'فني'),
            _buildStyleChip(ImageStyle.cartoon, 'كرتوني'),
            _buildStyleChip(ImageStyle.watercolor, 'ألوان مائية'),
            _buildStyleChip(ImageStyle.oil, 'ألوان زيتية'),
            _buildStyleChip(ImageStyle.digital, 'رقمي'),
          ],
        ),
      ],
    );
  }
  
  /// بناء رقاقة اختيار أسلوب
  Widget _buildStyleChip(ImageStyle style, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _options.imageStyle == style,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _options = _options.copyWith(imageStyle: style);
          });
          widget.onOptionsChanged(_options);
        }
      },
    );
  }
  
  /// بناء محدد نسبة أبعاد الصورة
  Widget _buildAspectRatioSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نسبة الأبعاد:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8.0),
        Wrap(
          spacing: 8.0,
          children: [
            _buildAspectRatioChip(ImageAspectRatio.square, 'مربع (1:1)'),
            _buildAspectRatioChip(ImageAspectRatio.landscape, 'أفقي (16:9)'),
            _buildAspectRatioChip(ImageAspectRatio.portrait, 'عمودي (9:16)'),
            _buildAspectRatioChip(ImageAspectRatio.standard, 'قياسي (4:3)'),
          ],
        ),
      ],
    );
  }
  
  /// بناء رقاقة اختيار نسبة أبعاد
  Widget _buildAspectRatioChip(ImageAspectRatio ratio, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _options.aspectRatio == ratio,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _options = _options.copyWith(aspectRatio: ratio);
          });
          widget.onOptionsChanged(_options);
        }
      },
    );
  }
  
  /// بناء محدد دقة الصورة
  Widget _buildResolutionSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'دقة الصورة:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8.0),
        Wrap(
          spacing: 8.0,
          children: [
            _buildResolutionChip(ImageResolution.low, 'منخفضة'),
            _buildResolutionChip(ImageResolution.medium, 'متوسطة'),
            _buildResolutionChip(ImageResolution.high, 'عالية'),
          ],
        ),
      ],
    );
  }
  
  /// بناء رقاقة اختيار دقة
  Widget _buildResolutionChip(ImageResolution resolution, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _options.resolution == resolution,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _options = _options.copyWith(resolution: resolution);
          });
          widget.onOptionsChanged(_options);
        }
      },
    );
  }
}
