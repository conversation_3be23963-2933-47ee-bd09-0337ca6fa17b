// lib/features/ai/presentation/widgets/ai_image_history_list.dart

import 'dart:io';

import 'package:flutter/material.dart';

import '../../domain/entities/ai_image_result.dart';

/// واجهة مستخدم لعرض تاريخ الصور المولدة
class AiImageHistoryList extends StatelessWidget {
  /// قائمة نتائج الصور المولدة
  final List<AiImageResult> history;
  
  /// متحكم التمرير
  final ScrollController scrollController;
  
  /// إنشاء واجهة جديدة
  const AiImageHistoryList({
    super.key,
    required this.history,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16.0),
          alignment: Alignment.center,
          child: const Text(
            'تاريخ الصور المولدة',
            style: TextStyle(
              fontSize: 18.0,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const Divider(),
        Expanded(
          child: history.isEmpty
              ? const Center(
                  child: Text('لا توجد صور مولدة في التاريخ'),
                )
              : ListView.builder(
                  controller: scrollController,
                  itemCount: history.length,
                  itemBuilder: (context, index) {
                    final result = history[index];
                    return _buildHistoryItem(context, result);
                  },
                ),
        ),
      ],
    );
  }
  
  /// بناء عنصر في قائمة التاريخ
  Widget _buildHistoryItem(BuildContext context, AiImageResult result) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 8.0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (result.firstImagePath != null)
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(4.0),
              ),
              child: Image.file(
                File(result.firstImagePath!),
                height: 150,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 150,
                    width: double.infinity,
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(
                        Icons.error_outline,
                        size: 48.0,
                        color: Colors.grey,
                      ),
                    ),
                  );
                },
              ),
            ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  result.originalPrompt,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.0,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4.0),
                Text(
                  _formatDate(result.timestamp),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12.0,
                  ),
                ),
                const SizedBox(height: 8.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة توليد'),
                      onPressed: () => _regenerateImage(context, result),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// إعادة توليد صورة من التاريخ
  void _regenerateImage(BuildContext context, AiImageResult result) {
    // سيتم تنفيذ هذه الوظيفة لاحقًا
    Navigator.pop(context); // إغلاق قائمة التاريخ
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ وظيفة إعادة التوليد قريبًا'),
      ),
    );
  }
  
  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day} ${date.hour}:${date.minute}';
  }
}
