// lib/features/ai/presentation/widgets/ai_image_result_display.dart

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

import '../../domain/entities/ai_image_result.dart';

/// واجهة مستخدم لعرض نتيجة توليد الصورة
class AiImageResultDisplay extends StatelessWidget {
  /// نتيجة توليد الصورة
  final AiImageResult result;

  /// إنشاء واجهة جديدة
  const AiImageResultDisplay({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    if (!result.hasImages) {
      return const Center(
        child: Text('لم يتم توليد أي صور'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الصورة المولدة:',
          style: TextStyle(
            fontSize: 16.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8.0),
        Card(
          elevation: 4.0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(4.0),
                ),
                child: _buildImageDisplay(),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المطالبة: ${result.originalPrompt}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    Text(
                      'تاريخ التوليد: ${_formatDate(result.timestamp)}',
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12.0,
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildActionButton(
                          icon: Icons.share,
                          label: 'مشاركة',
                          onPressed: () => _shareImage(context),
                        ),
                        _buildActionButton(
                          icon: Icons.save_alt,
                          label: 'حفظ',
                          onPressed: () => _saveImage(context),
                        ),
                        _buildActionButton(
                          icon: Icons.edit,
                          label: 'تحرير',
                          onPressed: () => _editImage(context),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء عرض الصورة
  Widget _buildImageDisplay() {
    if (result.firstImagePath != null) {
      final file = File(result.firstImagePath!);
      return Image.file(
        file,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('حدث خطأ أثناء تحميل الصورة'),
            ),
          );
        },
      );
    } else if (result.firstImageUrl != null) {
      return Image.network(
        result.firstImageUrl!,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('حدث خطأ أثناء تحميل الصورة'),
            ),
          );
        },
      );
    } else {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('لا توجد صورة متاحة'),
        ),
      );
    }
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return TextButton.icon(
      icon: Icon(icon),
      label: Text(label),
      onPressed: onPressed,
    );
  }

  /// مشاركة الصورة
  void _shareImage(BuildContext context) {
    if (result.firstImagePath != null) {
      // تخزين سياق البناء في متغير محلي
      final BuildContext localContext = context;

      // استخدام Future.microtask لتجنب استخدام BuildContext عبر فجوات غير متزامنة
      Future.microtask(() async {
        try {
          await Share.shareXFiles(
            [XFile(result.firstImagePath!)],
            text: 'صورة مولدة بالذكاء الاصطناعي: ${result.originalPrompt}',
          );
        } catch (e) {
          // عرض رسالة الخطأ
          if (localContext.mounted) {
            ScaffoldMessenger.of(localContext).showSnackBar(
              SnackBar(
                content: Text('حدث خطأ أثناء المشاركة: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد صورة متاحة للمشاركة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// حفظ الصورة
  void _saveImage(BuildContext context) {
    // سيتم تنفيذ هذه الوظيفة لاحقًا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ وظيفة الحفظ قريبًا'),
      ),
    );
  }

  /// تحرير الصورة
  void _editImage(BuildContext context) {
    // سيتم تنفيذ هذه الوظيفة لاحقًا
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ وظيفة التحرير قريبًا'),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day} ${date.hour}:${date.minute}';
  }
}
