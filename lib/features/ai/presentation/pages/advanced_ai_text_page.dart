// lib/features/ai/presentation/pages/advanced_ai_text_page.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../domain/entities/ai_text_options.dart' as ai_options;
import '../blocs/ai_bloc.dart';
import 'ai_text_result_page.dart';

/// صفحة توليد النص المتقدمة بالذكاء الاصطناعي
class AdvancedAiTextPage extends StatefulWidget {
  /// معرف المناسبة (اختياري)
  final String? occasionId;

  /// إنشاء صفحة توليد النص المتقدمة
  const AdvancedAiTextPage({super.key, this.occasionId});

  @override
  State<AdvancedAiTextPage> createState() => _AdvancedAiTextPageState();
}

class _AdvancedAiTextPageState extends State<AdvancedAiTextPage> {
  /// وحدة تحكم حقل إدخال المطالبة
  final TextEditingController _promptController = TextEditingController();

  /// خيارات توليد النص
  ai_options.AiTextOptions _options = const ai_options.AiTextOptions();

  /// قائمة الاقتراحات
  List<String> _suggestedPrompts = [];

  /// مؤشر تحميل الاقتراحات
  bool _loadingSuggestions = false;

  @override
  void initState() {
    super.initState();

    // تحميل الاقتراحات إذا كان هناك معرف مناسبة
    if (widget.occasionId != null) {
      _loadSuggestedPrompts();
    }
  }

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }

  /// تحميل اقتراحات المطالبات
  void _loadSuggestedPrompts() {
    if (widget.occasionId == null) return;

    setState(() {
      _loadingSuggestions = true;
    });

    // إرسال حدث للحصول على الاقتراحات
    context.read<AiBloc>().add(GetSuggestedPromptsEvent(widget.occasionId!));
  }

  /// توليد النص بالذكاء الاصطناعي
  void _generateText() {
    if (_promptController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'الرجاء إدخال مطالبة',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // إرسال حدث لتوليد النص
    context.read<AiBloc>().add(
          GenerateAiTextAdvancedEvent(
            prompt: _promptController.text.trim(),
            options: _options,
          ),
        );

    // إخفاء لوحة المفاتيح
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'توليد النص بالذكاء الاصطناعي',
          style: GoogleFonts.cairo(),
        ),
        centerTitle: true,
      ),
      body: BlocListener<AiBloc, AiState>(
        listener: (context, state) {
          if (state is AiSuggestedPromptsLoaded) {
            setState(() {
              _suggestedPrompts = state.prompts;
              _loadingSuggestions = false;
            });
          } else if (state is AiTextResultGenerated) {
            // التنقل إلى صفحة نتائج النص المولد
            _navigateToResultPage(state.result);
          } else if (state is AiError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'خطأ: ${state.message}',
                  style: GoogleFonts.cairo(),
                ),
                backgroundColor: Colors.red,
              ),
            );

            if (_loadingSuggestions) {
              setState(() {
                _loadingSuggestions = false;
              });
            }
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // حقل إدخال المطالبة
                TextField(
                  controller: _promptController,
                  decoration: InputDecoration(
                    labelText: 'أدخل مطالبة',
                    hintText: 'مثال: تهنئة بمناسبة عيد الفطر',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () => _promptController.clear(),
                    ),
                  ),
                  maxLines: 3,
                  textDirection: TextDirection.rtl,
                ),

                const SizedBox(height: 16),

                // اقتراحات المطالبات
                if (_suggestedPrompts.isNotEmpty || _loadingSuggestions) ...[
                  Text(
                    'اقتراحات المطالبات:',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (_loadingSuggestions)
                    const Center(child: CircularProgressIndicator())
                  else
                    SizedBox(
                      height: 40,
                      child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemCount: _suggestedPrompts.length,
                        separatorBuilder: (context, index) =>
                            const SizedBox(width: 8),
                        itemBuilder: (context, index) {
                          return ActionChip(
                            label: Text(_suggestedPrompts[index]),
                            onPressed: () {
                              _promptController.text = _suggestedPrompts[index];
                            },
                          );
                        },
                      ),
                    ),
                  const SizedBox(height: 16),
                ],

                // خيارات توليد النص
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'خيارات توليد النص:',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // طول النص
                        Text(
                          'طول النص:',
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        const SizedBox(height: 8),

                        Wrap(
                          spacing: 8,
                          children: [
                            _buildTextLengthChip(
                                ai_options.TextLength.short, 'قصير'),
                            _buildTextLengthChip(
                                ai_options.TextLength.medium, 'متوسط'),
                            _buildTextLengthChip(
                                ai_options.TextLength.long, 'طويل'),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // أسلوب النص
                        Text(
                          'أسلوب النص:',
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        const SizedBox(height: 8),

                        Wrap(
                          spacing: 8,
                          children: [
                            _buildTextStyleChip(
                                ai_options.TextStyle.formal, 'رسمي'),
                            _buildTextStyleChip(
                                ai_options.TextStyle.emotional, 'عاطفي'),
                            _buildTextStyleChip(
                                ai_options.TextStyle.humorous, 'فكاهي'),
                            _buildTextStyleChip(
                                ai_options.TextStyle.literary, 'أدبي'),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // عدد النصوص
                        Text(
                          'عدد النصوص:',
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        const SizedBox(height: 8),

                        Row(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.remove),
                              onPressed: _options.numberOfTexts > 1
                                  ? () {
                                      setState(() {
                                        _options = _options.copyWith(
                                          numberOfTexts:
                                              _options.numberOfTexts - 1,
                                        );
                                      });
                                    }
                                  : null,
                            ),
                            Text(
                              '${_options.numberOfTexts}',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.add),
                              onPressed: _options.numberOfTexts < 5
                                  ? () {
                                      setState(() {
                                        _options = _options.copyWith(
                                          numberOfTexts:
                                              _options.numberOfTexts + 1,
                                        );
                                      });
                                    }
                                  : null,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // زر توليد النص
                BlocBuilder<AiBloc, AiState>(
                  builder: (context, state) {
                    final isLoading = state is AiLoading;

                    return ElevatedButton(
                      onPressed: isLoading ? null : _generateText,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : Text(
                              'توليد النص',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء رقاقة طول النص
  Widget _buildTextLengthChip(ai_options.TextLength length, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _options.textLength == length,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _options = _options.copyWith(textLength: length);
          });
        }
      },
    );
  }

  /// بناء رقاقة أسلوب النص
  Widget _buildTextStyleChip(ai_options.TextStyle style, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _options.textStyle == style,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _options = _options.copyWith(textStyle: style);
          });
        }
      },
    );
  }

  /// التنقل إلى صفحة نتائج النص المولد
  void _navigateToResultPage(dynamic result) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AiTextResultPage(
              result: result,
            ),
          ),
        ).then((_) {
          // العودة إلى صفحة محرر البطاقة بعد إضافة النص
          if (mounted) {
            Navigator.pop(context);
          }
        });
      }
    });
  }
}
