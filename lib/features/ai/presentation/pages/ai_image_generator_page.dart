// lib/features/ai/presentation/pages/ai_image_generator_page.dart


import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/presentation/widgets/loading_indicator.dart';
import '../../domain/entities/ai_image_options.dart';
import '../blocs/ai_image_generator/ai_image_generator_bloc.dart';
import '../blocs/ai_image_generator/ai_image_generator_event.dart';
import '../blocs/ai_image_generator/ai_image_generator_state.dart';
import '../widgets/ai_image_history_list.dart';
import '../widgets/ai_image_options_selector.dart';
import '../widgets/ai_image_result_display.dart';

/// صفحة توليد الصور بالذكاء الاصطناعي
class AiImageGeneratorPage extends StatefulWidget {
  /// مسار الصفحة
  static const routeName = '/ai-image-generator';

  /// إنشاء صفحة جديدة
  const AiImageGeneratorPage({super.key});

  @override
  State<AiImageGeneratorPage> createState() => _AiImageGeneratorPageState();
}

class _AiImageGeneratorPageState extends State<AiImageGeneratorPage> {
  /// متحكم حقل الإدخال
  final TextEditingController _promptController = TextEditingController();
  
  /// مفتاح النموذج
  final _formKey = GlobalKey<FormState>();
  
  /// خيارات توليد الصور الحالية
  AiImageOptions _currentOptions = const AiImageOptions();

  @override
  void initState() {
    super.initState();
    // تحميل تاريخ الصور عند تهيئة الصفحة
    context.read<AiImageGeneratorBloc>().add(const LoadImageHistoryEvent());
  }

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('توليد الصور بالذكاء الاصطناعي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showHistory,
            tooltip: 'عرض التاريخ',
          ),
        ],
      ),
      body: BlocConsumer<AiImageGeneratorBloc, AiImageGeneratorState>(
        listener: (context, state) {
          // عرض رسالة خطأ إذا حدث خطأ
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildPromptForm(context, state),
                const SizedBox(height: 16.0),
                _buildOptionsSelector(context, state),
                const SizedBox(height: 24.0),
                _buildGenerateButton(context, state),
                const SizedBox(height: 24.0),
                if (state.isLoading) 
                  const Center(child: LoadingIndicator()),
                if (state.currentResult != null && !state.isLoading)
                  AiImageResultDisplay(result: state.currentResult!),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء نموذج إدخال المطالبة
  Widget _buildPromptForm(BuildContext context, AiImageGeneratorState state) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اكتب وصفًا للصورة التي تريد إنشاءها:',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8.0),
          TextFormField(
            controller: _promptController,
            decoration: const InputDecoration(
              hintText: 'مثال: منظر طبيعي لجبال مغطاة بالثلوج عند غروب الشمس',
              border: OutlineInputBorder(),
              filled: true,
            ),
            maxLines: 3,
            textDirection: TextDirection.rtl,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال وصف للصورة';
              }
              if (value.trim().length < 5) {
                return 'يرجى إدخال وصف أكثر تفصيلاً';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// بناء محدد خيارات توليد الصور
  Widget _buildOptionsSelector(BuildContext context, AiImageGeneratorState state) {
    return AiImageOptionsSelector(
      initialOptions: state.options,
      onOptionsChanged: (newOptions) {
        setState(() {
          _currentOptions = newOptions;
        });
        context.read<AiImageGeneratorBloc>().add(
          UpdateImageOptionsEvent(options: newOptions),
        );
      },
    );
  }

  /// بناء زر التوليد
  Widget _buildGenerateButton(BuildContext context, AiImageGeneratorState state) {
    return ElevatedButton(
      onPressed: state.isLoading
          ? null
          : () => _generateImage(context),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        textStyle: const TextStyle(fontSize: 18.0),
      ),
      child: Text(state.isLoading ? 'جاري التوليد...' : 'توليد الصورة'),
    );
  }

  /// عرض تاريخ الصور المولدة
  void _showHistory() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.black
          : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return BlocBuilder<AiImageGeneratorBloc, AiImageGeneratorState>(
              builder: (context, state) {
                return AiImageHistoryList(
                  history: state.history,
                  scrollController: scrollController,
                );
              },
            );
          },
        );
      },
    );
  }

  /// توليد صورة جديدة
  void _generateImage(BuildContext context) {
    // التحقق من صحة النموذج
    if (_formKey.currentState?.validate() ?? false) {
      // إخفاء لوحة المفاتيح
      FocusScope.of(context).unfocus();
      
      // إرسال حدث توليد الصورة
      context.read<AiImageGeneratorBloc>().add(
        GenerateAiImageEvent(
          prompt: _promptController.text.trim(),
          options: _currentOptions,
        ),
      );
    }
  }
}
