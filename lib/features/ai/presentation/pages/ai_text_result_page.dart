// lib/features/ai/presentation/pages/ai_text_result_page.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../domain/entities/ai_text_result.dart';
import '../../../mashair_card/presentation/blocs/card_editor/card_editor_bloc.dart';
import '../../../mashair_card/presentation/blocs/card_editor/card_editor_event.dart';

/// صفحة عرض نتيجة توليد النص بالذكاء الاصطناعي
class AiTextResultPage extends StatefulWidget {
  /// نتيجة توليد النص
  final AiTextResult result;

  /// إنشاء صفحة عرض نتيجة توليد النص
  const AiTextResultPage({super.key, required this.result});

  @override
  State<AiTextResultPage> createState() => _AiTextResultPageState();
}

class _AiTextResultPageState extends State<AiTextResultPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'النص المولد',
          style: GoogleFonts.cairo(),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // المطالبة الأصلية
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المطالبة:',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.result.originalPrompt,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // النصوص المولدة
            Expanded(
              child: ListView.builder(
                itemCount: widget.result.generatedTexts.length,
                itemBuilder: (context, index) {
                  final text = widget.result.generatedTexts[index];
                  if (text.isEmpty) return const SizedBox.shrink();

                  return Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (widget.result.generatedTexts.length > 1)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Text(
                                'النص ${index + 1}:',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          Text(
                            text,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                            ),
                            textDirection: TextDirection.rtl,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // زر نسخ النص
                              IconButton(
                                icon: const Icon(Icons.copy),
                                tooltip: 'نسخ النص',
                                onPressed: () {
                                  Clipboard.setData(ClipboardData(text: text));
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'تم نسخ النص',
                                        style: GoogleFonts.cairo(),
                                      ),
                                      duration: const Duration(seconds: 1),
                                    ),
                                  );
                                },
                              ),

                              // زر إضافة النص إلى البطاقة
                              IconButton(
                                icon: const Icon(Icons.add_circle_outline),
                                tooltip: 'إضافة إلى البطاقة',
                                onPressed: () {
                                  _addTextToCard(context, text);
                                  Navigator.pop(context);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// إضافة النص إلى البطاقة
  void _addTextToCard(BuildContext context, String text) {
    // إرسال حدث إضافة عنصر نصي
    context.read<CardEditorBloc>().add(
          AddTextEvent(text),
        );

    // عرض رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تمت إضافة النص إلى البطاقة',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 1),
      ),
    );

    // العودة إلى صفحة محرر البطاقة بعد إضافة النص
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.of(context).pop();
    });
  }
}
