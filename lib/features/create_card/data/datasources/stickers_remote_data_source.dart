import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/sticker_entity.dart';

/// واجهة مصدر البيانات البعيد للملصقات
abstract class StickersRemoteDataSource {
  /// استرجاع جميع الملصقات
  Future<List<StickerEntity>> getAllStickers();

  /// استرجاع الملصقات حسب الفئة
  Future<List<StickerEntity>> getStickersByCategory(String category);

  /// استرجاع جميع فئات الملصقات
  Future<List<String>> getAllCategories();

  /// استرجاع تدفق الملصقات حسب الفئة
  Stream<List<StickerEntity>> getStickersStreamByCategory(String category);
}

/// تنفيذ مصدر البيانات البعيد للملصقات
class StickersRemoteDataSourceImpl implements StickersRemoteDataSource {
  final FirebaseFirestore firestore;

  StickersRemoteDataSourceImpl({required this.firestore});

  @override
  Future<List<StickerEntity>> getAllStickers() async {
    try {
      final snapshot = await firestore
          .collection('stickers')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => StickerEntity.fromMap(doc.id, doc.data()))
          .toList();
    } catch (e) {
      debugPrint('Error getting stickers: $e');
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<List<StickerEntity>> getStickersByCategory(String category) async {
    try {
      final snapshot = await firestore
          .collection('stickers')
          .where('category', isEqualTo: category)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => StickerEntity.fromMap(doc.id, doc.data()))
          .toList();
    } catch (e) {
      debugPrint('Error getting stickers by category: $e');
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<List<String>> getAllCategories() async {
    try {
      final snapshot = await firestore.collection('stickers').get();

      // استخراج جميع الفئات الفريدة
      final Set<String> categories = {};
      for (var doc in snapshot.docs) {
        final category = doc.data()['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categories.add(category);
        }
      }

      return categories.toList()..sort();
    } catch (e) {
      debugPrint('Error getting sticker categories: $e');
      throw ServerException(message: e.toString());
    }
  }

  @override
  Stream<List<StickerEntity>> getStickersStreamByCategory(String category) {
    return firestore
        .collection('stickers')
        .where('category', isEqualTo: category)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => StickerEntity.fromMap(doc.id, doc.data()))
          .toList();
    });
  }
}
