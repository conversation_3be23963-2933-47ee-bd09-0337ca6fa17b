// lib/features/create_card/data/converters/legacy_to_unified_converter.dart

import 'package:flutter/material.dart';
import 'package:mashair/features/create_card/domain/entities/card_element.dart';

/// محول لتحويل العناصر من النموذج الموحد إلى النموذج القديم
class LegacyToUnifiedConverter {
  /// تحويل عناصر البطاقة من النموذج الموحد إلى النموذج القديم
  static List<CardElement> convertToLegacyElements(List<CardElement> unifiedElements) {
    debugPrint('=== بدء تحويل العناصر من النموذج الموحد إلى النموذج القديم ===');
    debugPrint('عدد العناصر المراد تحويلها: ${unifiedElements.length}');
    
    return unifiedElements.map((element) {
      debugPrint('تحويل العنصر: ${element.id}, النوع: ${element.type}');
      debugPrint('الإحداثيات الأصلية: (${element.x}, ${element.y})');
      debugPrint('الأبعاد الأصلية: (${element.width}x${element.height})');
      debugPrint('isFromAdminPanel: ${element.isFromAdminPanel}');
      debugPrint('isRelativeCoordinates: ${element.isRelativeCoordinates}');
      
      // نحافظ على العنصر كما هو لأنه بالفعل في النموذج الصحيح
      // نحتاج فقط للتأكد من أن العلامات صحيحة
      final convertedElement = element.copyWith(
        isFromAdminPanel: true, // تأكيد أن العنصر من لوحة التحكم
        isRelativeCoordinates: true, // تأكيد أن الإحداثيات نسبية
      );
      
      debugPrint('تم تحويل العنصر بنجاح');
      debugPrint('============================');
      
      return convertedElement;
    }).toList();
  }
}
