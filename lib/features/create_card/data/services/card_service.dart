// lib/features/create_card/data/services/card_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:mashair/features/create_card/domain/entities/card_element.dart';
import 'package:mashair/core/utils/coordinate_system.dart';

/// خدمة التعامل مع بيانات البطاقات في Firestore
class CardService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// الأبعاد القياسية للبطاقة
  static const double standardCardWidth = 350.0;
  static const double standardCardHeight = 500.0;

  /// استرداد عناصر البطاقة من Firestore
  Future<List<CardElement>> getCardElements(String cardId) async {
    try {
      final snapshot = await _firestore
          .collection('cards')
          .doc(cardId)
          .collection('elements')
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();

        // استخراج البيانات الأساسية
        final double x = (data['x'] as num).toDouble();
        final double y = (data['y'] as num).toDouble();
        final double width = (data['width'] as num).toDouble();
        final double height = (data['height'] as num).toDouble();

        // تحديد ما إذا كانت الإحداثيات نسبية
        bool isRelative = data['isRelativeCoordinates'] as bool? ??
            (width <= 1 && height <= 1);

        // إنشاء عنصر البطاقة مع الحفاظ على القيم الأصلية
        return CardElement(
          id: doc.id,
          type: _parseElementType(data['type'] ?? data['typeIndex']),
          content: data['content'] ?? '',
          x: x,
          y: y,
          width: width,
          height: height,
          rotation: (data['rotation'] as num?)?.toDouble() ?? 0.0,
          scale: (data['scale'] as num?)?.toDouble() ?? 1.0,
          colorValue: data['colorValue'] as int?,
          fontFamily: data['fontFamily'] as String?,
          fontSize: (data['fontSize'] as num?)?.toDouble() ?? 16.0,
          isBold: data['isBold'] as bool? ?? false,
          isItalic: data['isItalic'] as bool? ?? false,
          isUnderline: data['isUnderline'] as bool? ?? false,
          textAlign: data['textAlign'] as String?,
          letterSpacing: (data['letterSpacing'] as num?)?.toDouble(),
          lineHeight: (data['lineHeight'] as num?)?.toDouble(),
          isRelativeCoordinates: isRelative,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error getting card elements: $e');
      return [];
    }
  }

  /// حفظ عناصر البطاقة في Firestore
  Future<void> saveCardElements(
      String cardId, List<CardElement> elements) async {
    try {
      final batch = _firestore.batch();
      final cardRef = _firestore.collection('cards').doc(cardId);
      final elementsCollection = cardRef.collection('elements');

      // حذف العناصر الموجودة
      final existingElements = await elementsCollection.get();
      for (final doc in existingElements.docs) {
        batch.delete(doc.reference);
      }

      // إعداد قائمة العناصر المعيارية للحفظ في وثيقة البطاقة
      final List<Map<String, dynamic>> normalizedElements = [];

      // إضافة العناصر الجديدة
      for (final element in elements) {
        // تحويل الإحداثيات إلى نسبية إذا لم تكن كذلك
        double x = element.x;
        double y = element.y;
        double width = element.width;
        double height = element.height;

        // التأكد من أن الإحداثيات نسبية
        if (!element.isRelativeCoordinates) {
          final coordinateSystem = CoordinateSystem(
            containerWidth: standardCardWidth,
            containerHeight: standardCardHeight,
          );

          final relativePos = coordinateSystem.absoluteToRelative(x, y);
          final relativeSize =
              coordinateSystem.absoluteSizeToRelative(width, height);

          x = relativePos.dx;
          y = relativePos.dy;
          width = relativeSize.width;
          height = relativeSize.height;
        }

        // إنشاء بيانات العنصر
        final data = {
          'id': element.id,
          'type': element.type.toString(),
          'typeIndex': element.type.index,
          'content': element.content,
          'x': x,
          'y': y,
          'width': width,
          'height': height,
          'rotation': element.rotation,
          'scale': element.scale,
          'colorValue': element.colorValue,
          'fontFamily': element.fontFamily,
          'fontSize': element.fontSize,
          'isBold': element.isBold,
          'isItalic': element.isItalic,
          'isUnderline': element.isUnderline,
          'textAlign': element.textAlign,
          'letterSpacing': element.letterSpacing,
          'lineHeight': element.lineHeight,
          'isRelativeCoordinates': true, // دائمًا نسبية عند الحفظ
          'timestamp': FieldValue.serverTimestamp(),
        };

        // إضافة العنصر إلى المجموعة الفرعية
        batch.set(elementsCollection.doc(element.id), data);

        // إضافة العنصر إلى القائمة المعيارية
        normalizedElements.add(data);
      }

      // تنفيذ عمليات الحذف والإضافة
      await batch.commit();

      // حفظ العناصر في وثيقة البطاقة نفسها
      await cardRef.update({
        'elements': normalizedElements,
        'lastModified': FieldValue.serverTimestamp(),
      });

      debugPrint('تم حفظ ${elements.length} عنصر للبطاقة $cardId');
    } catch (e) {
      debugPrint('خطأ في حفظ عناصر البطاقة: $e');
      rethrow;
    }
  }
}

/// تحويل نص نوع العنصر أو رقم الفهرس إلى ElementType
ElementType _parseElementType(dynamic typeValue) {
  if (typeValue is int &&
      typeValue >= 0 &&
      typeValue < ElementType.values.length) {
    return ElementType.values[typeValue];
  } else if (typeValue is String) {
    if (typeValue.contains('text')) return ElementType.text;
    if (typeValue.contains('image')) return ElementType.image;
    if (typeValue.contains('sticker')) return ElementType.sticker;
    if (typeValue.contains('qrCode')) return ElementType.qrCode;
    if (typeValue.contains('signature')) return ElementType.signature;
  }
  return ElementType.text; // القيمة الافتراضية
}
