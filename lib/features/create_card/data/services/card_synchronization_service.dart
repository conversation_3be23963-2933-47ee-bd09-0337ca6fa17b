// lib/features/create_card/data/services/card_synchronization_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:mashair/core/constants/card_constants.dart';
import 'package:mashair/features/create_card/domain/entities/card_element.dart';
import 'package:shared_preferences/shared_preferences.dart';

// استخدام ElementType بدلاً من CardElementType
typedef CardElementType = ElementType;

/// خدمة مزامنة البطاقات
/// تستخدم لمزامنة بيانات البطاقات بين لوحة التحكم والتطبيق
class CardSynchronizationService {
  final FirebaseFirestore _firestore;

  /// إنشاء خدمة مزامنة البطاقات
  CardSynchronizationService({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// الأبعاد القياسية للبطاقة - استخدام الثوابت الموحدة
  static const double standardCardWidth = CardConstants.APP_CARD_WIDTH;
  static const double standardCardHeight = CardConstants.APP_CARD_HEIGHT;

  /// زيادة عداد زيارات البطاقة
  Future<void> incrementCardVisits(String cardId) async {
    try {
      // التأكد من أن معرف البطاقة ليس فارغًا
      if (cardId.isEmpty) {
        return;
      }

      // التحقق من التخزين المحلي لتجنب تكرار الزيارات في نفس الجلسة
      final prefs = await SharedPreferences.getInstance();
      final lastVisitKey = 'last_visit_$cardId';
      final lastVisit = prefs.getInt(lastVisitKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // إذا تمت زيارة البطاقة في آخر 5 دقائق، لا نزيد العداد
      if (now - lastVisit < 5 * 60 * 1000) {
        return;
      }

      // تحديث وقت آخر زيارة
      await prefs.setInt(lastVisitKey, now);

      // استخدام مجموعة منفصلة للإحصائيات
      final statsRef = _firestore.collection('card_stats').doc(cardId);

      // التحقق من وجود وثيقة الإحصائيات
      final statsDoc = await statsRef.get();

      if (statsDoc.exists) {
        // تحديث عداد الزيارات باستخدام FieldValue.increment
        await statsRef.update({
          'visits': FieldValue.increment(1),
          'lastVisit': FieldValue.serverTimestamp(),
        });
      } else {
        // إنشاء وثيقة إحصائيات جديدة
        await statsRef.set({
          'cardId': cardId,
          'visits': 1,
          'lastVisit': FieldValue.serverTimestamp(),
          'createdAt': FieldValue.serverTimestamp(),
        });
      }

      // تحديث وثيقة البطاقة نفسها
      try {
        await _firestore.collection('cards').doc(cardId).update({
          'visits': FieldValue.increment(1),
        });
      } catch (e) {
        debugPrint('خطأ في تحديث عداد الزيارات في البطاقة: $e');
        // تجاهل الأخطاء هنا لأننا نستخدم مجموعة منفصلة كحل بديل
      }
    } catch (e) {
      debugPrint('خطأ في زيادة عداد زيارات البطاقة: $e');
    }
  }

  /// استرجاع عناصر البطاقة من Firebase
  Future<List<CardElement>> getCardElements(String cardId) async {
    try {
      debugPrint('بدء استرجاع عناصر البطاقة للبطاقة: $cardId');

      // أولاً، نتحقق من وجود البطاقة نفسها
      final cardDoc = await _firestore.collection('cards').doc(cardId).get();

      if (!cardDoc.exists) {
        debugPrint('البطاقة غير موجودة: $cardId');
        return [];
      }

      debugPrint('البطاقة موجودة، نستعلم عن العناصر');

      // استرجاع لون الخلفية للبطاقة
      final cardData = cardDoc.data();
      String backgroundColor = 'FFFFFF'; // لون افتراضي

      if (cardData != null && cardData.containsKey('backgroundColor')) {
        backgroundColor = cardData['backgroundColor'] as String? ?? 'FFFFFF';
      }

      // التحقق مما إذا كانت العناصر مخزنة مباشرة في البطاقة
      List<CardElement> elements = [];

      if (cardData != null && cardData.containsKey('elements')) {
        debugPrint('العناصر موجودة مباشرة في وثيقة البطاقة');
        final elementsList = cardData['elements'] as List<dynamic>;

        elements = _parseElementsFromList(elementsList, backgroundColor);
        debugPrint('تم العثور على ${elements.length} عنصر في وثيقة البطاقة');
      } else {
        // جلب عناصر البطاقة من مجموعة فرعية
        final elementsSnapshot = await _firestore
            .collection('cards')
            .doc(cardId)
            .collection('elements')
            .get();

        debugPrint('تم العثور على ${elementsSnapshot.docs.length} عنصر في المجموعة الفرعية');

        elements = _parseElementsFromDocs(elementsSnapshot.docs, backgroundColor);
      }

      return elements;
    } catch (e) {
      debugPrint('خطأ في استرجاع عناصر البطاقة: $e');
      return [];
    }
  }

  /// تحليل العناصر من قائمة
  List<CardElement> _parseElementsFromList(List<dynamic> elementsList, String backgroundColor) {
    return elementsList.map((element) {
      final Map<String, dynamic> data = element as Map<String, dynamic>;

      // استخراج البيانات الأساسية
      final double x = (data['x'] as num).toDouble();
      final double y = (data['y'] as num).toDouble();
      final double width = (data['width'] as num).toDouble();
      final double height = (data['height'] as num).toDouble();

      // تحديد ما إذا كانت الإحداثيات نسبية
      bool isRelative = data['isRelativeCoordinates'] as bool? ??
          (width <= 1 && height <= 1);

      // طباعة معلومات العنصر للتصحيح
      debugPrint('معلومات العنصر من Firebase (قائمة):');
      debugPrint('النوع: ${data['type'] ?? data['typeIndex']}');
      debugPrint('المحتوى: ${data['content'] ?? data['text'] ?? data['remoteUrl'] ?? ''}');
      debugPrint('الإحداثيات: ($x, $y)');
      debugPrint('الأبعاد: (${width}x$height)');
      debugPrint('isRelativeCoordinates: $isRelative');

      // إنشاء عنصر البطاقة مع الحفاظ على القيم الأصلية
      return CardElement(
        id: data['id'] as String? ?? DateTime.now().millisecondsSinceEpoch.toString(),
        type: _parseElementType(data['type'] ?? data['typeIndex']),
        content: data['content'] ?? data['text'] ?? data['remoteUrl'] ?? '',
        x: x,
        y: y,
        width: width,
        height: height,
        rotation: (data['rotation'] as num?)?.toDouble() ?? 0.0,
        scale: (data['scale'] as num?)?.toDouble() ?? 1.0,
        colorValue: _parseColorValue(data['colorValue'] ?? data['fontColor'], backgroundColor),
        fontFamily: data['fontFamily'] as String?,
        fontSize: (data['fontSize'] as num?)?.toDouble() ?? 16.0,
        isBold: data['isBold'] as bool? ?? false,
        isItalic: data['isItalic'] as bool? ?? false,
        isUnderline: data['isUnderline'] as bool? ?? false,
        textAlign: data['textAlign'] as String?,
        letterSpacing: (data['letterSpacing'] as num?)?.toDouble(),
        lineHeight: (data['lineHeight'] as num?)?.toDouble(),
        isRelativeCoordinates: isRelative,
        isFromAdminPanel: true, // تعيين العناصر المستردة من Firebase على أنها من لوحة التحكم
      );
    }).toList();
  }

  /// تحليل العناصر من وثائق Firestore
  List<CardElement> _parseElementsFromDocs(List<QueryDocumentSnapshot> docs, String backgroundColor) {
    return docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;

      // استخراج البيانات الأساسية
      final double x = (data['x'] as num).toDouble();
      final double y = (data['y'] as num).toDouble();
      final double width = (data['width'] as num).toDouble();
      final double height = (data['height'] as num).toDouble();

      // تحديد ما إذا كانت الإحداثيات نسبية
      bool isRelative = data['isRelativeCoordinates'] as bool? ??
          (width <= 1 && height <= 1);

      // طباعة معلومات العنصر للتصحيح
      debugPrint('معلومات العنصر من Firebase (وثيقة):');
      debugPrint('النوع: ${data['type'] ?? data['typeIndex']}');
      debugPrint('المحتوى: ${data['content'] ?? data['text'] ?? data['remoteUrl'] ?? ''}');
      debugPrint('الإحداثيات: ($x, $y)');
      debugPrint('الأبعاد: (${width}x$height)');
      debugPrint('isRelativeCoordinates: $isRelative');

      // إنشاء عنصر البطاقة مع الحفاظ على القيم الأصلية
      return CardElement(
        id: doc.id,
        type: _parseElementType(data['type'] ?? data['typeIndex']),
        content: data['content'] ?? data['text'] ?? data['remoteUrl'] ?? '',
        x: x,
        y: y,
        width: width,
        height: height,
        rotation: (data['rotation'] as num?)?.toDouble() ?? 0.0,
        scale: (data['scale'] as num?)?.toDouble() ?? 1.0,
        colorValue: _parseColorValue(data['colorValue'] ?? data['fontColor'], backgroundColor),
        fontFamily: data['fontFamily'] as String?,
        fontSize: (data['fontSize'] as num?)?.toDouble() ?? 16.0,
        isBold: data['isBold'] as bool? ?? false,
        isItalic: data['isItalic'] as bool? ?? false,
        isUnderline: data['isUnderline'] as bool? ?? false,
        textAlign: data['textAlign'] as String?,
        letterSpacing: (data['letterSpacing'] as num?)?.toDouble(),
        lineHeight: (data['lineHeight'] as num?)?.toDouble(),
        isRelativeCoordinates: isRelative,
        isFromAdminPanel: true, // تعيين العناصر المستردة من Firebase على أنها من لوحة التحكم
      );
    }).toList();
  }

  /// تحليل نوع العنصر
  CardElementType _parseElementType(dynamic typeValue) {
    if (typeValue is int) {
      return CardElementType.values[typeValue];
    } else if (typeValue is String) {
      if (typeValue.contains('text')) {
        return CardElementType.text;
      } else if (typeValue.contains('image')) {
        return CardElementType.image;
      } else if (typeValue.contains('sticker')) {
        return CardElementType.sticker;
      } else if (typeValue.contains('signature')) {
        return CardElementType.signature;
      }
    }
    return CardElementType.text;
  }

  /// تحليل قيمة اللون
  int? _parseColorValue(dynamic colorValue, String backgroundColor) {
    if (colorValue == null) {
      return int.tryParse('0xFF$backgroundColor');
    }

    if (colorValue is int) {
      return colorValue;
    } else if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        return int.tryParse('0xFF${colorValue.substring(1)}');
      } else if (colorValue.startsWith('0x')) {
        return int.tryParse(colorValue);
      } else {
        return int.tryParse('0xFF$colorValue');
      }
    }

    return null;
  }
}
