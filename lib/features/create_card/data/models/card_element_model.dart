// lib/features/create_card/data/models/card_element_model.dart

import '../../domain/entities/card_element.dart';

/// A model class that represents a card element and extends the [CardElement] entity.
/// This class provides functionality to serialize and deserialize JSON data.
class CardElementModel extends CardElement {
  /// Creates a [CardElementModel] instance.
  ///
  /// The constructor requires basic properties like [id], [type], [content],
  /// and layout properties such as [x], [y], [width], and [height].
  /// Optional styling parameters include [fontSize], [isBold], [isItalic], and [isUnderline].
  const CardElementModel({
    required super.id,
    required super.type,
    required super.content,
    required super.x,
    required super.y,
    required super.width,
    required super.height,
    super.fontSize,
    super.isBold,
    super.isItalic,
    super.isUnderline,
  });

  /// Factory constructor to create a [CardElementModel] from a JSON map.
  ///
  /// This method parses the JSON map and converts it into a [CardElementModel] instance.
  /// It safely converts numerical values to double and handles the [ElementType] enumeration.
  factory CardElementModel.fromJson(Map<String, dynamic> json) {
    // Safe parsing of element type
    ElementType elementType;
    final typeValue = json['type'];
    if (typeValue is int) {
      elementType = ElementType.values[typeValue];
    } else if (typeValue is String) {
      // Handle string representation of type
      try {
        elementType = ElementType.values[int.parse(typeValue)];
      } catch (e) {
        // Default to text if parsing fails
        elementType = ElementType.text;
      }
    } else {
      elementType = ElementType.text;
    }

    return CardElementModel(
      id: json['id'] as String,
      type: elementType,
      content: json['content'] as String,
      x: _safeToDouble(json['x']),
      y: _safeToDouble(json['y']),
      width: _safeToDouble(json['width']),
      height: _safeToDouble(json['height']),
      fontSize: _safeToDouble(json['fontSize'], defaultValue: 16.0),
      isBold: json['isBold'] as bool? ?? false,
      isItalic: json['isItalic'] as bool? ?? false,
      isUnderline: json['isUnderline'] as bool? ?? false,
    );
  }

  /// Safely converts a value to double
  static double _safeToDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /// Converts this [CardElementModel] instance into a JSON map.
  ///
  /// Returns a [Map<String, dynamic>] where the keys correspond to the properties of the card element.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'content': content,
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'fontSize': fontSize,
      'isBold': isBold,
      'isItalic': isItalic,
      'isUnderline': isUnderline,
    };
  }
}
