import 'package:flutter/foundation.dart';
import '../../domain/entities/card_element.dart';
import '../../domain/repositories/card_repository.dart';

/// An in-memory implementation of the [CardRepository] interface.
/// This repository uses a list to store [CardElement] objects.
class CardRepositoryImpl implements CardRepository {
  /// Internal list to store card elements.
  final List<CardElement> _elements = [];

  /// Adds a new [CardElement] to the repository.
  @override
  Future<void> addElement(CardElement element) async {
    debugPrint('CardRepositoryImpl.addElement: إضافة عنصر جديد من النوع ${element.type}');
    debugPrint('CardRepositoryImpl.addElement: معرف العنصر: ${element.id}');
    debugPrint('CardRepositoryImpl.addElement: موقع العنصر: (${element.x}, ${element.y})');
    debugPrint('CardRepositoryImpl.addElement: أبعاد العنصر: (${element.width}x${element.height})');

    // تمييز العناصر التي يتم إنشاؤها يدويًا في التطبيق
    // نضع علامة isFromAdminPanel = false لتمييزها عن العناصر التي تأتي من Firebase
    final updatedElement = element.copyWith(
      isFromAdminPanel: false, // هذا عنصر تم إنشاؤه يدويًا في التطبيق
      isRelativeCoordinates: true, // نستخدم إحداثيات نسبية دائمًا
    );

    debugPrint('CardRepositoryImpl.addElement: تم تحديث العنصر لتمييزه كعنصر تم إنشاؤه يدويًا');
    debugPrint('CardRepositoryImpl.addElement: isFromAdminPanel = ${updatedElement.isFromAdminPanel}');
    debugPrint('CardRepositoryImpl.addElement: isRelativeCoordinates = ${updatedElement.isRelativeCoordinates}');

    _elements.add(updatedElement);

    debugPrint('CardRepositoryImpl.addElement: تم إضافة العنصر بنجاح');
    debugPrint('CardRepositoryImpl.addElement: عدد العناصر الحالي: ${_elements.length}');
  }

  /// Updates an existing [CardElement] in the repository.
  ///
  /// Searches for the element by its [id] and updates it if found.
  @override
  Future<void> updateElement(CardElement element) async {
    final index = _elements.indexWhere((e) => e.id == element.id);
    if (index != -1) {
      _elements[index] = element;
    }
  }

  /// Removes a [CardElement] from the repository by its [elementId].
  @override
  Future<void> removeElement(String elementId) async {
    _elements.removeWhere((e) => e.id == elementId);
  }

  /// Retrieves all [CardElement] objects from the repository.
  @override
  Future<List<CardElement>> getAllElements() async {
    return _elements;
  }

  /// Sets the repository's elements with the provided list of [CardElement].
  ///
  /// This method clears the current list and adds all elements from the new list.
  @override
  void setElements(List<CardElement> elements) {
    _elements
      ..clear()
      ..addAll(elements);
  }

  /// Clears all elements from the repository.
  ///
  /// This method removes all elements from the repository.
  @override
  void clearElements() {
    _elements.clear();
  }
}
