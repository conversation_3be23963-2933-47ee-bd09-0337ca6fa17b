// lib/features/create_card/data/repositories/card_elements_repository.dart

import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import '../../domain/entities/card_element.dart';

/// مستودع لاسترجاع عناصر البطاقة من Firebase
class CardElementsRepository {
  final FirebaseFirestore _firestore;

  CardElementsRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// استرجاع عناصر البطاقة من Firebase
  Future<List<CardElement>> getCardElements(dynamic cardId) async {
    try {
      // التأكد من أن معرف البطاقة هو نص (String)
      final String safeCardId = cardId.toString();

      debugPrint('بدء استرجاع عناصر البطاقة للبطاقة: $safeCardId');

      // أولاً، نتحقق من وجود البطاقة نفسها
      final cardDoc =
          await _firestore.collection('cards').doc(safeCardId).get();

      if (!cardDoc.exists) {
        debugPrint('البطاقة غير موجودة: $safeCardId');
        return [];
      }

      debugPrint('البطاقة موجودة، نستعلم عن العناصر');

      // استرجاع لون الخلفية للبطاقة
      final cardData = cardDoc.data();
      String backgroundColor = 'FFFFFF'; // لون افتراضي

      if (cardData != null) {
        debugPrint(
            'بيانات البطاقة: ${cardData.toString().substring(0, min(200, cardData.toString().length))}...');

        // استرجاع لون الخلفية
        if (cardData.containsKey('backgroundColor')) {
          backgroundColor = cardData['backgroundColor'] as String? ?? 'FFFFFF';
          debugPrint('لون خلفية البطاقة: $backgroundColor');
        }

        // التحقق من وجود عناصر في وثيقة البطاقة نفسها
        if (cardData.containsKey('elements')) {
          debugPrint('تم العثور على عناصر في وثيقة البطاقة نفسها');
          final elements = cardData['elements'] as List<dynamic>?;

          if (elements != null && elements.isNotEmpty) {
            debugPrint(
                'تم العثور على ${elements.length} عنصر في وثيقة البطاقة');
            return _parseElementsFromCardDocument(elements, cardId);
          }
        }
      }

      // إذا لم تكن العناصر موجودة في وثيقة البطاقة، نبحث في المجموعة الفرعية
      debugPrint('البحث عن العناصر في المجموعة الفرعية elements');
      final elementsSnapshot = await _firestore
          .collection('cards')
          .doc(safeCardId)
          .collection('elements')
          .get();

      debugPrint(
          'تم العثور على ${elementsSnapshot.docs.length} عنصر في المجموعة الفرعية للبطاقة $safeCardId');

      if (elementsSnapshot.docs.isEmpty) {
        debugPrint('لا توجد عناصر للبطاقة: $safeCardId');
        return [];
      }

      // تحويل المستندات إلى كيانات
      final elements = <CardElement>[];

      for (var doc in elementsSnapshot.docs) {
        try {
          final data = doc.data();
          debugPrint(
              'معالجة العنصر ${doc.id}: ${data.toString().substring(0, min(100, data.toString().length))}...');

          final properties = data['properties'] ?? {};

          // تحديد نوع العنصر
          ElementType type;
          final typeStr = data['type'] ?? '';
          if (typeStr.toString().toLowerCase().contains('text')) {
            type = ElementType.text;
          } else if (typeStr.toString().toLowerCase().contains('image')) {
            type = ElementType.image;
          } else if (typeStr.toString().toLowerCase().contains('sticker')) {
            type = ElementType.sticker;
          } else if (typeStr.toString().toLowerCase().contains('qrcode')) {
            type = ElementType.qrCode;
          } else if (typeStr.toString().toLowerCase().contains('signature')) {
            type = ElementType.signature;
          } else {
            // افتراضي: نص
            type = ElementType.text;
            debugPrint(
                'نوع عنصر غير معروف: $typeStr، استخدام النص كنوع افتراضي');
          }

          debugPrint('نوع العنصر: $type');

          // استخراج الخصائص المشتركة
          double x = 0;
          double y = 0;
          double width = 100;
          double height = 100;
          double scale = 1.0;
          double rotation = 0;

          // التحقق من وجود الخصائص وتحويلها إلى أرقام
          if (data.containsKey('x')) {
            x = parseDouble(data['x'], 0);
          }

          if (data.containsKey('y')) {
            y = parseDouble(data['y'], 0);
          }

          if (data.containsKey('width')) {
            width = parseDouble(data['width'], 100);
          }

          if (data.containsKey('height')) {
            height = parseDouble(data['height'], 100);
          }

          if (data.containsKey('scale')) {
            scale = parseDouble(data['scale'], 1.0);
          }

          if (data.containsKey('rotation')) {
            rotation = parseDouble(data['rotation'], 0);
          }

          // نحتفظ بالقيم الأصلية كما هي تماماً كما تم حفظها في لوحة التحكم
          // سيتم التحويل في واجهة المستخدم

          // نضمن فقط أن الأبعاد ليست صغيرة جداً
          if (width < 0.05) width = 0.05;
          if (height < 0.05) height = 0.05;

          debugPrint(
              'الموقع: ($x, $y), الأبعاد: (${width}x$height), المقياس: $scale, الدوران: $rotation');

          // استخراج المحتوى
          String content = data['content'] ?? '';

          // للصور والملصقات، نتحقق من وجود روابط
          if (type == ElementType.image || type == ElementType.sticker) {
            // نحاول استخدام الرابط البعيد أولاً
            if (data.containsKey('remoteUrl') && data['remoteUrl'] != null) {
              content = data['remoteUrl'];
            }
            // إذا كان المحتوى يبدو كرابط URL، نستخدمه
            else if (content.startsWith('http')) {
              // المحتوى هو بالفعل رابط URL
            }
            // إذا كان هناك حقل imageUrl، نستخدمه
            else if (data.containsKey('imageUrl') && data['imageUrl'] != null) {
              content = data['imageUrl'];
            }
          }

          if (content.isNotEmpty) {
            debugPrint(
                'المحتوى: ${content.substring(0, min(50, content.length))}...');
          } else {
            debugPrint('المحتوى فارغ');
          }

          // تعديل حجم الخط بناءً على الأبعاد
          double fontSize = parseDouble(properties['fontSize'], 16);

          // طباعة معلومات حجم الخط الأصلي للتصحيح
          debugPrint('حجم الخط الأصلي: $fontSize');

          // تعديل حجم الخط للنصوص القادمة من لوحة التحكم
          // إذا كان حجم الخط صغيرًا جدًا، نقوم بتكبيره
          if (fontSize < 12) {
            fontSize = 16; // حجم خط افتراضي مناسب
            debugPrint('تم تعديل حجم الخط الصغير جدًا إلى: $fontSize');
          }

          // إذا كانت الإحداثيات نسبية (من لوحة التحكم)، نقوم بتكبير حجم الخط
          if (width <= 1 && height <= 1 && type == ElementType.text) {
            // تكبير حجم الخط بنسبة مناسبة
            double scaleFactor = 1.5;
            fontSize = fontSize * scaleFactor;
            debugPrint('تم تكبير حجم الخط بمعامل $scaleFactor إلى: $fontSize');
          }

          // إنشاء عنصر البطاقة
          final element = CardElement(
            id: doc.id,
            type: type,
            content: content,
            x: x,
            y: y,
            width: width,
            height: height,
            scale: scale,
            rotation: rotation,
            // خصائص النص
            fontSize: fontSize,
            isBold: properties['isBold'] ?? false,
            isItalic: properties['isItalic'] ?? false,
            isUnderline: properties['isUnderline'] ?? false,
            fontFamily: properties['fontFamily'],
            colorValue: parseColorValue(properties['fontColor']),
            textAlign: parseTextAlign(properties['textAlign']),
            // إضافة خصائص إضافية إذا كانت موجودة
            letterSpacing: properties['letterSpacing']?.toDouble(),
            lineHeight: properties['lineHeight']?.toDouble(),
            // تحديد ما إذا كانت الإحداثيات نسبية
            isRelativeCoordinates: width <= 1 && height <= 1,
          );

          elements.add(element);
        } catch (e) {
          debugPrint('خطأ في معالجة العنصر: $e');
        }
      }

      debugPrint('تم تحويل ${elements.length} عنصر بنجاح');
      return elements;
    } catch (e) {
      debugPrint('خطأ في استرجاع عناصر البطاقة: $e');
      return [];
    }
  }

  /// تحويل قيمة إلى رقم عشري
  double parseDouble(dynamic value, double defaultValue) {
    if (value == null) return defaultValue;

    if (value is double) return value;
    if (value is int) return value.toDouble();

    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return defaultValue;
      }
    }

    return defaultValue;
  }

  /// تحويل قيمة اللون من سلسلة إلى عدد صحيح
  int? parseColorValue(dynamic colorHex) {
    if (colorHex == null) return null;

    String hexString;

    if (colorHex is int) {
      return colorHex;
    } else if (colorHex is String) {
      hexString = colorHex;
    } else {
      return null;
    }

    // إزالة علامة # إذا وجدت
    final hex = hexString.startsWith('#') ? hexString.substring(1) : hexString;

    // تحويل سلسلة اللون إلى عدد صحيح
    try {
      // إذا كان الرمز يحتوي على قناة ألفا بالفعل
      if (hex.length == 8) {
        return int.parse(hex, radix: 16);
      }
      // إذا كان الرمز بدون قناة ألفا
      return int.parse('FF$hex', radix: 16);
    } catch (e) {
      debugPrint('خطأ في تحويل اللون: $e');
      return null;
    }
  }

  /// تحويل محاذاة النص من سلسلة إلى TextAlign
  String? parseTextAlign(dynamic textAlignStr) {
    if (textAlignStr == null) return null;

    final String alignStr = textAlignStr.toString();

    if (alignStr.contains('left') || alignStr.contains('start')) {
      return 'left';
    } else if (alignStr.contains('center')) {
      return 'center';
    } else if (alignStr.contains('right') || alignStr.contains('end')) {
      return 'right';
    } else if (alignStr.contains('justify')) {
      return 'justify';
    }

    return null;
  }

  /// تحليل عناصر البطاقة من وثيقة البطاقة
  List<CardElement> _parseElementsFromCardDocument(
      List<dynamic> elements, dynamic cardId) {
    // التأكد من أن معرف البطاقة هو نص (String)
    final String safeCardId = cardId.toString();
    final result = <CardElement>[];

    debugPrint('بدء تحليل ${elements.length} عنصر من وثيقة البطاقة');

    // لا نحتاج إلى تحديد أبعاد البطاقة هنا

    for (var i = 0; i < elements.length; i++) {
      try {
        final element = elements[i] as Map<String, dynamic>;
        debugPrint(
            'معالجة العنصر $i: ${element.toString().substring(0, min(100, element.toString().length))}...');

        // تحديد نوع العنصر
        ElementType type;
        final typeStr = element['type'] ?? '';
        if (typeStr.toString().toLowerCase().contains('text')) {
          type = ElementType.text;
        } else if (typeStr.toString().toLowerCase().contains('image')) {
          type = ElementType.image;
        } else if (typeStr.toString().toLowerCase().contains('sticker')) {
          type = ElementType.sticker;
        } else if (typeStr.toString().toLowerCase().contains('qrcode')) {
          type = ElementType.qrCode;
        } else if (typeStr.toString().toLowerCase().contains('signature')) {
          type = ElementType.signature;
        } else {
          // افتراضي: نص
          type = ElementType.text;
          debugPrint('نوع عنصر غير معروف: $typeStr، استخدام النص كنوع افتراضي');
        }

        // استخراج الخصائص المشتركة
        double x = parseDouble(element['x'], 0);
        double y = parseDouble(element['y'], 0);
        double width = parseDouble(element['width'], 100);
        double height = parseDouble(element['height'], 100);
        double scale = parseDouble(element['scale'], 1.0);
        double rotation = parseDouble(element['rotation'], 0);

        // نحتفظ بالقيم الأصلية كما هي تماماً كما تم حفظها في لوحة التحكم
        // سيتم التحويل في واجهة المستخدم

        // نضمن فقط أن الأبعاد ليست صغيرة جداً
        if (width < 0.05) width = 0.05;
        if (height < 0.05) height = 0.05;

        // استخراج المحتوى
        String content = '';
        if (element.containsKey('content')) {
          content = element['content'] ?? '';
        } else if (type == ElementType.text) {
          content = element['text'] ?? '';
        } else {
          content = element['remoteUrl'] ??
              element['imageUrl'] ??
              element['content'] ??
              '';
        }

        // استخراج الخصائص
        Map<String, dynamic> properties;
        if (element.containsKey('properties')) {
          properties = element['properties'] as Map<String, dynamic>? ?? {};
        } else {
          // إذا لم تكن الخصائص موجودة، نستخرجها من العنصر نفسه
          properties = {
            'fontSize': element['fontSize'],
            'isBold': element['isBold'],
            'isItalic': element['isItalic'],
            'isUnderline': element['isUnderline'],
            'fontFamily': element['fontFamily'],
            'fontColor': element['fontColor'],
            'textAlign': element['textAlign'],
            'letterSpacing': element['letterSpacing'],
            'lineHeight': element['lineHeight'],
          };
        }

        // تحويل لون النص
        int? colorValue = parseColorValue(properties['fontColor']);

        // إذا كان اللون غير موجود في الخصائص، نبحث عنه في العنصر نفسه
        if (colorValue == null && element.containsKey('colorValue')) {
          colorValue = element['colorValue'] as int?;
        }

        // تعديل حجم الخط بناءً على الأبعاد
        double fontSize = parseDouble(properties['fontSize'], 16);

        // طباعة معلومات حجم الخط الأصلي للتصحيح
        debugPrint('حجم الخط الأصلي: $fontSize');

        // تعديل حجم الخط للنصوص القادمة من لوحة التحكم
        // إذا كان حجم الخط صغيرًا جدًا، نقوم بتكبيره
        if (fontSize < 12) {
          fontSize = 16; // حجم خط افتراضي مناسب
          debugPrint('تم تعديل حجم الخط الصغير جدًا إلى: $fontSize');
        }

        // إذا كانت الإحداثيات نسبية (من لوحة التحكم)، نقوم بتكبير حجم الخط
        if (width <= 1 && height <= 1) {
          // تكبير حجم الخط بنسبة مناسبة
          double scaleFactor = 1.5;
          fontSize = fontSize * scaleFactor;
          debugPrint('تم تكبير حجم الخط بمعامل $scaleFactor إلى: $fontSize');
        }

        // تحديد ما إذا كانت الإحداثيات نسبية
        bool isRelativeCoordinates = width <= 1 && height <= 1;

        // تحديد ما إذا كان العنصر من لوحة التحكم
        // نفترض أن جميع العناصر التي تأتي من Firestore هي من لوحة التحكم
        bool isFromAdminPanel = true;

        // طباعة معلومات الصورة للتصحيح
        if (type == ElementType.image) {
          debugPrint(
              'معلومات الصورة: المحتوى=${content.substring(0, min(50, content.length))}..., '
              'الإحداثيات=($x, $y), '
              'الأبعاد=(${width}x$height)');
        }

        // معالجة خاصة فقط للصور التي تبدأ من النقطة (0,0) وتكون صغيرة جدًا
        if (type == ElementType.image &&
            x == 0 &&
            y == 0 &&
            width < 0.05 &&
            height < 0.05) {
          debugPrint('صورة صغيرة جدًا في النقطة (0,0) - تكبيرها قليلاً');

          // تكبير الصورة قليلاً فقط إذا كانت صغيرة جدًا
          if (isRelativeCoordinates) {
            width = 0.2;
            height = 0.2;
          } else {
            width = 100.0;
            height = 100.0;
          }

          debugPrint('تم تكبير الصورة الصغيرة جدًا إلى: (${width}x$height)');
        }

        // إنشاء عنصر البطاقة
        final cardElement = CardElement(
          id: element['id'] ?? 'element_${safeCardId}_$i',
          type: type,
          content: content,
          x: x,
          y: y,
          width: width,
          height: height,
          scale: scale,
          rotation: rotation,
          // خصائص النص
          fontSize: fontSize,
          isBold: properties['isBold'] ?? false,
          isItalic: properties['isItalic'] ?? false,
          isUnderline: properties['isUnderline'] ?? false,
          fontFamily: properties['fontFamily'],
          colorValue: colorValue,
          textAlign: parseTextAlign(properties['textAlign']),
          // إضافة خصائص إضافية إذا كانت موجودة
          letterSpacing: properties['letterSpacing']?.toDouble(),
          lineHeight: properties['lineHeight']?.toDouble(),
          // تحديد ما إذا كانت الإحداثيات نسبية
          isRelativeCoordinates: isRelativeCoordinates,
          // تحديد ما إذا كان العنصر من لوحة التحكم
          isFromAdminPanel: isFromAdminPanel,
        );

        result.add(cardElement);

        // طباعة تفاصيل العنصر للتصحيح
        debugPrint('تم إنشاء عنصر: النوع=${cardElement.type}, '
            'المعرف=${cardElement.id}, '
            'الموقع=(${cardElement.x}, ${cardElement.y}), '
            'الأبعاد=(${cardElement.width}x${cardElement.height}), '
            'المقياس=${cardElement.scale}, '
            'الدوران=${cardElement.rotation}');
      } catch (e) {
        debugPrint('خطأ في معالجة العنصر $i: $e');
      }
    }

    debugPrint('تم تحليل ${result.length} عنصر بنجاح من وثيقة البطاقة');
    return result;
  }
}
