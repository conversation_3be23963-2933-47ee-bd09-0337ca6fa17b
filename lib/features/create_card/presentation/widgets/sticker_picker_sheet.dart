import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
// CachedNetworkImage removed

import '../../data/datasources/stickers_remote_data_source.dart';
import '../../domain/entities/sticker_entity.dart';

/// صفحة اختيار الملصقات مع تبويبات للمناسبات
class StickerPickerSheet extends StatefulWidget {
  /// دالة رد الاتصال عند اختيار ملصق
  final Function(String) onStickerSelected;

  const StickerPickerSheet({super.key, required this.onStickerSelected});

  @override
  State<StickerPickerSheet> createState() => _StickerPickerSheetState();
}

class _StickerPickerSheetState extends State<StickerPickerSheet>
    with TickerProviderStateMixin {
  /// مصدر البيانات البعيد للملصقات
  late final StickersRemoteDataSource _dataSource;

  /// تحكم التبويبات
  TabController? _tabController;

  /// قائمة فئات الملصقات
  List<String> _categories = [];

  /// حالة التحميل
  bool _isLoading = true;

  /// رسالة الخطأ
  String? _errorMessage;

  /// خريطة لتخزين الملصقات حسب الفئة
  final Map<String, List<StickerEntity>> _stickersByCategory = {};

  /// ترجمة معرفات الفئات إلى أسماء عربية - المناسبات المعتمدة فقط
  final Map<String, String> _categoryTranslations = {
    'sticker_category_wedding': 'زواج',
    'sticker_category_love': 'حب',
    'sticker_category_birthday': 'عيد ميلاد',
    'sticker_category_mother': 'عيد الأم',
    'sticker_category_father': 'عيد الأب',
    'sticker_category_graduation': 'تخرج',
    'sticker_category_newbaby': 'مولود جديد',
    'sticker_category_newhome': 'منزل مبارك',
    'sticker_category_getwellsoon': 'أجر وشفاء',
    'sticker_category_eid': 'عيد',
    'sticker_category_ramadan': 'رمضان',
    'sticker_category_hajj': 'حج',
  };

  @override
  void initState() {
    super.initState();
    _dataSource = StickersRemoteDataSourceImpl(
      firestore: FirebaseFirestore.instance,
    );
    _loadCategories();
  }

  /// تحميل فئات الملصقات المعتمدة فقط
  Future<void> _loadCategories() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // استخدام الفئات المعتمدة فقط من _categoryTranslations
      final List<String> allCategories = await _dataSource.getAllCategories();

      // طباعة جميع الفئات المتاحة للتصحيح
      debugPrint('All available categories: $allCategories');

      // تصفية الفئات للحصول على الفئات المعتمدة فقط
      final List<String> filteredCategories = allCategories
          .where((category) => _categoryTranslations.containsKey(category))
          .toList();

      // طباعة الفئات المعتمدة بعد التصفية
      debugPrint('Filtered categories: $filteredCategories');

      // ترتيب الفئات حسب الترجمة العربية
      filteredCategories.sort((a, b) {
        final aName = _categoryTranslations[a] ?? a;
        final bName = _categoryTranslations[b] ?? b;
        return aName.compareTo(bName);
      });

      // إذا لم تكن هناك فئات معتمدة، نستخدم جميع الفئات المتاحة
      if (filteredCategories.isEmpty && allCategories.isNotEmpty) {
        debugPrint(
            'No approved categories found, using all available categories');
        setState(() {
          _categories = allCategories;
          _isLoading = false;
        });

        // تهيئة تحكم التبويبات
        _tabController = TabController(
          length: allCategories.length,
          vsync: this,
        );

        // تحميل الملصقات للفئة الأولى
        _loadStickersForCategory(allCategories.first);
      } else if (filteredCategories.isNotEmpty) {
        // استخدام الفئات المعتمدة
        setState(() {
          _categories = filteredCategories;
          _isLoading = false;
        });

        // تهيئة تحكم التبويبات
        _tabController = TabController(
          length: filteredCategories.length,
          vsync: this,
        );

        // تحميل الملصقات للفئة الأولى
        _loadStickersForCategory(filteredCategories.first);
      } else {
        // لا توجد فئات على الإطلاق
        setState(() {
          _isLoading = false;
          _errorMessage = 'لا توجد فئات ملصقات متاحة حاليًا';
        });
      }
    } catch (e) {
      debugPrint('Error loading categories: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل الفئات: $e';
      });
    }
  }

  /// تحميل الملصقات لفئة معينة
  Future<void> _loadStickersForCategory(String category) async {
    try {
      // إذا كانت الملصقات محملة مسبقًا، لا نحملها مرة أخرى
      if (_stickersByCategory.containsKey(category)) {
        return;
      }

      // استرجاع الملصقات للفئة
      final stickers = await _dataSource.getStickersByCategory(category);

      setState(() {
        _stickersByCategory[category] = stickers;
      });
    } catch (e) {
      debugPrint('Error loading stickers for category $category: $e');
      // لا نعرض رسالة خطأ هنا لتجنب إزعاج المستخدم
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      height: MediaQuery.of(context).size.height *
          0.7, // ارتفاع الـ bottom sheet - 70% من ارتفاع الشاشة
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _categories.isEmpty
                  ? const Center(child: Text('لا توجد ملصقات متاحة'))
                  : _buildTabView(),
    );
  }

  /// بناء عرض التبويبات
  Widget _buildTabView() {
    return Column(
      children: [
        // عنوان
        const Text(
          'اختر ملصقًا',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // شريط التبويبات - تحسين مظهر التبويبات
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300, width: 0.5),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 8),
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            labelColor: Theme.of(context).primaryColor,
            unselectedLabelColor: Colors.grey.shade700,
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            indicator: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: const Color.fromRGBO(0, 0, 0, 0.05),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
              border: Border.all(
                color: const Color.fromRGBO(33, 150, 243, 0.3),
                width: 1,
              ),
            ),
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.normal,
              fontSize: 14,
            ),
            padding: EdgeInsets.zero,
            labelPadding: const EdgeInsets.symmetric(horizontal: 16),
            tabs: _categories.map((category) {
              return Tab(
                text: _categoryTranslations[category] ?? category,
                height: 40,
              );
            }).toList(),
            onTap: (index) {
              // تحميل الملصقات للفئة المحددة
              _loadStickersForCategory(_categories[index]);
            },
          ),
        ),
        const SizedBox(height: 16),

        // عرض الملصقات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: _categories.map((category) {
              return _buildStickersGrid(category);
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// بناء شبكة الملصقات لفئة معينة
  Widget _buildStickersGrid(String category) {
    final stickers = _stickersByCategory[category];

    if (stickers == null) {
      // إذا لم تكن الملصقات محملة بعد، نعرض مؤشر تحميل
      return const Center(child: CircularProgressIndicator());
    }

    if (stickers.isEmpty) {
      // إذا لم تكن هناك ملصقات للفئة، نعرض رسالة
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.image_not_supported, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'لا تتوفر ملصقات في فئة "${_categoryTranslations[category] ?? category}" حاليًا',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4, // زيادة عدد الأعمدة من 3 إلى 4
        crossAxisSpacing: 4, // تقليل المسافة بين الأعمدة
        mainAxisSpacing: 4, // تقليل المسافة بين الصفوف
        childAspectRatio: 1.0, // نسبة العرض إلى الارتفاع
      ),
      itemCount: stickers.length,
      itemBuilder: (context, index) {
        final sticker = stickers[index];
        return _buildStickerItem(sticker);
      },
    );
  }

  /// بناء عنصر الملصق بدون تظليل
  Widget _buildStickerItem(StickerEntity sticker) {
    return GestureDetector(
      onTap: () {
        // عند النقر على الملصق، نستدعي دالة رد الاتصال
        widget.onStickerSelected(sticker.imageUrl);
      },
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          // استخدام حدود خفيفة جدًا بدلاً من التظليل
          border: Border.all(
            color: const Color.fromRGBO(128, 128, 128, 0.2),
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            sticker.imageUrl,
            fit: BoxFit.contain,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return const Center(
                child: SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) => const Icon(
              Icons.error,
              color: Colors.red,
            ),
          ),
        ),
      ),
    );
  }
}
