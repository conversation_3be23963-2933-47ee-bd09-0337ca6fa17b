import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../domain/entities/card_element.dart';
import '../bloc/card_editor_bloc.dart';
import '../bloc/card_editor_event.dart';
import 'color_picker.dart';

/// A bottom toolbar for editing text properties of a card element.
/// It provides options for deleting, editing text content, choosing font and color,
/// adjusting font size, toggling bold/italic/underline styles, setting text alignment,
/// and duplicating the text element.
class TextEditBottomBar extends StatelessWidget {
  /// The card element representing the text that will be edited.
  final CardElement element;

  const TextEditBottomBar({super.key, required this.element});





  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).bottomAppBarTheme.color ??
             Theme.of(context).colorScheme.surface,
      height: 60,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Delete button: removes the element.
            IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'حذف العنصر', // "Delete Element"
              onPressed: () {
                context
                    .read<CardEditorBloc>()
                    .add(RemoveElementEvent(element.id));
              },
            ),
            // Text edit button: opens a dialog to edit the text content.
            TextButton(
              onPressed: () async {
                final controller = TextEditingController(text: element.content);
                final newText = await showDialog<String>(
                  context: context,
                  builder: (ctx) => AlertDialog(
                    title: Text(AppLocalizations.of(context).editText), // "Edit Text"
                    content: TextField(controller: controller),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(ctx),
                        child: Text(AppLocalizations.of(context).cancel), // "Cancel"
                      ),
                      TextButton(
                        onPressed: () => Navigator.pop(ctx, controller.text),
                        child: Text(AppLocalizations.of(context).ok), // "OK"
                      ),
                    ],
                  ),
                );
                if (newText != null) {
                  context.read<CardEditorBloc>().add(
                        UpdateTextPropsEvent(
                          elementId: element.id,
                          newContent: newText,
                        ),
                      );
                }
              },
              child: const Text('تعديل النص'), // "Edit Text"
            ),
            const VerticalDivider(),
            // Section for font and color selection.
            Row(
              children: [
                // Font selection button.
                IconButton(
                  icon: const Icon(Icons.font_download),
                  tooltip: 'اختر الخط', // "Choose Font"
                  onPressed: () async {
                    // List of available fonts.
                    final fonts = [
                      'Roboto',
                      'Cairo',
                      'Lobster',
                      'Dancing Script',
                      'Open Sans',
                      'Montserrat',
                      'Oswald',
                      'Merriweather',
                      'Amiri',
                      'Tajawal',
                      'El Messiri',
                      'Scheherazade',
                      'Noto Sans',
                      'Noto Serif',
                      'Lalezar',
                      'Reem Kufi',
                      'Changa',
                      'Lateef',
                      'Harmattan',
                      'Vazir',
                    ];
                    final chosen = await showDialog<String>(
                      context: context,
                      builder: (ctx) => SimpleDialog(
                        title: const Text('اختر الخط'), // "Choose Font"
                        children: fonts.map((f) {
                          return SimpleDialogOption(
                            onPressed: () => Navigator.pop(ctx, f),
                            child: Text(
                              f,
                              style: () {
                                try {
                                  return GoogleFonts.getFont(f);
                                } catch (e) {
                                  return TextStyle(fontFamily: f);
                                }
                              }(),
                            ),
                          );
                        }).toList(),
                      ),
                    );
                    if (chosen != null) {
                      context.read<CardEditorBloc>().add(
                            UpdateTextPropsEvent(
                              elementId: element.id,
                              fontFamily: chosen,
                            ),
                          );
                    }
                  },
                ),
                const Text('اختيار الخط'), // "Choose Font"
                const SizedBox(width: 8),
                // Color picker widget to choose text color.
                ColorPicker(
                  colors: [
                    Colors.black,
                    Colors.blue,
                    Colors.red,
                    Colors.green,
                    Colors.orange,
                    Colors.purple,
                    Colors.brown,
                    Colors.teal,
                    Colors.indigo,
                    Colors.pink,
                    Colors.cyan,
                    Colors.amber,
                  ],
                  initialColor: (element.colorValue != null)
                      ? Color(element.colorValue!)
                      : Colors.black,
                  onColorSelected: (color) {
                    context.read<CardEditorBloc>().add(
                          UpdateTextPropsEvent(
                            elementId: element.id,
                            colorValue: color.value,
                          ),
                        );
                  },
                ),
              ],
            ),
            const VerticalDivider(),
            // Button to decrease font size with smart frame adjustment.
            IconButton(
              icon: const Icon(Icons.text_decrease),
              tooltip: 'تصغير حجم الخط', // "Decrease Font Size"
              onPressed: () {
                // Smart font size decrease with frame adjustment
                const double minFontSize = 10.0;

                final double currentFontSize = element.fontSize;
                final double newFontSize = math.max(currentFontSize - 2.0, minFontSize);

                if (newFontSize >= minFontSize) {
                  // تصغير تناسبي مثل السحب - حساب نسبة التغيير
                  final double scaleFactor = newFontSize / currentFontSize;

                  // تطبيق نفس النسبة على الإطار
                  double newWidth = element.width * scaleFactor;
                  double newHeight = element.height * scaleFactor;

                  // فحص ما إذا كان العنصر يستخدم إحداثيات نسبية
                  bool isRelative = element.isRelativeCoordinates || (element.width <= 1 && element.height <= 1);

                  if (isRelative) {
                    // للإحداثيات النسبية: تطبيق حد أدنى نسبي
                    const double cardWidth = 350.0;
                    const double cardHeight = 500.0;
                    const double minAbsoluteWidth = 30.0;
                    const double minAbsoluteHeight = 20.0;

                    final double minRelativeWidth = minAbsoluteWidth / cardWidth;
                    final double minRelativeHeight = minAbsoluteHeight / cardHeight;

                    newWidth = math.max(newWidth, minRelativeWidth);
                    newHeight = math.max(newHeight, minRelativeHeight);
                  } else {
                    // للإحداثيات المطلقة: تطبيق حد أدنى مطلق
                    newWidth = math.max(newWidth, 30.0);
                    newHeight = math.max(newHeight, 20.0);
                  }

                  // تحديث حجم الخط
                  context.read<CardEditorBloc>().add(
                        UpdateTextPropsEvent(
                          elementId: element.id,
                          newFontSize: newFontSize,
                        ),
                      );

                  // تحديث حجم الإطار بنفس النسبة
                  context.read<CardEditorBloc>().add(
                        UpdateElementSizeEvent(
                          element.id,
                          newWidth,
                          newHeight,
                        ),
                      );
                }
              },
            ),
            // Button to increase font size with smart frame adjustment.
            IconButton(
              icon: const Icon(Icons.text_increase),
              tooltip: 'تكبير حجم الخط', // "Increase Font Size"
              onPressed: () {
                // Smart font size increase with frame adjustment
                const double maxFontSize = 120.0;

                final double currentFontSize = element.fontSize;
                final double newFontSize = math.min(currentFontSize + 2.0, maxFontSize);

                if (newFontSize <= maxFontSize) {
                  // تكبير تناسبي مثل السحب - حساب نسبة التغيير
                  final double scaleFactor = newFontSize / currentFontSize;

                  // تطبيق نفس النسبة على الإطار
                  double proposedWidth = element.width * scaleFactor;
                  double proposedHeight = element.height * scaleFactor;

                  // فحص ما إذا كان العنصر يستخدم إحداثيات نسبية
                  bool isRelative = element.isRelativeCoordinates || (element.width <= 1 && element.height <= 1);

                  // فحص حدود البطاقة
                  const double cardWidth = 350.0;
                  const double cardHeight = 500.0;
                  const double margin = 10.0;

                  double maxAllowedWidth, maxAllowedHeight;

                  if (isRelative) {
                    // للإحداثيات النسبية: حساب الحدود النسبية
                    final double absoluteX = element.x * cardWidth;
                    final double absoluteMaxWidth = cardWidth - absoluteX - margin;
                    final double absoluteMaxHeight = cardHeight - (element.y * cardHeight) - margin;

                    maxAllowedWidth = absoluteMaxWidth / cardWidth;
                    maxAllowedHeight = absoluteMaxHeight / cardHeight;
                  } else {
                    // للإحداثيات المطلقة: حساب الحدود المطلقة
                    maxAllowedWidth = cardWidth - element.x - margin;
                    maxAllowedHeight = cardHeight - element.y - margin;
                  }

                  // تطبيق الحدود
                  final double finalWidth = math.min(proposedWidth, maxAllowedWidth);
                  final double finalHeight = math.min(proposedHeight, maxAllowedHeight);

                  // التأكد من أن التكبير ممكن
                  if (finalWidth > element.width && finalHeight > element.height) {
                    // تحديث حجم الخط
                    context.read<CardEditorBloc>().add(
                          UpdateTextPropsEvent(
                            elementId: element.id,
                            newFontSize: newFontSize,
                          ),
                        );

                    // تحديث حجم الإطار بنفس النسبة
                    context.read<CardEditorBloc>().add(
                          UpdateElementSizeEvent(
                            element.id,
                            finalWidth,
                            finalHeight,
                          ),
                        );
                  }
                }
              },
            ),
            const VerticalDivider(),
            // Bold toggle button.
            IconButton(
              icon: Icon(
                Icons.format_bold,
                color: element.isBold ? Colors.blue : Colors.black,
              ),
              tooltip: 'خط عريض', // "Bold"
              onPressed: () {
                context.read<CardEditorBloc>().add(
                      UpdateTextPropsEvent(
                        elementId: element.id,
                        isBold: !element.isBold,
                      ),
                    );
              },
            ),
            // Italic toggle button.
            IconButton(
              icon: Icon(
                Icons.format_italic,
                color: element.isItalic ? Colors.blue : Colors.black,
              ),
              tooltip: 'خط مائل', // "Italic"
              onPressed: () {
                context.read<CardEditorBloc>().add(
                      UpdateTextPropsEvent(
                        elementId: element.id,
                        isItalic: !element.isItalic,
                      ),
                    );
              },
            ),
            // Underline toggle button.
            IconButton(
              icon: Icon(
                Icons.format_underline,
                color: element.isUnderline ? Colors.blue : Colors.black,
              ),
              tooltip: 'تحته خط', // "Underline"
              onPressed: () {
                context.read<CardEditorBloc>().add(
                      UpdateTextPropsEvent(
                        elementId: element.id,
                        isUnderline: !element.isUnderline,
                      ),
                    );
              },
            ),
            const VerticalDivider(),
            // Text alignment buttons.
            IconButton(
              icon: const Icon(Icons.format_align_left),
              tooltip: 'محاذاة لليسار', // "Align Left"
              onPressed: () {
                context.read<CardEditorBloc>().add(
                      UpdateTextPropsEvent(
                        elementId: element.id,
                        textAlign: 'left',
                      ),
                    );
              },
            ),
            IconButton(
              icon: const Icon(Icons.format_align_right),
              tooltip: 'محاذاة لليمين', // "Align Right"
              onPressed: () {
                context.read<CardEditorBloc>().add(
                      UpdateTextPropsEvent(
                        elementId: element.id,
                        textAlign: 'right',
                      ),
                    );
              },
            ),
            IconButton(
              icon: const Icon(Icons.format_align_center),
              tooltip: 'توسيط النص', // "Center Align"
              onPressed: () {
                context.read<CardEditorBloc>().add(
                      UpdateTextPropsEvent(
                        elementId: element.id,
                        textAlign: 'center',
                      ),
                    );
              },
            ),
            // Button to duplicate the text element.
            IconButton(
              icon: const Icon(Icons.copy),
              tooltip: 'تكرار النص', // "Duplicate Text"
              onPressed: () {
                context.read<CardEditorBloc>().add(
                      DuplicateTextElementEvent(element.id),
                    );
              },
            ),
          ],
        ),
      ),
    );
  }
}
