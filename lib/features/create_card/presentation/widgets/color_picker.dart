import 'package:flutter/material.dart';

/// A customizable color picker widget that displays a list of selectable colors.
/// When tapped, it expands to show all available colors. Once a color is selected,
/// it collapses back to display only the selected color.
class ColorPicker extends StatefulWidget {
  /// The list of colors available for selection.
  final List<Color> colors;

  /// The initially selected color.
  final Color initialColor;

  /// Callback function that is triggered when a color is selected.
  final ValueChanged<Color> onColorSelected;

  const ColorPicker({
    super.key,
    required this.colors,
    required this.initialColor,
    required this.onColorSelected,
  });

  @override
  State<ColorPicker> createState() => _ColorPickerState();
}

class _ColorPickerState extends State<ColorPicker>
    with SingleTickerProviderStateMixin {
  // Determines whether the picker is expanded to show all colors.
  bool _expanded = false;

  // Stores the currently selected color.
  late Color _selectedColor;

  @override
  void initState() {
    super.initState();
    // Initialize the selected color with the initial color provided.
    _selectedColor = widget.initialColor;
  }

  /// Toggles the expanded/collapsed state of the color picker.
  void _toggleExpanded() {
    setState(() {
      _expanded = !_expanded;
    });
  }

  /// Handles color selection.
  /// Updates the selected color, collapses the picker, and triggers the callback.
  void _selectColor(Color color) {
    setState(() {
      _selectedColor = color;
      _expanded = false;
    });
    widget.onColorSelected(color);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSize(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: _expanded
          // Expanded view: display all available colors in a horizontal row.
          ? Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: widget.colors.map((color) {
                  return GestureDetector(
                    onTap: () => _selectColor(color),
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: color,
                        // Highlight the selected color with a black border.
                        border: Border.all(
                          color: _selectedColor == color
                              ? Colors.black
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            )
          // Collapsed view: display only the selected color.
          : GestureDetector(
              onTap: _toggleExpanded,
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _selectedColor,
                  border: Border.all(color: Colors.white, width: 2),
                ),
              ),
            ),
    );
  }
}
