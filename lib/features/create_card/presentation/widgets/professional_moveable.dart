import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../../../../core/utils/professional_performance_optimizer.dart';
import '../../../../core/utils/professional_snapping_system.dart';
import '../../../../core/widgets/professional_handles.dart' as handles;

/// محرك التحكم الاحترافي للعناصر
class ProfessionalMoveable extends StatefulWidget {
  /// الموضع الأولي للعنصر
  final Offset offset;
  
  /// الحجم الأولي للعنصر
  final Size size;
  
  /// زاوية الدوران بالدرجات
  final double rotationAngle;
  
  /// معامل التكبير
  final double scale;
  
  /// العنصر الفرعي
  final Widget child;
  
  /// تفعيل السحب
  final bool withDragging;
  
  /// تفعيل تغيير الحجم
  final bool withResizing;
  
  /// تفعيل الدوران
  final bool withRotating;
  
  /// إظهار مقابض التحكم
  final bool showHandles;
  
  /// أبعاد الحاوية
  final double containerWidth;
  final double containerHeight;
  
  /// تفعيل الالتصاق
  final bool enableSnapping;
  
  /// تفعيل الشبكة
  final bool enableGrid;
  
  /// العناصر الأخرى للالتصاق بها
  final List<Rect> otherElements;
  
  /// دالة استدعاء عند التغيير
  final Function(Offset offset, Size size, double rotation)? onChanged;
  
  /// دالة استدعاء عند انتهاء التحويل
  final Function(Offset offset, Size size, double rotation)? onTransformEnd;
  
  /// اللون الأساسي للمقابض
  final Color primaryColor;
  
  /// اللون الثانوي للمقابض
  final Color secondaryColor;

  const ProfessionalMoveable({
    Key? key,
    required this.offset,
    required this.size,
    this.rotationAngle = 0.0,
    this.scale = 1.0,
    required this.child,
    this.withDragging = true,
    this.withResizing = true,
    this.withRotating = true,
    this.showHandles = true,
    required this.containerWidth,
    required this.containerHeight,
    this.enableSnapping = true,
    this.enableGrid = false,
    this.otherElements = const [],
    this.onChanged,
    this.onTransformEnd,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.white,
  }) : super(key: key);

  @override
  State<ProfessionalMoveable> createState() => _ProfessionalMoveableState();
}

class _ProfessionalMoveableState extends State<ProfessionalMoveable>
    with TickerProviderStateMixin {
  
  late Rect _currentRect;
  late double _currentRotation;
  late double _currentScale;
  
  // مدراء النظام
  late final ProfessionalPerformanceOptimizer _performanceOptimizer;
  
  // متغيرات الالتصاق
  List<SnapLine> _snapLines = [];
  
  // متغيرات الرسوم المتحركة
  late AnimationController _selectionController;
  late Animation<double> _selectionAnimation;
  
  // متغيرات التحكم
  bool _isSelected = false;
  bool _isDragging = false;
  bool _isResizing = false;
  bool _isRotating = false;
  
  @override
  void initState() {
    super.initState();
    _initializeState();
    _initializeAnimations();
    _performanceOptimizer = ProfessionalPerformanceOptimizer();
  }
  
  void _initializeState() {
    _currentRect = Rect.fromLTWH(
      widget.offset.dx,
      widget.offset.dy,
      widget.size.width,
      widget.size.height,
    );
    _currentRotation = widget.rotationAngle;
    _currentScale = widget.scale;
  }
  
  void _initializeAnimations() {
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _selectionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _selectionController,
      curve: Curves.easeOutCubic,
    ));
  }
  
  @override
  void dispose() {
    _performanceOptimizer.dispose();
    _selectionController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // العنصر الرئيسي
        _buildMainElement(),
        
        // مقابض التحكم
        if (_isSelected && widget.showHandles)
          _buildHandles(),
        
        // خطوط الالتصاق
        if (_snapLines.isNotEmpty)
          CustomPaint(
            painter: SnapLinePainter(snapLines: _snapLines),
            size: Size(widget.containerWidth, widget.containerHeight),
          ),
      ],
    );
  }
  
  Widget _buildMainElement() {
    return Positioned.fromRect(
      rect: _currentRect,
      child: RepaintBoundary(
        child: GestureDetector(
          onTap: _handleTap,
          onPanStart: widget.withDragging ? _handlePanStart : null,
          onPanUpdate: widget.withDragging ? _handlePanUpdate : null,
          onPanEnd: widget.withDragging ? _handlePanEnd : null,
          child: AnimatedBuilder(
            animation: _selectionAnimation,
            builder: (context, child) {
              return Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..scale(_currentScale * (1.0 + 0.02 * _selectionAnimation.value))
                  ..rotateZ(_currentRotation * math.pi / 180),
                child: Container(
                  width: _currentRect.width,
                  height: _currentRect.height,
                  decoration: BoxDecoration(
                    border: _isSelected
                        ? Border.all(
                            color: widget.primaryColor.withValues(alpha: _selectionAnimation.value),
                            width: 2.0,
                          )
                        : null,
                  ),
                  child: ClipRect(
                    child: widget.child,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
  
  Widget _buildHandles() {
    return handles.ProfessionalHandleSet(
      bounds: _currentRect,
      isSelected: _isSelected,
      onHandleDrag: _handleHandleDrag,
      onHandleDragEnd: _handleHandleDragEnd,
      primaryColor: widget.primaryColor,
      secondaryColor: widget.secondaryColor,
      showRotationHandle: widget.withRotating,
      showAnimation: true,
    );
  }
  
  // معالجات الأحداث
  void _handleTap() {
    setState(() {
      _isSelected = !_isSelected;
    });
    
    if (_isSelected) {
      _selectionController.forward();
    } else {
      _selectionController.reverse();
    }
  }
  
  void _handlePanStart(DragStartDetails details) {
    _isDragging = true;
    _snapLines.clear();
    setState(() {
      _isSelected = true;
    });
    _selectionController.forward();
  }
  
  void _handlePanUpdate(DragUpdateDetails details) {
    if (!_isDragging || !_performanceOptimizer.shouldUpdate()) return;
    
    var newRect = _currentRect.translate(details.delta.dx, details.delta.dy);
    
    // تطبيق الالتصاق
    if (widget.enableSnapping) {
      final snapResult = _applySnapping(newRect);
      newRect = newRect.translate(snapResult.offset.dx, snapResult.offset.dy);
      _snapLines = snapResult.snapLines;
    }
    
    // التأكد من البقاء داخل الحدود
    newRect = _constrainToBounds(newRect);
    
    setState(() {
      _currentRect = newRect;
    });
    
    _notifyChange();
  }
  
  void _handlePanEnd(DragEndDetails details) {
    _isDragging = false;
    _snapLines.clear();
    setState(() {});
    
    widget.onTransformEnd?.call(
      _currentRect.topLeft,
      _currentRect.size,
      _currentRotation,
    );
  }
  
  void _handleHandleDrag(handles.HandlePosition position, DragUpdateDetails details) {
    if (!widget.withResizing || !_performanceOptimizer.shouldUpdate()) return;

    _isResizing = true;
    var newRect = _currentRect;

    // تطبيق تغيير الحجم حسب المقبض
    switch (position) {
      case handles.HandlePosition.topLeft:
        newRect = Rect.fromLTRB(
          newRect.left + details.delta.dx,
          newRect.top + details.delta.dy,
          newRect.right,
          newRect.bottom,
        );
        break;
      case handles.HandlePosition.topRight:
        newRect = Rect.fromLTRB(
          newRect.left,
          newRect.top + details.delta.dy,
          newRect.right + details.delta.dx,
          newRect.bottom,
        );
        break;
      case handles.HandlePosition.bottomLeft:
        newRect = Rect.fromLTRB(
          newRect.left + details.delta.dx,
          newRect.top,
          newRect.right,
          newRect.bottom + details.delta.dy,
        );
        break;
      case handles.HandlePosition.bottomRight:
        newRect = Rect.fromLTRB(
          newRect.left,
          newRect.top,
          newRect.right + details.delta.dx,
          newRect.bottom + details.delta.dy,
        );
        break;
      case handles.HandlePosition.topCenter:
        newRect = Rect.fromLTRB(
          newRect.left,
          newRect.top + details.delta.dy,
          newRect.right,
          newRect.bottom,
        );
        break;
      case handles.HandlePosition.bottomCenter:
        newRect = Rect.fromLTRB(
          newRect.left,
          newRect.top,
          newRect.right,
          newRect.bottom + details.delta.dy,
        );
        break;
      case handles.HandlePosition.centerLeft:
        newRect = Rect.fromLTRB(
          newRect.left + details.delta.dx,
          newRect.top,
          newRect.right,
          newRect.bottom,
        );
        break;
      case handles.HandlePosition.centerRight:
        newRect = Rect.fromLTRB(
          newRect.left,
          newRect.top,
          newRect.right + details.delta.dx,
          newRect.bottom,
        );
        break;
      case handles.HandlePosition.rotation:
        if (widget.withRotating) {
          _handleRotation(details);
        }
        return;
    }
    
    // التأكد من الحد الأدنى للحجم
    if (newRect.width < 20 || newRect.height < 20) return;
    
    // التأكد من البقاء داخل الحدود
    newRect = _constrainToBounds(newRect);
    
    setState(() {
      _currentRect = newRect;
    });
    
    _notifyChange();
  }

  void _handleHandleDragEnd(handles.HandlePosition position, DragEndDetails details) {
    _isResizing = false;
    _isRotating = false;

    widget.onTransformEnd?.call(
      _currentRect.topLeft,
      _currentRect.size,
      _currentRotation,
    );
  }

  void _handleRotation(DragUpdateDetails details) {
    final center = _currentRect.center;
    final currentPosition = center + details.localPosition;
    final previousPosition = currentPosition - details.delta;

    final currentAngle = math.atan2(
      currentPosition.dy - center.dy,
      currentPosition.dx - center.dx,
    );

    final previousAngle = math.atan2(
      previousPosition.dy - center.dy,
      previousPosition.dx - center.dx,
    );

    final deltaAngle = currentAngle - previousAngle;
    final newRotation = _currentRotation + (deltaAngle * 180 / math.pi);

    setState(() {
      _currentRotation = newRotation % 360;
    });

    _notifyChange();
  }

  // وظائف مساعدة
  SnapResult _applySnapping(Rect bounds) {
    final containerRect = Rect.fromLTWH(0, 0, widget.containerWidth, widget.containerHeight);
    
    Offset totalSnapOffset = Offset.zero;
    List<SnapLine> allSnapLines = [];
    Rect currentBounds = bounds;

    // 1. Snap to other elements
    final elementSnap = ProfessionalSnappingSystem.snapToElements(currentBounds, widget.otherElements);
    if (elementSnap.snapped) {
        totalSnapOffset += elementSnap.offset;
        allSnapLines.addAll(elementSnap.snapLines);
        currentBounds = currentBounds.translate(elementSnap.offset.dx, elementSnap.offset.dy);
    }

    // 2. Snap to container edges
    final containerSnap = ProfessionalSnappingSystem.snapToContainer(currentBounds, containerRect);
    if (containerSnap != Offset.zero) {
        totalSnapOffset += containerSnap;
        currentBounds = currentBounds.translate(containerSnap.dx, containerSnap.dy);
    }

    // 3. Snap to container center
    final centerSnap = ProfessionalSnappingSystem.snapToCenter(currentBounds, containerRect);
    if (centerSnap != Offset.zero) {
        totalSnapOffset += centerSnap;
        currentBounds = currentBounds.translate(centerSnap.dx, centerSnap.dy);
    }

    // 4. Snap to grid
    if (widget.enableGrid) {
      final gridSnapPoint = ProfessionalSnappingSystem.snapToGrid(currentBounds.topLeft);
      final gridSnapOffset = gridSnapPoint - currentBounds.topLeft;
      if (gridSnapOffset != Offset.zero) {
        totalSnapOffset += gridSnapOffset;
      }
    }

    return SnapResult(
        offset: totalSnapOffset,
        snapLines: allSnapLines,
        snapped: totalSnapOffset != Offset.zero,
    );
  }

  Rect _constrainToBounds(Rect bounds) {
    final containerRect = Rect.fromLTWH(0, 0, widget.containerWidth, widget.containerHeight);

    double left = math.max(0, math.min(bounds.left, containerRect.width - bounds.width));
    double top = math.max(0, math.min(bounds.top, containerRect.height - bounds.height));

    return Rect.fromLTWH(left, top, bounds.width, bounds.height);
  }

  void _notifyChange() {
    widget.onChanged?.call(
      _currentRect.topLeft,
      _currentRect.size,
      _currentRotation,
    );
  }

  // واجهة برمجية عامة
  void updateTransform({
    Offset? offset,
    Size? size,
    double? rotation,
    double? scale,
  }) {
    setState(() {
      if (offset != null) {
        _currentRect = Rect.fromLTWH(
          offset.dx,
          offset.dy,
          _currentRect.width,
          _currentRect.height,
        );
      }

      if (size != null) {
        _currentRect = Rect.fromLTWH(
          _currentRect.left,
          _currentRect.top,
          size.width,
          size.height,
        );
      }

      if (rotation != null) {
        _currentRotation = rotation;
      }

      if (scale != null) {
        _currentScale = scale;
      }
    });

    _notifyChange();
  }

  void select() {
    setState(() {
      _isSelected = true;
    });
    _selectionController.forward();
  }

  void deselect() {
    setState(() {
      _isSelected = false;
    });
    _selectionController.reverse();
  }

  bool get isSelected => _isSelected;
  bool get isDragging => _isDragging;
  bool get isResizing => _isResizing;
  bool get isRotating => _isRotating;

  Rect get currentRect => _currentRect;
  double get currentRotation => _currentRotation;
  double get currentScale => _currentScale;
}
