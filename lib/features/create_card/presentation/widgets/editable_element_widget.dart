import 'dart:io';

import 'package:flutter/material.dart';

import '../../domain/entities/card_element.dart';

/// Callback type to be called at the end of a gesture transformation
/// (e.g., after onScaleEnd) to pass the final transformation values.
typedef OnTransformEnd = void Function({
  required double newX,
  required double newY,
  required double newScale,
  required double newRotation,
});

/// A widget that displays an editable card element which can be moved, scaled, and rotated.
/// It handles user gestures and applies the appropriate transformation.
class EditableElementWidget extends StatefulWidget {
  /// The card element to display and edit.
  final CardElement element;

  /// Whether this element is currently selected.
  final bool isSelected;

  /// Callback triggered when the user finishes a transformation.
  final OnTransformEnd onTransformEnd;

  const EditableElementWidget({
    super.key,
    required this.element,
    required this.isSelected,
    required this.onTransformEnd,
  });

  @override
  State<EditableElementWidget> createState() => _EditableElementWidgetState();
}

class _EditableElementWidgetState extends State<EditableElementWidget> {
  // Temporary values for tracking transformation during gestures.
  late double _tempX;
  late double _tempY;
  late double _tempScale;
  late double _tempRotation;

  // Stores the initial focal point when a scale gesture starts.
  late Offset _focalPointStart;

  @override
  void initState() {
    super.initState();
    // Initialize the temporary transformation values with the element's properties.
    _tempX = widget.element.x;
    _tempY = widget.element.y;
    _tempScale = widget.element.scale;
    _tempRotation = widget.element.rotation;
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _tempX,
      top: _tempY,
      child: GestureDetector(
        onTap: () {
          // Optionally handle single tap events here.
        },
        onScaleStart: (details) {
          // Store the initial focal point when scaling begins.
          _focalPointStart = details.focalPoint;
        },
        onScaleUpdate: (details) {
          setState(() {
            // Update position (translation):
            final dx = details.focalPoint.dx - _focalPointStart.dx;
            final dy = details.focalPoint.dy - _focalPointStart.dy;
            _tempX += dx;
            _tempY += dy;
            _focalPointStart = details.focalPoint;

            // Update scale (zoom):
            _tempScale *= details.scale;
            // Optionally constrain scale values:
            // _tempScale = _tempScale.clamp(0.5, 3.0);

            // Update rotation:
            _tempRotation += details.rotation;
          });
        },
        onScaleEnd: (details) {
          // When the gesture ends, call the provided callback with the final transformation values.
          widget.onTransformEnd(
            newX: _tempX,
            newY: _tempY,
            newScale: _tempScale,
            newRotation: _tempRotation,
          );
        },
        child: _buildTransformedChild(),
      ),
    );
  }

  /// Builds the child widget with the current transformation applied (rotation and scale).
  Widget _buildTransformedChild() {
    return Transform(
      alignment: Alignment.center,
      transform: Matrix4.identity()
        ..rotateZ(_tempRotation)
        ..scale(_tempScale),
      child: Container(
        decoration: BoxDecoration(
          // Highlight the border if the element is selected.
          border: Border.all(
            color: widget.isSelected ? Colors.blueAccent : Colors.transparent,
            width: 2,
          ),
        ),
        child: _buildElementContent(widget.element),
      ),
    );
  }

  /// Builds the content of the element based on its type.
  /// For text, it displays styled text; for sticker/image, it loads an image.
  Widget _buildElementContent(CardElement element) {
    switch (element.type) {
      case ElementType.text:
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            element.content,
            style: TextStyle(
              fontSize: element.fontSize,
              fontWeight: element.isBold ? FontWeight.bold : FontWeight.normal,
              fontStyle: element.isItalic ? FontStyle.italic : FontStyle.normal,
              decoration: element.isUnderline ? TextDecoration.underline : null,
              color: Colors.black,
            ),
          ),
        );
      case ElementType.sticker:
        // Load sticker image from network.
        return Image.network(
          element.content,
          width: element.width,
          height: element.height,
          fit: BoxFit.contain,
        );
      case ElementType.image:
        // Load image either from network or file.
        if (element.content.startsWith('http')) {
          return Image.network(
            element.content,
            width: element.width,
            height: element.height,
            fit: BoxFit.contain,
          );
        } else {
          return Image.file(
            File(element.content),
            width: element.width,
            height: element.height,
            fit: BoxFit.contain,
          );
        }
      case ElementType.qrCode:
        // Placeholder for QR code element
        return Container(
          width: element.width,
          height: element.height,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Text('QR Code', style: TextStyle(color: Colors.grey)),
          ),
        );
      case ElementType.signature:
        // Display signature image
        if (element.content.isNotEmpty) {
          if (element.content.startsWith('http')) {
            return Image.network(
              element.content,
              width: element.width,
              height: element.height,
              fit: BoxFit.contain,
            );
          } else {
            return Image.file(
              File(element.content),
              width: element.width,
              height: element.height,
              fit: BoxFit.contain,
            );
          }
        } else {
          return Container(
            width: element.width,
            height: element.height,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text('Signature', style: TextStyle(color: Colors.grey)),
            ),
          );
        }
    }
  }
}
