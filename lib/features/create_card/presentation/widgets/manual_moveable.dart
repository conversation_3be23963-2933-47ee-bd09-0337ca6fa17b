import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../../core/utils/drag_throttler.dart';
import '../../../../core/utils/transform_throttler.dart';
import '../../../../core/utils/transform_optimizer.dart';

/// Enum representing the corners and edges used for resizing.
enum ResizeCorner {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  topCenter,
  bottomCenter,
  leftCenter,
  rightCenter,
}

/// A custom widget that provides manual manipulation (dragging, resizing, and rotating)
/// of its child widget. Transformations are applied using Transform and Matrix4.
/// The widget constrains the movement of its child within the specified container dimensions.
class ManualMoveable extends StatefulWidget {
  /// The initial position of the element.
  final Offset offset;

  /// The initial size of the element.
  final Size size;

  /// The initial rotation angle in degrees.
  final double rotationAngle;

  /// The scale factor for the element. Default is 1.0 (no scaling).
  final double scale;

  /// The child widget to be transformed.
  final Widget child;

  // Flags to enable or disable dragging, resizing, and rotating functionalities.
  final bool withDragging;
  final bool withResizing;
  final bool withRotating;

  /// Whether to show the resize handles at the corners.
  final bool showResizeHandles;

  /// The width of the container that constrains the element.
  final double containerWidth;

  /// The height of the container that constrains the element.
  final double containerHeight;

  // Size constraints for the element.
  final double minWidth;
  final double minHeight;
  final double? maxWidth;
  final double? maxHeight;

  // Callback functions that are triggered when the element is dragged, resized, or rotated.
  final ValueChanged<Offset>? onDragUpdate;
  final ValueChanged<Size>? onResizeUpdate;
  final ValueChanged<double>? onRotateUpdate;

  // Callback for resize that includes both size and position changes (for center-based resizing)
  final void Function(Size newSize, Offset newOffset)? onResizeWithPositionUpdate;

  /// Whether this element is an image that can extend beyond card boundaries.
  final bool isImage;

  const ManualMoveable({
    super.key,
    required this.offset,
    required this.size,
    required this.rotationAngle,
    required this.child,
    required this.containerWidth,
    required this.containerHeight,
    this.scale = 1.0,
    this.withDragging = true,
    this.withResizing = true,
    this.withRotating = true,
    this.showResizeHandles = true,
    this.minWidth = 20,
    this.minHeight = 20,
    this.maxWidth,
    this.maxHeight,
    this.onDragUpdate,
    this.onResizeUpdate,
    this.onRotateUpdate,
    this.onResizeWithPositionUpdate,
    this.isImage = false,
  });

  @override
  State<ManualMoveable> createState() => _ManualMoveableState();
}

class _ManualMoveableState extends State<ManualMoveable> {
  // GlobalKey to get the context of the container.
  final GlobalKey _containerKey = GlobalKey();

  // Local state variables to track position, size, rotation, and scale.
  late Offset _offset;
  late Size _size;
  late double _rotationDeg; // Rotation in degrees.
  late double _scale;

  // Essential state variables for resize operations
  Offset? _dragStartOffset;
  Size? _initialSize;

  // Performance optimization throttlers
  late DragThrottler _dragThrottler;
  late TransformThrottler _transformThrottler;

  @override
  void initState() {
    super.initState();
    // Initialize the transformation values from the widget properties.
    _offset = widget.offset;
    _size = widget.size;
    _rotationDeg = widget.rotationAngle;
    _scale = widget.scale;

    // Initialize performance throttlers
    _dragThrottler = DragThrottler(
      intervalMs: 16, // ~60 FPS for smooth dragging
      callback: (offset) {
        if (widget.onDragUpdate != null) {
          widget.onDragUpdate!(offset);
        }
      },
    );

    _transformThrottler = TransformThrottler(
      intervalMs: 16, // ~60 FPS for smooth transformations
      callback: (transform) {
        if (transform.size != null && widget.onResizeUpdate != null) {
          widget.onResizeUpdate!(transform.size!);
        }
        if (transform.rotation != null && widget.onRotateUpdate != null) {
          widget.onRotateUpdate!(transform.rotation!);
        }
      },
    );
  }

  @override
  void didUpdateWidget(covariant ManualMoveable oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update local values if the widget's properties change.
    if (widget.offset != oldWidget.offset) _offset = widget.offset;
    if (widget.size != oldWidget.size) _size = widget.size;
    if (widget.rotationAngle != oldWidget.rotationAngle) {
      _rotationDeg = widget.rotationAngle;
    }
    if (widget.scale != oldWidget.scale) {
      _scale = widget.scale;
    }
  }

  @override
  void dispose() {
    // Clean up throttlers to prevent memory leaks
    _dragThrottler.dispose();
    _transformThrottler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Direct build for immediate response - no intermediate builders
    Widget mainContent = SizedBox(
      width: _size.width,
      height: _size.height,
      child: Stack(
        key: _containerKey,
        clipBehavior: Clip.none,
        children: [
          // Main content
          Positioned.fill(
            child: widget.child,
          ),
          // Display resize handles if resizing is enabled and they should be shown
          if (widget.withResizing && widget.showResizeHandles)
            ..._buildCorners(),
        ],
      ),
    );

    // Apply transformations (scale and rotation only - position handled by parent)
    Widget transformedContent = Transform(
      transform: Matrix4.diagonal3Values(_scale, _scale, 1.0),
      alignment: Alignment.center,
      child: Transform.rotate(
        angle: _degToRad(_rotationDeg),
        alignment: Alignment.center,
        child: mainContent,
      ),
    );

    // Ultra-optimized gesture detection with multiple RepaintBoundaries
    return RepaintBoundary(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onPanStart: widget.withDragging ? _onDragStart : null,
        onPanUpdate: widget.withDragging ? _onDragUpdate : null,
        onPanEnd: widget.withDragging ? _onDragEnd : null,
        child: RepaintBoundary(
          child: transformedContent,
        ),
      ),
    );
  }

  /// Builds ultra-smooth resize handles with double precision
  List<Widget> _buildCorners() {
    // Precise handle sizing with double precision
    const double handleDiameter = 32.0; // Exact size for smooth interaction
    const double handleRadius = handleDiameter / 2.0;

    // High-precision positioning system for pixel-perfect placement
    const double margin = 4.0; // Minimal margin for clean look

    // Calculate precise fixed positions with double precision
    const double leftOffset = -16.0;   // Exact half of handle diameter
    const double rightOffset = -16.0;  // Exact half of handle diameter
    const double topOffset = -16.0;    // Exact half of handle diameter
    const double bottomOffset = -16.0; // Exact half of handle diameter

    // No dynamic positioning - buttons stay fixed at corners for consistency

    return [
      // الركن العلوي الأيسر - ثابت في الركن الفعلي
      Positioned(
        top: topOffset,
        left: leftOffset,
        child: _resizeHandle(ResizeCorner.topLeft, handleDiameter),
      ),
      // Top right corner
      Positioned(
        top: topOffset,
        right: rightOffset,
        child: _resizeHandle(ResizeCorner.topRight, handleDiameter),
      ),
      // Bottom left corner
      Positioned(
        bottom: bottomOffset,
        left: leftOffset,
        child: _resizeHandle(ResizeCorner.bottomLeft, handleDiameter),
      ),
      // Bottom right corner
      Positioned(
        bottom: bottomOffset,
        right: rightOffset,
        child: _resizeHandle(ResizeCorner.bottomRight, handleDiameter),
      ),
    ];
  }

  /// Returns a widget that acts as a resize handle for a given [corner].
  Widget _resizeHandle(ResizeCorner corner, double diameter) {
    // تحديد الأيقونة المناسبة لكل زاوية فقط
    IconData getIconForCorner() {
      switch (corner) {
        case ResizeCorner.topLeft:
          return Icons.north_west;
        case ResizeCorner.topRight:
          return Icons.north_east;
        case ResizeCorner.bottomLeft:
          return Icons.south_west;
        case ResizeCorner.bottomRight:
          return Icons.south_east;
        default:
          return Icons.open_with; // أيقونة افتراضية
      }
    }

    return RepaintBoundary(
      child: GestureDetector(
        // Ultra-responsive behavior for reliable button interaction
        behavior: HitTestBehavior.translucent,
        onPanStart: (details) => _onResizeStart(corner, details),
        onPanUpdate: (details) => _onResizeUpdate(corner, details),
        onPanEnd: _onResizeEnd,
        // Add tap detection for better responsiveness
        onTap: () {
          // Visual feedback on tap
          setState(() {
            _activeCorner = corner;
          });
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              setState(() {
                _activeCorner = null;
              });
            }
          });
        },
      // Optimized touch area for better responsiveness
      child: Container(
        width: diameter + 16, // Larger touch area for better interaction
        height: diameter + 16,
        padding: const EdgeInsets.all(8), // Adequate padding for touch
        child: Container(
          width: diameter,
          height: diameter,
          decoration: BoxDecoration(
            // Clean and modern design
            color: _isResizing && _activeCorner == corner
                ? Colors.blue.shade700
                : Colors.blue.shade600,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2), // Thinner border
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.25), // Lighter shadow
                blurRadius: 4, // Smaller blur
                offset: const Offset(0, 2), // Smaller offset
              ),
            ],
          ),
          child: Center(
            child: Icon(
              getIconForCorner(),
              size: diameter * 0.4, // Smaller icon
              color: Colors.white,
            ),
          ),
        ),
      ),
    ));
  }

  /// Lightning-fast drag start
  void _onDragStart(DragStartDetails details) {
    // Minimal processing for maximum speed
  }

  /// Ultra-smooth drag update with double precision and throttling
  void _onDragUpdate(DragUpdateDetails details) {
    // High-precision delta for ultra-smooth movement
    final double dx = details.delta.dx;
    final double dy = details.delta.dy;

    // Precise position calculation with double precision using TransformOptimizer
    double newX = _offset.dx + dx;
    double newY = _offset.dy + dy;

    // Apply safe constraints to keep element within container bounds
    final constrainedOffset = TransformOptimizer.constrainToContainer(
      Offset(newX, newY),
      _size,
      Size(widget.containerWidth, widget.containerHeight),
      margin: 10.0,
    );

    // Ultra-smooth update with double precision
    _offset = constrainedOffset;

    // Immediate setState for smooth visual feedback
    setState(() {});

    // Use throttled callback for performance optimization
    _dragThrottler.call(_offset);
  }

  /// Lightning-fast drag end
  void _onDragEnd(DragEndDetails details) {
    // Minimal processing for maximum speed
  }

  /// Ultra-reliable resize start - prevents button freezing
  void _onResizeStart(ResizeCorner corner, DragStartDetails details) {
    _initialSize = _size;
    _dragStartOffset = details.globalPosition;
    _isResizing = true;
    _activeCorner = corner;

    // Force immediate visual update
    setState(() {});
  }

  // Performance variables
  bool _isResizing = false;
  ResizeCorner? _activeCorner;



  /// Ultra-reliable resize update with comprehensive checks
  void _onResizeUpdate(ResizeCorner corner, DragUpdateDetails details) {
    if (_initialSize == null || !_isResizing) return;

    // Convert rotation to radians for trigonometric functions with validation
    final angle = TransformOptimizer.optimizeRotation(_degToRad(_rotationDeg));
    final cosAngle = math.cos(angle);
    final sinAngle = math.sin(angle);

    // Validate trigonometric values
    if (!TransformOptimizer.isValidNumber(cosAngle) || !TransformOptimizer.isValidNumber(sinAngle)) {
      return; // Skip invalid calculations
    }

    // Transform the drag delta from screen coordinates to the widget's local rotated coordinates.
    // This makes resizing intuitive even when the widget is rotated.
    final localDelta = Offset(
      details.delta.dx * cosAngle + details.delta.dy * sinAngle,
      -details.delta.dx * sinAngle + details.delta.dy * cosAngle,
    );

    double newWidth = _size.width;
    double newHeight = _size.height;
    Offset newOffset = _offset;

    // Adjust size and position based on which corner is being dragged.
    // The logic ensures the opposite corner remains stationary.
    switch (corner) {
      case ResizeCorner.bottomRight:
        newWidth += localDelta.dx;
        newHeight += localDelta.dy;
        break;
      case ResizeCorner.bottomLeft:
        newWidth -= localDelta.dx;
        newHeight += localDelta.dy;
        // To keep the top-right corner fixed, we adjust the offset.
        newOffset += Offset(localDelta.dx * cosAngle, localDelta.dx * sinAngle);
        break;
      case ResizeCorner.topRight:
        newWidth += localDelta.dx;
        newHeight -= localDelta.dy;
        // To keep the bottom-left corner fixed, we adjust the offset.
        newOffset += Offset(localDelta.dy * sinAngle, -localDelta.dy * cosAngle);
        break;
      case ResizeCorner.topLeft:
        newWidth -= localDelta.dx;
        newHeight -= localDelta.dy;
        // To keep the bottom-right corner fixed, we adjust the offset.
        newOffset += Offset(
          localDelta.dx * cosAngle + localDelta.dy * sinAngle,
          localDelta.dx * sinAngle - localDelta.dy * cosAngle,
        );
        break;
      default:
        break;
    }

    // Enforce size constraints using TransformOptimizer
    final containerSize = Size(widget.containerWidth, widget.containerHeight);
    final proposedSize = Size(newWidth, newHeight);
    final constrainedSize = TransformOptimizer.constrainSizeToContainer(
      proposedSize,
      containerSize,
      minWidth: widget.minWidth,
      minHeight: widget.minHeight,
    );

    final constrainedWidth = constrainedSize.width;
    final constrainedHeight = constrainedSize.height;

    // If the size was constrained, adjust the offset to keep the correct corner fixed.
    if (newWidth != constrainedWidth) {
      if (corner == ResizeCorner.topLeft || corner == ResizeCorner.bottomLeft) {
        newOffset += Offset((newWidth - constrainedWidth) * cosAngle, (newWidth - constrainedWidth) * sinAngle);
      }
    }
    if (newHeight != constrainedHeight) {
       if (corner == ResizeCorner.topLeft || corner == ResizeCorner.topRight) {
        newOffset += Offset((newHeight - constrainedHeight) * sinAngle, -(newHeight - constrainedHeight) * cosAngle);
      }
    }

    final newSize = Size(constrainedWidth, constrainedHeight);

    // Final validation using TransformOptimizer
    if (!TransformOptimizer.isValidSize(newSize) || !TransformOptimizer.isValidOffset(newOffset)) {
      return;
    }

    // Update the state and notify the parent widget.
    setState(() {
      _size = newSize;
      _offset = newOffset;
    });

    // Use throttled callback for performance optimization
    _transformThrottler.callSize(newSize);
    widget.onResizeWithPositionUpdate?.call(newSize, newOffset);
  }

  /// Ultra-reliable resize end - ensures proper cleanup
  void _onResizeEnd(DragEndDetails details) {
    _initialSize = null;
    _dragStartOffset = null;
    _isResizing = false;
    _activeCorner = null;

    // Force final visual update
    setState(() {});
  }

  /// Converts degrees to radians.
  double _degToRad(double deg) => deg * math.pi / 180.0;
}
