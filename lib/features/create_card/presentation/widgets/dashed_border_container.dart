import 'package:flutter/material.dart';

/// A container widget that draws a dashed border around its child widget.
class DashedBorderContainer extends StatelessWidget {
  /// The child widget to display inside the container.
  final Widget child;

  /// The color of the dashed border.
  final Color color;

  /// The width of the dashed border line.
  final double strokeWidth;

  /// The width of each dash.
  final double dashWidth;

  /// The space between dashes.
  final double dashSpace;

  const DashedBorderContainer({
    super.key,
    required this.child,
    this.color = const Color(0xFFBCAAA4), // Light brown color.
    this.strokeWidth = 1,
    this.dashWidth = 4,
    this.dashSpace = 4,
  });

  @override
  Widget build(BuildContext context) {
    // Use CustomPaint to draw the dashed border around the child.
    return CustomPaint(
      painter: _DashedBorderPainter(
        color: color,
        strokeWidth: strokeWidth,
        dashWidth: dashWidth,
        dashSpace: dashSpace,
      ),
      child: child,
    );
  }
}

/// Custom painter that draws dashed lines along the four sides of a rectangle.
class _DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  _DashedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashWidth,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Create a paint object with the desired color, stroke width, and style.
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    // Draw the dashed border on all four sides.
    _drawDashedLine(canvas, Offset(0, 0), Offset(size.width, 0), paint); // Top
    _drawDashedLine(canvas, Offset(size.width, 0),
        Offset(size.width, size.height), paint); // Right
    _drawDashedLine(canvas, Offset(size.width, size.height),
        Offset(0, size.height), paint); // Bottom
    _drawDashedLine(
        canvas, Offset(0, size.height), Offset(0, 0), paint); // Left
  }

  /// Draws a dashed line between [start] and [end] using the specified [paint].
  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    final totalDistance = (end - start).distance;
    final dashCount = (totalDistance / (dashWidth + dashSpace)).floor();
    final dashVector = (end - start) / totalDistance;

    // Loop through and draw each dash segment.
    for (int i = 0; i < dashCount; i++) {
      final currentStart = start + dashVector * (i * (dashWidth + dashSpace));
      final currentEnd = currentStart + dashVector * dashWidth;
      canvas.drawLine(currentStart, currentEnd, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _DashedBorderPainter oldDelegate) {
    // Repaint if any of the properties have changed.
    return oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.dashWidth != dashWidth ||
        oldDelegate.dashSpace != dashSpace;
  }
}
