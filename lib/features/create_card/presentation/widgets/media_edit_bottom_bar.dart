import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/entities/card_element.dart';
import '../bloc/card_editor_bloc.dart';
import '../bloc/card_editor_event.dart';

/// A bottom bar for media elements (image/sticker) with options to delete, flip, and duplicate.
class MediaEditBottomBar extends StatelessWidget {
  final CardElement element;

  const MediaEditBottomBar({super.key, required this.element});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[200],
      height: 60,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'Delete Element',
            onPressed: () {
              context
                  .read<CardEditorBloc>()
                  .add(RemoveElementEvent(element.id));
            },
          ),
          IconButton(
            icon: const Icon(Icons.flip),
            tooltip: 'Flip Element',
            onPressed: () {
              double currentScale = element.scale;
              double newScale =
                  currentScale < 0 ? currentScale.abs() : -currentScale.abs();
              context.read<CardEditorBloc>().add(
                    UpdateElementTransformEvent(
                      elementId: element.id,
                      newScale: newScale,
                    ),
                  );
            },
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            tooltip: 'Duplicate Element',
            onPressed: () {
              context
                  .read<CardEditorBloc>()
                  .add(DuplicateElementEvent(element.id));
            },
          ),
        ],
      ),
    );
  }
}
