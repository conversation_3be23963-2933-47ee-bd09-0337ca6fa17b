import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/card_element.dart';
import '../../domain/repositories/card_repository.dart';
import '../../domain/usecases/add_image_element_usecase.dart';
import '../../domain/usecases/add_qr_code_element_usecase.dart';
import '../../domain/usecases/add_signature_element_usecase.dart';
import '../../domain/usecases/add_sticker_element_usecase.dart';
import '../../domain/usecases/add_text_element_usecase.dart';
import '../../domain/usecases/remove_element_usecase.dart';
import '../../domain/usecases/update_element_position_usecase.dart';
import '../../domain/usecases/update_element_transform_usecase.dart';
import '../../domain/usecases/update_text_properties_usecase.dart';
import 'card_editor_event.dart';
import 'card_editor_state.dart';

/// Bloc for managing the state of a card editor.
///
/// This bloc handles events for loading, adding, updating, removing, duplicating elements,
/// as well as undo/redo functionality.
class CardEditorBloc extends Bloc<CardEditorEvent, CardEditorState> {
  /// Repository for managing card elements.
  final CardRepository cardRepository;

  // Use Cases for different element operations.
  final AddTextElementUseCase addTextElementUseCase;
  final AddStickerElementUseCase addStickerElementUseCase;
  final AddImageElementUseCase addImageElementUseCase;
  final AddQrCodeElementUseCase? addQrCodeElementUseCase;
  final AddSignatureElementUseCase? addSignatureElementUseCase;
  final RemoveElementUseCase removeElementUseCase;
  final UpdateElementPositionUseCase updateElementPositionUseCase;
  final UpdateTextPropertiesUseCase updateTextPropertiesUseCase;
  final UpdateElementTransformUseCase? updateElementTransformUseCase;

  // آلية التراجع/الإعادة المحسنة: تاريخ لقطات العناصر والعناصر المحددة
  final List<Map<String, dynamic>> _history =
      []; // تخزين لقطات العناصر والعناصر المحددة والوصف
  int _historyIndex = -1; // مؤشر الحالة الحالية في التاريخ
  bool _isUndoRedoInProgress =
      false; // علامة لتجنب تداخل عمليات التراجع/الإعادة

  // الحد الأقصى لعدد الخطوات التي يمكن التراجع عنها
  static const int _maxHistorySize = 50;

  // مفاتيح البيانات في تاريخ التغييرات
  static const String _historyKeyElements = 'elements';
  static const String _historyKeySelectedId = 'selectedId';
  static const String _historyKeyDescription = 'description';

  /// UUID generator for creating unique element IDs.
  final Uuid uuid = const Uuid();

  /// Creates an instance of [CardEditorBloc] with the required repositories and use cases.
  CardEditorBloc({
    required this.cardRepository,
    required this.addTextElementUseCase,
    required this.addStickerElementUseCase,
    required this.addImageElementUseCase,
    required this.removeElementUseCase,
    required this.updateElementPositionUseCase,
    required this.updateTextPropertiesUseCase,
    this.updateElementTransformUseCase,
    this.addQrCodeElementUseCase,
    this.addSignatureElementUseCase,
  }) : super(CardEditorInitial()) {
    // Register event handlers.
    on<LoadElementsEvent>(_onLoadElements);
    on<AddTextEvent>(_onAddTextEvent);
    on<AddStickerEvent>(_onAddStickerEvent);
    on<AddImageEvent>(_onAddImageEvent);
    on<AddQrCodeEvent>(_onAddQrCodeEvent);
    on<AddSignatureEvent>(_onAddSignatureEvent);
    on<RemoveElementEvent>(_onRemoveElementEvent);
    on<UpdateElementPositionEvent>(_onUpdateElementPositionEvent);
    on<UpdateElementTransformEvent>(_onUpdateElementTransformEvent);
    on<UpdateTextPropsEvent>(_onUpdateTextPropsEvent);
    on<SelectElementEvent>(_onSelectElementEvent);
    on<UndoEvent>(_onUndoEvent);
    on<RedoEvent>(_onRedoEvent);
    on<DuplicateTextElementEvent>(_onDuplicateTextElementEvent);
    on<DuplicateElementEvent>(_onDuplicateElementEvent);
    on<UpdateElementSizeEvent>(_onUpdateElementSizeEvent);
    on<UpdateElementPropertiesEvent>(_onUpdateElementPropertiesEvent);
    on<ClearElementsEvent>(_onClearElementsEvent);
  }

  // === Event Handlers ===

  /// معالجة حدث [LoadElementsEvent] عن طريق تحميل جميع العناصر من المستودع.
  Future<void> _onLoadElements(
    LoadElementsEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    debugPrint('_onLoadElements: بدء تحميل العناصر من المستودع');
    emit(CardEditorLoading());
    try {
      final elems = await cardRepository.getAllElements();
      debugPrint(
          '_onLoadElements: تم استرجاع ${elems.length} عنصر من المستودع');

      // طباعة تفاصيل العناصر للتصحيح
      for (var i = 0; i < elems.length; i++) {
        final element = elems[i];
        debugPrint('_onLoadElements: العنصر $i: النوع=${element.type}, '
            'المعرف=${element.id}, '
            'الموقع=(${element.x}, ${element.y}), '
            'الأبعاد=(${element.width}x${element.height}), '
            'الدوران=${element.rotation}');
      }

      // إعادة تعيين التاريخ قبل حفظ الحالة الأولية
      _history.clear();
      _historyIndex = -1;

      // حفظ الحالة الأولية في التاريخ
      _saveToHistory(elems, null, 'الحالة الأولية');

      // تأكد من أن هناك عناصر قبل إرسال حالة CardEditorLoaded
      if (elems.isEmpty) {
        debugPrint('_onLoadElements: لا توجد عناصر في المستودع');
      } else {
        debugPrint('_onLoadElements: تم العثور على ${elems.length} عنصر في المستودع');
      }

      debugPrint(
          '_onLoadElements: إرسال حالة CardEditorLoaded مع ${elems.length} عنصر');
      emit(CardEditorLoaded(elems));
    } catch (e) {
      debugPrint('_onLoadElements: خطأ أثناء تحميل العناصر: $e');
      emit(CardEditorError(e.toString()));
    }
  }

  /// معالجة حدث [AddTextEvent] عن طريق إنشاء وإضافة عنصر نص جديد.
  Future<void> _onAddTextEvent(
    AddTextEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        await addTextElementUseCase(event.text);
        final updated = await cardRepository.getAllElements();
        final newElementId = updated.isNotEmpty ? updated.last.id : null;
        _saveToHistory(updated, newElementId, 'إضافة نص جديد');
        emit(CardEditorLoaded(
          updated,
          selectedElementId: newElementId,
          message: 'تم إضافة نص جديد',
        ));
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  /// معالجة حدث [AddStickerEvent] عن طريق إنشاء وإضافة عنصر ملصق جديد.
  Future<void> _onAddStickerEvent(
    AddStickerEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        await addStickerElementUseCase(event.stickerPath);
        final updated = await cardRepository.getAllElements();
        final newElementId = updated.isNotEmpty ? updated.last.id : null;
        _saveToHistory(updated, newElementId, 'إضافة ملصق جديد');
        emit(CardEditorLoaded(
          updated,
          selectedElementId: newElementId,
          message: 'تم إضافة ملصق جديد',
        ));
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  /// معالجة حدث [AddImageEvent] عن طريق إنشاء وإضافة عنصر صورة جديد.
  Future<void> _onAddImageEvent(
    AddImageEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        await addImageElementUseCase(event.imagePath);
        final updated = await cardRepository.getAllElements();
        final newElementId = updated.isNotEmpty ? updated.last.id : null;
        _saveToHistory(updated, newElementId, 'إضافة صورة جديدة');
        emit(CardEditorLoaded(
          updated,
          selectedElementId: newElementId,
          message: 'تم إضافة صورة جديدة',
        ));
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  /// معالجة حدث [RemoveElementEvent] عن طريق إزالة عنصر من المستودع.
  Future<void> _onRemoveElementEvent(
    RemoveElementEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        // الحصول على معلومات العنصر قبل حذفه
        final elements = await cardRepository.getAllElements();
        final elementToRemoveIndex =
            elements.indexWhere((e) => e.id == event.elementId);

        // تحديد نوع العنصر ووصفه
        String elementTypeDescription = 'عنصر';

        if (elementToRemoveIndex != -1) {
          final elementType = elements[elementToRemoveIndex].type;

          if (elementType == ElementType.text) {
            elementTypeDescription = 'نص';
          } else if (elementType == ElementType.image) {
            elementTypeDescription = 'صورة';
          } else if (elementType == ElementType.sticker) {
            elementTypeDescription = 'ملصق';
          } else if (elementType == ElementType.qrCode) {
            elementTypeDescription = 'رمز QR';
          } else if (elementType == ElementType.signature) {
            elementTypeDescription = 'توقيع';
          }
        }

        await removeElementUseCase(event.elementId);
        final updated = await cardRepository.getAllElements();
        _saveToHistory(updated, null, 'حذف $elementTypeDescription');
        emit(CardEditorLoaded(
          updated,
          message: 'تم حذف $elementTypeDescription',
        ));
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  /// معالجة حدث [UpdateElementPositionEvent] عن طريق تحديث موضع العنصر.
  Future<void> _onUpdateElementPositionEvent(
    UpdateElementPositionEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        // الحصول على معلومات العنصر قبل التحديث
        final elements = await cardRepository.getAllElements();
        final elementToUpdateIndex =
            elements.indexWhere((e) => e.id == event.elementId);

        // تحديد نوع العنصر ووصفه
        String elementTypeDescription = 'عنصر';

        if (elementToUpdateIndex != -1) {
          final elementType = elements[elementToUpdateIndex].type;

          if (elementType == ElementType.text) {
            elementTypeDescription = 'نص';
          } else if (elementType == ElementType.image) {
            elementTypeDescription = 'صورة';
          } else if (elementType == ElementType.sticker) {
            elementTypeDescription = 'ملصق';
          } else if (elementType == ElementType.qrCode) {
            elementTypeDescription = 'رمز QR';
          } else if (elementType == ElementType.signature) {
            elementTypeDescription = 'توقيع';
          }
        }

        await updateElementPositionUseCase(
          event.elementId,
          event.newX,
          event.newY,
        );
        final updated = await cardRepository.getAllElements();
        final selId = (state as CardEditorLoaded).selectedElementId;
        _saveToHistory(updated, selId, 'تحريك $elementTypeDescription');
        emit(CardEditorLoaded(updated, selectedElementId: selId));
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  /// Handles the [UpdateElementTransformEvent] by updating an element's transform properties.
  Future<void> _onUpdateElementTransformEvent(
    UpdateElementTransformEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      if (updateElementTransformUseCase != null) {
        await updateElementTransformUseCase!(
          elementId: event.elementId,
          newX: event.newX,
          newY: event.newY,
          newScale: event.newScale,
          newRotation: event.newRotation,
        );
      } else {
        // Fallback: manually update transformation if no use case is provided.
        final loadedState = state as CardEditorLoaded;
        final elements = loadedState.elements;
        final index = elements.indexWhere((e) => e.id == event.elementId);
        if (index == -1) return;
        final old = elements[index];
        final updatedEl = old.copyWith(
          x: event.newX ?? old.x,
          y: event.newY ?? old.y,
          scale: event.newScale ?? old.scale,
          rotation: event.newRotation ?? old.rotation,
        );
        await cardRepository.updateElement(updatedEl);
      }
      final updated = await cardRepository.getAllElements();
      _saveToHistory(updated, event.elementId);
      emit(CardEditorLoaded(updated, selectedElementId: event.elementId));
    }
  }

  /// Handles the [UpdateTextPropsEvent] by updating text properties of a text element.
  Future<void> _onUpdateTextPropsEvent(
    UpdateTextPropsEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        await updateTextPropertiesUseCase(
          elementId: event.elementId,
          newContent: event.newContent,
          newFontSize: event.newFontSize,
          isBold: event.isBold,
          isItalic: event.isItalic,
          isUnderline: event.isUnderline,
          fontFamily: event.fontFamily,
          colorValue: event.colorValue,
          letterSpacing: event.letterSpacing,
          lineHeight: event.lineHeight,
          textAlign: event.textAlign,
        );
        final updated = await cardRepository.getAllElements();
        _saveToHistory(updated, event.elementId);
        emit(CardEditorLoaded(updated, selectedElementId: event.elementId));
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  /// Handles the [SelectElementEvent] by updating the selected element.
  void _onSelectElementEvent(
    SelectElementEvent event,
    Emitter<CardEditorState> emit,
  ) {
    if (state is CardEditorLoaded) {
      final loadedState = state as CardEditorLoaded;
      emit(CardEditorLoaded(
        loadedState.elements,
        selectedElementId: event.elementId,
      ));
    }
  }

  /// معالجة حدث [UndoEvent] عن طريق الرجوع إلى الحالة السابقة.
  Future<void> _onUndoEvent(
    UndoEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is! CardEditorLoaded) return;

    // تعيين علامة أن عملية التراجع قيد التنفيذ
    _isUndoRedoInProgress = true;

    try {
      // طباعة معلومات التصحيح قبل التراجع
      debugPrint(
          'التراجع: المؤشر الحالي: $_historyIndex، عدد الخطوات: ${_history.length}');

      if (_historyIndex > 0) {
        // إظهار حالة التحميل للتغذية المرئية
        emit(CardEditorLoading());

        // تحديث المؤشر للإشارة إلى الحالة السابقة
        _historyIndex--;

        // الحصول على لقطة الحالة السابقة
        final historyEntry = _history[_historyIndex];
        final previousSnapshot =
            historyEntry[_historyKeyElements] as List<CardElement>;
        final selectedId = historyEntry[_historyKeySelectedId] as String?;
        final description = historyEntry[_historyKeyDescription] as String?;

        debugPrint(
            'التراجع: الانتقال إلى المؤشر: $_historyIndex، عدد العناصر: ${previousSnapshot.length}');
        if (description != null) {
          debugPrint('التراجع: وصف التغيير: $description');
        }

        // استبدال العناصر في المستودع بالعناصر السابقة
        await _replaceRepositoryElements(previousSnapshot);

        // إصدار حالة جديدة مع العناصر السابقة
        emit(CardEditorLoaded(
          previousSnapshot,
          selectedElementId: selectedId,
          message: description != null
              ? 'تم التراجع بنجاح ($description)'
              : 'تم التراجع بنجاح',
        ));

        // طباعة معلومات التصحيح بعد التراجع
        debugPrint(
            'التراجع: تم الانتقال إلى المؤشر: $_historyIndex، العنصر المحدد: $selectedId');
      } else {
        // إذا لم يكن هناك تاريخ للتراجع، نعرض نفس الحالة مع رسالة
        final currentState = state as CardEditorLoaded;
        emit(CardEditorLoaded(
          currentState.elements,
          selectedElementId: currentState.selectedElementId,
          message: 'لا توجد خطوات أخرى للتراجع عنها',
        ));

        debugPrint('التراجع: لا يمكن التراجع أكثر، المؤشر: $_historyIndex');
      }
    } finally {
      // إعادة تعيين علامة أن عملية التراجع انتهت
      _isUndoRedoInProgress = false;
    }
  }

  /// معالجة حدث [RedoEvent] عن طريق الانتقال إلى الحالة التالية.
  Future<void> _onRedoEvent(
    RedoEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is! CardEditorLoaded) return;

    // تعيين علامة أن عملية الإعادة قيد التنفيذ
    _isUndoRedoInProgress = true;

    try {
      // طباعة معلومات التصحيح قبل الإعادة
      debugPrint(
          'الإعادة: المؤشر الحالي: $_historyIndex، عدد الخطوات: ${_history.length}');

      if (_historyIndex < _history.length - 1) {
        // إظهار حالة التحميل للتغذية المرئية
        emit(CardEditorLoading());

        // تحديث المؤشر للإشارة إلى الحالة التالية
        _historyIndex++;

        // الحصول على لقطة الحالة التالية
        final historyEntry = _history[_historyIndex];
        final nextSnapshot =
            historyEntry[_historyKeyElements] as List<CardElement>;
        final selectedId = historyEntry[_historyKeySelectedId] as String?;
        final description = historyEntry[_historyKeyDescription] as String?;

        debugPrint(
            'الإعادة: الانتقال إلى المؤشر: $_historyIndex، عدد العناصر: ${nextSnapshot.length}');
        if (description != null) {
          debugPrint('الإعادة: وصف التغيير: $description');
        }

        // استبدال العناصر في المستودع بالعناصر التالية
        await _replaceRepositoryElements(nextSnapshot);

        // إصدار حالة جديدة مع العناصر التالية
        emit(CardEditorLoaded(
          nextSnapshot,
          selectedElementId: selectedId,
          message: description != null
              ? 'تم الإعادة بنجاح ($description)'
              : 'تم الإعادة بنجاح',
        ));

        // طباعة معلومات التصحيح بعد الإعادة
        debugPrint(
            'الإعادة: تم الانتقال إلى المؤشر: $_historyIndex، العنصر المحدد: $selectedId');
      } else {
        // إذا لم يكن هناك تاريخ للإعادة، نعرض نفس الحالة مع رسالة
        final currentState = state as CardEditorLoaded;
        emit(CardEditorLoaded(
          currentState.elements,
          selectedElementId: currentState.selectedElementId,
          message: 'لا توجد خطوات أخرى للإعادة',
        ));

        debugPrint('الإعادة: لا يمكن الإعادة أكثر، المؤشر: $_historyIndex');
      }
    } finally {
      // إعادة تعيين علامة أن عملية الإعادة انتهت
      _isUndoRedoInProgress = false;
    }
  }

  /// Handles the [DuplicateTextElementEvent] by duplicating a text element.
  Future<void> _onDuplicateTextElementEvent(
    DuplicateTextElementEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      final elems = await cardRepository.getAllElements();
      final index = elems.indexWhere((e) => e.id == event.elementId);
      if (index == -1) return;
      final original = elems[index];
      // Ensure the element is a text element before duplicating.
      if (original.type != ElementType.text) return;
      // حساب الإزاحة النسبية للعنصر النصي المكرر
      // إزاحة 0.05 تعادل حوالي 5% من حجم البطاقة
      const double relativeOffset = 0.05;

      final duplicated = original.copyWith(
        id: uuid.v4(),
        x: (original.x + relativeOffset).clamp(0.0, 0.9), // التأكد من عدم تجاوز حدود البطاقة
        y: (original.y + relativeOffset).clamp(0.0, 0.9), // التأكد من عدم تجاوز حدود البطاقة
      );
      await cardRepository.addElement(duplicated);
      final updated = await cardRepository.getAllElements();
      _saveToHistory(updated, duplicated.id);
      emit(CardEditorLoaded(updated, selectedElementId: duplicated.id));
    }
  }

  /// Handles the [DuplicateElementEvent] by duplicating any type of element.
  Future<void> _onDuplicateElementEvent(
    DuplicateElementEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      final elems = await cardRepository.getAllElements();
      final index = elems.indexWhere((e) => e.id == event.elementId);
      if (index == -1) return;
      final original = elems[index];
      // حساب الإزاحة النسبية للعنصر المكرر
      // إزاحة 0.05 تعادل حوالي 5% من حجم البطاقة
      const double relativeOffset = 0.05;

      final duplicated = original.copyWith(
        id: uuid.v4(),
        x: (original.x + relativeOffset).clamp(0.0, 0.9), // التأكد من عدم تجاوز حدود البطاقة
        y: (original.y + relativeOffset).clamp(0.0, 0.9), // التأكد من عدم تجاوز حدود البطاقة
      );
      await cardRepository.addElement(duplicated);
      final updated = await cardRepository.getAllElements();
      _saveToHistory(updated, duplicated.id);
      emit(CardEditorLoaded(updated, selectedElementId: duplicated.id));
    }
  }

  /// Handles the [AddQrCodeEvent] by creating and adding a new QR code element.
  Future<void> _onAddQrCodeEvent(
    AddQrCodeEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        if (addQrCodeElementUseCase != null) {
          await addQrCodeElementUseCase!(
            event.url,
            title: event.title,
            position: event.position,
          );
          final updated = await cardRepository.getAllElements();
          final newElementId = updated.isNotEmpty ? updated.last.id : null;
          _saveToHistory(updated, newElementId);
          emit(CardEditorLoaded(updated, selectedElementId: newElementId));
        } else {
          emit(CardEditorError('QR Code functionality not available'));
        }
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  /// Handles the [AddSignatureEvent] by creating and adding a new signature element.
  Future<void> _onAddSignatureEvent(
    AddSignatureEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        if (addSignatureElementUseCase != null) {
          await addSignatureElementUseCase!(
            event.signatureData,
            signatureText: event.signatureText,
            isDrawnSignature: event.isDrawnSignature,
            position: event.position,
            fontFamily: event.fontFamily,
          );
          final updated = await cardRepository.getAllElements();
          final newElementId = updated.isNotEmpty ? updated.last.id : null;
          _saveToHistory(updated, newElementId);
          emit(CardEditorLoaded(updated, selectedElementId: newElementId));
        } else {
          emit(CardEditorError('Signature functionality not available'));
        }
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  /// Handles the [UpdateElementSizeEvent] by updating an element's size.
  Future<void> _onUpdateElementSizeEvent(
    UpdateElementSizeEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      final elements = await cardRepository.getAllElements();
      final index = elements.indexWhere((e) => e.id == event.elementId);
      if (index == -1) return;

      final oldElement = elements[index];
      final updatedElement = oldElement.copyWith(
        width: event.newWidth,
        height: event.newHeight,
      );
      await cardRepository.updateElement(updatedElement);

      final updatedList = await cardRepository.getAllElements();
      final selId = (state as CardEditorLoaded).selectedElementId;

      _saveToHistory(updatedList, selId);
      emit(CardEditorLoaded(updatedList, selectedElementId: selId));
    }
  }

  /// Handles the [UpdateElementPropertiesEvent] by updating various properties of an element.
  Future<void> _onUpdateElementPropertiesEvent(
    UpdateElementPropertiesEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    if (state is CardEditorLoaded) {
      try {
        // Get the current elements
        final elements = await cardRepository.getAllElements();
        final index = elements.indexWhere((e) => e.id == event.elementId);
        if (index == -1) return;

        // Get the element to update
        final oldElement = elements[index];

        // طباعة معلومات التصحيح
        debugPrint('تحديث العنصر ${oldElement.id} من النوع ${oldElement.type}');
        debugPrint('اللون الأصلي: ${oldElement.colorValue}');
        debugPrint('اللون الجديد: ${event.colorValue}');

        // Create updated element with new properties
        final updatedElement = oldElement.copyWith(
          content: event.content,
          x: event.x,
          y: event.y,
          width: event.width,
          height: event.height,
          fontSize: event.fontSize,
          isBold: event.isBold,
          isItalic: event.isItalic,
          isUnderline: event.isUnderline,
          fontFamily: event.fontFamily,
          colorValue: event.colorValue,
          letterSpacing: event.letterSpacing,
          lineHeight: event.lineHeight,
          textAlign: event.textAlign,
          scale: event.scale,
          rotation: event.rotation,
        );

        // طباعة معلومات التصحيح بعد إنشاء العنصر المحدث
        debugPrint(
            'العنصر المحدث: ${updatedElement.id}، النوع: ${updatedElement.type}، اللون: ${updatedElement.colorValue}');

        // Update the element in the repository
        await cardRepository.updateElement(updatedElement);

        // Debug print after updating the repository
        final checkElements = await cardRepository.getAllElements();
        final checkIndex =
            checkElements.indexWhere((e) => e.id == event.elementId);
        if (checkIndex != -1) {
          debugPrint(
              'العنصر بعد التحديث: ${checkElements[checkIndex].id}، اللون: ${checkElements[checkIndex].colorValue}');
        }

        // Get the updated list of elements
        final updatedList = await cardRepository.getAllElements();
        final selId = (state as CardEditorLoaded).selectedElementId;

        // Save to history and emit new state
        _saveToHistory(updatedList, selId);
        emit(CardEditorLoaded(updatedList,
            selectedElementId: selId, message: 'Color updated'));
      } catch (e) {
        emit(CardEditorError(e.toString()));
      }
    }
  }

  // === Helper Methods ===

  /// حفظ الحالة الحالية للعناصر في تاريخ التغييرات لوظائف التراجع/الإعادة.
  ///
  /// @param elements قائمة العناصر الحالية
  /// @param selectedId معرف العنصر المحدد حاليًا
  /// @param description وصف للتغيير (اختياري)
  void _saveToHistory(List<CardElement> elements, String? selectedId,
      [String? description]) {
    // إذا كانت عملية التراجع/الإعادة قيد التنفيذ، لا نقوم بحفظ الحالة
    if (_isUndoRedoInProgress) {
      debugPrint(
          'حفظ الحالة: تم تجاهل الحفظ لأن عملية التراجع/الإعادة قيد التنفيذ');
      return;
    }

    // طباعة معلومات التصحيح قبل الحفظ
    debugPrint(
        'حفظ الحالة: المؤشر الحالي: $_historyIndex، عدد الخطوات: ${_history.length}');

    // تحقق مما إذا كانت الحالة الجديدة مختلفة عن الحالة الحالية
    bool isDifferentFromCurrent = true;
    if (_historyIndex >= 0 && _historyIndex < _history.length) {
      final currentSnapshot =
          _history[_historyIndex][_historyKeyElements] as List<CardElement>;

      // تحقق من تطابق عدد العناصر
      if (currentSnapshot.length == elements.length) {
        isDifferentFromCurrent = false;

        // تحقق من تطابق محتوى العناصر
        for (int i = 0; i < elements.length; i++) {
          final currentElement = currentSnapshot[i];
          final newElement = elements[i];

          if (currentElement.id != newElement.id ||
              currentElement.x != newElement.x ||
              currentElement.y != newElement.y ||
              currentElement.width != newElement.width ||
              currentElement.height != newElement.height ||
              currentElement.rotation != newElement.rotation ||
              currentElement.scale != newElement.scale ||
              currentElement.content != newElement.content ||
              currentElement.fontFamily != newElement.fontFamily ||
              currentElement.fontSize != newElement.fontSize ||
              currentElement.colorValue != newElement.colorValue ||
              currentElement.textAlign != newElement.textAlign ||
              currentElement.isBold != newElement.isBold ||
              currentElement.isItalic != newElement.isItalic ||
              currentElement.isUnderline != newElement.isUnderline) {
            isDifferentFromCurrent = true;
            break;
          }
        }
      }
    }

    // إذا كانت الحالة الجديدة مطابقة للحالة الحالية، لا نقوم بحفظها
    if (!isDifferentFromCurrent) {
      debugPrint(
          'حفظ الحالة: الحالة الجديدة مطابقة للحالة الحالية، تم تجاهل الحفظ');
      return;
    }

    // إذا كان هناك حالات مستقبلية بعد المؤشر الحالي، نقوم بإزالتها
    if (_historyIndex < _history.length - 1) {
      debugPrint(
          'حفظ الحالة: إزالة ${_history.length - _historyIndex - 1} حالات مستقبلية');
      _history.removeRange(_historyIndex + 1, _history.length);
    }

    // إنشاء نسخة من العناصر الحالية
    final snapshot = elements.map((e) => e.copyWith()).toList();

    // إنشاء حالة جديدة وإضافتها إلى التاريخ
    final historyEntry = {
      _historyKeyElements: snapshot,
      _historyKeySelectedId: selectedId,
      _historyKeyDescription: description ?? 'تغيير غير محدد',
    };

    _history.add(historyEntry);
    _historyIndex = _history.length - 1;

    // إذا تجاوز عدد الخطوات الحد الأقصى، نحذف أقدم خطوة
    if (_history.length > _maxHistorySize) {
      _history.removeAt(0);
      _historyIndex--;
    }

    // طباعة معلومات التصحيح بعد الحفظ
    debugPrint(
        'حفظ الحالة: تم الحفظ بنجاح. عدد الخطوات: ${_history.length}، المؤشر الجديد: $_historyIndex');
    if (description != null) {
      debugPrint('حفظ الحالة: وصف التغيير: $description');
    }
  }

  /// Replaces the repository's elements with a new list of elements.
  /// تحسين: استخدام مقارنة ذكية لتحديد العناصر التي تحتاج إلى إضافة أو حذف أو تحديث
  Future<void> _replaceRepositoryElements(List<CardElement> newElements) async {
    final currentElems = await cardRepository.getAllElements();

    // 1. تحديد العناصر التي يجب حذفها (موجودة في الحالة الحالية ولكن ليست في الحالة الجديدة)
    final elementsToRemove = currentElems
        .where((current) =>
            !newElements.any((newElem) => newElem.id == current.id))
        .toList();

    // 2. تحديد العناصر التي يجب إضافتها (موجودة في الحالة الجديدة ولكن ليست في الحالة الحالية)
    final elementsToAdd = newElements
        .where((newElem) =>
            !currentElems.any((current) => current.id == newElem.id))
        .toList();

    // 3. تحديد العناصر التي يجب تحديثها (موجودة في كلتا الحالتين ولكن بخصائص مختلفة)
    final elementsToUpdate = newElements.where((newElem) {
      final currentElem = currentElems.firstWhere(
        (current) => current.id == newElem.id,
        orElse: () => newElem,
      );

      // إذا كان العنصر موجودًا في الحالة الحالية، نتحقق مما إذا كان يحتاج إلى تحديث
      if (currentElem.id == newElem.id) {
        return currentElem.x != newElem.x ||
            currentElem.y != newElem.y ||
            currentElem.width != newElem.width ||
            currentElem.height != newElem.height ||
            currentElem.rotation != newElem.rotation ||
            currentElem.scale != newElem.scale ||
            currentElem.content != newElem.content ||
            currentElem.fontFamily != newElem.fontFamily ||
            currentElem.fontSize != newElem.fontSize ||
            currentElem.colorValue != newElem.colorValue ||
            currentElem.textAlign != newElem.textAlign ||
            currentElem.isBold != newElem.isBold ||
            currentElem.isItalic != newElem.isItalic ||
            currentElem.isUnderline != newElem.isUnderline;
      }
      return false;
    }).toList();

    // طباعة معلومات التصحيح
    debugPrint(
        'تحديث العناصر: ${elementsToRemove.length} للحذف، ${elementsToAdd.length} للإضافة، ${elementsToUpdate.length} للتحديث');

    // 4. تنفيذ العمليات

    // حذف العناصر
    for (final e in elementsToRemove) {
      await cardRepository.removeElement(e.id);
    }

    // إضافة العناصر الجديدة
    for (final e in elementsToAdd) {
      await cardRepository.addElement(e);
    }

    // تحديث العناصر الموجودة
    for (final e in elementsToUpdate) {
      await cardRepository.updateElement(e);
    }
  }

  /// Handles the [ClearElementsEvent] by clearing all elements from the repository.
  Future<void> _onClearElementsEvent(
    ClearElementsEvent event,
    Emitter<CardEditorState> emit,
  ) async {
    try {
      // Clear all elements from the repository
      cardRepository.clearElements();

      // Reset history
      _history.clear();
      _historyIndex = -1;

      // Emit empty state
      emit(CardEditorLoaded([]));

      debugPrint('All elements cleared from repository');
    } catch (e) {
      debugPrint('Error clearing elements: $e');
      emit(CardEditorError(e.toString()));
    }
  }
}
