import 'package:flutter/material.dart' show Offset;

/// Abstract base class for all card editor events.
abstract class CardEditorEvent {}

/// Event for loading all card elements.
class LoadElementsEvent extends CardEditorEvent {}

/// Event for adding a new text element.
class AddTextEvent extends CardEditor<PERSON>vent {
  /// The text content to be added.
  final String text;

  /// Creates an [AddTextEvent] with the given [text].
  AddTextEvent(this.text);
}

/// Event for adding a new sticker element.
class AddStickerEvent extends CardEditorEvent {
  /// The file path or URL for the sticker.
  final String stickerPath;

  /// Creates an [AddStickerEvent] with the given [stickerPath].
  AddStickerEvent(this.stickerPath);
}

/// Event for adding a new image element.
class AddImageEvent extends CardEditorEvent {
  /// The file path or URL for the image.
  final String imagePath;

  /// Creates an [AddImageEvent] with the given [imagePath].
  AddImageEvent(this.imagePath);
}

/// Event for removing a card element.
class RemoveElementEvent extends Card<PERSON>ditor<PERSON>vent {
  /// The unique identifier of the element to remove.
  final String elementId;

  /// Creates a [RemoveElementEvent] with the specified [elementId].
  RemoveElementEvent(this.elementId);
}

/// Event for updating the position of a card element.
class UpdateElementPositionEvent extends CardEditorEvent {
  /// The unique identifier of the element to update.
  final String elementId;

  /// The new x-coordinate.
  final double newX;

  /// The new y-coordinate.
  final double newY;

  /// Creates an [UpdateElementPositionEvent] with the given [elementId], [newX], and [newY].
  UpdateElementPositionEvent(this.elementId, this.newX, this.newY);
}

/// Event for updating the text properties of a text element.
class UpdateTextPropsEvent extends CardEditorEvent {
  /// The unique identifier of the text element to update.
  final String elementId;

  /// The new text content (optional).
  final String? newContent;

  /// The new font size (optional).
  final double? newFontSize;

  /// Whether the text should be bold (optional).
  final bool? isBold;

  /// Whether the text should be italic (optional).
  final bool? isItalic;

  /// Whether the text should be underlined (optional).
  final bool? isUnderline;

  // Additional text properties:

  /// The font family for the text (optional).
  final String? fontFamily;

  /// The ARGB color value of the text (optional).
  final int? colorValue;

  /// The letter spacing for the text (optional).
  final double? letterSpacing;

  /// The line height for the text (optional).
  final double? lineHeight;

  /// The text alignment (e.g., left, center, right) (optional).
  final String? textAlign;

  /// Creates an [UpdateTextPropsEvent] with the specified text properties.
  UpdateTextPropsEvent({
    required this.elementId,
    this.newContent,
    this.newFontSize,
    this.isBold,
    this.isItalic,
    this.isUnderline,
    this.fontFamily,
    this.colorValue,
    this.letterSpacing,
    this.lineHeight,
    this.textAlign,
  });
}

/// Event for selecting a card element.
class SelectElementEvent extends CardEditorEvent {
  /// The unique identifier of the element to select (nullable).
  final String? elementId;

  /// Creates a [SelectElementEvent] with the given [elementId].
  SelectElementEvent(this.elementId);
}

/// Event for undoing the last action.
class UndoEvent extends CardEditorEvent {}

/// Event for redoing the previously undone action.
class RedoEvent extends CardEditorEvent {}

/// Event for updating the transformation properties (position, scale, rotation) of a card element.
class UpdateElementTransformEvent extends CardEditorEvent {
  /// The unique identifier of the element to update.
  final String elementId;

  /// The new x-coordinate (optional).
  final double? newX;

  /// The new y-coordinate (optional).
  final double? newY;

  /// The new scale factor (optional).
  final double? newScale;

  /// The new rotation angle (optional).
  final double? newRotation;

  /// Creates an [UpdateElementTransformEvent] with the specified transformation properties.
  UpdateElementTransformEvent({
    required this.elementId,
    this.newX,
    this.newY,
    this.newScale,
    this.newRotation,
  });
}

/// Event for updating the size (width and height) of a card element.
class UpdateElementSizeEvent extends CardEditorEvent {
  /// The unique identifier of the element to update.
  final String elementId;

  /// The new width for the element.
  final double newWidth;

  /// The new height for the element.
  final double newHeight;

  /// Creates an [UpdateElementSizeEvent] with the given [elementId], [newWidth], and [newHeight].
  UpdateElementSizeEvent(this.elementId, this.newWidth, this.newHeight);
}

/// Event for duplicating a text element.
class DuplicateTextElementEvent extends CardEditorEvent {
  /// The unique identifier of the text element to duplicate.
  final String elementId;

  /// Creates a [DuplicateTextElementEvent] with the given [elementId].
  DuplicateTextElementEvent(this.elementId);
}

/// Event for duplicating any type of card element (e.g., image, sticker, text).
class DuplicateElementEvent extends CardEditorEvent {
  /// The unique identifier of the element to duplicate.
  final String elementId;

  /// Creates a [DuplicateElementEvent] with the given [elementId].
  DuplicateElementEvent(this.elementId);
}

/// Event to add a QR code element to the card.
class AddQrCodeEvent extends CardEditorEvent {
  /// The URL to encode in the QR code.
  final String url;

  /// Optional title for the QR code.
  final String? title;

  /// The position where to place the QR code (optional: uses default bottom right if null).
  final Offset? position;

  /// Creates an instance of [AddQrCodeEvent].
  ///
  /// The [url] parameter is required and represents the URL to encode in the QR code.
  /// The [title] parameter is optional and represents a title for the QR code.
  /// The [position] parameter specifies where to place the QR code on the card.
  AddQrCodeEvent(
    this.url, {
    this.title,
    this.position, // Position is optional, uses default if null
  });
}

/// Event to add a signature element to the card.
class AddSignatureEvent extends CardEditorEvent {
  /// The encoded signature image data or text content.
  final String signatureData;

  /// Optional text to display with the signature.
  final String? signatureText;

  /// Whether this is a drawn signature (true) or text signature (false).
  final bool isDrawnSignature;

  /// The position where to place the signature (optional: uses default bottom left if null).
  final Offset? position;

  /// The font family to use for text signatures.
  final String? fontFamily;

  /// Creates an instance of [AddSignatureEvent].
  ///
  /// The [signatureData] parameter is required and represents the encoded signature image data
  /// or the text content for a text signature.
  /// The [signatureText] parameter is optional and represents text to display with the signature.
  /// The [isDrawnSignature] parameter indicates whether this is a drawn signature (true) or text signature (false).
  /// The [position] parameter specifies where to place the signature on the card.
  /// The [fontFamily] parameter is used for text signatures to specify the font.
  AddSignatureEvent(
    this.signatureData, {
    this.signatureText,
    this.isDrawnSignature = true,
    this.position, // Position is optional, uses default if null
    this.fontFamily,
  });
}

/// Event to update properties of an element.
class UpdateElementPropertiesEvent extends CardEditorEvent {
  /// The ID of the element to update.
  final String elementId;

  /// The new content for the element.
  final String? content;

  /// The new x-coordinate for the element.
  final double? x;

  /// The new y-coordinate for the element.
  final double? y;

  /// The new width for the element.
  final double? width;

  /// The new height for the element.
  final double? height;

  /// The new font size for text elements.
  final double? fontSize;

  /// Whether the text should be bold.
  final bool? isBold;

  /// Whether the text should be italic.
  final bool? isItalic;

  /// Whether the text should be underlined.
  final bool? isUnderline;

  /// The font family for text elements.
  final String? fontFamily;

  /// The color value for the element.
  final int? colorValue;

  /// The letter spacing for text elements.
  final double? letterSpacing;

  /// The line height for text elements.
  final double? lineHeight;

  /// The text alignment for text elements.
  final String? textAlign;

  /// The scale factor for the element.
  final double? scale;

  /// The rotation angle for the element.
  final double? rotation;

  /// Creates an instance of [UpdateElementPropertiesEvent].
  ///
  /// The [elementId] parameter is required and represents the ID of the element to update.
  /// All other parameters are optional and represent the properties to update.
  UpdateElementPropertiesEvent(
    this.elementId, {
    this.content,
    this.x,
    this.y,
    this.width,
    this.height,
    this.fontSize,
    this.isBold,
    this.isItalic,
    this.isUnderline,
    this.fontFamily,
    this.colorValue,
    this.letterSpacing,
    this.lineHeight,
    this.textAlign,
    this.scale,
    this.rotation,
  });
}

/// Event to clear all elements from the repository.
/// This is used when navigating away from the card editor to prevent elements
/// from persisting between different editing sessions.
class ClearElementsEvent extends CardEditorEvent {
  /// Creates a new [ClearElementsEvent].
  ClearElementsEvent();
}
