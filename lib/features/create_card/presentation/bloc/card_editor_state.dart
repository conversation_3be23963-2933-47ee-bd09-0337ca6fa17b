import '../../domain/entities/card_element.dart';

/// Abstract base class for all card editor states.
abstract class CardEditorState {
  /// Creates an immutable [CardEditorState].
  const CardEditorState();
}

/// Represents the initial state of the card editor.
class CardEditorInitial extends CardEditorState {}

/// Represents the loading state of the card editor while data is being fetched.
class CardEditorLoading extends CardEditorState {}

/// Represents the loaded state of the card editor.
///
/// This state contains the current list of [CardElement] objects and optionally
/// the identifier of the currently selected element.
class CardEditorLoaded extends CardEditorState {
  /// The list of card elements currently loaded.
  final List<CardElement> elements;

  /// The identifier of the currently selected element, if any.
  final String? selectedElementId;

  /// Optional message to display to the user (e.g., for undo/redo operations).
  final String? message;

  /// Creates a [CardEditorLoaded] state with the provided [elements] and optional parameters.
  ///
  /// [selectedElementId] identifies the currently selected element, if any.
  /// [message] provides feedback to the user about the current state.
  const CardEditorLoaded(
    this.elements, {
    this.selectedElementId,
    this.message,
  });
}

/// Represents an error state in the card editor.
///
/// This state contains an error message indicating the reason for the failure.
class CardEditorError extends CardEditorState {
  /// The error message describing what went wrong.
  final String message;

  /// Creates a [CardEditorError] state with the given error [message].
  const CardEditorError(this.message);
}
