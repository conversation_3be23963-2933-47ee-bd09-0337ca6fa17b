// lib/features/create_card/presentation/pages/card_editor_page_wrapper.dart

import 'package:flutter/material.dart';
import 'package:mashair/features/create_card/domain/entities/card_element.dart';
import 'package:mashair/features/create_card/presentation/pages/card_editor_page.dart';

/// غلاف لصفحة محرر البطاقات
///
/// يستخدم هذا الغلاف لتوجيه المستخدم إلى محرر البطاقات الموحد الجديد
/// أو محرر البطاقات القديم حسب الإعدادات.
class CardEditorPageWrapper extends StatefulWidget {
  /// معرف البطاقة
  final dynamic cardId;

  /// لون خلفية البطاقة
  final Color? initialBackgroundColor;

  /// عناصر البطاقة الأولية
  final List<CardElement>? initialElements;

  /// مسار ملف المسودة
  final String? draftFilePath;

  /// هل هو وضع الذكاء الاصطناعي
  final bool isAiMode;

  /// هل يستخدم المحرر الموحد الجديد
  final bool useUnifiedEditor;

  /// إنشاء غلاف لصفحة محرر البطاقات
  const CardEditorPageWrapper({
    super.key,
    this.cardId,
    this.initialBackgroundColor,
    this.initialElements,
    this.draftFilePath,
    this.isAiMode = false,
    this.useUnifiedEditor = true, // استخدام المحرر الموحد الجديد افتراضيًا
  });

  @override
  _CardEditorPageWrapperState createState() => _CardEditorPageWrapperState();
}

class _CardEditorPageWrapperState extends State<CardEditorPageWrapper> {
  @override
  Widget build(BuildContext context) {
    // استخدام المحرر القديم
    return CardEditorPage(
      cardId: widget.cardId,
      initialBackgroundColor: widget.initialBackgroundColor,
      initialElements: widget.initialElements,
      draftFilePath: widget.draftFilePath,
      isAiMode: widget.isAiMode,
    );
  }
}

/// مدخل لمحرر البطاقات
///
/// يستخدم هذا المدخل لفتح محرر البطاقات بالطريقة المناسبة.
class CardEditorEntry {
  /// فتح محرر البطاقات
  static Future<void> openCardEditor(
    BuildContext context, {
    dynamic cardId,
    Color? backgroundColor,
    List<CardElement>? initialElements,
    String? draftFilePath,
    bool isAiMode = false,
    bool useUnifiedEditor = true,
  }) async {
    // تحميل عناصر البطاقة إذا تم توفير معرف بطاقة
    // فتح محرر البطاقات مباشرة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CardEditorPageWrapper(
          cardId: cardId,
          initialBackgroundColor: backgroundColor,
          initialElements: initialElements,
          draftFilePath: draftFilePath,
          isAiMode: isAiMode,
          useUnifiedEditor: useUnifiedEditor,
        ),
      ),
    );
  }
}
