import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui' as ui;

// ColorPicker removed

// CachedNetworkImage removed
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:signature/signature.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_sizes.dart';
import '../../../../core/constants/card_constants.dart';
import '../../../../core/models/unified_card_element.dart';
import '../../../../core/utils/transform_optimizer.dart';

import '../../../../core/utils/coordinate_system.dart';
import '../../../ai/domain/entities/ai_text_options.dart' as ai_options;
import '../../../ai/presentation/blocs/ai_bloc.dart';
import '../../data/repositories/card_elements_repository.dart';
import '../../domain/entities/card_element.dart';
import '../../data/models/card_element_model.dart';
import '../bloc/card_editor_bloc.dart';
import '../bloc/card_editor_event.dart';
import '../bloc/card_editor_state.dart';
import '../widgets/dashed_border_container.dart';
import '../widgets/manual_moveable.dart';
import '../widgets/sticker_picker_sheet.dart';
import '../widgets/text_edit_bottom_bar.dart';
import '../widgets/media_edit_bottom_bar.dart';
// Printing functions are implemented directly in this file
import 'drafts_page.dart';

import 'preview_page.dart';

/// Converts a [CardElement] to a JSON-compatible Map for saving drafts or other purposes.
///
/// This function ensures all properties of the element are properly serialized,
/// including text formatting, position, size, rotation, and other attributes.
/// It also normalizes coordinates to relative values (0-1) for better compatibility
/// across different screen sizes and between the app and admin panel.
Map<String, dynamic> cardElementToJson(CardElement e) {
  // textAlign is already a String in CardElement, no conversion needed
  String? textAlignString = e.textAlign;

  // تحويل الإحداثيات والأبعاد إلى قيم نسبية (0-1)
  // إذا كانت القيم أكبر من 1، نفترض أنها قيم مطلقة ونحولها إلى نسبية
  final double cardWidth = 350.0;
  final double cardHeight = 500.0;

  double normalizedX = e.x;
  double normalizedY = e.y;
  double normalizedWidth = e.width;
  double normalizedHeight = e.height;

  // تحويل الإحداثيات المطلقة إلى نسبية إذا كانت أكبر من 1
  if (e.x > 1 || e.y > 1 || e.width > 1 || e.height > 1) {
    normalizedX = e.x / cardWidth;
    normalizedY = e.y / cardHeight;
    normalizedWidth = e.width / cardWidth;
    normalizedHeight = e.height / cardHeight;

    debugPrint('تحويل الإحداثيات من مطلقة إلى نسبية: '
        '($normalizedX, $normalizedY), '
        '(${normalizedWidth}x$normalizedHeight)');
  }

  // تحويل لون النص إلى سلسلة نصية
  String? fontColorString;
  if (e.colorValue != null) {
    fontColorString = e.colorValue.toString();
    // إذا كان اللون بتنسيق عشري، نحوله إلى تنسيق سداسي عشري
    if (!fontColorString.startsWith('0x') && !fontColorString.startsWith('#')) {
      fontColorString = '0x${e.colorValue?.toRadixString(16).padLeft(8, '0')}';
    }
  }

  // إنشاء كائن JSON مع جميع خصائص العنصر مع معالجة آمنة للأنواع
  return {
    'id': e.id,
    'type': e.type.index, // تخزين الفهرس كرقم صحيح للتوافق
    'typeString': e.type.toString(), // تخزين النوع كسلسلة نصية للوضوح
    'content': e.content,

    // الموقع والأبعاد (نسبية)
    'x': normalizedX,
    'y': normalizedY,
    'width': normalizedWidth,
    'height': normalizedHeight,

    // الموقع والأبعاد الأصلية (مطلقة) للتوافق
    'originalX': e.x,
    'originalY': e.y,
    'originalWidth': e.width,
    'originalHeight': e.height,

    // خصائص التحويل
    'scale': e.scale,
    'rotation': e.rotation,

    // خصائص النص
    'fontSize': e.fontSize,
    'isBold': e.isBold,
    'isItalic': e.isItalic,
    'isUnderline': e.isUnderline,
    'fontFamily': e.fontFamily,
    'colorValue': e.colorValue,
    'letterSpacing': e.letterSpacing,
    'lineHeight': e.lineHeight,
    'textAlign': textAlignString,

    // خصائص إضافية للتوافق مع لوحة التحكم
    'properties': {
      'fontSize': e.fontSize,
      'isBold': e.isBold,
      'isItalic': e.isItalic,
      'isUnderline': e.isUnderline,
      'fontFamily': e.fontFamily,
      'fontColor': fontColorString,
      'textAlign': textAlignString,
      'letterSpacing': e.letterSpacing,
      'lineHeight': e.lineHeight,
      'flippedHorizontally': false, // قيمة افتراضية
    },

    // معلومات إضافية
    'timestamp': DateTime.now().millisecondsSinceEpoch,
    'isRelativeCoordinates': true, // علامة تشير إلى أن الإحداثيات نسبية
  };
}

/// A page that displays a print preview of a card and provides printing functionality.
class _CardPrintPreviewPage extends StatefulWidget {
  final Widget cardWidget;
  final Color backgroundColor;

  const _CardPrintPreviewPage({
    required this.cardWidget,
    required this.backgroundColor,
  });

  @override
  State<_CardPrintPreviewPage> createState() => _CardPrintPreviewPageState();
}

class _CardPrintPreviewPageState extends State<_CardPrintPreviewPage> {
  final GlobalKey _cardKey = GlobalKey();
  bool _isLoading = false;
  Uint8List? _pdfBytes;

  @override
  void initState() {
    super.initState();
    _generatePdf();
  }

  /// Generates a PDF from the card widget.
  Future<void> _generatePdf() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // انتظار لضمان تحديث الواجهة
      await Future.delayed(const Duration(milliseconds: 300));

      // Ensure the widget is still mounted
      if (!mounted) return;

      // Get the render object
      final RenderObject? renderObject = _cardKey.currentContext?.findRenderObject();
      if (renderObject == null) {
        throw Exception('Could not find render object');
      }

      final RenderRepaintBoundary boundary = renderObject as RenderRepaintBoundary;

      // Check if the boundary needs painting and wait for it to complete
      if (boundary.debugNeedsPaint) {
        debugPrint('Boundary needs painting, waiting...');
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // انتظار إضافي لضمان تحميل جميع الصور والملصقات
      await Future.delayed(const Duration(milliseconds: 800));

      // Capture the image with high quality
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        throw Exception('Could not convert image to bytes');
      }

      final Uint8List pngBytes = byteData.buffer.asUint8List();

      // Create a PDF document
      final pdf = pw.Document();
      final pdfImage = pw.MemoryImage(pngBytes);

      // Add a page with the card image
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Image(
                pdfImage,
                fit: pw.BoxFit.contain,
              ),
            );
          },
        ),
      );

      // Get the PDF bytes
      final bytes = await pdf.save();

      if (mounted) {
        setState(() {
          _pdfBytes = bytes;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error generating PDF: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).pdfError}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Prints the card.
  Future<void> _printCard() async {
    if (_pdfBytes == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).pdfNotGenerated),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => _pdfBytes!,
    );
  }

  /// Saves the card as a PDF file.
  Future<void> _saveAsPdf() async {
    if (_pdfBytes == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).pdfNotGenerated),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath =
          '${directory.path}/card_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File(filePath);
      await file.writeAsBytes(_pdfBytes!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).pdfSavedTo}: $filePath'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving PDF: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).errorSavingPdf}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).printPreview),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printCard,
            tooltip: AppLocalizations.of(context).print,
          ),
          IconButton(
            icon: const Icon(Icons.save_alt),
            onPressed: _saveAsPdf,
            tooltip: AppLocalizations.of(context).saveAsPdf,
          ),
        ],
      ),
      body: BlocBuilder<CardEditorBloc, CardEditorState>(
        builder: (context, state) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Card preview with RepaintBoundary for capturing
                RepaintBoundary(
                  key: _cardKey,
                  child: Container(
                    decoration: BoxDecoration(
                      color: widget.backgroundColor,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                      border: Border.all(
                        color: Colors.grey.withOpacity(0.2),
                        width: 0.5,
                      ),
                    ),
                    child: widget.cardWidget,
                  ),
                ),
                const SizedBox(height: 20),
                if (_isLoading)
                  const CircularProgressIndicator()
                else
                  ElevatedButton.icon(
                    icon: const Icon(
                      Icons.print,
                      color: Colors.white, // أيقونة بيضاء
                    ),
                    label: Text(
                      AppLocalizations.of(context).print,
                      style: const TextStyle(
                        color: Colors.white, // نص أبيض
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      elevation: 4,
                      shadowColor: AppColors.primaryColor.withValues(alpha: 0.3),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                    ),
                    onPressed: _printCard,
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// Main page for editing a card.
/// Provides functionalities such as adding elements, saving drafts, exporting to PNG/PDF,
/// sharing, and printing.
class CardEditorPage extends StatefulWidget {
  final List<CardElement>? initialElements;
  final Color? initialBackgroundColor;
  final String? draftFilePath;
  final bool isAiMode;
  final dynamic cardId; // معرف البطاقة في Firebase - يمكن أن يكون String أو int

  const CardEditorPage({
    super.key,
    this.initialElements,
    this.initialBackgroundColor,
    this.draftFilePath,
    this.isAiMode = false,
    this.cardId,
  });

  @override
  State<CardEditorPage> createState() => _CardEditorPageState();
}

class _CardEditorPageState extends State<CardEditorPage>
    with TickerProviderStateMixin {
  final _picker = ImagePicker();

  // GlobalKey used to capture the content of the card during editing.
  final GlobalKey _cardKey = GlobalKey();

  // Controller for AI prompt input
  final TextEditingController _promptController = TextEditingController();

  bool _showOverlays = true;
  Color _backgroundColor = Colors.white;
  String? _draftFilePath;



  // استخدام نفس أبعاد البطاقة في لوحة التحكم
  final double _cardWidth = CardConstants.APP_CARD_WIDTH;
  final double _cardHeight = CardConstants.APP_CARD_HEIGHT;

  // Predefined card sizes (currently not displayed).
  final List<Map<String, dynamic>> cardSizes = [
    {"name": "A6", "width": 300.0, "height": 400.0},
    {"name": "A5", "width": 350.0, "height": 500.0},
    {"name": "A4", "width": 400.0, "height": 600.0},
    {"name": "Letter", "width": 450.0, "height": 650.0},
    {"name": "Legal", "width": 500.0, "height": 700.0},
    {"name": "Square", "width": 350.0, "height": 350.0},
  ];

  @override
  void initState() {
    super.initState();

    // تعيين لون الخلفية الافتراضي
    _backgroundColor = widget.initialBackgroundColor ?? Colors.white;
    _draftFilePath = widget.draftFilePath;



    // طباعة معلومات تصحيح
    debugPrint('=== بدء تهيئة محرر البطاقة ===');

    // If initial elements are provided, set them into the repository.
    if (widget.initialElements != null) {
      debugPrint(
          'تم توفير عناصر أولية، عددها: ${widget.initialElements!.length}');

      // تأكد من أن المستودع فارغ قبل إضافة العناصر الجديدة
      context.read<CardEditorBloc>().cardRepository.clearElements();

      context
          .read<CardEditorBloc>()
          .cardRepository
          .setElements(widget.initialElements!);

      // تحميل العناصر من المستودع
      context.read<CardEditorBloc>().add(LoadElementsEvent());

      debugPrint('تم إرسال حدث LoadElementsEvent');
    }
    // إذا كان هناك معرف بطاقة، استرجع عناصر البطاقة من Firebase
    else if (widget.cardId != null) {
      // التأكد من أن معرف البطاقة ليس فارغًا إذا كان نصًا
      final String safeCardId = widget.cardId.toString();
      if (safeCardId.isNotEmpty) {
        debugPrint(
            'تم توفير معرف بطاقة، سيتم استرجاع العناصر من Firebase: $safeCardId');

        // استرجاع عناصر البطاقة من Firebase
        _loadCardElementsFromFirebase(widget.cardId);
      } else {
        debugPrint('معرف البطاقة فارغ، سيتم إنشاء بطاقة جديدة');

        // تأكد من أن المستودع فارغ
        context.read<CardEditorBloc>().cardRepository.clearElements();

        // تحميل العناصر من المستودع (ستكون فارغة)
        context.read<CardEditorBloc>().add(LoadElementsEvent());

        debugPrint('تم إرسال حدث LoadElementsEvent');
      }

      // لا نقوم بتحميل العناصر هنا، سيتم تحميلها بعد استرجاعها من Firebase
    } else {
      debugPrint(
          'لم يتم توفير عناصر أولية أو معرف بطاقة، سيتم إنشاء بطاقة جديدة');

      // تأكد من أن المستودع فارغ
      context.read<CardEditorBloc>().cardRepository.clearElements();

      // تحميل العناصر من المستودع (ستكون فارغة)
      context.read<CardEditorBloc>().add(LoadElementsEvent());

      debugPrint('تم إرسال حدث LoadElementsEvent');
    }

    debugPrint('=== انتهاء تهيئة محرر البطاقة ===');
  }

  /// استرجاع عناصر البطاقة من Firebase
  Future<void> _loadCardElementsFromFirebase(dynamic cardId) async {
    try {
      // التأكد من أن معرف البطاقة هو نص (String)
      final String safeCardId = cardId.toString();

      // استرجاع عناصر البطاقة
      final cardElementsRepository = CardElementsRepository();
      final elements = await cardElementsRepository.getCardElements(safeCardId);

      if (elements.isNotEmpty && mounted) {
        // تعيين العناصر في المستودع
        final bloc = context.read<CardEditorBloc>();

        // تأكد من أن المستودع فارغ قبل إضافة العناصر الجديدة
        bloc.cardRepository.clearElements();

        // تعيين العناصر في المستودع
        bloc.cardRepository.setElements(elements);

        // إرسال حدث تحميل العناصر للتأكد من تحديث الواجهة
        bloc.add(LoadElementsEvent());

        // تحديث الحالة
        setState(() {
          _backgroundColor = widget.initialBackgroundColor ?? Colors.white;
        });
      }
    } catch (e) {
      debugPrint('خطأ في استرجاع عناصر البطاقة: $e');
    }
  }



  /// تحويل CardElement إلى JSON
  Map<String, dynamic> cardElementToJson(CardElement element) {
    return CardElementModel(
      id: element.id,
      type: element.type,
      content: element.content,
      x: element.x,
      y: element.y,
      width: element.width,
      height: element.height,
      fontSize: element.fontSize,
      isBold: element.isBold,
      isItalic: element.isItalic,
      isUnderline: element.isUnderline,
    ).toJson();
  }

  /// تحويل JSON إلى CardElement
  CardElement cardElementFromJson(Map<String, dynamic> json) {
    return CardElementModel.fromJson(json);
  }

  /// إضافة عنصر بناءً على نوعه
  void _addElementToBloc(CardElement element) {
    switch (element.type) {
      case ElementType.text:
        context.read<CardEditorBloc>().add(AddTextEvent(element.content));
        break;
      case ElementType.sticker:
        context.read<CardEditorBloc>().add(AddStickerEvent(element.content));
        break;
      case ElementType.image:
        context.read<CardEditorBloc>().add(AddImageEvent(element.content));
        break;
      case ElementType.qrCode:
        context.read<CardEditorBloc>().add(AddQrCodeEvent(element.content));
        break;
      case ElementType.signature:
        context.read<CardEditorBloc>().add(AddSignatureEvent(element.content));
        break;
    }
  }

  /// استعادة حالة من التاريخ
  void _restoreState(Map<String, dynamic> state) {
    try {
      // استعادة العناصر
      final elementsList = state['elements'] as List;
      final elements = elementsList.map((e) => cardElementFromJson(e)).toList();

      // مسح العناصر الحالية
      context.read<CardEditorBloc>().add(ClearElementsEvent());

      // إضافة العناصر المستعادة
      for (final element in elements) {
        _addElementToBloc(element);
      }

      // استعادة لون الخلفية
      if (state.containsKey('backgroundColor')) {
        setState(() {
          _backgroundColor = Color(state['backgroundColor']);
        });
      }

      debugPrint('تم استعادة الحالة بنجاح: ${elements.length} عنصر');
    } catch (e) {
      debugPrint('خطأ في استعادة الحالة: $e');
    }
  }



  @override
  void dispose() {
    _promptController.dispose();

    // Clear all elements from the repository when leaving the page
    if (mounted) {
      context.read<CardEditorBloc>().add(ClearElementsEvent());
    }

    super.dispose();
  }



  /// Retrieves the number of saved draft files.
  Future<int> _getDraftCount() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final draftsDir = Directory('${directory.path}/drafts');
      if (await draftsDir.exists()) {
        final files = draftsDir.listSync();
        return files.length;
      }
    } catch (e) {
      // Error handling if necessary.
    }
    return 0;
  }

  /// Displays a bottom sheet for picking a background color.
  void _showBackgroundColorPicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.black
          : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        final colors = [
          const Color(0xFFF44336), // أحمر
          const Color(0xFFE53935), // أحمر داكن
          const Color(0xFFFB8C00), // برتقالي
          const Color(0xFFF57C00), // برتقالي داكن
          const Color(0xFFFFB300), // أصفر
          const Color(0xFFFFA000), // أصفر داكن
          const Color(0xFF7CB342), // أخضر فاتح
          const Color(0xFF689F38), // أخضر
          const Color(0xFF43A047), // أخضر داكن
          const Color(0xFF2E7D32), // أخضر غامق
          const Color(0xFF00897B), // تركواز
          const Color(0xFF00796B), // تركواز داكن
          const Color(0xFF1E88E5), // أزرق
          const Color(0xFF1976D2), // أزرق داكن
          const Color(0xFF3949AB), // أزرق غامق
          const Color(0xFF5E35B1), // بنفسجي
          const Color(0xFF8E24AA), // أرجواني
          const Color(0xFFD81B60), // وردي
          const Color(0xFFAD1457), // وردي داكن
          const Color(0xFF6D4C41), // بني
          const Color(0xFFFFFFFF), // أبيض
          const Color(0xFFF5F5F5), // رمادي فاتح
          const Color(0xFF9E9E9E), // رمادي
          const Color(0xFF616161), // رمادي داكن
          const Color(0xFF212121), // أسود
        ];
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                AppLocalizations.of(context).chooseBackgroundColor,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.headlineSmall?.color ??
                         Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: colors.map((color) {
                    final isSelected = _backgroundColor.value == color.value;
                    return GestureDetector(
                      onTap: () {
                        debugPrint(
                            'تغيير لون الخلفية من ${_backgroundColor.value.toRadixString(16)} إلى ${color.value.toRadixString(16)}');
                        setState(() {
                          _backgroundColor = color;
                        });
                        Navigator.pop(context);
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 6),
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color:
                                  isSelected ? Colors.blue : Colors.transparent,
                              width: 2,
                            ),
                          ),
                          child: CircleAvatar(
                            backgroundColor: color,
                            radius: 20,
                            child: isSelected
                                ? const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 16,
                                  )
                                : null,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Captures the card as a PNG image and navigates to the preview page.
  Future<void> _saveCardAsPNG() async {
    try {
      // إلغاء تحديد أي عنصر محدد وإخفاء الإطارات مؤقتاً
      context.read<CardEditorBloc>().add(SelectElementEvent(null));
      setState(() {
        _showOverlays = false;
      });

      // انتظار قصير للتأكد من إخفاء الإطارات وإلغاء التحديد
      await Future.delayed(const Duration(milliseconds: 200));

      // التقاط الصورة من البطاقة الحالية
      final boundary = _cardKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        throw Exception('Failed to find render boundary');
      }

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData?.buffer.asUint8List() ?? Uint8List(0);

      // إعادة إظهار الإطارات
      if (mounted) {
        setState(() {
          _showOverlays = true;
        });
      }

      final directory = await getApplicationDocumentsDirectory();
      final filePath =
          '${directory.path}/card_${DateTime.now().millisecondsSinceEpoch}.png';
      final imgFile = File(filePath);
      await imgFile.writeAsBytes(pngBytes);

      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => PreviewPage(
              filePath: filePath,
              previewType: PreviewType.image,
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving image: $e');
      if (mounted) {
        setState(() {
          _showOverlays = true; // التأكد من إعادة إظهار الإطارات في حالة الخطأ
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving image: $e')),
        );
      }
    }
  }

  /// Captures the card as a PDF document and navigates to the preview page.
  Future<void> _saveCardAsPDF() async {
    try {
      // إلغاء تحديد أي عنصر محدد وإخفاء الإطارات مؤقتاً
      context.read<CardEditorBloc>().add(SelectElementEvent(null));
      setState(() {
        _showOverlays = false;
      });

      // انتظار قصير للتأكد من إخفاء الإطارات وإلغاء التحديد
      await Future.delayed(const Duration(milliseconds: 200));

      // التقاط الصورة من البطاقة الحالية
      final boundary = _cardKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        throw Exception('Failed to find render boundary');
      }

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData?.buffer.asUint8List() ?? Uint8List(0);

      // إعادة إظهار الإطارات
      if (mounted) {
        setState(() {
          _showOverlays = true;
        });
      }

      // إنشاء PDF
      final pdf = pw.Document();
      final pdfImage = pw.MemoryImage(pngBytes);
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Center(child: pw.Image(pdfImage));
          },
        ),
      );

      final directory = await getApplicationDocumentsDirectory();
      final filePath =
          '${directory.path}/card_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => PreviewPage(
              filePath: filePath,
              previewType: PreviewType.pdf,
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving PDF: $e');
      if (mounted) {
        setState(() {
          _showOverlays = true; // التأكد من إعادة إظهار الإطارات في حالة الخطأ
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving PDF: $e')),
        );
      }
    }
  }



  /// Captures the card as an image and shares it via available applications.
  Future<void> _shareCard() async {
    try {
      // إلغاء تحديد أي عنصر محدد وإخفاء الإطارات مؤقتاً
      context.read<CardEditorBloc>().add(SelectElementEvent(null));
      setState(() {
        _showOverlays = false;
      });

      // انتظار قصير للتأكد من إخفاء الإطارات وإلغاء التحديد
      await Future.delayed(const Duration(milliseconds: 200));

      // التقاط الصورة من البطاقة الحالية
      final boundary = _cardKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        throw Exception('Failed to find render boundary');
      }

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData?.buffer.asUint8List() ?? Uint8List(0);

      // إعادة إظهار الإطارات
      if (mounted) {
        setState(() {
          _showOverlays = true;
        });
      }

      final directory = await getApplicationDocumentsDirectory();
      final filePath =
          '${directory.path}/card_${DateTime.now().millisecondsSinceEpoch}.png';
      final imgFile = File(filePath);
      await imgFile.writeAsBytes(pngBytes);

      if (mounted) {
        await Share.shareXFiles(
          [XFile(filePath)],
          text: 'Check out my card!',
        );
      }
    } catch (e) {
      debugPrint('Error sharing card: $e');
      if (mounted) {
        setState(() {
          _showOverlays = true; // التأكد من إعادة إظهار الإطارات في حالة الخطأ
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sharing card: $e')),
        );
      }
    }
  }

  /// Saves the current card state as a draft (JSON file).
  Future<void> _saveDraft() async {
    try {
      // استرجاع العناصر من المستودع
      final elements =
          await context.read<CardEditorBloc>().cardRepository.getAllElements();

      debugPrint(
          'حفظ مسودة مع ${elements.length} عنصر ولون خلفية ${_backgroundColor.value.toRadixString(16)}');

      // طباعة تفاصيل العناصر للتصحيح
      for (var i = 0; i < elements.length; i++) {
        final element = elements[i];
        debugPrint('العنصر $i للحفظ: النوع=${element.type}, '
            'المعرف=${element.id}, '
            'الموقع=(${element.x}, ${element.y}), '
            'الأبعاد=(${element.width}x${element.height}), '
            'المقياس=${element.scale}, '
            'الدوران=${element.rotation}');
      }

      // إنشاء كائن JSON للمسودة
      final draftJson = {
        "backgroundColor": _backgroundColor.value.toRadixString(16),
        "elements": elements.map((e) => cardElementToJson(e)).toList(),
        "timestamp": DateTime.now().millisecondsSinceEpoch,
        "width": _cardWidth,
        "height": _cardHeight,
      };

      // إنشاء مجلد المسودات إذا لم يكن موجودًا
      final directory = await getApplicationDocumentsDirectory();
      final draftsDir = Directory('${directory.path}/drafts');
      if (!await draftsDir.exists()) {
        await draftsDir.create();
      }

      if (_draftFilePath != null) {
        // تحديث مسودة موجودة
        final file = File(_draftFilePath!);
        await file.writeAsString(jsonEncode(draftJson));

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث المسودة'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        debugPrint('تم تحديث المسودة في: $_draftFilePath');
      } else {
        // إنشاء مسودة جديدة
        final fileName = 'draft_${DateTime.now().millisecondsSinceEpoch}.json';
        final file = File('${draftsDir.path}/$fileName');

        await file.writeAsString(jsonEncode(draftJson));
        _draftFilePath = file.path;

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ المسودة'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        debugPrint('تم حفظ مسودة جديدة في: $_draftFilePath');
      }

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('خطأ في حفظ المسودة: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ المسودة: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Saves the card as a high-quality PNG to the "My Cards" collection.
  Future<void> _saveCardToMyCards() async {
    try {
      // إلغاء تحديد أي عنصر محدد وإخفاء الإطارات مؤقتاً
      context.read<CardEditorBloc>().add(SelectElementEvent(null));
      setState(() {
        _showOverlays = false;
      });

      // انتظار قصير للتأكد من إخفاء الإطارات وإلغاء التحديد
      await Future.delayed(const Duration(milliseconds: 200));

      // التقاط الصورة من البطاقة الحالية بجودة عالية
      final boundary = _cardKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        throw Exception('Failed to find render boundary');
      }

      final image = await boundary.toImage(pixelRatio: 4.0); // جودة عالية
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      // إعادة إظهار الإطارات
      if (mounted) {
        setState(() {
          _showOverlays = true;
        });
      }

      if (byteData != null) {
        final Uint8List pngBytes = byteData.buffer.asUint8List();

        // Create directory for saved cards if it doesn't exist
        final directory = await getApplicationDocumentsDirectory();
        final cardsDir = Directory('${directory.path}/my_cards');
        if (!await cardsDir.exists()) {
          await cardsDir.create();
        }

        // Save the image with timestamp
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final filePath = '${cardsDir.path}/card_$timestamp.png';
        final imgFile = File(filePath);
        await imgFile.writeAsBytes(pngBytes);

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حفظ البطاقة في بطاقاتي'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _showOverlays = true; // التأكد من إعادة إظهار الإطارات في حالة الخطأ
        });
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ البطاقة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Displays a bottom sheet with options for saving, sharing, printing, etc.
  void _showNextBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.black
          : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (_) => Padding(
        padding: const EdgeInsets.only(top: 8, bottom: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.save_alt),
              title: Text(AppLocalizations.of(context).saveMyCard ?? 'حفظ بطاقتي'),
              subtitle: Text(AppLocalizations.of(context).saveMyCardDesc ?? 'حفظ كصورة عالية الجودة في بطاقاتي'),
              onTap: () {
                Navigator.pop(context);
                _saveCardToMyCards();
              },
            ),
            ListTile(
              leading: const Icon(Icons.image),
              title: Text(AppLocalizations.of(context).saveAsImage ?? 'حفظ كصورة'),
              onTap: () {
                Navigator.pop(context);
                _saveCardAsPNG();
              },
            ),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf),
              title: Text(AppLocalizations.of(context).saveAsPdf ?? 'حفظ كملف PDF'),
              onTap: () {
                Navigator.pop(context);
                _saveCardAsPDF();
              },
            ),
            ListTile(
              leading: const Icon(Icons.print),
              title: Text(AppLocalizations.of(context).printPreview ?? 'معاينة الطباعة'),
              onTap: () async {
                Navigator.pop(context);

                // إلغاء تحديد أي عنصر محدد وإخفاء الإطارات مؤقتاً
                context.read<CardEditorBloc>().add(SelectElementEvent(null));
                setState(() {
                  _showOverlays = false;
                });

                // انتظار قصير للتأكد من إخفاء الإطارات وإلغاء التحديد
                await Future.delayed(const Duration(milliseconds: 200));

                // التقاط صورة البطاقة النظيفة
                final boundary = _cardKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
                if (boundary != null) {
                  final image = await boundary.toImage(pixelRatio: 3.0);
                  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
                  final pngBytes = byteData?.buffer.asUint8List() ?? Uint8List(0);

                  // إعادة إظهار الإطارات
                  if (mounted) {
                    setState(() {
                      _showOverlays = true;
                    });
                  }

                  // إنشاء widget للمعاينة من الصورة
                  final cardPreview = Container(
                    width: _cardWidth,
                    height: _cardHeight,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: MemoryImage(pngBytes),
                        fit: BoxFit.contain,
                      ),
                    ),
                  );

                  if (mounted) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => BlocProvider.value(
                          value: context.read<CardEditorBloc>(),
                          child: _CardPrintPreviewPage(
                            cardWidget: cardPreview,
                            backgroundColor: _backgroundColor,
                          ),
                        ),
                      ),
                    );
                  }
                } else {
                  // إعادة إظهار الإطارات في حالة الخطأ
                  if (mounted) {
                    setState(() {
                      _showOverlays = true;
                    });
                  }
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: Text(AppLocalizations.of(context).shareAsImage),
              onTap: () {
                Navigator.pop(context);
                _shareCard();
              },
            ),

          ],
        ),
      ),
    );
  }

  /// Displays a bottom sheet with options for adding a new element (text, sticker, image, background).
  void _showAddElementOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.black
          : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (_) => SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[600]
                      : Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              ListTile(
              leading: const Icon(Icons.text_fields),
              title: Text(AppLocalizations.of(context).addText),
              onTap: () {
                Navigator.pop(context);
                // Adding a new text element with default text.
                context.read<CardEditorBloc>().add(AddTextEvent(AppLocalizations.of(context).newText));
              },
            ),
            ListTile(
              leading: const Icon(Icons.emoji_emotions_outlined),
              title: Text(AppLocalizations.of(context).addSticker),
              onTap: () {
                Navigator.pop(context);
                _showStickersSheet();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: Text(AppLocalizations.of(context).addImage),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
            ListTile(
              leading: const Icon(Icons.qr_code),
              title: const Text('إضافة رابط أغنية'),
              onTap: () {
                Navigator.pop(context);
                _showQrCodeDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.draw),
              title: Text(AppLocalizations.of(context).addSignature),
              onTap: () {
                Navigator.pop(context);
                _showSignatureDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.format_color_fill),
              title: Text(AppLocalizations.of(context).cardBackgroundColor),
              onTap: () {
                Navigator.pop(context);
                _showBackgroundColorPicker();
              },
            ),
            ],
          ),
        ),
      ),
    );
  }

  /// Displays a sticker picker sheet.
  void _showStickersSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // السماح بالتمرير
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.black
          : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (_) => StickerPickerSheet(
        onStickerSelected: (stickerUrl) {
          Navigator.pop(context);
          context.read<CardEditorBloc>().add(AddStickerEvent(stickerUrl));
        },
      ),
    );
  }

  /// Picks an image from the gallery and adds it as an image element.
  Future<void> _pickImageFromGallery() async {
    final picked = await _picker.pickImage(source: ImageSource.gallery);
    if (picked != null && mounted) {
      context.read<CardEditorBloc>().add(AddImageEvent(picked.path));
    }
  }



  /// Shows a dialog to create a QR code.
  void _showQrCodeDialog() {
    final TextEditingController urlController =
        TextEditingController(text: "https://");

    // Store a reference to the bloc before opening the dialog
    final cardEditorBloc = context.read<CardEditorBloc>();

    // Preview QR code
    String previewUrl = '';

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (builderContext, setState) {
          return Dialog(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Title
                  Row(
                    children: [
                      Icon(Icons.qr_code, color: AppColors.primaryColor),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context).addQrCode,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Content
                  Flexible(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // URL input field
                          TextField(
                            controller: urlController,
                            style: TextStyle(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : Colors.black87,
                              fontSize: 16,
                            ),
                            decoration: InputDecoration(
                              labelText: AppLocalizations.of(context).enterLink,
                              hintText: 'www.example.com',
                              labelStyle: TextStyle(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey[300]
                                    : Colors.grey[700],
                              ),
                              hintStyle: TextStyle(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey[400]
                                    : Colors.grey[600],
                              ),
                              prefixIcon:
                                  const Icon(Icons.link, color: Colors.blue),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: const BorderSide(
                                    color: Colors.blue, width: 2),
                              ),
                            ),
                            keyboardType: TextInputType.url,
                            onChanged: (value) {
                              setState(() {
                                previewUrl = value.trim();
                              });
                            },
                          ),

                          const SizedBox(height: 24),

                          // QR Code preview
                          if (previewUrl.isNotEmpty)
                            Center(
                              child: Column(
                                children: [
                                  Text(
                                    AppLocalizations.of(context).preview,
                                    style: const
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(8),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withAlpha(26),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    padding: const EdgeInsets.all(16),
                                    child: QrImageView(
                                      data: previewUrl,
                                      version: QrVersions.auto,
                                      size: 120, // Smaller preview size
                                      backgroundColor: Colors.white,
                                      eyeStyle: const QrEyeStyle(
                                        eyeShape: QrEyeShape.square,
                                        color: Colors.black,
                                      ),
                                      dataModuleStyle: const QrDataModuleStyle(
                                        dataModuleShape:
                                            QrDataModuleShape.square,
                                        color: Colors.black,
                                      ),
                                      errorStateBuilder: (context, error) {
                                        return Center(
                                          child: Text(
                                            AppLocalizations.of(context).errorGeneratingQr,
                                            style: const TextStyle(color: Colors.red),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Actions
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(dialogContext),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[700],
                        ),
                        child: Text(AppLocalizations.of(context).cancel),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.add),
                        label: Text(AppLocalizations.of(context).addToCard),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () {
                          final url = urlController.text.trim();

                          if (url.isNotEmpty) {
                            // Use the stored bloc reference instead of trying to read from context
                            cardEditorBloc.add(
                              AddQrCodeEvent(
                                url,
                                title: null,
                                position: null, // استخدام الموضع الافتراضي (أسفل يمين)
                              ),
                            );
                            Navigator.pop(dialogContext);

                            // Show success message
                            ScaffoldMessenger.of(dialogContext).showSnackBar(
                              SnackBar(
                                content: Text(AppLocalizations.of(context).qrCodeAddedSuccessfully),
                                backgroundColor: Colors.green,
                              ),
                            );
                          } else {
                            // Show error for empty URL
                            ScaffoldMessenger.of(dialogContext).showSnackBar(
                              SnackBar(
                                content: Text(AppLocalizations.of(context).pleaseEnterValidUrl),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Helper method to get Google Font style for signature fonts
  TextStyle _getGoogleFontStyle(String fontName) {
    try {
      switch (fontName) {
        case 'Dancing Script':
          return GoogleFonts.dancingScript();
        case 'Pacifico':
          return GoogleFonts.pacifico();
        case 'Great Vibes':
          return GoogleFonts.greatVibes();
        case 'Satisfy':
          return GoogleFonts.satisfy();
        case 'Caveat':
          return GoogleFonts.caveat();
        case 'Cairo':
          return GoogleFonts.cairo();
        case 'Amiri':
          return GoogleFonts.amiri();
        case 'Tajawal':
          return GoogleFonts.tajawal();
        case 'Scheherazade New':
          return GoogleFonts.scheherazadeNew();
        case 'Reem Kufi':
          return GoogleFonts.reemKufi();
        default:
          return GoogleFonts.cairo(); // Default fallback
      }
    } catch (e) {
      // Fallback to default font if Google Font fails to load
      debugPrint('Failed to load Google Font $fontName: $e');
      return const TextStyle(); // Use system default
    }
  }

  /// Shows a dialog to create a signature.
  void _showSignatureDialog() {
    // Create signature controller for drawing
    SignatureController signatureController = SignatureController(
      penStrokeWidth: 3,
      penColor: Colors.black,
      exportBackgroundColor: Colors.transparent, // Transparent background
    );

    // Controllers for text input
    final TextEditingController nameController = TextEditingController();
    final TextEditingController textSignatureController =
        TextEditingController();

    // Track if signature is being drawn
    bool isDrawing = false;

    // Track which signature type is selected (0 = drawn, 1 = text)
    int selectedSignatureType = 0;

    // Store a reference to the bloc before opening the dialog
    final cardEditorBloc = context.read<CardEditorBloc>();

    // List of signature fonts - using Google Fonts
    final List<String> signatureFonts = [
      'Dancing Script',
      'Pacifico',
      'Great Vibes',
      'Satisfy',
      'Caveat',
      'Cairo',
      'Amiri',
      'Tajawal',
      'Scheherazade New',
      'Reem Kufi',
    ];

    // Default selected font
    String selectedFont = signatureFonts[0];

    showDialog(
      context: context,
      barrierDismissible: false, // منع إغلاق الحوار بالنقر خارجه
      builder: (dialogContext) => StatefulBuilder(
        builder: (builderContext, setState) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(
              viewInsets: EdgeInsets.zero, // تجاهل لوحة المفاتيح
            ),
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              insetPadding: const EdgeInsets.all(16), // هوامش ثابتة
              child: Container(
                width: MediaQuery.of(context).size.width * 0.9,
                height: MediaQuery.of(context).size.height * 0.8, // ارتفاع ثابت
                padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                    // Title
                    Row(
                      children: [
                        Icon(Icons.draw, color: AppColors.primaryColor),
                        const SizedBox(width: 8),
                        Text(
                          AppLocalizations.of(context).addSignature,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Signature type selector
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () =>
                                setState(() => selectedSignatureType = 0),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                color: selectedSignatureType == 0
                                    ? AppColors.primaryColor.withAlpha(26)
                                    : Colors.transparent,
                                border: Border(
                                  bottom: BorderSide(
                                    color: selectedSignatureType == 0
                                        ? AppColors.primaryColor
                                        : Colors.transparent,
                                    width: 2,
                                  ),
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  AppLocalizations.of(context).drawSignature,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: InkWell(
                            onTap: () =>
                                setState(() => selectedSignatureType = 1),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                color: selectedSignatureType == 1
                                    ? AppColors.primaryColor.withAlpha(26)
                                    : Colors.transparent,
                                border: Border(
                                  bottom: BorderSide(
                                    color: selectedSignatureType == 1
                                        ? AppColors.primaryColor
                                        : Colors.transparent,
                                    width: 2,
                                  ),
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  AppLocalizations.of(context).textSignature,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Content area with flexible space - قابل للتمرير
                    Flexible(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Drawn signature UI
                            if (selectedSignatureType == 0) ...[
                              // Signature drawing area
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300, width: 2),
                                  borderRadius: BorderRadius.circular(12),
                                  color: Colors.white,
                                ),
                                height: 200,
                                width: double.infinity,
                                child: GestureDetector(
                                  onPanStart: (_) => setState(() => isDrawing = true),
                                  onPanEnd: (_) => setState(() => isDrawing = false),
                                  onPanUpdate: (_) => setState(() {}), // تحديث فوري أثناء الرسم
                                  child: Signature(
                                    controller: signatureController,
                                    backgroundColor: Colors.white,
                                    width: double.infinity,
                                    height: 200,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 12),

                              // Clear button for drawn signature
                              Align(
                                alignment: Alignment.centerRight,
                                child: TextButton.icon(
                                  onPressed: () {
                                    signatureController.clear();
                                    setState(() {});
                                  },
                                  icon: const Icon(Icons.clear, size: 18),
                                  label: Text(AppLocalizations.of(context).clear),
                                  style: TextButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),

                              // معاينة فورية للتوقيع المرسوم
                              Text(
                                AppLocalizations.of(context).signaturePreview,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                width: double.infinity,
                                height: 80,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300, width: 1),
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.grey.shade50,
                                ),
                                child: signatureController.isNotEmpty
                                    ? FutureBuilder<ui.Image?>(
                                        future: signatureController.toImage(),
                                        builder: (context, snapshot) {
                                          if (snapshot.hasData && snapshot.data != null) {
                                            return CustomPaint(
                                              painter: SignaturePreviewPainter(snapshot.data!),
                                              size: Size.infinite,
                                            );
                                          }
                                          return const Center(
                                            child: Text(
                                              'جاري تحديث المعاينة...',
                                              style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: 12,
                                              ),
                                            ),
                                          );
                                        },
                                      )
                                    : const Center(
                                        child: Text(
                                          'ارسم توقيعك أعلاه لرؤية المعاينة',
                                          style: TextStyle(
                                            color: Colors.grey,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                              ),
                            ],

                            // Text signature UI
                            if (selectedSignatureType == 1) ...[
                              // Text signature input
                              TextField(
                                controller: textSignatureController,
                                decoration: InputDecoration(
                                  labelText: AppLocalizations.of(context).yourSignature,
                                  hintText: AppLocalizations.of(context).typeYourSignature,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  suffixIcon: textSignatureController.text.isNotEmpty
                                      ? IconButton(
                                          icon: const Icon(Icons.clear),
                                          onPressed: () {
                                            textSignatureController.clear();
                                            setState(() {});
                                          },
                                        )
                                      : null,
                                ),
                                onChanged: (value) {
                                  // تحديث المعاينة فوراً عند تغيير النص
                                  setState(() {
                                    // Force rebuild to update preview immediately
                                  });
                                },
                              ),
                              const SizedBox(height: 16),

                              // Font selector
                              Text(
                                AppLocalizations.of(context).selectFont,
                                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                              ),
                              const SizedBox(height: 12),
                              SizedBox(
                                height: 50,
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: signatureFonts.length,
                                  itemBuilder: (context, index) {
                                    final font = signatureFonts[index];
                                    return Padding(
                                      padding: const EdgeInsets.only(right: 8),
                                      child: InkWell(
                                        onTap: () {
                                          // تحديث الخط المحدد وتحديث المعاينة فوراً
                                          setState(() {
                                            selectedFont = font;
                                            // Force rebuild to update preview immediately
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 12,
                                          ),
                                          decoration: BoxDecoration(
                                            color: selectedFont == font
                                                ? AppColors.primaryColor.withAlpha(26)
                                                : Colors.grey.withAlpha(26),
                                            borderRadius: BorderRadius.circular(16),
                                            border: Border.all(
                                              color: selectedFont == font
                                                  ? AppColors.primaryColor
                                                  : Colors.transparent,
                                            ),
                                          ),
                                          child: Text(
                                            font,
                                            style: _getGoogleFontStyle(font).copyWith(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 14, // حجم خط أكبر قليلاً لأسماء الخطوط
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(height: 20),

                              // Preview - معاينة فورية للتوقيع مع تغيير الخط
                              Text(
                                AppLocalizations.of(context).signaturePreview,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                              const SizedBox(height: 12),
                              AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                width: double.infinity,
                                height: 120, // ارتفاع أكبر لمعاينة أفضل
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300, width: 2),
                                  borderRadius: BorderRadius.circular(12),
                                  color: Colors.grey.shade50,
                                ),
                                child: Center(
                                  child: AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 300), // مدة أطول للانتقال السلس
                                    transitionBuilder: (Widget child, Animation<double> animation) {
                                      return FadeTransition(
                                        opacity: animation,
                                        child: ScaleTransition(
                                          scale: animation,
                                          child: child,
                                        ),
                                      );
                                    },
                                    child: Text(
                                      textSignatureController.text.isNotEmpty
                                          ? textSignatureController.text
                                          : 'نموذج توقيع', // نص تجريبي ثابت بدلاً من الترجمة
                                      key: ValueKey('${textSignatureController.text}_$selectedFont'),
                                      style: _getGoogleFontStyle(selectedFont).copyWith(
                                        fontSize: 32, // حجم خط أكبر للمعاينة
                                        fontWeight: FontWeight.w600,
                                        color: textSignatureController.text.isNotEmpty
                                            ? Colors.black87
                                            : Colors.grey.shade500,
                                        letterSpacing: 1.2,
                                      ),
                                      textAlign: TextAlign.left, // دائماً من اليسار إلى اليمين
                                      textDirection: TextDirection.ltr, // دائماً من اليسار إلى اليمين
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    // Buttons at bottom - ثابتة في الأسفل
                    const SizedBox(height: 16),
                    SafeArea(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.pop(dialogContext),
                            child: Text(AppLocalizations.of(context).cancel),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            ),
                            onPressed: () async {
                            // For drawn signature
                            if (selectedSignatureType == 0) {
                              if (signatureController.isNotEmpty) {
                                try {
                                  // Export the signature to a PNG image
                                  ui.Image? image =
                                      await signatureController.toImage();
                                  if (image == null) {
                                    throw Exception(
                                        'Failed to create signature image');
                                  }

                                  // Convert the image to bytes
                                  ByteData? byteData = await image.toByteData(
                                      format: ui.ImageByteFormat.png);

                                  // Convert to Uint8List
                                  final Uint8List signatureBytes =
                                      byteData != null
                                          ? byteData.buffer.asUint8List()
                                          : Uint8List(0);

                                  // Convert to base64 string for storage
                                  final String base64Data =
                                      base64Encode(signatureBytes);

                                  // Store the data we need before closing the dialog
                                  final String signatureBase64 = base64Data;
                                  final String nameText =
                                      nameController.text.trim();

                                  // Close the dialog first to avoid context issues
                                  Navigator.pop(dialogContext);

                                  // Check if the context is still valid
                                  if (!mounted) return;

                                  // Add the signature to the card using the stored data
                                  cardEditorBloc.add(
                                    AddSignatureEvent(
                                      signatureBase64,
                                      signatureText: nameText,
                                      isDrawnSignature: true,
                                      position: null, // استخدام الموضع الافتراضي (أسفل يسار)
                                    ),
                                  );
                                } catch (e) {
                                  // Check if the context is still valid
                                  if (!mounted) return;

                                  // Show error message
                                  final scaffoldMessenger =
                                      ScaffoldMessenger.of(context);
                                  scaffoldMessenger.showSnackBar(
                                    SnackBar(
                                      content:
                                          Text('Error adding signature: $e'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              } else {
                                // Show error for empty signature
                                final scaffoldMessenger =
                                    ScaffoldMessenger.of(context);
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text(AppLocalizations.of(context).pleaseDrawSignature),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                            // For text signature
                            else {
                              if (textSignatureController.text.isNotEmpty) {
                                // Store the data we need before closing the dialog
                                final String signatureText =
                                    textSignatureController.text;
                                final String nameText = "";
                                final String fontName = selectedFont;

                                // Close the dialog first to avoid context issues
                                Navigator.pop(dialogContext);

                                // Check if the context is still valid
                                if (!mounted) return;

                                // Add the text signature to the card using the stored data
                                cardEditorBloc.add(
                                  AddSignatureEvent(
                                    signatureText,
                                    signatureText: nameText,
                                    isDrawnSignature: false,
                                    position: null, // استخدام الموضع الافتراضي (أسفل يسار)
                                    fontFamily: fontName,
                                  ),
                                );
                              } else {
                                // Show error for empty text signature
                                final scaffoldMessenger =
                                    ScaffoldMessenger.of(context);
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text(
                                        AppLocalizations.of(context).pleaseEnterSignatureText),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                          },
                          child: Text(AppLocalizations.of(context).addToCard),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ));
        },
      ),
    );
  }

  /// Generates text using AI based on the prompt
  void _generateAiText() {
    // Check if prompt is empty
    if (_promptController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).enterPrompt),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Get the AI bloc
    final aiBloc = context.read<AiBloc>();

    // Create default options
    final options = ai_options.AiTextOptions(
      textLength: ai_options.TextLength.medium,
      textStyle: ai_options.TextStyle.formal,
    );

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.of(context).preparing),
        duration: const Duration(seconds: 2),
      ),
    );

    // Declare the subscription variable
    late final StreamSubscription subscription;

    // Create a subscription to listen for the result
    subscription = aiBloc.stream.listen((state) {
      // Check if the widget is still mounted
      if (!mounted) {
        subscription.cancel();
        return;
      }

      if (state is AiTextResultGenerated) {
        // Get the first text from the result
        final generatedText = state.result.firstText;

        if (generatedText != null) {
          // إضافة النص المولد بالذكاء الاصطناعي مع خصائص محسنة
          final bloc = context.read<CardEditorBloc>();

          // إضافة النص المولد بالذكاء الاصطناعي إلى البطاقة
          // سيتم إنشاء العنصر تلقائياً بخصائص محسنة
          bloc.add(AddTextEvent(generatedText));

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).addToCard),
              backgroundColor: Colors.green,
            ),
          );
        }

        // Cancel the subscription after receiving the result
        subscription.cancel();
      } else if (state is AiError) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).error}: ${state.message}'),
            backgroundColor: Colors.red,
          ),
        );

        // Cancel the subscription after receiving an error
        subscription.cancel();
      }
    });

    // Send event to generate text
    aiBloc.add(
      GenerateAiTextAdvancedEvent(
        prompt: _promptController.text.trim(),
        options: options,
      ),
    );
  }

  /// Builds a print-ready version of an element (without overlays).
  Widget _buildPrintElement(CardElement element) {
    // تحويل الإحداثيات والأبعاد للطباعة
    double x = element.x;
    double y = element.y;
    double width = element.width;
    double height = element.height;

    // تحقق مما إذا كانت الإحداثيات نسبية أم مطلقة
    bool isRelative = element.isRelativeCoordinates ||
                     (element.width <= 1 && element.height <= 1);

    if (isRelative) {
      // تحويل الإحداثيات النسبية إلى مطلقة
      x = element.x * _cardWidth;
      y = element.y * _cardHeight;
      width = element.width * _cardWidth;
      height = element.height * _cardHeight;
    }

    // استخدام الدوران مباشرة

    return Positioned(
      left: x,
      top: y,
      child: Transform.rotate(
        angle: element.rotation,
        child: Transform.scale(
          scale: element.scale,
          child: SizedBox(
            width: width,
            height: height,
            child: _buildElementContent(element),
          ),
        ),
      ),
    );
  }

  /// بناء البطاقة بدون إطارات للمعاينة والحفظ
  Widget _buildCleanCard(List<CardElement> elements) {
    return Container(
      width: _cardWidth,
      height: _cardHeight,
      decoration: BoxDecoration(
        color: _backgroundColor,
      ),
      clipBehavior: Clip.hardEdge,
      child: ClipRect(
        child: Stack(
          clipBehavior: Clip.none,
          fit: StackFit.expand,
          children: [
            for (final element in elements)
              _buildCleanElement(element), // استخدام دالة منفصلة للعناصر النظيفة
          ],
        ),
      ),
    );
  }

  /// بناء عنصر نظيف بدون إطارات أو دوائر سحب للمعاينة والحفظ
  Widget _buildCleanElement(CardElement element) {
    // تحويل الإحداثيات والأبعاد
    double x = element.x;
    double y = element.y;
    double width = element.width;
    double height = element.height;

    // تحقق مما إذا كانت الإحداثيات نسبية أم مطلقة
    bool isRelative = element.isRelativeCoordinates ||
                     (element.width <= 1 && element.height <= 1);

    if (isRelative) {
      // تحويل الإحداثيات النسبية إلى مطلقة
      x = element.x * _cardWidth;
      y = element.y * _cardHeight;
      width = element.width * _cardWidth;
      height = element.height * _cardHeight;
    }

    // بناء محتوى العنصر النظيف بدون أي إطارات
    Widget cleanContent = SizedBox(
      width: width,
      height: height,
      child: _buildElementContent(element),
    );

    // تطبيق التحويلات (الدوران والمقياس)
    if (element.scale != 1.0) {
      cleanContent = Transform.scale(
        scale: element.scale,
        alignment: Alignment.center,
        child: cleanContent,
      );
    }

    if (element.rotation != 0.0) {
      cleanContent = Transform.rotate(
        angle: element.rotation,
        alignment: Alignment.center,
        child: cleanContent,
      );
    }

    return Positioned(
      left: x,
      top: y,
      child: cleanContent,
    );
  }

  /// عرض مربع حوار تأكيد عند محاولة الخروج من المحرر
  Future<bool> _onWillPop() async {
    // الحصول على العناصر الحالية
    final elements =
        await context.read<CardEditorBloc>().cardRepository.getAllElements();

    // إذا لم تكن هناك عناصر، نخرج مباشرة
    if (elements.isEmpty) {
      return true;
    }

    // عرض مربع حوار تأكيد
    final completer = Completer<bool>();

    showDialog<String>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(AppLocalizations.of(context).saveDraftQuestion),
        content: Text(AppLocalizations.of(context).saveDraftMessage),
        actions: [
          TextButton(
            onPressed: () {
              // عدم الحفظ وتنظيف البطاقة
              context.read<CardEditorBloc>().add(ClearElementsEvent());
              // إغلاق مربع الحوار وإكمال المستقبل بقيمة true للخروج
              Navigator.of(dialogContext).pop();
              completer.complete(true);
            },
            child: Text(AppLocalizations.of(context).dontSave),
          ),
          TextButton(
            onPressed: () async {
              // حفظ كمسودة
              await _saveDraft();

              // تنظيف البطاقة
              if (mounted) {
                context.read<CardEditorBloc>().add(ClearElementsEvent());
                // إغلاق مربع الحوار وإكمال المستقبل بقيمة true للخروج
                Navigator.of(dialogContext).pop();
                completer.complete(true);
              }
            },
            child: Text(AppLocalizations.of(context).saveDraft),
          ),
          TextButton(
            onPressed: () {
              // إلغاء الخروج
              Navigator.of(dialogContext).pop();
              completer.complete(false);
            },
            child: Text(AppLocalizations.of(context).cancel),
          ),
        ],
      ),
    );

    // انتظار نتيجة مربع الحوار
    return await completer.future;
  }

  /// حساب حجم الخط المناسب للإطار المحدد
  double _calculateFontSizeForFrame(
    String text,
    Size frameSize,
    CardElement element,
  ) {
    // التحقق من صحة frameSize قبل المتابعة
    if (!TransformOptimizer.isValidSize(frameSize)) {
      debugPrint('Invalid frameSize in _calculateOptimalFontSize: $frameSize');
      return 16.0; // حجم خط افتراضي آمن
    }

    // البدء بحجم خط كبير والتقليل تدريجياً
    double fontSize = 120.0;
    const double minFontSize = 8.0;

    // إزالة الهوامش من حجم الإطار المتاح (مطابقة للـ padding)
    double availableWidth = frameSize.width * 0.90;  // 5% padding on each side
    double availableHeight = frameSize.height * 0.84; // 8% padding on each side

    // التأكد من أن المساحة المتاحة إيجابية وصحيحة
    if (!TransformOptimizer.isValidNumber(availableWidth) ||
        !TransformOptimizer.isValidNumber(availableHeight) ||
        availableWidth <= 1 || availableHeight <= 1) {
      debugPrint('Invalid available dimensions: ${availableWidth}x$availableHeight');
      return minFontSize;
    }

    while (fontSize > minFontSize) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: element.isBold ? FontWeight.bold : FontWeight.normal,
            fontStyle: element.isItalic ? FontStyle.italic : FontStyle.normal,
            fontFamily: element.fontFamily,
            letterSpacing: element.letterSpacing,
            height: element.lineHeight,
          ),
        ),
        textDirection: containsArabicCharacters(text) ? TextDirection.rtl : TextDirection.ltr,
        textAlign: _convertTextAlign(element.textAlign),
        maxLines: null, // السماح بعدة أسطر
      );

      // التحقق مرة أخرى من صحة availableWidth قبل layout
      if (!TransformOptimizer.isValidNumber(availableWidth) || availableWidth <= 0) {
        debugPrint('Invalid availableWidth in loop: $availableWidth');
        return minFontSize;
      }

      textPainter.layout(maxWidth: availableWidth);

      // إذا كان النص يتسع في الإطار، استخدم هذا الحجم
      if (textPainter.height <= availableHeight) {
        return fontSize;
      }

      // تقليل حجم الخط
      fontSize -= 1.0;
    }

    return minFontSize;
  }


  /// حساب حجم الإطار المتوافق مع النص (سطر واحد أو عدة أسطر حسب الطول)
  Size _calculateOptimalFrameSize(String text, double fontSize, CardElement element, double cardWidth) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: element.isBold ? FontWeight.bold : FontWeight.normal,
          fontStyle: element.isItalic ? FontStyle.italic : FontStyle.normal,
          fontFamily: element.fontFamily,
          letterSpacing: element.letterSpacing,
          height: element.lineHeight,
        ),
      ),
      textDirection: containsArabicCharacters(text) ? TextDirection.rtl : TextDirection.ltr,
      textAlign: _convertTextAlign(element.textAlign),
    );

    // Layout with a reasonable constraint first.
    textPainter.layout(maxWidth: cardWidth * 0.8); // Max 80% of card width

    // Add some padding to the calculated size.
    final double paddingHorizontal = 24.0;
    final double paddingVertical = 20.0;
    
    Size finalSize = Size(
      textPainter.width + paddingHorizontal,
      textPainter.height + paddingVertical,
    );

    // Enforce minimum dimensions.
    finalSize = Size(
      math.max(finalSize.width, 80.0),
      math.max(finalSize.height, 40.0),
    );

    return finalSize;
  }

  /// تحويل محاذاة النص إلى Alignment
  Alignment _getAlignmentFromTextAlign(TextAlign textAlign) {
    switch (textAlign) {
      case TextAlign.left:
        return Alignment.centerLeft;
      case TextAlign.right:
        return Alignment.centerRight;
      case TextAlign.center:
        return Alignment.center;
      case TextAlign.justify:
        return Alignment.centerLeft;
      case TextAlign.start:
        return Alignment.centerLeft;
      case TextAlign.end:
        return Alignment.centerRight;
    }
  }



  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;

          // Appeler _onWillPop() qui gère la logique de sauvegarde/nettoyage
          final bool shouldPop = await _onWillPop();

          // Si _onWillPop() retourne true, cela signifie que nous devons quitter la page
          if (shouldPop && mounted) {
            Navigator.pop(context);
          }
        },
        child: Scaffold(
          resizeToAvoidBottomInset:
              false, // Empêcher le redimensionnement de la carte lors de l'apparition du clavier
          backgroundColor: Colors.grey[200],
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              tooltip: AppLocalizations.of(context).back,
              onPressed: () async {
                // استدعاء نفس المنطق الموجود في _onWillPop
                final shouldPop = await _onWillPop();
                if (shouldPop && mounted) {
                  // استخدام Navigator.pop مباشرة للخروج من الصفحة
                  Navigator.pop(context);
                }
              },
            ),
            actions: [
              // زر التراجع
              IconButton(
                icon: const Icon(Icons.undo),
                onPressed: () {
                  context.read<CardEditorBloc>().add(UndoEvent());
                },
              ),
              IconButton(
                icon: const Icon(Icons.redo),
                tooltip: AppLocalizations.of(context).redo,
                onPressed: () {
                  context.read<CardEditorBloc>().add(RedoEvent());
                },
              ),
              TextButton(
                onPressed: () async {
                  // استرجاع العناصر من المستودع
                  final elements = await context
                      .read<CardEditorBloc>()
                      .cardRepository
                      .getAllElements();

                  // إذا لم تكن هناك عناصر، نعرض رسالة
                  if (elements.isEmpty) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(AppLocalizations.of(context).noElementsToSave),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                    return;
                  }

                  // حفظ المسودة
                  await _saveDraft();
                },
                child: Text(
                  'حفظ المسودة',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              FutureBuilder<int>(
                future: _getDraftCount(),
                builder: (context, snapshot) {
                  int count = snapshot.data ?? 0;
                  return IconButton(
                    icon: Stack(
                      children: [
                        const Icon(Icons.folder_open),
                        if (count > 0)
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              padding: const EdgeInsets.all(1),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 16,
                                minHeight: 16,
                              ),
                              child: Text(
                                '$count',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                    tooltip: 'المسودات',
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const DraftsPage()),
                      ).then((_) => setState(() {}));
                    },
                  );
                },
              ),
              TextButton(
                onPressed: _showNextBottomSheet,
                child: Text(
                  AppLocalizations.of(context).next,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          body: Column(
            children: [
              // AI Text Generation section (only visible in AI mode)
              if (widget.isAiMode)
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color.fromARGB(
                            13,
                            AppColors.primaryColor.r.toInt(),
                            AppColors.primaryColor.g.toInt(),
                            AppColors.primaryColor.b.toInt()),
                        Color.fromARGB(
                            26,
                            AppColors.accentColor.r.toInt(),
                            AppColors.accentColor.g.toInt(),
                            AppColors.accentColor.b.toInt()),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(AppSizes.borderRadius),
                  ),
                  margin: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 4.0),
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Text field with improved styling - 2 lines
                      TextField(
                        controller: _promptController,
                        style: TextStyle(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                          fontSize: 14,
                          fontWeight: FontWeight.w500, // جعل النص أكثر وضوحاً
                        ),
                        decoration: InputDecoration(
                          floatingLabelBehavior: FloatingLabelBehavior.never,
                          hintText: AppLocalizations.of(context).aiPrompt,
                          hintStyle: TextStyle(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.grey[400]
                                : AppColors.primaryColor,
                            fontSize: 13,
                          ),
                          border: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(AppSizes.borderRadius),
                            borderSide: BorderSide(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey[600]!
                                  : AppColors.primaryColor,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(AppSizes.borderRadius),
                            borderSide: BorderSide(
                                color: AppColors.primaryColor, width: 2),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(AppSizes.borderRadius),
                            borderSide: BorderSide(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey[700]!
                                  : Color.fromARGB(
                                      128,
                                      AppColors.primaryColor.r.toInt(),
                                      AppColors.primaryColor.g.toInt(),
                                      AppColors.primaryColor.b.toInt()),
                            ),
                          ),
                          prefixIcon: Icon(Icons.auto_awesome,
                              color: AppColors.primaryColor, size: 18),
                          filled: true,
                          fillColor: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[800]
                              : Colors.white,
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 12.0, horizontal: 12.0),
                        ),
                        maxLines: 2,
                        minLines: 2,
                      ),
                      const SizedBox(height: 4),
                      // Generate button with improved styling
                      ElevatedButton.icon(
                        onPressed: _generateAiText,
                        icon: const Icon(Icons.auto_awesome, size: 16),
                        label: Text(AppLocalizations.of(context).generateText,
                            style: const TextStyle(fontSize: 13)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppSizes.borderRadius),
                          ),
                          elevation: 1,
                          minimumSize: const Size(double.infinity, 32),
                        ),
                      ),
                    ],
                  ),
                ),

              // Card editing area.
              Expanded(
                flex: 9,
                child: BlocConsumer<CardEditorBloc, CardEditorState>(
                  listener: (context, state) {
                    // Mostrar mensajes de feedback para operaciones de deshacer/rehacer
                    if (state is CardEditorLoaded && state.message != null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(state.message!),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    }
                  },
                  builder: (context, state) {
                    return Center(
                      child: Container(
                        constraints: BoxConstraints(
                          maxWidth: MediaQuery.of(context).size.width * 0.95,
                          maxHeight: MediaQuery.of(context).size.height * 0.8,
                        ),
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              // Deselect the element if tapped outside.
                              final bloc = context.read<CardEditorBloc>();
                              if (bloc.state is CardEditorLoaded &&
                                  (bloc.state as CardEditorLoaded)
                                          .selectedElementId !=
                                      null) {
                                bloc.add(SelectElementEvent(null));
                              }
                            },
                            child: Container(
                              width: _cardWidth,
                              height: _cardHeight,
                              margin: const EdgeInsets.all(24.0),
                              child: RepaintBoundary(
                                key: _cardKey,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: _backgroundColor,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Color.fromARGB(
                                            38,
                                            AppColors.primaryColor.r.toInt(),
                                            AppColors.primaryColor.g.toInt(),
                                            AppColors.primaryColor.b.toInt()),
                                        blurRadius: 6,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                    border: Border.all(
                                      color: Color.fromARGB(26, 200, 200, 200),
                                      width: 0.5,
                                    ),
                                  ),
                                  clipBehavior: Clip
                                      .hardEdge, // اقتطاع العناصر الخارجة عن حدود البطاقة على مستوى البطاقة فقط
                                  child: BlocBuilder<CardEditorBloc,
                                      CardEditorState>(
                                    builder: (context, state) {
                                      if (state is CardEditorLoading) {
                                        return const Center(
                                            child: CircularProgressIndicator());
                                      } else if (state is CardEditorLoaded) {
                                        final elements = state.elements;
                                        final selectedId =
                                            state.selectedElementId;

                                        // استخدام ClipRect لاقتطاع العناصر الخارجة عن حدود البطاقة في معاينة الطباعة
                                        // وStack مع Clip.none للسماح للعناصر بالامتداد خارج حدود البطاقة أثناء التحرير
                                        return ClipRect(
                                          child: Stack(
                                            clipBehavior: Clip
                                                .none, // السماح للعناصر بالامتداد خارج حدود البطاقة
                                            fit: StackFit
                                                .expand, // ضمان أن Stack يملأ المساحة المتاحة
                                            children: [
                                              for (final element in elements)
                                                _buildMainElement(
                                                    element, selectedId),
                                            ],
                                          ),
                                        );
                                      } else if (state is CardEditorError) {
                                        return Center(
                                          child:
                                              Text("Error: ${state.message}"),
                                        );
                                      }
                                      return const SizedBox();
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              // Slider for rotating the selected element.
              BlocBuilder<CardEditorBloc, CardEditorState>(
                builder: (context, state) {
                  if (state is CardEditorLoaded &&
                      state.selectedElementId != null) {
                    final selectedElement = state.elements.firstWhereOrNull(
                      (element) => element.id == state.selectedElementId,
                    );
                    if (selectedElement != null) {
                      double rotationDegrees =
                          selectedElement.rotation * 180.0 / math.pi;
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        color: Colors.white,
                        child: Row(
                          children: [
                            const Icon(
                              Icons.rotate_right,
                              color: Colors.green,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: SliderTheme(
                                data: SliderTheme.of(context).copyWith(
                                  activeTrackColor:
                                      Color.fromARGB(128, 76, 175, 80),
                                  inactiveTrackColor:
                                      Color.fromARGB(77, 76, 175, 80),
                                  thumbColor: Colors.green,
                                  overlayColor:
                                      Color.fromARGB(51, 76, 175, 80),
                                  trackHeight: 4,
                                ),
                                child: Slider(
                                  value: rotationDegrees,
                                  min: 0,
                                  max: 360,
                                  label:
                                      "${rotationDegrees.toStringAsFixed(0)}°",
                                  onChanged: (double value) {
                                    final newRad = value * math.pi / 180.0;
                                    context.read<CardEditorBloc>().add(
                                          UpdateElementTransformEvent(
                                            elementId: selectedElement.id,
                                            newRotation: newRad,
                                          ),
                                        );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                  }
                  return const SizedBox.shrink();
                },
              ),
              // Bottom toolbar for the selected element.
              BlocBuilder<CardEditorBloc, CardEditorState>(
                builder: (context, state) {
                  // إخفاء أشرطة التحرير عند الحفظ والمعاينة
                  if (!_showOverlays) {
                    return const SizedBox();
                  }

                  if (state is CardEditorLoaded &&
                      state.selectedElementId != null) {
                    final CardElement? selectedElement =
                        state.elements.firstWhereOrNull(
                      (element) => element.id == state.selectedElementId,
                    );
                    if (selectedElement != null) {
                      if (selectedElement.type == ElementType.text) {
                        return TextEditBottomBar(element: selectedElement);
                      } else if (selectedElement.type == ElementType.image ||
                          selectedElement.type == ElementType.sticker) {
                        return MediaEditBottomBar(element: selectedElement);
                      } else if (selectedElement.type == ElementType.qrCode) {
                        return _buildQrCodeEditBar(selectedElement);
                      } else if (selectedElement.type ==
                          ElementType.signature) {
                        return _buildSignatureEditBar(selectedElement);
                      }
                    }
                  }
                  return const SizedBox();
                },
              ),
            ],
          ),
          floatingActionButton: BlocBuilder<CardEditorBloc, CardEditorState>(
            builder: (context, state) {
              // إخفاء زر الإضافة عند الحفظ والمعاينة
              if (!_showOverlays) {
                return const SizedBox();
              }

              // Hide the add button when an element is selected.
              if (state is CardEditorLoaded &&
                  state.selectedElementId != null) {
                return const SizedBox();
              } else {
                return FloatingActionButton(
                  tooltip: 'Add Element',
                  onPressed: _showAddElementOptions,
                  child: const Icon(Icons.add),
                );
              }
            },
          ),
        ));
  }

  /// Builds the interactive main element widget in edit mode.
  /// Applies decorations based on whether the element is selected.
  Widget _buildMainElement(CardElement element, String? selectedId) {
    final isSelected = (element.id == selectedId) && _showOverlays; // إخفاء التحديد عند الحفظ والمعاينة

    // تحويل الدوران من راديان إلى درجات إذا كان بالراديان
    final rotationDegrees = element.rotation > math.pi * 2
        ? element.rotation
        : element.rotation * 180.0 / math.pi;

    // تحويل الإحداثيات من قيم نسبية إلى قيم مطلقة
    double x = element.x;
    double y = element.y;
    double width = element.width;
    double height = element.height;

    // استخدام الإحداثيات والقياسات النهائية إذا كانت متوفرة
    final unifiedElement = element as dynamic;
    if (unifiedElement is UnifiedCardElement &&
        unifiedElement.finalX != null &&
        unifiedElement.finalY != null &&
        unifiedElement.finalWidth != null &&
        unifiedElement.finalHeight != null) {
      debugPrint('استخدام الإحداثيات والقياسات النهائية في _buildMainElement');

      // استخدام الإحداثيات النهائية مباشرة بدون تحويل إضافي
      // لأنها تم تخزينها بالفعل بالشكل الصحيح في لوحة التحكم
      if (element.isFromAdminPanel) {
        // تحويل الإحداثيات النهائية من نسبية إلى مطلقة
        x = unifiedElement.finalX! * _cardWidth;
        y = unifiedElement.finalY! * _cardHeight;
        width = unifiedElement.finalWidth! * _cardWidth;
        height = unifiedElement.finalHeight! * _cardHeight;
      } else {
        // للعناصر المضافة في التطبيق، نستخدم الإحداثيات النهائية كما هي
        x = unifiedElement.finalX!;
        y = unifiedElement.finalY!;
        width = unifiedElement.finalWidth!;
        height = unifiedElement.finalHeight!;
      }

      debugPrint('الإحداثيات والقياسات النهائية بعد التحويل: ($x, $y), (${width}x$height)');
    } else if (element.type == ElementType.image || element.type == ElementType.sticker) {
      // معالجة خاصة للصور والملصقات التي لا تحتوي على إحداثيات وقياسات نهائية
      debugPrint('لا توجد إحداثيات وقياسات نهائية للصورة، تطبيق معالجة خاصة');

      // معالجة الصور
      if (element.type == ElementType.image) {
        debugPrint('معالجة خاصة للصورة: ($x, $y), (${width}x$height)');

        // حالة 1: صورة صغيرة جداً أو في الزاوية العلوية اليسرى
        if (width < 10 || height < 10 || (x < 10 && y < 10)) {
          debugPrint('تم اكتشاف صورة صغيرة جداً أو في الزاوية العلوية اليسرى');

          // جعل الصورة تغطي البطاقة كاملة
          x = 0;
          y = 0;
          width = _cardWidth;
          height = _cardHeight;

          debugPrint('تم تصحيح الصورة لتغطي البطاقة كاملة: ($x, $y), (${width}x$height)');
        }
        // حالة 2: صورة كبيرة تتجاوز حدود البطاقة
        else if (width > _cardWidth * 0.9 || height > _cardHeight * 0.9) {
          debugPrint('تم اكتشاف صورة كبيرة تتجاوز حدود البطاقة');

          // التأكد من أن الصورة تغطي البطاقة كاملة
          x = 0;
          y = 0;
          width = _cardWidth;
          height = _cardHeight;

          debugPrint('تم تصحيح الصورة لتغطي البطاقة كاملة: ($x, $y), (${width}x$height)');
        }
      }
      // معالجة الملصقات والنصوص
      else if (x < 0 || y < 0 || width < 10 || height < 10) {
        debugPrint('العنصر له إحداثيات سالبة أو أبعاد صغيرة جداً: ($x, $y), (${width}x$height)');

        // تصحيح الإحداثيات السالبة
        if (x < 0) x = 0;
        if (y < 0) y = 0;

        if (element.type == ElementType.sticker) {
          // للملصقات، نستخدم قيم أصغر
          if (width < 10) width = 0.2 * _cardWidth;
          if (height < 10) height = 0.2 * _cardHeight;
        } else {
          // للنصوص، نستخدم قيم أصغر
          if (width < 10) width = 0.3 * _cardWidth;
          if (height < 10) height = 0.3 * _cardHeight;
        }

        debugPrint('تم تصحيح الإحداثيات والأبعاد: ($x, $y), (${width}x$height)');
      }

      // معالجة خاصة للصور التي تتجاوز حدود البطاقة
      // نتأكد من أن الصورة تظهر بشكل كامل داخل البطاقة
      if (x + width > _cardWidth || y + height > _cardHeight) {
        debugPrint('الصورة تتجاوز حدود البطاقة: ($x, $y), (${width}x$height)');

        // حساب نسبة الأبعاد الأصلية للصورة
        double aspectRatio = width / height;

        // تعديل أبعاد الصورة لتناسب حدود البطاقة مع الحفاظ على نسبة الأبعاد
        if (x + width > _cardWidth) {
          width = _cardWidth - x;
          height = width / aspectRatio;
        }

        if (y + height > _cardHeight) {
          height = _cardHeight - y;
          width = height * aspectRatio;
        }

        debugPrint('تم تعديل أبعاد الصورة لتناسب حدود البطاقة: (${width}x$height)');
      }
    }

    // معالجة خاصة للصور ذات الإحداثيات السالبة والأبعاد الصغيرة
    // تم نقل هذه المعالجة إلى الأعلى لتجنب التكرار

    // نستخدم دائمًا إحداثيات نسبية (0-1) كما في لوحة التحكم
    // ونحولها إلى إحداثيات مطلقة (بالبكسل) عند العرض

    // طباعة معلومات العنصر قبل التحويل
    debugPrint('=== معلومات العنصر قبل التحويل ===');
    debugPrint('المعرف: ${element.id}');
    debugPrint('النوع: ${element.type}');
    debugPrint('الإحداثيات: (${element.x}, ${element.y})');
    debugPrint('الأبعاد: (${element.width}x${element.height})');
    debugPrint('isFromAdminPanel: ${element.isFromAdminPanel}');
    debugPrint('============================');

    // تعامل خاص مع العناصر من لوحة التحكم
    if (element.isRelativeCoordinates) {
      // إذا كان العنصر من لوحة التحكم، نستخدم الإحداثيات كما هي
      // لأن لوحة التحكم تستخدم نفس أبعاد البطاقة (800×1000)
      x = x * _cardWidth;
      y = y * _cardHeight;
      width = width * _cardWidth;
      height = height * _cardHeight;

      debugPrint('العنصر من لوحة التحكم - استخدام الإحداثيات كما هي');
    }

    debugPrint('بعد التحويل: ($x, $y), (${width}x$height)');

    // إذا كان العنصر من لوحة التحكم، نستخدم حجم الخط كما هو
    if (element.isFromAdminPanel && element.type == ElementType.text) {
      // نستخدم حجم الخط كما هو بدون تعديل
      // لأننا نستخدم نفس أبعاد البطاقة في لوحة التحكم
      debugPrint(
          'استخدام حجم الخط الأصلي: ${element.fontSize}');
    }

    // تحقق ما إذا كان العنصر يتجاوز حدود البطاقة
    bool isElementExtendingBeyondBounds =
        x < 0 || y < 0 || x + width > _cardWidth || y + height > _cardHeight;

    // معالجة خاصة للصور التي تتجاوز حدود البطاقة
    if (element.type == ElementType.image ||
        element.type == ElementType.sticker) {
      // طباعة معلومات العنصر للتصحيح
      debugPrint(
          'معلومات الصورة: الإحداثيات=($x, $y), الأبعاد=($width x $height)');

      // طباعة معلومات مفصلة عن الصورة للتصحيح
      debugPrint('=== معلومات الصورة المفصلة في _buildMainElement ===');
      debugPrint('الإحداثيات الأصلية: (${element.x}, ${element.y})');
      debugPrint('الإحداثيات المحولة: ($x, $y)');
      debugPrint('الأبعاد الأصلية: (${element.width}x${element.height})');
      debugPrint('الأبعاد المحولة: (${width}x$height)');
      debugPrint('isRelativeCoordinates: ${element.isRelativeCoordinates}');
      debugPrint('isFromAdminPanel: ${element.isFromAdminPanel}');
      debugPrint('نوع العنصر: ${element.type}');
      debugPrint('============================');

      // معالجة خاصة فقط للصور التي تكون صغيرة جدًا
      if (width < 50 || height < 50) {
        debugPrint('صورة صغيرة جدًا - تكبيرها قليلاً');

        // تكبير الصورة قليلاً فقط إذا كانت صغيرة جدًا
        // نحافظ على نسبة الأبعاد الأصلية
        double aspectRatio = width / height;

        if (width < 50) {
          width = 150;
          height = width / aspectRatio;
        }

        if (height < 50) {
          height = 150;
          width = height * aspectRatio;
        }

        debugPrint('تم تكبير الصورة الصغيرة جدًا إلى: (${width}x$height)');
      }
    }

    debugPrint('العنصر يتجاوز حدود البطاقة: $isElementExtendingBeyondBounds');

    if (element.type == ElementType.image ||
        element.type == ElementType.sticker) {
      // طباعة معلومات الصورة للتصحيح
      debugPrint(
          'معلومات الصورة: المحتوى=${element.content.substring(0, math.min(50, element.content.length))}..., '
          'الموقع=($x, $y), '
          'الأبعاد=(${width}x$height)');
    }

    debugPrint('عرض العنصر: ${element.id}, النوع=${element.type}, '
        'الموقع الأصلي=(${element.x}, ${element.y}), '
        'الموقع المحول=($x, $y), '
        'الأبعاد الأصلية=(${element.width}x${element.height}), '
        'الأبعاد المحولة=(${width}x$height), '
        'المقياس=${element.scale}, '
        'الدوران=${element.rotation} ($rotationDegrees درجة)');

    // معالجة جميع العناصر بما فيها التوقيع بنفس الطريقة
    // Basic content of the element wrapped in a gesture detector.
    Widget content = GestureDetector(
      onTap: () {
        context.read<CardEditorBloc>().add(SelectElementEvent(element.id));
      },
      // عرض جميع العناصر بدون قص لضمان ظهورها كاملة
      // هذا يسمح للعناصر بالامتداد خارج حدود البطاقة
      child: _buildElementContent(element),
    );

    // Decorate the element if it is selected.
    Widget decoratedContent;
    if (selectedId != null) {
      if (isSelected) {
        // للصور والملصقات: استخدام حدود مختلفة مع السماح بتجاوز حدود البطاقة
        if (element.type == ElementType.image ||
            element.type == ElementType.sticker) {
          decoratedContent = Stack(
            clipBehavior: Clip.none, // السماح للصور بتجاوز حدود البطاقة
            children: [
              content,
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: Colors.blueAccent, width: 2), // تقليل سمك الحدود
                  ),
                ),
              ),
            ],
          );
        } else {
          decoratedContent = Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              border: Border.all(
                  color: Colors.blueAccent, width: 2), // تقليل سمك الحدود
            ),
            child: content,
          );
        }
      } else {
        // للصور والملصقات: استخدام حدود مختلفة مع السماح بتجاوز حدود البطاقة
        if (element.type == ElementType.image ||
            element.type == ElementType.sticker) {
          decoratedContent = Stack(
            clipBehavior: Clip.none, // السماح للصور بتجاوز حدود البطاقة
            children: [
              content,
              Positioned.fill(
                child: DashedBorderContainer(
                  color: Colors.blue[300] ??
                      Colors.blue, // تغيير اللون ليكون أكثر وضوحاً
                  strokeWidth: 1.0, // تقليل سمك الخط
                  dashWidth: 3,
                  dashSpace: 3,
                  child: const SizedBox.expand(), // إضافة child فارغ
                ),
              ),
            ],
          );
        } else {
          decoratedContent = SizedBox(
            width: width,
            height: height,
            child: DashedBorderContainer(
              color: Colors.blue[300] ??
                  Colors.blue, // تغيير اللون ليكون أكثر وضوحاً
              strokeWidth: 1.0, // تقليل سمك الخط
              dashWidth: 3,
              dashSpace: 3,
              child: content,
            ),
          );
        }
      }
    } else {
      decoratedContent = content;
    }

    // استخدام ManualMoveable لجميع أنواع العناصر
    // هذا يضمن أن جميع العناصر يمكن تحريكها وتغيير حجمها وتدويرها
    // بما في ذلك الصور والملصقات

    // طباعة معلومات العنصر للتصحيح
    debugPrint('=== معلومات العنصر في _buildMainElement ===');
    debugPrint('المعرف: ${element.id}');
    debugPrint('النوع: ${element.type}');
    debugPrint('الإحداثيات: (${element.x}, ${element.y})');
    debugPrint('الأبعاد: (${element.width}x${element.height})');
    debugPrint('isFromAdminPanel: ${element.isFromAdminPanel}');
    debugPrint('isRelativeCoordinates: ${element.isRelativeCoordinates}');
    debugPrint('============================');

    // تحقق مما إذا كانت الإحداثيات نسبية أم مطلقة
    bool isRelative = element.isRelativeCoordinates || (element.width <= 1 && element.height <= 1);

    // طباعة معلومات إضافية للتصحيح
    debugPrint('الإحداثيات نسبية: $isRelative');
    debugPrint('من لوحة التحكم: ${element.isFromAdminPanel}');

    // تحويل الإحداثيات النسبية إلى مطلقة إذا لزم الأمر
    if (isRelative) {
      // تحويل الإحداثيات النسبية (0-1) إلى إحداثيات مطلقة (بالبكسل)
      x = element.x * _cardWidth;
      y = element.y * _cardHeight;
      width = element.width * _cardWidth;
      height = element.height * _cardHeight;

      debugPrint('تحويل الإحداثيات النسبية إلى مطلقة:');
      debugPrint('الإحداثيات النسبية: (${element.x}, ${element.y}), (${element.width}x${element.height})');
      debugPrint('الإحداثيات المطلقة: ($x, $y), (${width}x$height)');
    }

    // استخدام أبعاد العنصر الأصلية مباشرة لجميع أنواع العناصر
    // هذا يضمن أن الإطار يتطابق مع حجم العنصر ويخضع للتكبير والتصغير
    Size actualSize = Size(width, height);

    if (element.type == ElementType.text && (width <= 1 || height <= 1)) {
      actualSize = _calculateOptimalFrameSize(element.content, element.fontSize, element, _cardWidth);
      width = actualSize.width;
      height = actualSize.height;
      
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          context.read<CardEditorBloc>().add(
            UpdateElementSizeEvent(
              element.id,
              isRelative ? width / _cardWidth : width,
              isRelative ? height / _cardHeight : height,
            ),
          );
        }
      });
    }

    // استخدام Positioned بدلاً من SizedBox لتحديد موضع العنصر بدقة
    // هذا يضمن أن الإطار يكون بحجم العنصر فقط وليس بحجم البطاقة
    return Positioned(
      left: x,
      top: y,
      width: actualSize.width,
      height: actualSize.height,
      child: ManualMoveable(
            offset: Offset(x, y),
            size: actualSize,
            rotationAngle: rotationDegrees,
            scale: element.scale,
            showResizeHandles: isSelected && _showOverlays,
            withDragging: true,
            withResizing: true,
            withRotating: true,
            containerWidth: _cardWidth,
            containerHeight: _cardHeight,
            minWidth: 40,
            minHeight: 40,
            isImage: element.type == ElementType.image || element.type == ElementType.sticker,
            onDragUpdate: (newOffset) {
              debugPrint('تحديث موقع العنصر ${element.id}: (${newOffset.dx}, ${newOffset.dy})');

              double newX = newOffset.dx;
              double newY = newOffset.dy;

              bool isRelative = element.isRelativeCoordinates || (element.width <= 1 && element.height <= 1);
              if (isRelative) {
                newX = newOffset.dx / _cardWidth;
                newY = newOffset.dy / _cardHeight;
              }

              context.read<CardEditorBloc>().add(
                UpdateElementPositionEvent(element.id, newX, newY),
              );
            },
            onResizeWithPositionUpdate: (newSize, newOffset) {
              final bloc = context.read<CardEditorBloc>();
              bool isRelative = element.isRelativeCoordinates || (element.width <= 1 && element.height <= 1);

              // للنصوص، حساب حجم الخط الجديد
              if (element.type == ElementType.text) {
                final newFontSize = _calculateFontSizeForFrame(element.content, newSize, element);
                bloc.add(UpdateTextPropsEvent(elementId: element.id, newFontSize: newFontSize));
              }

              // تحديث الحجم والموضع
              double newWidth = newSize.width;
              double newHeight = newSize.height;
              double newX = newOffset.dx;
              double newY = newOffset.dy;

              if (isRelative) {
                newWidth = newSize.width / _cardWidth;
                newHeight = newSize.height / _cardHeight;
                newX = newOffset.dx / _cardWidth;
                newY = newOffset.dy / _cardHeight;
              }

              bloc.add(UpdateElementSizeEvent(element.id, newWidth, newHeight));
              bloc.add(UpdateElementPositionEvent(element.id, newX, newY));
            },
        onRotateUpdate: (newDeg) {
          final newRad = newDeg * math.pi / 180.0;
          debugPrint(
              'تحديث دوران العنصر ${element.id}: $newDeg درجة ($newRad راديان)');
          context.read<CardEditorBloc>().add(
                UpdateElementTransformEvent(
                  elementId: element.id,
                  newRotation: newRad,
                ),
              );
        },
        child: GestureDetector(
          onTap: _showOverlays ? () {
            // إضافة تأثير صوتي عند النقر على العنصر (اختياري)
            // HapticFeedback.lightImpact();

            context.read<CardEditorBloc>().add(SelectElementEvent(element.id));
          } : null, // إلغاء التفاعل عند الحفظ والمعاينة
          child: decoratedContent,
        ),
      ),
    );
  }

  /// Builds a bottom bar for editing QR code properties
  Widget _buildQrCodeEditBar(CardElement element) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Theme.of(context).colorScheme.surface,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Delete button
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'حذف',
            onPressed: () {
              context
                  .read<CardEditorBloc>()
                  .add(RemoveElementEvent(element.id));
            },
          ),

          // Color picker
          IconButton(
            icon: const Icon(Icons.color_lens),
            tooltip: 'تغيير اللون',
            onPressed: () {
              _showColorPicker(element);
            },
          ),
        ],
      ),
    );
  }

  /// Builds a bottom bar for editing signature properties
  Widget _buildSignatureEditBar(CardElement element) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Theme.of(context).colorScheme.surface,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Delete button
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'حذف',
            onPressed: () {
              context
                  .read<CardEditorBloc>()
                  .add(RemoveElementEvent(element.id));
            },
          ),

          // Color picker
          IconButton(
            icon: const Icon(Icons.color_lens),
            tooltip: 'تغيير اللون',
            onPressed: () {
              _showColorPicker(element);
            },
          ),
        ],
      ),
    );
  }

  /// Shows a color picker dialog for changing element color
  void _showColorPicker(CardElement element) {
    // Current color
    Color currentColor =
        element.colorValue != null ? Color(element.colorValue!) : Colors.black;

    // Available colors
    final List<Color> availableColors = [
      Colors.black,
      Colors.white,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.brown,
      Colors.pink,
      Colors.teal,
      Colors.indigo,
      Colors.cyan,
      Colors.amber,
      Colors.grey,
      const Color(0xFF8E24AA), // أرجواني
      const Color(0xFFD81B60), // وردي
    ];

    showDialog<void>(
      context: context,
      builder: (BuildContext context) {

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(AppLocalizations.of(context).chooseColor),
              content: SizedBox(
                width: 300,
                height: 200,
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: availableColors.length,
                  itemBuilder: (context, index) {
                    final color = availableColors[index];
                    final isSelected = currentColor == color;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          currentColor = color;
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected ? Colors.blue : Colors.grey,
                            width: isSelected ? 3 : 1,
                          ),
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 20,
                              )
                            : null,
                      ),
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  child: Text(AppLocalizations.of(context).cancel),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: Text(AppLocalizations.of(context).confirm),
                  onPressed: () {
                    // Update element color
                    final int colorValue = 0xFF000000 |
                        ((currentColor.r * 255).round() << 16) |
                        ((currentColor.g * 255).round() << 8) |
                        (currentColor.b * 255).round();

                    // Close the dialog first
                    Navigator.of(context).pop();

                    // Then update the element in the repository
                    _updateElementColor(element.id, colorValue);
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Updates the color of an element directly in the repository
  Future<void> _updateElementColor(String elementId, int colorValue) async {
    try {
      // Get the CardEditorBloc
      final bloc = context.read<CardEditorBloc>();

      // Get the current elements
      final elements = await bloc.cardRepository.getAllElements();

      // Find the element to update
      final index = elements.indexWhere((e) => e.id == elementId);
      if (index == -1) {
        debugPrint('العنصر غير موجود: $elementId');
        return;
      }

      // Get the element to update
      final oldElement = elements[index];
      debugPrint(
          'تم العثور على العنصر: ${oldElement.id}, النوع=${oldElement.type}, اللون=${oldElement.colorValue}');

      // Create updated element with new color
      final updatedElement = oldElement.copyWith(
        colorValue: colorValue,
      );
      debugPrint(
          'تم تحديث العنصر: ${updatedElement.id}, النوع=${updatedElement.type}, اللون=${updatedElement.colorValue}');

      // Update the element in the repository
      await bloc.cardRepository.updateElement(updatedElement);

      // Add the event to update the element color
      bloc.add(
        UpdateElementPropertiesEvent(
          elementId,
          colorValue: colorValue,
        ),
      );

      // Force a rebuild of the UI if the widget is still mounted
      if (mounted) {
        setState(() {});

        // Show a snackbar to confirm the color change
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم تحديث اللون'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحديث اللون: $e');

      // Show error message if the widget is still mounted
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث اللون: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Builds the inner content of a [CardElement] based on its type.
  Widget _buildElementContent(CardElement e) {
    // تحويل الإحداثيات والأبعاد
    double width = e.width;
    double height = e.height;
    double fontSize = e.fontSize;
    double rotation = 0.0;
    double scale = 1.0;

    // تحقق ما إذا كانت الإحداثيات نسبية أم مطلقة
    bool isRelative = e.isRelativeCoordinates || (e.width <= 1 && e.height <= 1);

    // تحقق ما إذا كان العنصر يتجاوز حدود البطاقة
    bool isElementExtendingBeyondBounds = e.x < 0 ||
        e.y < 0 ||
        e.x + e.width > 1 ||
        e.y + e.height > 1;

    // Remove debug prints to prevent infinite loops

    // معالجة خاصة للصور والملصقات
    if (e.type == ElementType.image || e.type == ElementType.sticker) {

      // استخدام الإحداثيات والأبعاد النهائية إذا كانت متوفرة
      final unifiedElement = e as dynamic;
      if (unifiedElement is UnifiedCardElement &&
          unifiedElement.finalX != null &&
          unifiedElement.finalY != null &&
          unifiedElement.finalWidth != null &&
          unifiedElement.finalHeight != null) {

        // استخدام الإحداثيات والأبعاد النهائية المخزنة في Firestore
        // تحويل من نسبية إلى مطلقة باستخدام أبعاد البطاقة الموحدة (800×1000)

        // معالجة موحدة لجميع العناصر من لوحة التحكم
        // استخدام الإحداثيات والأبعاد النهائية مباشرة
        if (unifiedElement.finalWidth != null && unifiedElement.finalHeight != null) {
          // تحويل الإحداثيات النسبية إلى مطلقة باستخدام الثوابت الموحدة
          final converted = CardConstants.convertRelativeToAbsolute(
            x: unifiedElement.finalX!,
            y: unifiedElement.finalY!,
            width: unifiedElement.finalWidth!,
            height: unifiedElement.finalHeight!,
            isForAdminPanel: false, // نحول للتطبيق
          );

          width = converted['width']!;
          height = converted['height']!;
        } else {
          // إذا لم تكن الإحداثيات النهائية متوفرة، استخدم الإحداثيات الأساسية
          final converted = CardConstants.convertRelativeToAbsolute(
            x: e.x,
            y: e.y,
            width: e.width,
            height: e.height,
            isForAdminPanel: false, // نحول للتطبيق
          );

          width = converted['width']!;
          height = converted['height']!;
        }

        // لا نقوم بأي تعديل على الإحداثيات والأبعاد النهائية
        // نستخدمها كما هي لضمان ظهور العناصر بنفس الحجم والموقع كما تم تصميمها في لوحة التحكم
      } else {
        // معالجة العناصر التي ليس لها إحداثيات وأبعاد نهائية
        // معالجة خاصة للصور
        if (e.type == ElementType.image) {
          // تحقق ما إذا كان العنصر يتجاوز حدود البطاقة
          if (isElementExtendingBeyondBounds) {

            // إذا كانت الصورة صغيرة جداً (أقل من 5% من حجم البطاقة)
            if (e.width < 0.05 || e.height < 0.05) {
              // جعل الصورة تغطي البطاقة كاملة
              width = _cardWidth;
              height = _cardHeight;
            }
            // إذا كانت الصورة كبيرة وتتجاوز حدود البطاقة
            else {
              // نحسب نسبة العرض إلى الارتفاع الأصلية
              double aspectRatio = (e.width > 0 && e.height > 0) ? e.width / e.height : 1.0;

              // استخدام الأبعاد الكاملة للبطاقة مع الحفاظ على نسبة العرض إلى الارتفاع
              width = _cardWidth;
              height = width / aspectRatio;

              // التأكد من أن الصورة لا تتجاوز ارتفاع البطاقة بشكل كبير
              if (height > _cardHeight * 1.2) {
                height = _cardHeight;
                width = height * aspectRatio;
              }
            }
          }
          // حالة 1: صورة صغيرة جداً أو في الزاوية العلوية اليسرى
          else if (width < 100 || height < 100 || (e.x < 0.01 && e.y < 0.01 && e.width < 0.1)) {
            // نحسب نسبة العرض إلى الارتفاع الأصلية
            double aspectRatio = (e.width > 0 && e.height > 0) ? e.width / e.height : 1.0;

            // إذا كانت الصورة في الزاوية العلوية اليسرى وصغيرة جداً، نجعلها تغطي البطاقة كاملة
            if (e.x < 0.01 && e.y < 0.01 && e.width < 0.1) {
              width = _cardWidth;
              height = _cardHeight;
            } else {
              // إذا كانت الصورة صغيرة فقط، نزيد حجمها مع الحفاظ على نسبة العرض إلى الارتفاع
              width = math.max(width, 200.0);
              height = width / aspectRatio;
            }
          }
          // حالة 2: صورة كبيرة
          else if (width > _cardWidth * 0.9 || height > _cardHeight * 0.9) {
            // التأكد من أن الصورة تغطي البطاقة كاملة
            width = _cardWidth;
            height = _cardHeight;
          }
        }
        // معالجة الملصقات
        else if (e.type == ElementType.sticker) {
          // تحقق ما إذا كان العنصر يتجاوز حدود البطاقة
          if (isElementExtendingBeyondBounds) {
            // نحافظ على الإحداثيات والأبعاد الأصلية للملصق الذي يتجاوز حدود البطاقة
            // لضمان ظهوره بنفس الشكل كما تم تصميمه في لوحة التحكم
          }
          // حالة: ملصق صغير جداً
          else if (width < 100 || height < 100) {
            // حساب نسبة الأبعاد الأصلية
            double aspectRatio = (width > 0 && height > 0) ? width / height : 1.0;

            // الملصقات يمكن أن تكون أصغر
            width = math.max(width, 160);
            height = width / aspectRatio;
          }
        }
      }
    }

    // Handle text elements
    if (e.type == ElementType.text) {
      bool isFromAdminPanel = e.isFromAdminPanel;

      // Adjust font size for texts if it's too small
      if (fontSize < 12) {
        fontSize = 16; // Default appropriate font size
      }

      // Apply conversion factor for texts from admin panel
      if (isFromAdminPanel) {
        double scaleFactor = CoordinateSystem.heightScaleFactor;
        fontSize = fontSize * scaleFactor;
      }
    }

    // Return the appropriate widget based on element type
    switch (e.type) {
      case ElementType.text:
        // التحقق من صحة fontSize قبل إنشاء TextStyle
        if (!TransformOptimizer.isValidNumber(fontSize) || fontSize <= 0) {
          debugPrint('Invalid fontSize: $fontSize, using fallback');
          fontSize = 16.0; // قيمة افتراضية آمنة
        }

        // التحقق من صحة letterSpacing و lineHeight
        double? safLetterSpacing = e.letterSpacing;
        if (safLetterSpacing != null && !TransformOptimizer.isValidNumber(safLetterSpacing)) {
          debugPrint('Invalid letterSpacing: $safLetterSpacing, setting to null');
          safLetterSpacing = null;
        }

        double? safeLineHeight = e.lineHeight;
        if (safeLineHeight != null && !TransformOptimizer.isValidNumber(safeLineHeight)) {
          debugPrint('Invalid lineHeight: $safeLineHeight, setting to null');
          safeLineHeight = null;
        }

        // Base text style based on element properties.
        final baseStyle = TextStyle(
          fontSize: fontSize,
          fontWeight: e.isBold ? FontWeight.bold : FontWeight.normal,
          fontStyle: e.isItalic ? FontStyle.italic : FontStyle.normal,
          decoration: e.isUnderline ? TextDecoration.underline : null,
          color: (e.colorValue != null) ? Color(e.colorValue!) : Colors.black,
          letterSpacing: safLetterSpacing,
          height: safeLineHeight,
        );
        // Use a Google font if provided.
        final textStyle = (e.fontFamily != null && e.fontFamily!.isNotEmpty)
            ? GoogleFonts.getFont(e.fontFamily!, textStyle: baseStyle)
            : baseStyle;

        // Display the text exactly as designed in the admin panel
        // Use SizedBox instead of Container to avoid any effect on width
        // Remove all constraints on the text to display it completely

        // Determine text alignment based on text direction
        final bool isArabicText = containsArabicCharacters(e.content);
        final TextAlign textAlign = _convertTextAlign(e.textAlign);

        // التحقق من صحة width و height قبل إنشاء TextPainter
        if (!TransformOptimizer.isValidNumber(width) || !TransformOptimizer.isValidNumber(height) ||
            width <= 0 || height <= 0) {
          return Container(
            width: 100,
            height: 50,
            color: Colors.red.withValues(alpha: 0.3),
            child: const Center(
              child: Text('Error', style: TextStyle(color: Colors.white)),
            ),
          );
        }

        // حساب الأبعاد الفعلية للنص لضمان احتواء الإطار له بالكامل
        final textPainter = TextPainter(
          text: TextSpan(text: e.content, style: textStyle),
          textDirection: isArabicText ? TextDirection.rtl : TextDirection.ltr,
          textAlign: textAlign,
        );

        // تحديد العرض الأقصى للنص بناءً على عرض العنصر
        double maxTextWidth = width - 16; // ترك هامش للحشو

        // التحقق من صحة maxTextWidth قبل استخدام TextPainter
        if (!TransformOptimizer.isValidNumber(maxTextWidth) || maxTextWidth <= 0) {
          maxTextWidth = 100.0; // قيمة افتراضية آمنة
        }

        try {
          textPainter.layout(maxWidth: maxTextWidth);
        } catch (e) {
          return Container(
            width: width,
            height: height,
            color: Colors.red.withValues(alpha: 0.3),
            child: const Center(
              child: Text('Layout Error', style: TextStyle(color: Colors.white)),
            ),
          );
        }

        // استخدام حجم الخط المحدد مباشرة بدون تعديل
        final adjustedTextStyle = textStyle;

        return SizedBox(
          width: width,
          height: height,
          child: Container(
            width: width,
            height: height,
            padding: EdgeInsets.symmetric(
              horizontal: width * 0.05, // 5% من العرض
              vertical: height * 0.08,   // 8% من الارتفاع
            ),
            decoration: BoxDecoration(
              color: Colors.transparent,
            ),
            child: Text(
              e.content,
              style: adjustedTextStyle,
              textAlign: textAlign,
              textDirection: isArabicText ? TextDirection.rtl : TextDirection.ltr,
              softWrap: true,
              maxLines: null,
            ),
          ),
        );
      case ElementType.sticker:
        // تحميل الملصق من الأصول أو الشبكة أو الملف
        Widget stickerWidget;

        // استخدام BoxFit.contain للملصقات للتأكد من ظهورها بشكل كامل داخل المساحة المخصصة
        // مع الحفاظ على نسبة العرض إلى الارتفاع الأصلية
        final BoxFit fitMode = BoxFit.contain;

        if (e.content.startsWith('assets/')) {
          stickerWidget = SizedBox(
            width: width,
            height: height,
            child: Image.asset(
              e.content,
              width: width,
              height: height,
              fit: fitMode,
              errorBuilder: (context, error, stackTrace) {
                return const Center(
                  child: Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 40,
                  ),
                );
              },
            ),
          );
        } else if (e.content.startsWith('http')) {
          // استخدام CachedNetworkImage لتحسين أداء تحميل الصور
          stickerWidget = SizedBox(
            width: width,
            height: height,
            child: Image.network(
              e.content,
              width: width,
              height: height,
              fit: fitMode,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return const Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return const Center(
                  child: Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 40,
                  ),
                );
              },
            ),
          );
        } else {
          final file = File(e.content);
          stickerWidget = SizedBox(
            width: width,
            height: height,
            child: Image.file(
              file,
              width: width,
              height: height,
              fit: fitMode,
              errorBuilder: (context, error, stackTrace) {
                return const Center(
                  child: Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 40,
                  ),
                );
              },
            ),
          );
        }

        // تطبيق زاوية الدوران إذا كانت متوفرة
        if (rotation != 0.0) {
          debugPrint('تطبيق زاوية الدوران للملصق: $rotation');
          return Transform.rotate(
            angle: rotation * math.pi / 180, // تحويل من درجة إلى راديان
            child: stickerWidget,
          );
        }

        return stickerWidget;
      case ElementType.image:
        // تحميل الصورة من الشبكة أو الملف
        Widget imageWidget;

        // تحديد BoxFit المناسب للصورة
        BoxFit fitMode;

        // تحديد BoxFit المناسب للصور بناءً على خصائصها
        // استخدام BoxFit.cover للصور التي تغطي البطاقة كاملة أو تتجاوز حدودها
        // استخدام BoxFit.contain للصور الصغيرة التي لا تتجاوز حدود البطاقة

        // تحديد BoxFit المناسب للصور من لوحة التحكم
        if (e.isFromAdminPanel) {
          // للصور من لوحة التحكم، نستخدم BoxFit.fill لضمان ملء المساحة المحددة بالضبط
          fitMode = BoxFit.fill;
          debugPrint('استخدام BoxFit.fill للصورة من لوحة التحكم لضمان ظهورها بنفس الحجم والموقع');
        } else {
          // للصور المضافة يدوياً في التطبيق
          if (isElementExtendingBeyondBounds) {
            // الصور التي تتجاوز حدود البطاقة
            if (e.width < 0.05 || e.height < 0.05) {
              // الصور الصغيرة جداً التي تتجاوز حدود البطاقة
              fitMode = BoxFit.cover;
              debugPrint('استخدام BoxFit.cover للصورة الصغيرة جداً التي تتجاوز حدود البطاقة');
            } else {
              // الصور الكبيرة التي تتجاوز حدود البطاقة
              fitMode = BoxFit.fill;
              debugPrint('استخدام BoxFit.fill للصورة الكبيرة التي تتجاوز حدود البطاقة');
            }
          }
          // معالجة الصور الكبيرة أو في الزاوية العلوية اليسرى
          else if (e.width >= 0.9 || e.height >= 0.9 || (e.x <= 0.01 && e.y <= 0.01)) {
            // صورة كبيرة أو في الزاوية العلوية اليسرى
            fitMode = BoxFit.cover;
            debugPrint('استخدام BoxFit.cover للصورة لضمان تغطية كاملة للمساحة المخصصة');
          }
          // معالجة الصور الصغيرة داخل حدود البطاقة
          else {
            // صورة صغيرة داخل حدود البطاقة
            fitMode = BoxFit.contain;
            debugPrint('استخدام BoxFit.contain للصورة للحفاظ على نسبة العرض إلى الارتفاع');
          }
        }

        // طباعة معلومات عن الصورة النهائية
        debugPrint('الصورة النهائية: (${width}x$height), BoxFit: $fitMode');

        if (e.content.startsWith('http')) {
          // تحسين عرض الصور من الإنترنت
          // استخدام ClipRect لقص الصور التي تتجاوز حدود البطاقة
          // معالجة خاصة للصور من لوحة التحكم
          if (e.isFromAdminPanel) {
            // للصور من لوحة التحكم، نستخدم SizedBox بدون ClipRect لضمان ظهورها بنفس الحجم والموقع
            imageWidget = SizedBox(
              width: width,
              height: height,
              child: Image.network(
                e.content,
                fit: fitMode,
                width: width,
                height: height,
                alignment: Alignment.center,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red[300],
                          size: 40,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          AppLocalizations.of(context).errorSavingImage,
                          style: TextStyle(
                            color: Colors.red[300],
                            fontSize: fontSize > 20 ? fontSize / 2 : fontSize,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
            debugPrint('استخدام SizedBox بدون ClipRect للصورة من لوحة التحكم');
          }
          // معالجة الصور التي تتجاوز حدود البطاقة
          else if (isElementExtendingBeyondBounds) {
            // للصور التي تتجاوز حدود البطاقة، نستخدم ClipRect
            imageWidget = SizedBox(
              width: width,
              height: height,
              child: Image.network(
                e.content,
                fit: fitMode,
                width: width,
                height: height,
                alignment: Alignment.center,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red[300],
                          size: 40,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'خطأ في تحميل الصورة',
                          style: TextStyle(
                            color: Colors.red[300],
                            fontSize: fontSize > 20 ? fontSize / 2 : fontSize,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
            debugPrint('استخدام ClipRect للصورة التي تتجاوز حدود البطاقة');
          } else {
            // للصور العادية
            imageWidget = SizedBox(
              width: width,
              height: height,
              child: Image.network(
                e.content,
                fit: fitMode,
                width: width,
                height: height,
                alignment: Alignment.center,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red[300],
                          size: 40,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'خطأ في تحميل الصورة',
                          style: TextStyle(
                            color: Colors.red[300],
                            fontSize: fontSize > 20 ? fontSize / 2 : fontSize,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          }
        } else {
          // تحسين عرض الصور من الملفات المحلية
          final file = File(e.content);

          // معالجة خاصة للصور من لوحة التحكم
          if (e.isFromAdminPanel) {
            // للصور من لوحة التحكم، نستخدم SizedBox بدون ClipRect لضمان ظهورها بنفس الحجم والموقع
            imageWidget = SizedBox(
              width: width,
              height: height,
              child: Image.file(
                file,
                width: width,
                height: height,
                fit: fitMode,
                alignment: Alignment.center,
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red[300],
                          size: 40,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'خطأ في تحميل الصورة',
                          style: TextStyle(
                            color: Colors.red[300],
                            fontSize: fontSize > 20 ? fontSize / 2 : fontSize,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
            debugPrint('استخدام SizedBox بدون ClipRect للصورة المحلية من لوحة التحكم');
          }
          // معالجة الصور التي تتجاوز حدود البطاقة
          else if (isElementExtendingBeyondBounds) {
            // للصور التي تتجاوز حدود البطاقة، نستخدم ClipRect
            imageWidget = ClipRect(
              child: SizedBox(
                width: width,
                height: height,
                child: Image.file(
                  file,
                  width: width,
                  height: height,
                  fit: fitMode,
                  alignment: Alignment.center,
                  errorBuilder: (context, error, stackTrace) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red[300],
                            size: 40,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'خطأ في تحميل الصورة',
                            style: TextStyle(
                              color: Colors.red[300],
                              fontSize: fontSize > 20 ? fontSize / 2 : fontSize,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            );
            debugPrint('استخدام ClipRect للصورة المحلية التي تتجاوز حدود البطاقة');
          } else {
            // للصور العادية
            imageWidget = SizedBox(
              width: width,
              height: height,
              child: Image.file(
                file,
                width: width,
                height: height,
                fit: fitMode,
                alignment: Alignment.center,
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red[300],
                          size: 40,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'خطأ في تحميل الصورة',
                          style: TextStyle(
                            color: Colors.red[300],
                            fontSize: fontSize > 20 ? fontSize / 2 : fontSize,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          }
        }

        // طباعة معلومات الصورة النهائية
        debugPrint('الصورة النهائية: (${width}x$height), BoxFit: $fitMode');

        // تطبيق زاوية الدوران إذا كانت متوفرة
        if (rotation != 0.0) {
          debugPrint('تطبيق زاوية الدوران: $rotation');
          return Transform.rotate(
            angle: rotation * math.pi / 180, // تحويل من درجة إلى راديان
            child: imageWidget,
          );
        }

        return imageWidget;

      case ElementType.qrCode:
        return SizedBox(
          width: width,
          height: height,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // QR Code - without background or shadow
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(2.0),
                  child: QrImageView(
                    data: e.content,
                    version: QrVersions.auto,
                    size: width - 10, // Smaller padding for better visibility
                    backgroundColor: Colors.transparent,
                    padding: EdgeInsets.zero, // Remove padding
                    eyeStyle: QrEyeStyle(
                      eyeShape: QrEyeShape.square,
                      color: e.colorValue != null
                          ? Color(e.colorValue!)
                          : Colors.black,
                    ),
                    dataModuleStyle: QrDataModuleStyle(
                      dataModuleShape: QrDataModuleShape.square,
                      color: e.colorValue != null
                          ? Color(e.colorValue!)
                          : Colors.black,
                    ),
                    errorStateBuilder: (context, error) {
                      debugPrint('Error creating QR code: ${e.content} - $error');
                      return Center(
                        child: Text(
                          'Error in QR code',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: fontSize > 20 ? fontSize / 2 : fontSize,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // Optional title
              if (e.fontFamily != null && e.fontFamily!.isNotEmpty)
                Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(33, 150, 243, 0.1),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Text(
                    e.fontFamily!,
                    style: TextStyle(
                      fontSize: e.width > 120 ? 12 : 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
            ],
          ),
        );

      case ElementType.signature:
        // For drawn signatures
        return SizedBox(
          width: width,
          height: height,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Signature image without background
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Center(
                    // Use a simplified but effective approach for signature rendering
                    child: RepaintBoundary(
                      child: ColorFiltered(
                        colorFilter: ColorFilter.mode(
                          e.colorValue != null
                              ? Color(e.colorValue!)
                              : Colors.black,
                          BlendMode.srcIn,
                        ),
                        child: Image.memory(
                          base64Decode(e.content),
                          fit: BoxFit.contain,
                          // Disable cache to avoid rendering issues
                          cacheWidth: null,
                          cacheHeight: null,
                          // Use gaplessPlayback to avoid flickering
                          gaplessPlayback: true,
                          errorBuilder: (context, error, stackTrace) {
                            debugPrint('Error loading signature: $error');
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    color: Colors.red,
                                    size: 40,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Error in signature',
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontSize: fontSize > 20
                                          ? fontSize / 2
                                          : fontSize,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // Optional signature text
              if (e.fontFamily != null && e.fontFamily!.isNotEmpty)
                Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Text(
                    e.fontFamily!,
                    style: TextStyle(
                      fontSize: e.width > 120 ? 12 : 10,
                      fontStyle: FontStyle.italic,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
            ],
          ),
        );
    }
  }

  /// Converts a text alignment string to [TextAlign].
  TextAlign _convertTextAlign(String? textAlign) {
    if (textAlign == null) {
      return TextAlign.left;
    }

    // Convert text alignment correctly

    // Convert text alignment correctly
    // Handle different cases of text alignment values
    if (textAlign.contains('center') || textAlign == 'TextAlign.center') {
      return TextAlign.center;
    } else if (textAlign.contains('right') || textAlign == 'TextAlign.right') {
      return TextAlign.right;
    } else if (textAlign.contains('justify') ||
        textAlign == 'TextAlign.justify') {
      return TextAlign.justify;
    } else if (textAlign.contains('left') || textAlign == 'TextAlign.left') {
      return TextAlign.left;
    } else if (textAlign.contains('start') || textAlign == 'TextAlign.start') {
      return TextAlign.start;
    } else if (textAlign.contains('end') || textAlign == 'TextAlign.end') {
      return TextAlign.end;
    } else {
      // Default value
      return TextAlign.left;
    }
  }

  /// Check if the text contains Arabic characters
  bool containsArabicCharacters(String text) {
    // Range of Arabic characters in Unicode
    // From 0x0600 to 0x06FF (Basic Arabic character range)
    // From 0x0750 to 0x077F (Arabic Extended-A)
    // From 0x08A0 to 0x08FF (Arabic Extended-B)
    // From 0xFB50 to 0xFDFF (Arabic Presentation Forms-A)
    // From 0xFE70 to 0xFEFF (Arabic Presentation Forms-B)

    final arabicRegExp = RegExp(
        r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return arabicRegExp.hasMatch(text);
  }
}

/// CustomPainter لمعاينة التوقيع المرسوم
class SignaturePreviewPainter extends CustomPainter {
  final ui.Image image;

  SignaturePreviewPainter(this.image);

  @override
  void paint(Canvas canvas, Size size) {
    // حساب النسبة للحفاظ على نسبة العرض إلى الارتفاع
    final double imageAspectRatio = image.width / image.height;
    final double canvasAspectRatio = size.width / size.height;

    double drawWidth, drawHeight;
    double offsetX = 0, offsetY = 0;

    if (imageAspectRatio > canvasAspectRatio) {
      // الصورة أعرض من الكانفاس
      drawWidth = size.width;
      drawHeight = size.width / imageAspectRatio;
      offsetY = (size.height - drawHeight) / 2;
    } else {
      // الصورة أطول من الكانفاس
      drawHeight = size.height;
      drawWidth = size.height * imageAspectRatio;
      offsetX = (size.width - drawWidth) / 2;
    }

    final Rect srcRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final Rect dstRect = Rect.fromLTWH(offsetX, offsetY, drawWidth, drawHeight);

    canvas.drawImageRect(image, srcRect, dstRect, Paint());
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}
