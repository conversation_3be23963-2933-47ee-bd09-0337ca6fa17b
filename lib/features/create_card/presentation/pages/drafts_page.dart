import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';
import '../../data/models/card_element_model.dart';
import '../../data/repositories_impl/card_repository_impl.dart';
import '../../domain/entities/card_element.dart';
import '../../domain/usecases/add_image_element_usecase.dart';
import '../../domain/usecases/add_sticker_element_usecase.dart';
import '../../domain/usecases/add_text_element_usecase.dart';
import '../../domain/usecases/remove_element_usecase.dart';
import '../../domain/usecases/update_element_position_usecase.dart';
import '../../domain/usecases/update_text_properties_usecase.dart';
import '../bloc/card_editor_bloc.dart';
import 'card_editor_page.dart';

/// A page that displays saved drafts for the card editor.
/// Drafts are stored as JSON files in the device's local storage.
class DraftsPage extends StatefulWidget {
  const DraftsPage({super.key});

  @override
  State<DraftsPage> createState() => _DraftsPageState();
}

class _DraftsPageState extends State<DraftsPage> {
  // List to hold the file system entities representing saved drafts.
  List<FileSystemEntity> _draftFiles = [];

  @override
  void initState() {
    super.initState();
    _loadDrafts();
  }

  /// Loads draft files from the 'drafts' directory in the application documents directory.
  Future<void> _loadDrafts() async {
    final directory = await getApplicationDocumentsDirectory();
    final draftsDir = Directory('${directory.path}/drafts');
    if (await draftsDir.exists()) {
      setState(() {
        _draftFiles = draftsDir.listSync();
      });
    }
  }

  /// Reads the contents of a draft file and decodes it into a Map.
  Future<Map<String, dynamic>?> _readDraft(File file) async {
    try {
      final content = await file.readAsString();
      return jsonDecode(content);
    } catch (e) {
      // Return null if there's an error reading or decoding the file.
      return null;
    }
  }

  /// Parses a list of JSON objects into a list of [CardElement] objects.
  /// Uses safe parsing to handle type conversion issues.
  List<CardElement> _parseElements(List<dynamic> jsonList) {
    List<CardElement> elements = [];
    for (var item in jsonList) {
      try {
        if (item is Map<String, dynamic>) {
          final element = CardElementModel.fromJson(item);
          elements.add(element);
        }
      } catch (e) {
        debugPrint('Error parsing element: $e');
        debugPrint('Element data: $item');
        // Skip this element and continue with others
      }
    }
    return elements;
  }

  /// Deletes a draft file from the device and updates the list of drafts.
  Future<void> _deleteDraft(File file) async {
    try {
      if (await file.exists()) {
        await file.delete();
      }
      // Refresh the drafts list after deletion.
      _loadDrafts();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Draft deleted.')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting draft: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Drafts'),
      ),
      body: _draftFiles.isEmpty
          ? const Center(child: Text('No drafts saved.'))
          : ListView.builder(
              itemCount: _draftFiles.length,
              itemBuilder: (context, index) {
                final file = _draftFiles[index] as File;
                return FutureBuilder<Map<String, dynamic>?>(
                  future: _readDraft(file),
                  builder: (context, snapshot) {
                    if (snapshot.hasData && snapshot.data != null) {
                      try {
                        final data = snapshot.data!;
                        final timestamp = data['timestamp'] as int? ??
                                         DateTime.now().millisecondsSinceEpoch;

                        // Parse the background color safely
                        final backgroundColorValue = int.tryParse(
                                data['backgroundColor']?.toString() ?? 'FFFFFF',
                                radix: 16) ??
                            0xFFFFFFFF; // White color value

                        final elementsJson = data['elements'] as List<dynamic>? ?? [];
                        final elements = _parseElements(elementsJson);

                        return Dismissible(
                        key: ValueKey(file.path),
                        direction: DismissDirection
                            .startToEnd, // Swipe right to delete.
                        background: Container(
                          color: Colors.red,
                          alignment: Alignment.centerLeft,
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: const Icon(
                            Icons.delete,
                            color: Colors.white,
                          ),
                        ),
                        onDismissed: (direction) {
                          _deleteDraft(file);
                        },
                        child: Card(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          child: ListTile(
                            title: Text(
                                'Draft saved on ${DateTime.fromMillisecondsSinceEpoch(timestamp)}'),
                            subtitle:
                                Text('Number of elements: ${elements.length}'),
                            trailing: const Icon(Icons.arrow_forward),
                            onTap: () {
                              // Open the draft in the card editor.
                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                  builder: (_) => BlocProvider<CardEditorBloc>(
                                    create: (context) {
                                      // Create the repository and use case instances.
                                      final repository = CardRepositoryImpl();
                                      // Set the draft elements into the repository.
                                      repository.setElements(elements);
                                      return CardEditorBloc(
                                        cardRepository: repository,
                                        addTextElementUseCase:
                                            AddTextElementUseCase(repository),
                                        addStickerElementUseCase:
                                            AddStickerElementUseCase(
                                                repository),
                                        addImageElementUseCase:
                                            AddImageElementUseCase(repository),
                                        removeElementUseCase:
                                            RemoveElementUseCase(repository),
                                        updateElementPositionUseCase:
                                            UpdateElementPositionUseCase(
                                                repository),
                                        updateTextPropertiesUseCase:
                                            UpdateTextPropertiesUseCase(
                                                repository),
                                      );
                                    },
                                    child: CardEditorPage(
                                      initialElements: elements,
                                      initialBackgroundColor:
                                          Color(backgroundColorValue),
                                      draftFilePath: file.path,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      );
                      } catch (e) {
                        debugPrint('Error loading draft: $e');
                        return ListTile(
                          title: const Text('Error loading draft'),
                          subtitle: Text('Error: $e'),
                          leading: const Icon(Icons.error, color: Colors.red),
                        );
                      }
                    } else {
                      return const ListTile(
                        title: Text('Loading draft...'),
                      );
                    }
                  },
                );
              },
            ),
    );
  }
}
