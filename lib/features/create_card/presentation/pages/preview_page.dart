import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';

/// Enum representing the type of preview to display: either an image or a PDF.
enum PreviewType { image, pdf }

/// A page that previews a file (either an image or a PDF) with a confetti animation overlay.
class PreviewPage extends StatefulWidget {
  /// The file path of the image or PDF to preview.
  final String filePath;

  /// The type of preview to display.
  final PreviewType previewType;

  const PreviewPage({
    super.key,
    required this.filePath,
    required this.previewType,
  });

  @override
  State<PreviewPage> createState() => _PreviewPageState();
}

class _PreviewPageState extends State<PreviewPage> {
  /// Controller for managing the confetti animation.
  // Confetti controller removed

  @override
  void initState() {
    super.initState();
    // Confetti initialization removed
  }

  @override
  void dispose() {
    // Confetti disposal removed
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Determine the preview content based on the preview type.
    Widget content;
    if (widget.previewType == PreviewType.image) {
      // Display the image file.
      content = Image.file(File(widget.filePath));
    } else {
      // Display the PDF file
      content = PDFView(
        filePath: widget.filePath,
        enableSwipe: true,
        swipeHorizontal: false,
        autoSpacing: false,
        pageFling: false,
        onRender: (pages) {
          debugPrint('PDF rendered with $pages pages');
        },
        onError: (error) {
          debugPrint('PDF error: $error');
        },
        onPageError: (page, error) {
          debugPrint('PDF page $page error: $error');
        },
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('معاينة'), // "Preview" in Arabic.
      ),
      body: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Stack(
          children: [
            // Center the preview content.
            Center(child: content),
            // Confetti animation removed
          ],
        ),
      ),
    );
  }
}
