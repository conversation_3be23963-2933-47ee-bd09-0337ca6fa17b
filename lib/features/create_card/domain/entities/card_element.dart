// lib/features/create_card/domain/entities/card_element.dart

/// Enum representing the different types of card elements.
enum ElementType {
  text, // Represents a text element.
  sticker, // Represents a sticker element.
  image, // Represents an image element.
  qrCode, // Represents a QR code element.
  signature, // Represents a signature element.
}

/// Entity representing a card element with various properties such as position,
/// dimensions, text styling attributes, and transformation properties.
class CardElement {
  /// Unique identifier for the card element.
  final String id;

  /// The type of the card element (text, sticker, image).
  final ElementType type;

  /// The content of the element, which can be text or a path to a sticker/image.
  final String content;

  /// X-coordinate position of the element.
  final double x;

  /// Y-coordinate position of the element.
  final double y;

  /// Width of the element.
  final double width;

  /// Height of the element.
  final double height;

  // Text styling properties:

  /// The font size of the text.
  final double fontSize;

  /// Indicates whether the text is bold.
  final bool isBold;

  /// Indicates whether the text is italicized.
  final bool isItalic;

  /// Indicates whether the text is underlined.
  final bool isUnderline;

  // Additional optional properties for text customization:

  /// The font family used for the text.
  final String? fontFamily;

  /// The ARGB color value of the text, stored as an integer.
  final int? colorValue;

  /// The spacing between letters in the text.
  final double? letterSpacing;

  /// The line height (spacing between lines) for the text.
  final double? lineHeight;

  /// The alignment of the text (e.g., "left", "center", "right", "justify").
  final String? textAlign;

  // Transformation properties:

  /// The scale factor for resizing the element.
  final double scale;

  /// The rotation angle of the element in degrees.
  final double rotation;

  /// Indicates whether the coordinates are relative (0-1) or absolute (pixels)
  final bool isRelativeCoordinates;

  /// Indicates whether the element is from the admin panel
  /// This is used to apply the scale factor between admin panel and mobile app
  final bool isFromAdminPanel;

  /// الموقع الأفقي النهائي بعد التعديل (نسبي من 0 إلى 1)
  final double? finalX;

  /// الموقع الرأسي النهائي بعد التعديل (نسبي من 0 إلى 1)
  final double? finalY;

  /// العرض النهائي بعد التعديل (نسبي من 0 إلى 1)
  final double? finalWidth;

  /// الارتفاع النهائي بعد التعديل (نسبي من 0 إلى 1)
  final double? finalHeight;

  /// Creates a [CardElement] instance with the provided properties.
  ///
  /// Required properties: [id], [type], [content], [x], [y], [width], and [height].
  /// Optional properties include text styling attributes, additional text properties,
  /// and transformation parameters with default values.
  const CardElement({
    required this.id,
    required this.type,
    required this.content,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.fontSize = 16,
    this.isBold = false,
    this.isItalic = false,
    this.isUnderline = false,
    this.fontFamily,
    this.colorValue,
    this.letterSpacing,
    this.lineHeight,
    this.textAlign = 'center', // توسيط النص كتنسيق افتراضي
    this.scale = 1.0,
    this.rotation = 0.0,
    this.isRelativeCoordinates = false,
    this.isFromAdminPanel = false,
    this.finalX,
    this.finalY,
    this.finalWidth,
    this.finalHeight,
  });

  /// Creates a copy of this [CardElement] with updated property values.
  ///
  /// If a property is not provided, the current value is retained.
  CardElement copyWith({
    String? id,
    ElementType? type,
    String? content,
    double? x,
    double? y,
    double? width,
    double? height,
    double? fontSize,
    bool? isBold,
    bool? isItalic,
    bool? isUnderline,
    String? fontFamily,
    int? colorValue,
    double? letterSpacing,
    double? lineHeight,
    String? textAlign,
    double? scale,
    double? rotation,
    bool? isRelativeCoordinates,
    bool? isFromAdminPanel,
    double? finalX,
    double? finalY,
    double? finalWidth,
    double? finalHeight,
  }) {
    return CardElement(
      id: id ?? this.id,
      type: type ?? this.type,
      content: content ?? this.content,
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      fontSize: fontSize ?? this.fontSize,
      isBold: isBold ?? this.isBold,
      isItalic: isItalic ?? this.isItalic,
      isUnderline: isUnderline ?? this.isUnderline,
      fontFamily: fontFamily ?? this.fontFamily,
      colorValue: colorValue ?? this.colorValue,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineHeight: lineHeight ?? this.lineHeight,
      textAlign: textAlign ?? this.textAlign,
      scale: scale ?? this.scale,
      rotation: rotation ?? this.rotation,
      isRelativeCoordinates:
          isRelativeCoordinates ?? this.isRelativeCoordinates,
      isFromAdminPanel: isFromAdminPanel ?? this.isFromAdminPanel,
      finalX: finalX ?? this.finalX,
      finalY: finalY ?? this.finalY,
      finalWidth: finalWidth ?? this.finalWidth,
      finalHeight: finalHeight ?? this.finalHeight,
    );
  }
}
