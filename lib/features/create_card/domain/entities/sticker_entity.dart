/// نموذج للملصق
class StickerEntity {
  /// معرف الملصق
  final String id;

  /// رابط صورة الملصق
  final String imageUrl;

  /// فئة الملصق (المناسبة)
  final String category;

  /// تاريخ إنشاء الملصق
  final String createdAt;

  /// إنشاء نموذج جديد للملصق
  const StickerEntity({
    required this.id,
    required this.imageUrl,
    required this.category,
    required this.createdAt,
  });

  /// إنشاء نموذج من خريطة
  factory StickerEntity.fromMap(String id, Map<String, dynamic> map) {
    return StickerEntity(
      id: id,
      imageUrl: map['imageUrl'] ?? '',
      category: map['category'] ?? '',
      createdAt: map['createdAt'] ?? '',
    );
  }
}
