// lib/features/create_card/domain/usecases/update_text_properties_usecase.dart

import '../entities/card_element.dart';
import '../repositories/card_repository.dart';

/// A use case for updating the text properties of a card element.
/// This use case allows updating various text properties such as content, font size,
/// and styling options (bold, italic, underline) for elements of type [ElementType.text].
class UpdateTextPropertiesUseCase {
  /// The repository used to manage card elements.
  final CardRepository repository;

  /// Creates an instance of [UpdateTextPropertiesUseCase] with the provided [repository].
  UpdateTextPropertiesUseCase(this.repository);

  /// Executes the use case to update the text properties of a specific card element.
  ///
  /// The [elementId] parameter identifies the card element to be updated.
  /// Optional parameters include [newContent], [newFontSize], [isBold], [isItalic],
  /// [isUnderline], [fontFamily], [colorValue], [letterSpacing], [lineHeight], and [textAlign].
  /// If a property is not provided, the current value is retained.
  ///
  /// If the target element is not found or is not of type text, the update is ignored.
  Future<void> call({
    required String elementId,
    String? newContent,
    double? newFontSize,
    bool? isBold,
    bool? isItalic,
    bool? isUnderline,
    String? fontFamily,
    int? colorValue,
    double? letterSpacing,
    double? lineHeight,
    String? textAlign,
  }) async {
    // Retrieve all card elements from the repository.
    final elements = await repository.getAllElements();

    // Find the index of the element with the specified elementId.
    final index = elements.indexWhere((element) => element.id == elementId);

    // If the element is not found, exit the function.
    if (index == -1) return;

    // Retrieve the target element.
    final oldElement = elements[index];

    // If the target element is not a text element, ignore the update.
    if (oldElement.type != ElementType.text) {
      // If the element is not of type text, do nothing.
      return;
    }

    // Create a new element with updated text properties using the copyWith method.
    // If a new value is not provided for a property, the existing value is retained.
    final updated = oldElement.copyWith(
      content: newContent ?? oldElement.content,
      fontSize: newFontSize ?? oldElement.fontSize,
      isBold: isBold ?? oldElement.isBold,
      isItalic: isItalic ?? oldElement.isItalic,
      isUnderline: isUnderline ?? oldElement.isUnderline,
      fontFamily: fontFamily ?? oldElement.fontFamily,
      colorValue: colorValue ?? oldElement.colorValue,
      letterSpacing: letterSpacing ?? oldElement.letterSpacing,
      lineHeight: lineHeight ?? oldElement.lineHeight,
      textAlign: textAlign ?? oldElement.textAlign,
    );

    // Update the repository with the modified element.
    await repository.updateElement(updated);
  }
}
