import 'package:uuid/uuid.dart';

import '../entities/card_element.dart';
import '../repositories/card_repository.dart';

/// A use case for adding a new image element to a card.
/// This use case creates a new [CardElement] of type [ElementType.image] using the provided image path or URL,
/// and then adds it to the repository.
class AddImageElementUseCase {
  /// The repository used to manage card elements.
  final CardRepository repository;

  /// A UUID generator instance to create unique IDs for new card elements.
  final Uuid uuid = const Uuid();

  /// Creates an instance of [AddImageElementUseCase] with the given [repository].
  AddImageElementUseCase(this.repository);

  /// Executes the use case to add a new image element.
  ///
  /// The [imagePathOrUrl] parameter represents the path or URL of the image.
  /// The new element is created with default positioning and dimensions,
  /// and then it is added to the repository.
  Future<void> call(String imagePathOrUrl) async {
    // استخدام إحداثيات نسبية (0-1) بدلاً من إحداثيات مطلقة
    // هذا يضمن أن العناصر تظهر بشكل صحيح بغض النظر عن حجم البطاقة

    // وضع العنصر في وسط البطاقة
    // حساب الإحداثيات بحيث يكون العنصر في المنتصف
    final double elementWidth = 0.4;  // عرض العنصر النسبي (40% من عرض البطاقة)
    final double elementHeight = 0.4; // ارتفاع العنصر النسبي (40% من ارتفاع البطاقة)

    // حساب الإحداثيات لوضع العنصر في المنتصف
    final double centerX = 0.5 - (elementWidth / 2); // وسط البطاقة أفقياً
    final double centerY = 0.5 - (elementHeight / 2); // وسط البطاقة رأسياً

    final newElement = CardElement(
      id: uuid.v4(),
      type: ElementType.image,
      content: imagePathOrUrl,
      x: centerX,                 // Center x-coordinate position (relative 0-1).
      y: centerY,                 // Center y-coordinate position (relative 0-1).
      width: elementWidth,        // Default width of the image element (relative 0-1).
      height: elementHeight,      // Default height of the image element (relative 0-1).
      isRelativeCoordinates: true, // Use relative coordinates (0-1).
      isFromAdminPanel: false,     // This element is created manually in the app.
    );

    await repository.addElement(newElement);
  }
}
