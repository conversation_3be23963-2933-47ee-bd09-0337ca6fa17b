import 'package:uuid/uuid.dart';

import '../entities/card_element.dart';
import '../repositories/card_repository.dart';

/// A use case for adding a new sticker element to a card.
/// This use case creates a new [CardElement] of type [ElementType.sticker] using the provided sticker URL,
/// and then adds it to the repository.
class AddStickerElementUseCase {
  /// The repository used to manage card elements.
  final CardRepository repository;

  /// A UUID generator instance to create unique IDs for new card elements.
  final Uuid uuid = const Uuid();

  /// Creates an instance of [AddStickerElementUseCase] with the given [repository].
  AddStickerElementUseCase(this.repository);

  /// Executes the use case to add a new sticker element.
  ///
  /// The [stickerUrl] parameter represents the URL of the sticker.
  /// The new element is created with default positioning and dimensions,
  /// and then it is added to the repository.
  Future<void> call(String stickerUrl) async {
    // استخدام إحداثيات نسبية (0-1) بدلاً من إحداثيات مطلقة
    // هذا يضمن أن العناصر تظهر بشكل صحيح بغض النظر عن حجم البطاقة

    // وضع العنصر في وسط البطاقة مع أبعاد محسنة للملصقات
    // حساب الإحداثيات بحيث يكون العنصر في المنتصف تماماً
    final double elementWidth = 0.25;  // عرض العنصر النسبي (25% من عرض البطاقة) - محسن للملصقات
    final double elementHeight = 0.25; // ارتفاع العنصر النسبي (25% من ارتفاع البطاقة) - محسن للملصقات

    // حساب الإحداثيات لوضع العنصر في وسط البطاقة تماماً
    final double centerX = 0.5 - (elementWidth / 2); // وسط البطاقة أفقياً (37.5% من اليسار)
    final double centerY = 0.5 - (elementHeight / 2); // وسط البطاقة رأسياً (37.5% من الأعلى)

    final newElement = CardElement(
      id: uuid.v4(),              // Generates a unique ID for the new sticker element.
      type: ElementType.sticker,  // Sets the element type to sticker.
      content: stickerUrl,        // Sets the content to the provided sticker URL.
      x: centerX,                 // Center x-coordinate position (relative 0-1).
      y: centerY,                 // Center y-coordinate position (relative 0-1).
      width: elementWidth,        // Default width of the sticker element (relative 0-1).
      height: elementHeight,      // Default height of the sticker element (relative 0-1).
      isRelativeCoordinates: true, // Use relative coordinates (0-1).
      isFromAdminPanel: false,     // This element is created manually in the app.
    );

    await repository.addElement(newElement);
  }
}
