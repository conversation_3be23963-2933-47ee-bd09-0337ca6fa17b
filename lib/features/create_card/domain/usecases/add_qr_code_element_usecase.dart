// lib/features/create_card/domain/usecases/add_qr_code_element_usecase.dart

import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../entities/card_element.dart';
import '../repositories/card_repository.dart';

/// Use case for adding a QR code element to a card.
class AddQrCodeElementUseCase {
  /// The repository that manages card elements.
  final CardRepository repository;

  /// UUID generator for creating unique element IDs.
  final Uuid uuid = const Uuid();

  /// Creates an instance of [AddQrCodeElementUseCase].
  ///
  /// The [repository] parameter is required and represents the repository
  /// that will be used to add the QR code element.
  AddQrCodeElementUseCase(this.repository);

  /// Executes the use case to add a new QR code element.
  ///
  /// The [url] parameter represents the URL that will be encoded in the QR code.
  /// The [title] parameter is an optional title for the QR code (e.g., "Scan to listen").
  /// The [position] parameter specifies where to place the QR code on the card.
  /// The new element is created with specified positioning and dimensions,
  /// and then it is added to the repository.
  Future<void> call(String url, {String? title, Offset? position}) async {
    // استخدام إحداثيات نسبية (0-1) بدلاً من إحداثيات مطلقة
    // هذا يضمن أن العناصر تظهر بشكل صحيح بغض النظر عن حجم البطاقة

    // وضع العنصر في وسط البطاقة إذا لم يتم تحديد موقع
    // حساب الإحداثيات بحيث يكون العنصر في المنتصف
    final double elementWidth = 0.25;  // عرض العنصر النسبي (25% من عرض البطاقة) - تم تكبيره
    final double elementHeight = 0.25; // ارتفاع العنصر النسبي (25% من ارتفاع البطاقة) - تم تكبيره

    double x, y;

    if (position != null) {
      // إذا تم تحديد موقع، استخدمه بعد تحويله إلى إحداثيات نسبية
      x = position.dx / 400; // تحويل الإحداثيات المطلقة إلى نسبية
      y = position.dy / 600; // تحويل الإحداثيات المطلقة إلى نسبية
    } else {
      // وضع QR Code في أسفل يمين البطاقة مع هوامش 24 بكسل
      x = 1.0 - elementWidth - (24.0 / 400); // هامش 24 بكسل من اليمين (تحويل إلى نسبي)
      y = 1.0 - elementHeight - (24.0 / 600); // هامش 24 بكسل من الأسفل (تحويل إلى نسبي)
    }

    final newElement = CardElement(
      id: uuid.v4(), // Generates a unique ID for the new QR code element.
      type: ElementType.qrCode, // Sets the element type to QR code.
      content: url, // Sets the content to the provided URL.
      x: x, // X-coordinate position (relative 0-1)
      y: y, // Y-coordinate position (relative 0-1)
      width: elementWidth, // Width for the QR code element (relative 0-1)
      height: elementHeight, // Height for the QR code element (relative 0-1)
      // If a title is provided, store it in the fontFamily field (repurposing)
      fontFamily: title,
      isRelativeCoordinates: true, // Use relative coordinates (0-1)
      isFromAdminPanel: false, // This element is created manually in the app
    );

    await repository.addElement(newElement);
  }
}
