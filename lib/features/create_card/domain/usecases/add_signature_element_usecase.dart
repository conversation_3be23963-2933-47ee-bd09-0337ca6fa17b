// lib/features/create_card/domain/usecases/add_signature_element_usecase.dart

import 'package:flutter/material.dart' show Offset;
import 'package:uuid/uuid.dart';

import '../entities/card_element.dart';
import '../repositories/card_repository.dart';

/// Use case for adding a signature element to a card.
class AddSignatureElementUseCase {
  /// The repository that manages card elements.
  final CardRepository repository;

  /// UUID generator for creating unique element IDs.
  final Uuid uuid = const Uuid();

  /// Creates an instance of [AddSignatureElementUseCase].
  ///
  /// The [repository] parameter is required and represents the repository
  /// that will be used to add the signature element.
  AddSignatureElementUseCase(this.repository);

  /// Executes the use case to add a new signature element.
  ///
  /// The [signatureData] parameter represents the encoded signature image data or text content.
  /// The [signatureText] parameter is an optional text to display with the signature.
  /// The [isDrawnSignature] parameter indicates whether this is a drawn signature or text signature.
  /// The [position] parameter specifies where to place the signature on the card.
  /// The [fontFamily] parameter is used for text signatures to specify the font.
  /// The new element is created with the specified positioning and dimensions,
  /// and then it is added to the repository.
  Future<void> call(
    String signatureData, {
    String? signatureText,
    bool isDrawnSignature = true,
    Offset? position,
    String? fontFamily,
  }) async {
    // استخدام إحداثيات نسبية (0-1) بدلاً من إحداثيات مطلقة
    // هذا يضمن أن العناصر تظهر بشكل صحيح بغض النظر عن حجم البطاقة

    // حساب الحجم المناسب بناءً على نوع التوقيع (نسبي 0-1) - تم تكبير الأحجام
    double relativeWidth = isDrawnSignature ? 0.4 : 0.45;  // تم تكبير العرض
    double relativeHeight = isDrawnSignature ? 0.18 : 0.15; // تم تكبير الارتفاع

    double relativeX, relativeY;

    if (position != null) {
      // إذا تم تحديد موقع، استخدمه بعد تحويله إلى إحداثيات نسبية
      relativeX = position.dx / 400; // تقسيم على عرض البطاقة الافتراضي
      relativeY = position.dy / 600; // تقسيم على ارتفاع البطاقة الافتراضي
    } else {
      // وضع التوقيع في أسفل يسار البطاقة مع هوامش 24 بكسل
      relativeX = 24.0 / 400; // هامش 24 بكسل من اليسار (تحويل إلى نسبي)
      relativeY = 1.0 - relativeHeight - (24.0 / 600); // هامش 24 بكسل من الأسفل (تحويل إلى نسبي)
    }

    // إنشاء العنصر بالخصائص المناسبة بناءً على نوع التوقيع
    CardElement newElement;

    if (isDrawnSignature) {
      // للتوقيعات المرسومة
      newElement = CardElement(
        id: uuid.v4(),
        type: ElementType.signature,
        content: signatureData,
        x: relativeX,
        y: relativeY,
        width: relativeWidth,
        height: relativeHeight,
        fontFamily: signatureText, // تخزين الاسم في حقل fontFamily
        isRelativeCoordinates: true, // استخدام إحداثيات نسبية (0-1)
        isFromAdminPanel: false, // هذا العنصر تم إنشاؤه يدويًا في التطبيق
      );
    } else {
      // للتوقيعات النصية - تحديد خط مميز حسب اللغة
      String signatureFont;
      bool isArabicText = _isArabicText(signatureData);

      if (fontFamily == null || fontFamily.isEmpty) {
        // تطبيق خط مميز للتوقيع حسب اللغة
        if (isArabicText) {
          signatureFont = 'Amiri'; // خط أميري للعربية - خط تقليدي أنيق
        } else {
          signatureFont = 'Dancing Script'; // خط راقص للإنجليزية - خط توقيع أنيق
        }
      } else {
        signatureFont = fontFamily;
      }

      newElement = CardElement(
        id: uuid.v4(),
        type: ElementType.text,
        content: signatureData,
        x: relativeX,
        y: relativeY,
        width: relativeWidth,
        height: relativeHeight,
        fontFamily: signatureFont,
        fontSize: isArabicText ? 32.0 : 28.0, // حجم أكبر للعربية
        colorValue: 0xFF000000, // لون أسود للتوقيعات النصية
        letterSpacing: isArabicText ? 0.8 : 1.5, // تباعد مناسب حسب اللغة
        isBold: false, // الخطوط المميزة لا تحتاج للغامق
        isItalic: !isArabicText, // مائل للإنجليزية فقط
        textAlign: 'center', // توسيط التوقيع
        isRelativeCoordinates: true, // استخدام إحداثيات نسبية (0-1)
        isFromAdminPanel: false, // هذا العنصر تم إنشاؤه يدويًا في التطبيق
      );
    }

    await repository.addElement(newElement);
  }

  /// فحص ما إذا كان النص يحتوي على أحرف عربية
  bool _isArabicText(String text) {
    // التحقق من وجود أحرف عربية في النص
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return arabicRegex.hasMatch(text);
  }
}
