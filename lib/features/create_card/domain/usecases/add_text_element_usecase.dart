import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'dart:math' as math;

import '../entities/card_element.dart';
import '../repositories/card_repository.dart';

/// A use case for adding a new text element to a card.
/// This use case creates a new [CardElement] of type [ElementType.text] using the provided text,
/// and then adds it to the repository.
class AddTextElementUseCase {
  /// The repository used to manage card elements.
  final CardRepository repository;

  /// A UUID generator instance to create unique IDs for new card elements.
  final Uuid uuid = const Uuid();

  /// Creates an instance of [AddTextElementUseCase] with the given [repository].
  AddTextElementUseCase(this.repository);

  /// حساب أبعاد النص بناءً على محتواه الفعلي بشكل احترافي ودقيق
  /// يضمن أن الإطار يحتوي النص بالكامل دون مساحات فارغة زائدة
  Size _calculateTextDimensions(String text, double fontSize) {
    // التحقق من صحة المدخلات
    if (text.isEmpty) {
      return const Size(80.0, 40.0); // حجم افتراضي للنص الفارغ
    }

    if (fontSize <= 0 || fontSize.isNaN || fontSize.isInfinite) {
      fontSize = 24.0; // حجم خط افتراضي آمن
    }

    // تحديد اتجاه النص
    final bool isArabic = _containsArabicCharacters(text);

    // إنشاء TextPainter لحساب أبعاد النص الفعلية
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          fontSize: fontSize,
          fontFamily: 'Cairo',
          fontWeight: FontWeight.normal,
          letterSpacing: 0.3, // تباعد أحرف محسن
          height: 1.2, // ارتفاع السطر محسن
        ),
      ),
      textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
      textAlign: TextAlign.center,
      maxLines: null, // السماح بعدد غير محدود من الأسطر
    );

    // تحديد العرض الأقصى للنص بناءً على طول النص وعدد الكلمات
    const double cardWidth = 350.0;
    final int wordCount = text.split(' ').length;
    double maxWidthRatio;

    // تحديد نسبة العرض بناءً على طول النص وعدد الكلمات
    if (text.length <= 10 || wordCount <= 2) {
      maxWidthRatio = 0.4; // نص قصير جداً - عرض ضيق جداً
    } else if (text.length <= 20 || wordCount <= 4) {
      maxWidthRatio = 0.55; // نص قصير - عرض ضيق
    } else if (text.length <= 40 || wordCount <= 8) {
      maxWidthRatio = 0.7; // نص متوسط - عرض متوسط
    } else if (text.length <= 80 || wordCount <= 15) {
      maxWidthRatio = 0.85; // نص طويل - عرض كبير
    } else {
      maxWidthRatio = 0.95; // نص طويل جداً - عرض كامل تقريباً
    }

    final double maxWidth = cardWidth * maxWidthRatio;

    try {
      // حساب أبعاد النص مع العرض المحدد
      textPainter.layout(maxWidth: maxWidth);
    } catch (e) {
      // في حالة فشل layout، استخدم قيم افتراضية آمنة
      return Size(math.min(text.length * 8.0 + 32.0, cardWidth * 0.8), 60.0);
    }

    // التحقق من صحة النتائج
    if (textPainter.width.isNaN || textPainter.width.isInfinite ||
        textPainter.height.isNaN || textPainter.height.isInfinite) {
      // استخدام قيم افتراضية آمنة
      return Size(math.min(text.length * 8.0 + 32.0, cardWidth * 0.8), 60.0);
    }

    // إضافة هوامش دقيقة ومحسنة للنص بناءً على طول النص
    // زيادة الهوامش لضمان احتواء النص بالكامل داخل الإطار
    double horizontalPadding;
    double verticalPadding;

    if (text.length <= 10) {
      horizontalPadding = 16.0;  // هامش أفقي أكبر للنص القصير
      verticalPadding = 12.0;    // هامش رأسي أكبر للنص القصير
    } else if (text.length <= 30) {
      horizontalPadding = 20.0; // هامش أفقي أكبر متوسط
      verticalPadding = 14.0;    // هامش رأسي أكبر متوسط
    } else {
      horizontalPadding = 24.0; // هامش أفقي أكبر للنص الطويل
      verticalPadding = 16.0;    // هامش رأسي أكبر للنص الطويل
    }

    final double textWidth = textPainter.width + horizontalPadding;
    final double textHeight = textPainter.height + verticalPadding;

    // تحديد حد أدنى وأقصى للأبعاد بشكل دقيق بناءً على طول النص
    // زيادة الحد الأدنى لضمان احتواء النص بالكامل
    double minWidth;
    double minHeight;

    if (text.length <= 10) {
      minWidth = 80.0;   // حد أدنى أكبر للعرض للنص القصير
      minHeight = 40.0;  // حد أدنى أكبر للارتفاع للنص القصير
    } else if (text.length <= 30) {
      minWidth = 100.0;   // حد أدنى أكبر متوسط للعرض
      minHeight = 50.0;  // حد أدنى أكبر متوسط للارتفاع
    } else {
      minWidth = 120.0;  // حد أدنى أكبر للعرض للنص الطويل
      minHeight = 60.0;  // حد أدنى أكبر للارتفاع للنص الطويل
    }

    const double maxHeight = cardWidth * 0.8; // 80% من ارتفاع البطاقة

    // التأكد من صحة القيم قبل استخدام math.min و math.max
    final double safeTextWidth = textWidth.isFinite ? textWidth : minWidth;
    final double safeTextHeight = textHeight.isFinite ? textHeight : minHeight;

    final double finalWidth = math.max(minWidth, math.min(safeTextWidth, maxWidth));
    final double finalHeight = math.max(minHeight, math.min(safeTextHeight, maxHeight));

    // التحقق النهائي من صحة النتائج
    if (finalWidth.isNaN || finalWidth.isInfinite || finalWidth <= 0 ||
        finalHeight.isNaN || finalHeight.isInfinite || finalHeight <= 0) {
      // إرجاع قيم افتراضية آمنة
      return Size(math.min(text.length * 8.0 + 32.0, cardWidth * 0.8), 60.0);
    }

    return Size(finalWidth, finalHeight);
  }

  /// التحقق من وجود أحرف عربية في النص
  bool _containsArabicCharacters(String text) {
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return arabicRegex.hasMatch(text);
  }

  /// Executes the use case to add a new text element.
  ///
  /// The [text] parameter represents the content of the text element.
  /// The new element is created with smart positioning and dimensions based on text content,
  /// ensuring the frame perfectly contains the text without overflow.
  Future<void> call(String text) async {
    // تحديد حجم الخط بناءً على نوع النص
    double defaultFontSize;

    // إذا كان النص افتراضياً (قصير)، استخدم خط أصغر
    if (text.length <= 10 || text == 'نص جديد' || text == 'New Text') {
      defaultFontSize = 24.0; // خط أصغر للنص الافتراضي
    } else {
      defaultFontSize = 32.0; // خط أكبر للنص الطويل
    }

    // حساب أبعاد النص بناءً على محتواه الفعلي
    final textDimensions = _calculateTextDimensions(text, defaultFontSize);

    // تحويل الأبعاد المطلقة إلى نسبية (0-1) بدقة عالية
    // افتراض أن أبعاد البطاقة القياسية هي 350x350 بكسل
    const double cardWidth = 350.0;
    const double cardHeight = 350.0;

    // التحقق من صحة أبعاد النص قبل التحويل
    if (textDimensions.width <= 0 || textDimensions.height <= 0 ||
        textDimensions.width.isNaN || textDimensions.height.isNaN ||
        textDimensions.width.isInfinite || textDimensions.height.isInfinite) {
      // استخدام أبعاد افتراضية آمنة
      final double elementWidth = 0.3; // 30% من عرض البطاقة
      final double elementHeight = 0.15; // 15% من ارتفاع البطاقة

      final double centerX = (1.0 - elementWidth) / 2;
      final double centerY = (1.0 - elementHeight) / 2;

      final newElement = CardElement(
        id: uuid.v4(),
        type: ElementType.text,
        content: text,
        x: centerX,
        y: centerY,
        width: elementWidth,
        height: elementHeight,
        fontSize: defaultFontSize,
        textAlign: 'center',
        isRelativeCoordinates: true,
        isFromAdminPanel: false,
      );

      await repository.addElement(newElement);
      return;
    }

    final double elementWidth = textDimensions.width / cardWidth;
    final double elementHeight = textDimensions.height / cardHeight;

    // التأكد من أن الأبعاد النسبية دقيقة ومناسبة للنص مع التحقق من صحة القيم
    double finalWidth = elementWidth;
    double finalHeight = elementHeight;

    // التحقق من صحة القيم قبل استخدام clamp
    if (finalWidth.isFinite && finalWidth > 0) {
      finalWidth = finalWidth.clamp(0.1, 0.95); // حد أدنى 10% وأقصى 95%
    } else {
      finalWidth = 0.3; // قيمة افتراضية آمنة
    }

    if (finalHeight.isFinite && finalHeight > 0) {
      finalHeight = finalHeight.clamp(0.08, 0.9); // حد أدنى 8% وأقصى 90%
    } else {
      finalHeight = 0.15; // قيمة افتراضية آمنة
    }

    // حساب الإحداثيات لوضع العنصر في وسط البطاقة بدقة
    final double centerX = (1.0 - finalWidth) / 2; // وسط البطاقة أفقياً بدقة
    final double centerY = (1.0 - finalHeight) / 2; // وسط البطاقة رأسياً بدقة

    // التأكد من أن العنصر يبقى داخل حدود البطاقة مع هامش أمان
    final double safeX = centerX.clamp(0.02, 0.98 - finalWidth); // هامش أمان 2%
    final double safeY = centerY.clamp(0.02, 0.98 - finalHeight); // هامش أمان 2%

    final newElement = CardElement(
      id: uuid.v4(),             // Generates a unique ID for the new text element.
      type: ElementType.text,     // Sets the element type to text.
      content: text,              // Sets the content of the element to the provided text.
      x: safeX,                   // Safe x-coordinate position (relative 0-1).
      y: safeY,                   // Safe y-coordinate position (relative 0-1).
      width: finalWidth,          // Calculated width based on text content (relative 0-1).
      height: finalHeight,        // Calculated height based on text content (relative 0-1).
      fontSize: defaultFontSize,  // Sets the default font size for the text.
      textAlign: 'center',        // توسيط النص كتنسيق افتراضي
      isRelativeCoordinates: true, // Use relative coordinates (0-1).
      isFromAdminPanel: false,     // This element is created manually in the app.
    );

    // Adds the new text element to the repository.
    await repository.addElement(newElement);
  }
}
