import '../repositories/card_repository.dart';

/// A use case for updating the position of a card element.
/// This use case retrieves all current card elements from the repository,
/// finds the target element by its [elementId], updates its [x] and [y] coordinates,
/// and then saves the updated element back to the repository.
class UpdateElementPositionUseCase {
  /// The repository used to manage card elements.
  final CardRepository repository;

  /// Creates an instance of [UpdateElementPositionUseCase] with the given [repository].
  UpdateElementPositionUseCase(this.repository);

  /// Executes the use case to update the position of a specific card element.
  ///
  /// The [elementId] parameter identifies the card element to update.
  /// The [newX] and [newY] parameters represent the new x and y coordinates, respectively.
  ///
  /// If the card element is found, its position is updated using the `copyWith` method,
  /// and the updated element is saved back to the repository.
  Future<void> call(String elementId, double newX, double newY) async {
    // Retrieve all card elements from the repository.
    final elements = await repository.getAllElements();

    // Find the index of the element with the specified elementId.
    final index = elements.indexWhere((element) => element.id == elementId);

    // If the element is not found, exit the function.
    if (index == -1) return;

    // Get the existing element.
    final existingElement = elements[index];

    // Create a new element with the updated x and y positions using the copyWith method.
    final updatedElement = existingElement.copyWith(x: newX, y: newY);

    // Update the element in the repository with the new position.
    await repository.updateElement(updatedElement);
  }
}
