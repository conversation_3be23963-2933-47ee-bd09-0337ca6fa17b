import '../repositories/card_repository.dart';

/// A use case for removing a card element from the repository.
/// This use case encapsulates the logic required to remove a specific card element
/// identified by its [elementId].
class RemoveElementUseCase {
  /// The repository used to manage card elements.
  final CardRepository repository;

  /// Creates an instance of [RemoveElementUseCase] with the given [repository].
  RemoveElementUseCase(this.repository);

  /// Executes the use case to remove a card element.
  ///
  /// The [elementId] parameter represents the unique identifier of the card element to be removed.
  /// This method calls the [removeElement] method of the repository to perform the removal.
  Future<void> call(String elementId) async {
    await repository.removeElement(elementId);
  }
}
