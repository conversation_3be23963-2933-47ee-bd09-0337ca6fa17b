import '../repositories/card_repository.dart';

/// A use case for updating the transformation properties of a card element.
/// This use case updates the element's position (x and y), scale, and rotation based on
/// the provided new values. If a new value is not provided, the current value is retained.
class UpdateElementTransformUseCase {
  /// The repository used to manage card elements.
  final CardRepository repository;

  /// Creates an instance of [UpdateElementTransformUseCase] with the given [repository].
  UpdateElementTransformUseCase(this.repository);

  /// Executes the use case to update the transformation properties of a card element.
  ///
  /// Parameters:
  /// - [elementId]: The unique identifier of the card element to be updated.
  /// - [newX]: Optional new x-coordinate. If not provided, the current x value is used.
  /// - [newY]: Optional new y-coordinate. If not provided, the current y value is used.
  /// - [newScale]: Optional new scale factor. If not provided, the current scale is used.
  /// - [newRotation]: Optional new rotation angle. If not provided, the current rotation is used.
  ///
  /// If the element is found, its transformation properties are updated using the `copyWith` method,
  /// and the updated element is saved back to the repository.
  Future<void> call({
    required String elementId,
    double? newX,
    double? newY,
    double? newScale,
    double? newRotation,
  }) async {
    // Retrieve all card elements from the repository.
    final elements = await repository.getAllElements();

    // Find the index of the element with the specified elementId.
    final index = elements.indexWhere((element) => element.id == elementId);

    // If the element is not found, exit the function.
    if (index == -1) return;

    // Retrieve the existing element.
    final existingElement = elements[index];

    // Create a new element with updated transformation properties.
    // If a new value is not provided, the existing value is retained.
    final updatedElement = existingElement.copyWith(
      x: newX ?? existingElement.x,
      y: newY ?? existingElement.y,
      scale: newScale ?? existingElement.scale,
      rotation: newRotation ?? existingElement.rotation,
    );

    // Update the element in the repository with the new transformation values.
    await repository.updateElement(updatedElement);
  }
}
