// lib/features/create_card/domain/repositories/card_repository.dart

import '../entities/card_element.dart';

/// An abstract repository interface that defines the contract for managing card elements.
///
/// This repository provides methods to add, update, remove, and retrieve card elements.
/// Implementations of this repository can utilize various storage mechanisms (e.g., in-memory, database).
abstract class CardRepository {
  /// Adds a new [CardElement] to the repository.
  ///
  /// Returns a [Future] that completes when the operation is finished.
  Future<void> addElement(CardElement element);

  /// Updates an existing [CardElement] in the repository.
  ///
  /// The element to be updated is identified by its [CardElement.id].
  /// Returns a [Future] that completes when the update operation is finished.
  Future<void> updateElement(CardElement element);

  /// Removes the [CardElement] with the specified [elementId] from the repository.
  ///
  /// Returns a [Future] that completes when the removal operation is finished.
  Future<void> removeElement(String elementId);

  /// Retrieves all [CardElement] objects from the repository.
  ///
  /// Returns a [Future] that completes with a list of [CardElement].
  Future<List<CardElement>> getAllElements();

  /// Replaces the current list of elements in the repository with the provided list.
  ///
  /// This method is synchronous and does not return a [Future].
  void setElements(List<CardElement> elements);

  /// Clears all elements from the repository.
  ///
  /// This method removes all elements from the repository.
  /// It is used when navigating away from the card editor to prevent elements
  /// from persisting between different editing sessions.
  void clearElements();
}
