// lib/features/user_guide/presentation/pages/user_guide_page.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/layout/page_template.dart';
import '../widgets/guide_section.dart';
import '../widgets/guide_step.dart';

/// صفحة دليل المستخدم الشاملة
/// توفر شرحًا مفصلاً لكيفية استخدام التطبيق
class UserGuidePage extends StatefulWidget {
  /// إنشاء صفحة دليل المستخدم
  const UserGuidePage({super.key});

  @override
  State<UserGuidePage> createState() => _UserGuidePageState();
}

class _UserGuidePageState extends State<UserGuidePage>
    with SingleTickerProviderStateMixin {
  // متحكم التبويب
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PageTemplate(
      title: AppLocalizations.of(context).userGuide ?? 'دليل المستخدم',
      body: Column(
        children: [
          // شريط التبويب المحسن
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey,
              labelStyle: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              unselectedLabelStyle: GoogleFonts.cairo(
                fontWeight: FontWeight.normal,
                fontSize: 14,
              ),
              indicator: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(30),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              labelPadding: const EdgeInsets.symmetric(horizontal: 16),
              splashBorderRadius: BorderRadius.circular(30),
              tabs: [
                _buildTab(
                  Icons.play_arrow,
                  AppLocalizations.of(context).gettingStarted ?? 'البداية',
                ),
                _buildTab(
                  Icons.edit,
                  AppLocalizations.of(context).cardEditor ?? 'محرر البطاقات',
                ),
                _buildTab(
                  Icons.auto_awesome,
                  AppLocalizations.of(context).aiFeatures ??
                      'ميزات الذكاء الاصطناعي',
                ),
                _buildTab(
                  Icons.share,
                  AppLocalizations.of(context).sharingAndExporting ??
                      'المشاركة والتصدير',
                ),
                _buildTab(
                  Icons.lightbulb_outline,
                  AppLocalizations.of(context).tips ?? 'نصائح وحيل',
                ),
              ],
            ),
          ),

          // محتوى التبويب
          Expanded(
            child: Container(
              margin: const EdgeInsets.only(top: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
              ),
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildGettingStartedGuide(),
                  _buildCardEditorGuide(),
                  _buildAiFeaturesGuide(),
                  _buildSharingGuide(),
                  _buildTipsGuide(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء علامة تبويب مع أيقونة
  Widget _buildTab(IconData icon, String text) {
    return Tab(
      height: 40,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 8),
          Text(text),
        ],
      ),
    );
  }

  /// بناء دليل البداية
  Widget _buildGettingStartedGuide() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GuideSection(
            title: AppLocalizations.of(context).introduction ?? 'مقدمة',
            children: [
              Text(
                AppLocalizations.of(context).appIntroduction ??
                    'مرحبًا بك في تطبيق مشاعر! هذا التطبيق يساعدك على إنشاء بطاقات تهنئة جميلة للمناسبات المختلفة. يمكنك تخصيص البطاقات باستخدام النصوص والصور والأشكال والملصقات.',
                style: GoogleFonts.cairo(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Image.asset(
                'assets/images/guide/app_overview.png',
                height: 200,
                fit: BoxFit.contain,
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title:
                AppLocalizations.of(context).navigation ?? 'التنقل في التطبيق',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).homeScreen ??
                    'الشاشة الرئيسية',
                description: AppLocalizations.of(context).homeScreenDesc ??
                    'الشاشة الرئيسية تعرض المناسبات المختلفة والبطاقات الشائعة. يمكنك النقر على أي مناسبة لعرض البطاقات المتعلقة بها.',
              ),
              GuideStep(
                step: 2,
                title:
                    AppLocalizations.of(context).sideMenu ?? 'القائمة الجانبية',
                description: AppLocalizations.of(context).sideMenuDesc ??
                    'يمكنك الوصول إلى القائمة الجانبية بالنقر على زر القائمة في الزاوية العلوية اليسرى. تحتوي القائمة على روابط للصفحات المختلفة في التطبيق.',
              ),
              GuideStep(
                step: 3,
                title: AppLocalizations.of(context).createNewCard ??
                    'إنشاء بطاقة جديدة',
                description: AppLocalizations.of(context).createNewCardDesc ??
                    'يمكنك إنشاء بطاقة جديدة بالنقر على زر الإضافة العائم في الزاوية السفلية اليمنى من الشاشة الرئيسية.',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء دليل محرر البطاقات
  Widget _buildCardEditorGuide() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GuideSection(
            title: AppLocalizations.of(context).editorOverview ??
                'نظرة عامة على المحرر',
            children: [
              Text(
                AppLocalizations.of(context).editorOverviewDesc ??
                    'محرر البطاقات هو المكان الذي يمكنك فيه تصميم بطاقتك. يحتوي على منطقة تحرير مركزية وشريط أدوات في الأسفل.',
                style: GoogleFonts.cairo(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Image.asset(
                'assets/images/guide/editor_overview.png',
                height: 200,
                fit: BoxFit.contain,
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title:
                AppLocalizations.of(context).addingElements ?? 'إضافة العناصر',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).addingText ?? 'إضافة نص',
                description: AppLocalizations.of(context).addingTextDesc ??
                    'انقر على زر "نص" في شريط الأدوات لإضافة نص جديد. يمكنك تحرير النص بالنقر عليه.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).addingImages ?? 'إضافة صور',
                description: AppLocalizations.of(context).addingImagesDesc ??
                    'انقر على زر "صورة" في شريط الأدوات لإضافة صورة من معرض الصور أو التقاط صورة جديدة.',
              ),
              GuideStep(
                step: 3,
                title:
                    AppLocalizations.of(context).addingShapes ?? 'إضافة أشكال',
                description: AppLocalizations.of(context).addingShapesDesc ??
                    'انقر على زر "شكل" في شريط الأدوات لإضافة أشكال مختلفة مثل المستطيلات والدوائر والنجوم.',
              ),
              GuideStep(
                step: 4,
                title: AppLocalizations.of(context).addingStickers ??
                    'إضافة ملصقات',
                description: AppLocalizations.of(context).addingStickersDesc ??
                    'انقر على زر "ملصق" في شريط الأدوات لإضافة ملصقات جاهزة إلى بطاقتك.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title:
                AppLocalizations.of(context).editingElements ?? 'تحرير العناصر',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).selectingElements ??
                    'تحديد العناصر',
                description: AppLocalizations.of(context)
                        .selectingElementsDesc ??
                    'انقر على أي عنصر لتحديده. سيظهر إطار حول العنصر المحدد مع مقابض للتحكم.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).movingElements ??
                    'تحريك العناصر',
                description: AppLocalizations.of(context).movingElementsDesc ??
                    'اسحب العنصر المحدد لتحريكه إلى موقع جديد على البطاقة.',
              ),
              GuideStep(
                step: 3,
                title: AppLocalizations.of(context).resizingElements ??
                    'تغيير حجم العناصر',
                description: AppLocalizations.of(context)
                        .resizingElementsDesc ??
                    'اسحب مقابض التحجيم في زوايا العنصر المحدد لتغيير حجمه.',
              ),
              GuideStep(
                step: 4,
                title: AppLocalizations.of(context).rotatingElements ??
                    'تدوير العناصر',
                description:
                    AppLocalizations.of(context).rotatingElementsDesc ??
                        'اسحب مقبض التدوير لتدوير العنصر المحدد.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title: AppLocalizations.of(context).textFormatting ?? 'تنسيق النص',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).changingFonts ??
                    'تغيير الخطوط',
                description: AppLocalizations.of(context).changingFontsDesc ??
                    'حدد النص ثم انقر على زر "الخط" في شريط الأدوات العلوي لاختيار خط مختلف.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).changingColors ??
                    'تغيير الألوان',
                description: AppLocalizations.of(context).changingColorsDesc ??
                    'حدد النص ثم انقر على زر "اللون" لاختيار لون مختلف للنص.',
              ),
              GuideStep(
                step: 3,
                title:
                    AppLocalizations.of(context).textEffects ?? 'تأثيرات النص',
                description: AppLocalizations.of(context).textEffectsDesc ??
                    'حدد النص ثم انقر على زر "تأثيرات" لإضافة تأثيرات مثل الظل والتوهج والانعكاس.',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء دليل ميزات الذكاء الاصطناعي
  Widget _buildAiFeaturesGuide() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GuideSection(
            title: AppLocalizations.of(context).aiCardCreation ??
                'إنشاء بطاقات بالذكاء الاصطناعي',
            children: [
              Text(
                AppLocalizations.of(context).aiCardCreationDesc ??
                    'يمكنك استخدام الذكاء الاصطناعي لإنشاء بطاقات فريدة بناءً على وصف نصي تقدمه.',
                style: GoogleFonts.cairo(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Image.asset(
                'assets/images/guide/ai_card_creation.png',
                height: 200,
                fit: BoxFit.contain,
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title: AppLocalizations.of(context).aiTextGeneration ??
                'توليد النصوص بالذكاء الاصطناعي',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).accessingAiText ??
                    'الوصول إلى ميزة توليد النصوص',
                description: AppLocalizations.of(context).accessingAiTextDesc ??
                    'في محرر البطاقات، انقر على زر "توليد نص" في شريط الأدوات العلوي.',
              ),
              GuideStep(
                step: 2,
                title:
                    AppLocalizations.of(context).writingPrompt ?? 'كتابة الوصف',
                description: AppLocalizations.of(context).writingPromptDesc ??
                    'اكتب وصفًا للنص الذي ترغب في إنشائه، مثل "تهنئة بعيد ميلاد لصديق مقرب" أو "رسالة تهنئة بالتخرج".',
              ),
              GuideStep(
                step: 3,
                title: AppLocalizations.of(context).usingGeneratedText ??
                    'استخدام النص المولد',
                description: AppLocalizations.of(context)
                        .usingGeneratedTextDesc ??
                    'بعد توليد النص، يمكنك تعديله أو استخدامه كما هو بالنقر على زر "استخدام هذا النص".',
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title: AppLocalizations.of(context).aiImageGeneration ??
                'توليد الصور بالذكاء الاصطناعي',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).accessingAiImage ??
                    'الوصول إلى ميزة توليد الصور',
                description: AppLocalizations.of(context)
                        .accessingAiImageDesc ??
                    'في محرر البطاقات، انقر على زر "توليد صورة" في شريط الأدوات العلوي.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).describingImage ??
                    'وصف الصورة',
                description: AppLocalizations.of(context).describingImageDesc ??
                    'اكتب وصفًا تفصيليًا للصورة التي ترغب في إنشائها، مثل "باقة ورد جميلة بألوان زاهية" أو "منظر طبيعي لغروب الشمس على الشاطئ".',
              ),
              GuideStep(
                step: 3,
                title: AppLocalizations.of(context).customizingImage ??
                    'تخصيص الصورة',
                description: AppLocalizations.of(context)
                        .customizingImageDesc ??
                    'يمكنك تحديد أسلوب الصورة ونسبة العرض إلى الارتفاع قبل توليدها.',
              ),
              GuideStep(
                step: 4,
                title: AppLocalizations.of(context).usingGeneratedImage ??
                    'استخدام الصورة المولدة',
                description: AppLocalizations.of(context)
                        .usingGeneratedImageDesc ??
                    'بعد توليد الصورة، يمكنك إضافتها إلى بطاقتك بالنقر على زر "استخدام هذه الصورة".',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء دليل المشاركة والتصدير
  Widget _buildSharingGuide() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GuideSection(
            title: AppLocalizations.of(context).savingCards ?? 'حفظ البطاقات',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).savingDraft ?? 'حفظ كمسودة',
                description: AppLocalizations.of(context).savingDraftDesc ??
                    'يمكنك حفظ البطاقة كمسودة للعمل عليها لاحقًا بالنقر على زر "حفظ كمسودة" في شريط الأدوات العلوي.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).savingFinal ?? 'حفظ نهائي',
                description: AppLocalizations.of(context).savingFinalDesc ??
                    'عند الانتهاء من تصميم البطاقة، انقر على زر "حفظ" في شريط الأدوات العلوي لحفظها بشكل نهائي.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title:
                AppLocalizations.of(context).exportingCards ?? 'تصدير البطاقات',
            children: [
              GuideStep(
                step: 1,
                title:
                    AppLocalizations.of(context).exportAsImage ?? 'تصدير كصورة',
                description: AppLocalizations.of(context).exportAsImageDesc ??
                    'يمكنك تصدير البطاقة كصورة بتنسيق PNG أو JPEG بالنقر على زر "تصدير" ثم اختيار "صورة".',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).exportAsPdf ??
                    'تصدير كملف PDF',
                description: AppLocalizations.of(context).exportAsPdfDesc ??
                    'يمكنك تصدير البطاقة كملف PDF بالنقر على زر "تصدير" ثم اختيار "PDF".',
              ),
              GuideStep(
                step: 3,
                title: AppLocalizations.of(context).exportOptions ??
                    'خيارات التصدير',
                description: AppLocalizations.of(context).exportOptionsDesc ??
                    'يمكنك تحديد جودة الصورة وحجمها عند التصدير.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title:
                AppLocalizations.of(context).sharingCards ?? 'مشاركة البطاقات',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).sharingDirectly ??
                    'المشاركة المباشرة',
                description: AppLocalizations.of(context).sharingDirectlyDesc ??
                    'يمكنك مشاركة البطاقة مباشرة عبر تطبيقات التواصل الاجتماعي بالنقر على زر "مشاركة" واختيار التطبيق.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).sharingSocial ??
                    'المشاركة على وسائل التواصل الاجتماعي',
                description: AppLocalizations.of(context).sharingSocialDesc ??
                    'يمكنك مشاركة البطاقة على فيسبوك أو تويتر أو انستغرام بالنقر على أيقونة التطبيق المناسب.',
              ),
              GuideStep(
                step: 3,
                title: AppLocalizations.of(context).sharingWhatsapp ??
                    'المشاركة عبر واتساب',
                description: AppLocalizations.of(context).sharingWhatsappDesc ??
                    'يمكنك مشاركة البطاقة عبر واتساب بالنقر على أيقونة واتساب في قائمة المشاركة.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title: AppLocalizations.of(context).printing ?? 'طباعة البطاقات',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).accessingPrint ??
                    'الوصول إلى ميزة الطباعة',
                description: AppLocalizations.of(context).accessingPrintDesc ??
                    'انقر على زر "طباعة" في شريط الأدوات العلوي.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).printOptions ??
                    'خيارات الطباعة',
                description: AppLocalizations.of(context).printOptionsDesc ??
                    'يمكنك تحديد حجم الورق واتجاه الطباعة وعدد النسخ قبل الطباعة.',
              ),
              GuideStep(
                step: 3,
                title: AppLocalizations.of(context).printPreview ??
                    'معاينة الطباعة',
                description: AppLocalizations.of(context).printPreviewDesc ??
                    'يمكنك معاينة البطاقة قبل طباعتها للتأكد من أنها ستظهر بالشكل المطلوب.',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء دليل النصائح والحيل
  Widget _buildTipsGuide() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GuideSection(
            title: AppLocalizations.of(context).designTips ?? 'نصائح التصميم',
            children: [
              GuideStep(
                step: 1,
                title:
                    AppLocalizations.of(context).colorScheme ?? 'نظام الألوان',
                description: AppLocalizations.of(context).colorSchemeDesc ??
                    'استخدم ألوانًا متناسقة ولا تستخدم أكثر من 3-4 ألوان في البطاقة الواحدة.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).typography ?? 'الطباعة',
                description: AppLocalizations.of(context).typographyDesc ??
                    'استخدم خطوطًا مقروءة واحرص على التباين بين لون النص والخلفية.',
              ),
              GuideStep(
                step: 3,
                title: AppLocalizations.of(context).balance ?? 'التوازن',
                description: AppLocalizations.of(context).balanceDesc ??
                    'وزع العناصر بشكل متوازن على البطاقة ولا تكدس الكثير من العناصر في منطقة واحدة.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title:
                AppLocalizations.of(context).performanceTips ?? 'نصائح الأداء',
            children: [
              GuideStep(
                step: 1,
                title: AppLocalizations.of(context).imageOptimization ??
                    'تحسين الصور',
                description: AppLocalizations.of(context)
                        .imageOptimizationDesc ??
                    'استخدم صورًا بحجم معقول لتجنب بطء التطبيق. يمكنك ضغط الصور قبل استخدامها.',
              ),
              GuideStep(
                step: 2,
                title: AppLocalizations.of(context).savingRegularly ??
                    'الحفظ بانتظام',
                description: AppLocalizations.of(context).savingRegularlyDesc ??
                    'احفظ عملك بانتظام لتجنب فقدان التغييرات في حالة حدوث أي مشكلة.',
              ),
            ],
          ),
          const SizedBox(height: 24),
          GuideSection(
            title: AppLocalizations.of(context).keyboardShortcuts ??
                'اختصارات لوحة المفاتيح',
            children: [
              Text(
                AppLocalizations.of(context).keyboardShortcutsDesc ??
                    'استخدم اختصارات لوحة المفاتيح التالية لتسريع عملك:',
                style: GoogleFonts.cairo(fontSize: 16),
              ),
              const SizedBox(height: 16),
              _buildShortcutItem(
                  'Ctrl+Z', AppLocalizations.of(context).undo ?? 'تراجع'),
              _buildShortcutItem(
                  'Ctrl+Y', AppLocalizations.of(context).redo ?? 'إعادة'),
              _buildShortcutItem(
                  'Ctrl+S', AppLocalizations.of(context).save ?? 'حفظ'),
              _buildShortcutItem(
                  'Ctrl+C', AppLocalizations.of(context).copy ?? 'نسخ'),
              _buildShortcutItem(
                  'Ctrl+V', AppLocalizations.of(context).paste ?? 'لصق'),
              _buildShortcutItem(
                  'Ctrl+X', AppLocalizations.of(context).cut ?? 'قص'),
              _buildShortcutItem(
                  'Delete', AppLocalizations.of(context).delete ?? 'حذف'),
              _buildShortcutItem('Ctrl+A',
                  AppLocalizations.of(context).selectAll ?? 'تحديد الكل'),
              _buildShortcutItem(
                  'Ctrl+G', AppLocalizations.of(context).group ?? 'تجميع'),
              _buildShortcutItem('Ctrl+Shift+G',
                  AppLocalizations.of(context).ungroup ?? 'فك التجميع'),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر اختصار لوحة المفاتيح
  Widget _buildShortcutItem(String shortcut, String description) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.accentColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: AppColors.accentColor),
            ),
            child: Text(
              shortcut,
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              description,
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
