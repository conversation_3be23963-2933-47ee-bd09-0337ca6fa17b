// lib/features/user_guide/presentation/pages/modern_user_guide_page.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/constants/app_colors.dart';
import '../../../../core/widgets/layout/page_template.dart';
import '../widgets/guide_step.dart';
import '../widgets/interactive_guide_card.dart';

/// صفحة دليل المستخدم الحديثة
/// توفر شرحًا مفصلاً لكيفية استخدام التطبيق بتصميم عصري
class ModernUserGuidePage extends StatefulWidget {
  /// إنشاء صفحة دليل المستخدم الحديثة
  const ModernUserGuidePage({super.key});

  @override
  State<ModernUserGuidePage> createState() => _ModernUserGuidePageState();
}

class _ModernUserGuidePageState extends State<ModernUserGuidePage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // للبحث في الدليل
  final TextEditingController _searchController = TextEditingController();
  final String _searchQuery = '';

  // قائمة الأقسام المفضلة
  final List<String> _favoriteSections = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PageTemplate(
      title: 'دليل المستخدم',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسوم متحركة ترحيبية
            Center(
              child: Container(
                height: 200,
                width: 200,
                decoration: BoxDecoration(
                  color: Color.fromRGBO(
                    AppColors.primaryColor.red,
                    AppColors.primaryColor.green,
                    AppColors.primaryColor.blue,
                    0.1,
                  ),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.info_outline,
                  size: 80,
                  color: AppColors.primaryColor,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // قسم المقدمة
            InteractiveGuideCard(
              title: 'مقدمة',
              icon: Icons.info_outline_rounded,
              isFavorite: _favoriteSections.contains('introduction'),
              onFavoriteToggle: () => _toggleFavorite('introduction'),
              children: [
                Text(
                  'مرحبًا بك في تطبيق مشاعر! هذا التطبيق يساعدك على إنشاء بطاقات تهنئة جميلة للمناسبات المختلفة. يمكنك تخصيص البطاقات باستخدام النصوص والصور والأشكال والملصقات.',
                  style: GoogleFonts.cairo(fontSize: 16),
                ),
                const SizedBox(height: 16),
                Text(
                  'تم تصميم التطبيق ليكون سهل الاستخدام وبديهيًا، مع توفير أدوات متقدمة للتصميم الإبداعي. سواء كنت تريد إنشاء بطاقة تهنئة بعيد ميلاد، أو بطاقة تهنئة بمناسبة زواج، أو بطاقة شكر، فإن تطبيق مشاعر يوفر لك كل ما تحتاجه.',
                  style: GoogleFonts.cairo(fontSize: 16),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // قسم التنقل في التطبيق
            InteractiveGuideCard(
              title: 'التنقل في التطبيق',
              icon: Icons.navigation_rounded,
              isFavorite: _favoriteSections.contains('navigation'),
              onFavoriteToggle: () => _toggleFavorite('navigation'),
              children: [
                GuideStep(
                  step: 1,
                  title: 'الشاشة الرئيسية',
                  description:
                      'الشاشة الرئيسية تعرض المناسبات المختلفة والبطاقات الشائعة. يمكنك النقر على أي مناسبة لعرض البطاقات المتعلقة بها. كما يمكنك استعراض البطاقات الأكثر شيوعًا والمقترحة لك بناءً على اهتماماتك.',
                  imagePath: 'assets/images/guide/home_screen.png',
                ),
                GuideStep(
                  step: 2,
                  title: 'القائمة الجانبية',
                  description:
                      'يمكنك الوصول إلى القائمة الجانبية بالنقر على زر القائمة في الزاوية العلوية اليسرى. تحتوي القائمة على روابط للصفحات المختلفة في التطبيق، مثل الصفحة الرئيسية، والملف الشخصي، وتاريخ البطاقات، والإشعارات، والإعدادات.',
                  imagePath: 'assets/images/guide/side_menu.png',
                ),
                GuideStep(
                  step: 3,
                  title: 'شريط البحث',
                  description:
                      'يمكنك استخدام شريط البحث في أعلى الشاشة للبحث عن بطاقات أو مناسبات محددة. ما عليك سوى كتابة كلمة أو عبارة في شريط البحث والنقر على زر البحث.',
                  imagePath: 'assets/images/guide/search_bar.png',
                ),
                GuideStep(
                  step: 4,
                  title: 'تصفح المناسبات',
                  description:
                      'يمكنك تصفح المناسبات المختلفة من خلال التمرير أفقيًا في قسم المناسبات. انقر على أي مناسبة لعرض البطاقات المتعلقة بها.',
                  imagePath: 'assets/images/guide/browse_occasions.png',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // قسم إنشاء البطاقات
            InteractiveGuideCard(
              title: 'إنشاء البطاقات',
              icon: Icons.add_card,
              isFavorite: _favoriteSections.contains('create_cards'),
              onFavoriteToggle: () => _toggleFavorite('create_cards'),
              children: [
                GuideStep(
                  step: 1,
                  title: 'إنشاء بطاقة جديدة',
                  description:
                      'لإنشاء بطاقة جديدة، انقر على زر "إنشاء بطاقة" في الشاشة الرئيسية. يمكنك البدء من قالب فارغ أو اختيار قالب جاهز من المعرض.',
                  imagePath: 'assets/images/guide/create_card.png',
                ),
                GuideStep(
                  step: 2,
                  title: 'اختيار قالب',
                  description:
                      'يمكنك اختيار قالب من مجموعة متنوعة من القوالب المصنفة حسب المناسبة. انقر على القالب الذي تريده لبدء التحرير.',
                  imagePath: 'assets/images/guide/select_template.png',
                ),
                GuideStep(
                  step: 3,
                  title: 'تخصيص البطاقة',
                  description:
                      'بعد اختيار القالب، يمكنك تخصيص البطاقة بإضافة النصوص والصور والأشكال. يمكنك أيضًا تغيير الألوان والخطوط وحجم العناصر.',
                  imagePath: 'assets/images/guide/customize_card.png',
                ),
                GuideStep(
                  step: 4,
                  title: 'حفظ البطاقة',
                  description:
                      'بعد الانتهاء من تصميم البطاقة، انقر على زر "حفظ" لحفظ البطاقة في حسابك. يمكنك العودة إلى تحرير البطاقة في أي وقت.',
                  imagePath: 'assets/images/guide/save_card.png',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // قسم محرر البطاقات
            InteractiveGuideCard(
              title: 'محرر البطاقات',
              icon: Icons.edit_rounded,
              isFavorite: _favoriteSections.contains('card_editor'),
              onFavoriteToggle: () => _toggleFavorite('card_editor'),
              children: [
                GuideStep(
                  step: 1,
                  title: 'واجهة المحرر',
                  description:
                      'محرر البطاقات يتكون من منطقة العمل الرئيسية في الوسط، وشريط الأدوات في الأسفل، وقائمة الخيارات في الجانب. يمكنك تحريك وتعديل العناصر بسهولة باستخدام اللمس أو الماوس.',
                  imagePath: 'assets/images/guide/editor_interface.png',
                ),
                GuideStep(
                  step: 2,
                  title: 'إضافة نص',
                  description:
                      'لإضافة نص، انقر على زر "نص" في شريط الأدوات. يمكنك تحرير النص وتغيير حجمه ولونه ونوع الخط من خلال قائمة الخيارات التي تظهر عند تحديد النص.',
                  imagePath: 'assets/images/guide/add_text_editor.png',
                ),
                GuideStep(
                  step: 3,
                  title: 'إضافة صورة',
                  description:
                      'لإضافة صورة، انقر على زر "صورة" في شريط الأدوات. يمكنك اختيار صورة من معرض الصور على جهازك أو التقاط صورة جديدة باستخدام الكاميرا.',
                  imagePath: 'assets/images/guide/add_image_editor.png',
                ),
                GuideStep(
                  step: 4,
                  title: 'إضافة شكل',
                  description:
                      'لإضافة شكل، انقر على زر "شكل" في شريط الأدوات. يمكنك اختيار من بين مجموعة متنوعة من الأشكال مثل المربعات والدوائر والخطوط.',
                  imagePath: 'assets/images/guide/add_shape.png',
                ),
                GuideStep(
                  step: 5,
                  title: 'تغيير الخلفية',
                  description:
                      'لتغيير خلفية البطاقة، انقر على زر "خلفية" في شريط الأدوات. يمكنك اختيار لون أو صورة كخلفية للبطاقة.',
                  imagePath: 'assets/images/guide/change_background.png',
                ),
                GuideStep(
                  step: 6,
                  title: 'ترتيب العناصر',
                  description:
                      'يمكنك تغيير ترتيب العناصر (إلى الأمام أو الخلف) بالضغط المطول على العنصر واختيار "نقل إلى الأمام" أو "نقل إلى الخلف" من القائمة.',
                  imagePath: 'assets/images/guide/arrange_elements.png',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // قسم ميزات الذكاء الاصطناعي
            InteractiveGuideCard(
              title: 'ميزات الذكاء الاصطناعي',
              icon: Icons.auto_awesome,
              isFavorite: _favoriteSections.contains('ai_features'),
              onFavoriteToggle: () => _toggleFavorite('ai_features'),
              children: [
                GuideStep(
                  step: 1,
                  title: 'توليد النصوص',
                  description:
                      'يمكنك استخدام ميزة توليد النصوص بالذكاء الاصطناعي لإنشاء نصوص مخصصة للبطاقات. ما عليك سوى وصف ما تريده، مثل "تهنئة بعيد ميلاد لصديق مقرب" أو "رسالة شكر لمعلم"، وسيقوم الذكاء الاصطناعي بإنشاء نص مناسب.',
                  imagePath: 'assets/images/guide/ai_text.png',
                ),
                GuideStep(
                  step: 2,
                  title: 'توليد الصور',
                  description:
                      'يمكنك استخدام ميزة توليد الصور بالذكاء الاصطناعي لإنشاء صور فريدة للبطاقات. ما عليك سوى وصف الصورة التي تريدها، مثل "باقة ورد جميلة" أو "منظر طبيعي لغروب الشمس"، وسيقوم الذكاء الاصطناعي بإنشاء صورة مناسبة.',
                  imagePath: 'assets/images/guide/ai_image.png',
                ),
                GuideStep(
                  step: 3,
                  title: 'اقتراحات التصميم',
                  description:
                      'يقدم الذكاء الاصطناعي اقتراحات لتحسين تصميم البطاقة، مثل تنسيق الألوان وترتيب العناصر. انقر على زر "اقتراحات" للحصول على توصيات مخصصة لبطاقتك.',
                  imagePath: 'assets/images/guide/ai_suggestions.png',
                ),
                GuideStep(
                  step: 4,
                  title: 'إنشاء بطاقة كاملة',
                  description:
                      'يمكنك استخدام ميزة "إنشاء بطاقة بالذكاء الاصطناعي" لإنشاء بطاقة كاملة بناءً على وصفك. ما عليك سوى تحديد المناسبة ووصف ما تريده، وسيقوم الذكاء الاصطناعي بإنشاء بطاقة كاملة يمكنك تعديلها لاحقًا.',
                  imagePath: 'assets/images/guide/ai_full_card.png',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // نصائح التصميم
            InteractiveGuideCard(
              title: 'نصائح التصميم',
              icon: Icons.design_services_rounded,
              isFavorite: _favoriteSections.contains('design_tips'),
              onFavoriteToggle: () => _toggleFavorite('design_tips'),
              children: [
                GuideStep(
                  step: 1,
                  title: 'نظام الألوان',
                  description:
                      'استخدم ألوانًا متناسقة ولا تستخدم أكثر من 3-4 ألوان في البطاقة الواحدة. يمكنك استخدام أداة اختيار الألوان المتناسقة في التطبيق للحصول على مجموعة ألوان متناسقة.',
                  imagePath: 'assets/images/guide/color_scheme.png',
                ),
                GuideStep(
                  step: 2,
                  title: 'الطباعة والخطوط',
                  description:
                      'استخدم خطوطًا مقروءة واحرص على التباين بين لون النص والخلفية. تجنب استخدام أكثر من نوعين أو ثلاثة من الخطوط في البطاقة الواحدة.',
                  imagePath: 'assets/images/guide/typography.png',
                ),
                GuideStep(
                  step: 3,
                  title: 'التوازن البصري',
                  description:
                      'احرص على توزيع العناصر بشكل متوازن في البطاقة. تجنب تكديس جميع العناصر في جانب واحد واترك مساحات فارغة كافية بين العناصر.',
                  imagePath: 'assets/images/guide/visual_balance.png',
                ),
                GuideStep(
                  step: 4,
                  title: 'البساطة',
                  description:
                      'البساطة هي مفتاح التصميم الجيد. تجنب استخدام الكثير من العناصر والتأثيرات التي قد تشتت الانتباه عن الرسالة الرئيسية للبطاقة.',
                  imagePath: 'assets/images/guide/simplicity.png',
                ),
                GuideStep(
                  step: 5,
                  title: 'التناسق',
                  description:
                      'احرص على التناسق في استخدام الأنماط والألوان والخطوط في جميع أنحاء البطاقة. هذا يساعد على إنشاء تصميم متماسك ومهني.',
                  imagePath: 'assets/images/guide/consistency.png',
                ),
              ],
            ),
            // قسم مشاركة البطاقات
            InteractiveGuideCard(
              title: 'مشاركة البطاقات',
              icon: Icons.share_rounded,
              isFavorite: _favoriteSections.contains('sharing_cards'),
              onFavoriteToggle: () => _toggleFavorite('sharing_cards'),
              children: [
                GuideStep(
                  step: 1,
                  title: 'مشاركة البطاقة',
                  description:
                      'بعد الانتهاء من تصميم البطاقة، يمكنك مشاركتها مع الآخرين. انقر على زر "مشاركة" في شريط الأدوات لفتح قائمة خيارات المشاركة.',
                  imagePath: 'assets/images/guide/share_button.png',
                ),
                GuideStep(
                  step: 2,
                  title: 'خيارات المشاركة',
                  description:
                      'يمكنك مشاركة البطاقة عبر تطبيقات التواصل الاجتماعي مثل واتساب وفيسبوك وتويتر، أو عبر البريد الإلكتروني أو الرسائل النصية.',
                  imagePath: 'assets/images/guide/share_options.png',
                ),
                GuideStep(
                  step: 3,
                  title: 'حفظ البطاقة كصورة',
                  description:
                      'يمكنك حفظ البطاقة كصورة على جهازك بالنقر على زر "حفظ كصورة" في قائمة المشاركة. سيتم حفظ البطاقة في معرض الصور على جهازك.',
                  imagePath: 'assets/images/guide/save_as_image.png',
                ),
                GuideStep(
                  step: 4,
                  title: 'طباعة البطاقة',
                  description:
                      'يمكنك طباعة البطاقة بالنقر على زر "طباعة" في قائمة المشاركة. سيتم فتح نافذة الطباعة حيث يمكنك اختيار الطابعة وإعدادات الطباعة.',
                  imagePath: 'assets/images/guide/print_card.png',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // قسم الإعدادات والتخصيص
            InteractiveGuideCard(
              title: 'الإعدادات والتخصيص',
              icon: Icons.settings,
              isFavorite: _favoriteSections.contains('settings'),
              onFavoriteToggle: () => _toggleFavorite('settings'),
              children: [
                GuideStep(
                  step: 1,
                  title: 'الوصول إلى الإعدادات',
                  description:
                      'يمكنك الوصول إلى إعدادات التطبيق من خلال النقر على زر "الإعدادات" في القائمة الجانبية. هنا يمكنك تخصيص تجربتك في التطبيق.',
                  imagePath: 'assets/images/guide/settings_access.png',
                ),
                GuideStep(
                  step: 2,
                  title: 'تغيير اللغة',
                  description:
                      'يمكنك تغيير لغة التطبيق من خلال النقر على "اللغة" في قائمة الإعدادات واختيار اللغة المفضلة لديك.',
                  imagePath: 'assets/images/guide/language_settings.png',
                ),
                GuideStep(
                  step: 3,
                  title: 'تغيير المظهر',
                  description:
                      'يمكنك تغيير مظهر التطبيق من خلال النقر على "المظهر" في قائمة الإعدادات واختيار الوضع الفاتح أو الوضع الداكن أو اتباع إعدادات النظام.',
                  imagePath: 'assets/images/guide/theme_settings.png',
                ),
                GuideStep(
                  step: 4,
                  title: 'إدارة الحساب',
                  description:
                      'يمكنك إدارة حسابك من خلال النقر على "حسابي" في قائمة الإعدادات. هنا يمكنك تحديث معلومات ملفك الشخصي وتغيير كلمة المرور وإدارة إعدادات الخصوصية.',
                  imagePath: 'assets/images/guide/account_settings.png',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // قسم استكشاف الأخطاء وإصلاحها
            InteractiveGuideCard(
              title: 'استكشاف الأخطاء وإصلاحها',
              icon: Icons.help_outline,
              isFavorite: _favoriteSections.contains('troubleshooting'),
              onFavoriteToggle: () => _toggleFavorite('troubleshooting'),
              children: [
                GuideStep(
                  step: 1,
                  title: 'مشاكل تسجيل الدخول',
                  description:
                      'إذا واجهت مشكلة في تسجيل الدخول، تأكد من صحة بريدك الإلكتروني وكلمة المرور. يمكنك استخدام خيار "نسيت كلمة المرور" لإعادة تعيين كلمة المرور الخاصة بك.',
                  imagePath: 'assets/images/guide/login_issues.png',
                ),
                GuideStep(
                  step: 2,
                  title: 'مشاكل في حفظ البطاقات',
                  description:
                      'إذا واجهت مشكلة في حفظ البطاقات، تأكد من اتصالك بالإنترنت وأنك قمت بتسجيل الدخول إلى حسابك. يمكنك أيضًا محاولة حفظ البطاقة كمسودة أولاً ثم حفظها بشكل نهائي.',
                  imagePath: 'assets/images/guide/saving_issues.png',
                ),
                GuideStep(
                  step: 3,
                  title: 'مشاكل في تحميل الصور',
                  description:
                      'إذا واجهت مشكلة في تحميل الصور، تأكد من أن حجم الصورة لا يتجاوز الحد المسموح به (10 ميجابايت). يمكنك أيضًا محاولة تقليل حجم الصورة قبل تحميلها.',
                  imagePath: 'assets/images/guide/image_upload_issues.png',
                ),
                GuideStep(
                  step: 4,
                  title: 'الاتصال بالدعم',
                  description:
                      'إذا استمرت المشكلة، يمكنك الاتصال بفريق الدعم من خلال النقر على "الدعم" في قائمة الإعدادات أو إرسال بريد إلكتروني إلى <EMAIL>.',
                  imagePath: 'assets/images/guide/contact_support.png',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تبديل حالة المفضلة للقسم
  void _toggleFavorite(String sectionId) {
    setState(() {
      if (_favoriteSections.contains(sectionId)) {
        _favoriteSections.remove(sectionId);
      } else {
        _favoriteSections.add(sectionId);
      }
    });
  }
}
