// lib/features/user_guide/presentation/widgets/guide_section.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../core/constants/app_colors.dart';

/// قسم في دليل المستخدم
/// يحتوي على عنوان ومجموعة من العناصر
class GuideSection extends StatelessWidget {
  /// عنوان القسم
  final String title;

  /// عناصر القسم
  final List<Widget> children;

  /// إنشاء قسم في دليل المستخدم
  const GuideSection({
    super.key,
    required this.title,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.with<PERSON><PERSON><PERSON>(13), // 0.05 * 255 = ~13
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getIconForTitle(title),
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // محتوى القسم
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار أيقونة مناسبة لعنوان القسم
  IconData _getIconForTitle(String title) {
    // تحديد الأيقونة بناءً على عنوان القسم
    if (title.contains('مقدمة') || title.contains('نظرة عامة')) {
      return Icons.info_outline;
    } else if (title.contains('التنقل')) {
      return Icons.navigation;
    } else if (title.contains('إضافة') || title.contains('العناصر')) {
      return Icons.add_circle_outline;
    } else if (title.contains('تحرير')) {
      return Icons.edit;
    } else if (title.contains('تنسيق') || title.contains('النص')) {
      return Icons.text_fields;
    } else if (title.contains('الذكاء') || title.contains('توليد')) {
      return Icons.auto_awesome;
    } else if (title.contains('حفظ')) {
      return Icons.save;
    } else if (title.contains('تصدير')) {
      return Icons.upload_file;
    } else if (title.contains('مشاركة')) {
      return Icons.share;
    } else if (title.contains('طباعة')) {
      return Icons.print;
    } else if (title.contains('نصائح')) {
      return Icons.lightbulb_outline;
    } else if (title.contains('اختصارات')) {
      return Icons.keyboard;
    }

    // أيقونة افتراضية
    return Icons.article;
  }
}
