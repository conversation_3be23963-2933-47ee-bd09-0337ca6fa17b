import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:google_fonts/google_fonts.dart';
import 'app.dart';
import 'core/services/cache_cleanup_service.dart';
import 'core/services/cache_service.dart';
import 'core/services/firebase_messaging_service.dart';
import 'core/services/notification_service.dart';
// تم تعليق استيراد user_occasions_notification_service.dart لأنه غير متوفر حاليًا
import 'firebase_options.dart';
import 'injection_container.dart' as di;

/* مفتاح تنقّل عام */
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

/* معالج خلفية واحد */
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    // استخدم UID للمستخدم الحالي بدلاً من message.data['uid']
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;
    final uid = user.uid;

    try {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(uid)
          .collection('notifications')
          .add({
        'title': message.notification?.title ?? message.data['title'] ?? '',
        'body': message.notification?.body ?? message.data['body'] ?? '',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
      });
    } catch (e) {
      // تجاهل أخطاء Firestore في الخلفية
      debugPrint('خطأ في حفظ الإشعار في Firestore: $e');
    }
  } catch (e) {
    // تجاهل أي أخطاء في معالج الخلفية
    debugPrint('خطأ في معالج الرسائل في الخلفية: $e');
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // تهيئة خطوط Google Fonts - السماح بتحميل الخطوط في وقت التشغيل دائماً
  GoogleFonts.config.allowRuntimeFetching = true;

  // تهيئة حاوية التبعيات
  await di.init(); // Dependency Injection

  // تنظيف ذاكرة التخزين المؤقت القديمة
  await di.sl<CacheService>().clearOldCache(olderThan: 3);

  // بدء خدمة تنظيف الذاكرة المؤقتة بشكل دوري
  di.sl<CacheCleanupService>().startPeriodicCleanup();

  // ── اشترك في topic لجميع المستخدمين ليصلك broadcast ──
  // تجاهل الاشتراك في الموضوعات على منصة الويب لأنها غير مدعومة
  try {
    if (!kIsWeb) {
      await FirebaseMessaging.instance.subscribeToTopic('allUsers');
    }

    // تهيئة خدمات الإشعارات
    await NotificationService.instance
        .initLocalNotifications(); // تهيئة الإشعارات المحلية
    await FirebaseMessagingService.instance.init(); // تهيئة إشعارات Firebase
  } catch (e) {
    debugPrint('خطأ في تهيئة خدمات الإشعارات: $e');
    // تجاهل الخطأ واستمر في تشغيل التطبيق
  }

  // تم تعليق تهيئة خدمة الإشعارات للمناسبات الخاصة لأنها غير متوفرة حاليًا
  // await di.sl<UserOccasionsNotificationService>().initialize();

  // تجاهل إعدادات الإشعارات على منصة الويب لأنها قد تسبب مشاكل
  try {
    if (!kIsWeb) {
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);
      await FirebaseMessaging.instance
          .requestPermission(); // طلب إذن الإشعارات لنظام iOS

      /* ── Foreground إشعار محلي فقط (فتح الصفحة يتولاه LocalNotificationsService) ── */
      FirebaseMessaging.onMessage.listen((msg) async {
        try {
          await NotificationService.instance.showLocalNotification(
            msg.notification?.title ?? 'بطاقة جديدة',
            msg.notification?.body ?? '',
            payload: 'notifications',
          );
        } catch (e) {
          debugPrint('خطأ في عرض الإشعار المحلي: $e');
        }
      });

      /* ── إذا ضغط المستخدم على Push وكان التطبيق بالخلفية ── */
      FirebaseMessaging.onMessageOpenedApp.listen((msg) {
        if (msg.data['route'] == 'notifications') {
          navigatorKey.currentState?.pushNamed('/notifications');
        }
      });

      /* ── إذا فتح التطبيق عن طريق Push وهو مغلق ── */
      try {
        final initialMsg = await FirebaseMessaging.instance.getInitialMessage();
        if (initialMsg?.data['route'] == 'notifications') {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            navigatorKey.currentState?.pushNamed('/notifications');
          });
        }
      } catch (e) {
        debugPrint('خطأ في الحصول على الرسالة الأولية: $e');
      }
    }
  } catch (e) {
    debugPrint('خطأ في إعداد خدمات الإشعارات: $e');
    // تجاهل الخطأ واستمر في تشغيل التطبيق
  }

  // Ocultar la barra de estado y la barra de navegación para maximizar el espacio de la pantalla
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

  // Establecer la orientación preferida
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(MyApp(navigatorKey: navigatorKey));
}
