// lib/injection_container.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import 'core/services/auth_persistence_service.dart';
import 'core/services/cache_cleanup_service.dart';
import 'core/services/cache_service.dart';
import 'core/services/feedback_service.dart';
import 'core/services/navigation_service.dart';
import 'core/services/notification_service.dart';
import 'core/services/user_guide_service.dart';

// ========================= AI =========================
import 'features/ai/data/datasources/ai_local_data_source.dart';
import 'features/ai/data/datasources/ai_remote_data_source.dart';
import 'features/ai/data/repositories/ai_repository_impl.dart';
import 'features/ai/domain/repositories/ai_repository.dart';
import 'features/ai/domain/usecases/generate_ai_image_usecase.dart';
import 'features/ai/domain/usecases/generate_ai_text_usecase.dart';
import 'features/ai/domain/usecases/generate_ai_text_advanced_usecase.dart';
import 'features/ai/domain/usecases/get_image_history_usecase.dart';
import 'features/ai/domain/usecases/get_suggested_prompts_usecase.dart';
import 'features/ai/domain/usecases/get_text_history_usecase.dart';
import 'features/ai/domain/usecases/save_image_to_history_usecase.dart';
import 'features/ai/domain/usecases/save_to_history_usecase.dart';
import 'features/ai/presentation/blocs/ai_bloc.dart';
import 'features/ai/presentation/blocs/ai_image_generator/ai_image_generator_bloc.dart';
// ========================= Auth =========================
import 'features/auth/data/datasources/auth_remote_data_source.dart';
import 'features/auth/data/repositories_impl/auth_repository_impl.dart';
import 'features/auth/domain/repositories/auth_repository.dart';
import 'features/auth/domain/usecases/get_current_user_usecase.dart';
import 'features/auth/domain/usecases/login_with_email_usecase.dart';
import 'features/auth/domain/usecases/logout_usecase.dart';
import 'features/auth/domain/usecases/register_with_email_usecase.dart';
import 'features/auth/presentation/blocs/auth/auth_bloc.dart';
import 'features/auth/presentation/blocs/login/login_bloc.dart';
import 'features/auth/presentation/blocs/register/register_bloc.dart';
// ========================= Home =========================
import 'features/home/<USER>/datasources/home_remote_data_source.dart';
import 'features/home/<USER>/repositories_impl/home_repository_impl.dart';
import 'features/home/<USER>/repositories/home_repository.dart';
import 'features/home/<USER>/usecases/get_banners_usecase.dart';
import 'features/home/<USER>/usecases/get_banners_stream_usecase.dart';
import 'features/home/<USER>/usecases/get_card_by_id_usecase.dart';
import 'features/home/<USER>/usecases/get_occasions_usecase.dart';
import 'features/home/<USER>/usecases/get_occasions_stream_usecase.dart';
import 'features/home/<USER>/usecases/get_popular_cards_usecase.dart';
import 'features/home/<USER>/usecases/get_popular_cards_stream_usecase.dart';
import 'features/home/<USER>/blocs/home/<USER>';
// تم تعليق استيرادات mashair_card لأنها غير متوفرة حاليًا
// import 'features/mashair_card/data/datasources/mashair_cards_remote_data_source.dart';
// import 'features/mashair_card/data/repositories_impl/card_repository_impl.dart';
// import 'features/mashair_card/data/repositories_impl/mashair_cards_repository_impl.dart';
// import 'features/mashair_card/domain/repositories/card_repository.dart';
// import 'features/mashair_card/domain/repositories/mashair_cards_repository.dart';
// import 'features/mashair_card/domain/usecases/add_image_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/add_signature_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/add_song_link_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/add_sticker_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/add_text_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/add_text_signature_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/add_rectangle_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/add_circle_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/get_cards_by_occasion_usecase.dart';
// import 'features/mashair_card/domain/usecases/get_popular_cards_usecase.dart' as mashair_card;
// import 'features/mashair_card/domain/usecases/remove_element_usecase.dart';
// import 'features/mashair_card/domain/usecases/update_element_position_usecase.dart';
// import 'features/mashair_card/domain/usecases/update_element_transform_usecase.dart';
// import 'features/mashair_card/domain/usecases/update_text_properties_usecase.dart';
// import 'features/mashair_card/presentation/blocs/cards_list/cards_list_bloc.dart';
// ========================= Notifications =========================
import 'features/notifications/data/datasources/notifications_remote_data_source.dart';
import 'features/notifications/data/repositories/notifications_repository_impl.dart'
    as notif_impl;
import 'features/notifications/domain/repositories/notifications_repository.dart';
import 'features/notifications/domain/usecases/get_notifications_usecase.dart';
import 'features/notifications/domain/usecases/mark_notification_read_usecase.dart';
import 'features/notifications/presentation/bloc/notifications_bloc.dart';
// ========================= Profile =========================
import 'features/profile/data/datasources/profile_remote_data_source.dart';
import 'features/profile/data/repositories_impl/profile_repository_impl.dart';
import 'features/profile/domain/repositories/profile_repository.dart';
import 'features/profile/domain/usecases/get_profile_usecase.dart';
import 'features/profile/domain/usecases/update_profile_usecase.dart';
import 'features/profile/domain/usecases/upload_profile_image_usecase.dart';
import 'features/profile/presentation/blocs/profile/profile_bloc.dart';
// ========================= Suggestions =========================
// Suggestions feature removed
// ========================= Settings =========================
import 'features/settings/data/repositories/settings_repository_impl.dart';
import 'features/settings/domain/repositories/settings_repository.dart';
import 'features/settings/domain/usecases/load_settings_usecase.dart';
import 'features/settings/domain/usecases/save_settings_usecase.dart';
import 'features/settings/domain/usecases/update_language_usecase.dart';
import 'features/settings/domain/usecases/update_theme_mode_usecase.dart';
import 'features/settings/presentation/blocs/settings/settings_bloc.dart';
// ========================= Templates =========================
// Templates feature removed
// ========================= Cards History ========================
// تم تعليق استيرادات Cards History لأنها غير متوفرة حاليًا
// import 'features/cards_history/domain/repositories/cards_history_repository.dart';
// import 'features/cards_history/data/repositories/cards_history_repository_impl.dart';
// import 'features/cards_history/presentation/blocs/cards_history/cards_history_bloc.dart';
// ========================= User Occasions ========================
// تم تعليق استيرادات User Occasions لأنها غير متوفرة حاليًا
// import 'features/user_occasions/data/datasources/user_occasions_remote_datasource.dart';
// import 'features/user_occasions/data/datasources/user_occasions_notification_service.dart';
// import 'features/user_occasions/data/repositories/user_occasions_repository_impl.dart';
// import 'features/user_occasions/domain/repositories/user_occasions_repository.dart';
// import 'features/user_occasions/presentation/blocs/user_occasions/user_occasions_bloc.dart';

import 'features/create_card/domain/repositories/card_repository.dart';
import 'features/create_card/data/repositories/card_repository_impl.dart';
import 'features/create_card/presentation/bloc/card_editor_bloc.dart';
import 'features/create_card/domain/usecases/add_text_element_usecase.dart';
import 'features/create_card/domain/usecases/add_sticker_element_usecase.dart';
import 'features/create_card/domain/usecases/add_image_element_usecase.dart';
import 'features/create_card/domain/usecases/add_qr_code_element_usecase.dart';
import 'features/create_card/domain/usecases/add_signature_element_usecase.dart';
import 'features/create_card/domain/usecases/remove_element_usecase.dart';
import 'features/create_card/domain/usecases/update_element_position_usecase.dart';
import 'features/create_card/domain/usecases/update_element_transform_usecase.dart';
import 'features/create_card/domain/usecases/update_text_properties_usecase.dart';

final sl = GetIt.instance;

Future<void> init() async {
// ========== External ==========
  sl.registerLazySingleton(() => FirebaseAuth.instance);
  sl.registerLazySingleton(() => FirebaseFirestore.instance);
  // GoogleSignIn removed
  sl.registerLazySingleton(() => http.Client());

  // Inicializar SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);

// ========== Services ==========
  sl.registerLazySingleton(() => NotificationService.instance);

  // تسجيل خدمة الذاكرة المؤقتة كخدمة وحيدة (Singleton)
  sl.registerLazySingleton(() => CacheService());

  // تسجيل خدمة تنظيف الذاكرة المؤقتة كخدمة وحيدة (Singleton)
  sl.registerLazySingleton(() => CacheCleanupService());

  // تسجيل خدمة التنقل المحسنة
  sl.registerLazySingleton(() => NavigationService());

  // تسجيل خدمة دليل المستخدم
  sl.registerLazySingleton(() => UserGuideService());

  // تسجيل خدمة التغذية الراجعة
  sl.registerLazySingleton(() => FeedbackService());

  // تسجيل خدمة حفظ بيانات المستخدم للتسجيل التلقائي
  sl.registerLazySingleton(() => AuthPersistenceService());

// ========== Data Sources ==========
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      firebaseAuth: sl<FirebaseAuth>(),
      firestore: sl<FirebaseFirestore>(),
    ),
  );

  // تم تعليق تسجيل GreetingCardsRemoteDataSource لأنه غير متوفر حاليًا
  // sl.registerLazySingleton<GreetingCardsRemoteDataSource>(
  //   () => GreetingCardsRemoteDataSourceImpl(
  //     firestore: sl<FirebaseFirestore>(),
  //   ),
  // );

  sl.registerLazySingleton<HomeRemoteDataSource>(
    () => HomeRemoteDataSourceImpl(
      firestore: sl<FirebaseFirestore>(),
    ),
  );

  sl.registerLazySingleton<ProfileRemoteDataSource>(
    () => ProfileRemoteDataSourceImpl(
      firestore: sl<FirebaseFirestore>(),
    ),
  );

  sl.registerLazySingleton<AiRemoteDataSource>(
    () => AiRemoteDataSourceImpl(client: sl<http.Client>()),
  );

  sl.registerLazySingleton<AiLocalDataSource>(
    () => AiLocalDataSourceImpl(sharedPreferences: sl<SharedPreferences>()),
  );

  // Suggestions removed

  sl.registerLazySingleton<NotificationsRemoteDataSource>(
    () => NotificationsRemoteDataSourceImpl(
      firestore: sl<FirebaseFirestore>(),
    ),
  );

// ========== Repositories ==========
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(remoteDataSource: sl<AuthRemoteDataSource>()),
  );

  // تم تعليق تسجيل GreetingCardsRepository لأنه غير متوفر حاليًا
  // sl.registerLazySingleton<GreetingCardsRepository>(
  //   () => GreetingCardsRepositoryImpl(
  //     remoteDataSource: sl<GreetingCardsRemoteDataSource>(),
  //   ),
  // );

  sl.registerLazySingleton<HomeRepository>(
    () => HomeRepositoryImpl(remoteDataSource: sl<HomeRemoteDataSource>()),
  );

  sl.registerLazySingleton<ProfileRepository>(
    () =>
        ProfileRepositoryImpl(remoteDataSource: sl<ProfileRemoteDataSource>()),
  );

  sl.registerLazySingleton<AiRepository>(
    () => AiRepositoryImpl(
      remoteDataSource: sl<AiRemoteDataSource>(),
      localDataSource: sl<AiLocalDataSource>(),
    ),
  );

  sl.registerLazySingleton<CardRepository>(
    () => CardRepositoryImpl(),
  );

  // Templates and Suggestions removed

  sl.registerLazySingleton<NotificationsRepository>(
    () => notif_impl.NotificationsRepositoryImpl(
      remote: sl<NotificationsRemoteDataSource>(),
    ),
  );

  // Settings Repository
  sl.registerLazySingleton<SettingsRepository>(
    () => SettingsRepositoryImpl(),
  );

// ========== UseCases ==========
// Auth
  sl.registerLazySingleton(
    () => LoginWithEmailUseCase(sl<AuthRepository>()),
  );
  sl.registerLazySingleton(
    () => RegisterWithEmailUseCase(sl<AuthRepository>()),
  );

  sl.registerLazySingleton(
    () => LogoutUseCase(sl<AuthRepository>()),
  );
  sl.registerLazySingleton(
    () => GetCurrentUserUseCase(sl<AuthRepository>()),
  );

// تم تعليق تسجيل Greeting Cards لأنه غير متوفر حاليًا
  // sl.registerLazySingleton(
  //   () => mashair_card.GetPopularCardsUseCase(sl<GreetingCardsRepository>()),
  // );
  // sl.registerLazySingleton(
  //   () => GetCardsByOccasionUseCase(sl<GreetingCardsRepository>()),
  // );
  // CreateCardUseCase removed

// Home
  sl.registerLazySingleton(
    () => GetBannersUseCase(sl<HomeRepository>()),
  );
  sl.registerLazySingleton(
    () => GetOccasionsUseCase(sl<HomeRepository>()),
  );
  sl.registerLazySingleton(
    () => GetPopularCardsUseCase(sl<HomeRepository>()),
  );
  sl.registerLazySingleton(
    () => GetCardByIdUseCase(sl<HomeRepository>()),
  );

  // Stream use cases for real-time updates
  sl.registerLazySingleton(
    () => GetBannersStreamUseCase(sl<HomeRepository>()),
  );
  sl.registerLazySingleton(
    () => GetOccasionsStreamUseCase(sl<HomeRepository>()),
  );
  sl.registerLazySingleton(
    () => GetPopularCardsStreamUseCase(sl<HomeRepository>()),
  );

// Profile
  sl.registerLazySingleton(
    () => GetProfileUseCase(sl<ProfileRepository>()),
  );
  sl.registerLazySingleton(
    () => UpdateProfileUseCase(sl<ProfileRepository>()),
  );
  sl.registerLazySingleton(
    () => UploadProfileImageUseCase(sl<ProfileRepository>()),
  );

// AI - Text Generation
  sl.registerLazySingleton(
    () => GenerateAiTextUseCase(sl<AiRepository>()),
  );
  sl.registerLazySingleton(
    () => GenerateAiTextAdvancedUseCase(sl<AiRepository>()),
  );
  sl.registerLazySingleton(
    () => GetSuggestedPromptsUseCase(sl<AiRepository>()),
  );
  sl.registerLazySingleton(
    () => GetTextHistoryUseCase(sl<AiRepository>()),
  );
  sl.registerLazySingleton(
    () => SaveToHistoryUseCase(sl<AiRepository>()),
  );

  // AI - Image Generation
  sl.registerLazySingleton(
    () => GenerateAiImageUseCase(sl<AiRepository>()),
  );
  sl.registerLazySingleton(
    () => GetImageHistoryUseCase(sl<AiRepository>()),
  );
  sl.registerLazySingleton(
    () => SaveImageToHistoryUseCase(sl<AiRepository>()),
  );

// تسجيل Card Editor UseCases
  sl.registerLazySingleton(
    () => AddTextElementUseCase(sl<CardRepository>()),
  );
  sl.registerLazySingleton(
    () => AddStickerElementUseCase(sl<CardRepository>()),
  );
  sl.registerLazySingleton(
    () => AddImageElementUseCase(sl<CardRepository>()),
  );
  sl.registerLazySingleton(
    () => AddQrCodeElementUseCase(sl<CardRepository>()),
  );
  sl.registerLazySingleton(
    () => AddSignatureElementUseCase(sl<CardRepository>()),
  );
  sl.registerLazySingleton(
    () => RemoveElementUseCase(sl<CardRepository>()),
  );
  sl.registerLazySingleton(
    () => UpdateElementPositionUseCase(sl<CardRepository>()),
  );
  sl.registerLazySingleton(
    () => UpdateElementTransformUseCase(sl<CardRepository>()),
  );
  sl.registerLazySingleton(
    () => UpdateTextPropertiesUseCase(sl<CardRepository>()),
  );
  // sl.registerLazySingleton(
  //   () => AddRectangleElementUseCase(sl<CardRepository>()),
  // );
  // sl.registerLazySingleton(
  //   () => AddCircleElementUseCase(sl<CardRepository>()),
  // );
  // sl.registerLazySingleton(
  //   () => RemoveElementUseCase(sl<CardRepository>()),
  // );
  // sl.registerLazySingleton(
  //   () => UpdateElementPositionUseCase(sl<CardRepository>()),
  // );
  // sl.registerLazySingleton(
  //   () => UpdateElementTransformUseCase(sl<CardRepository>()),
  // );
  // sl.registerLazySingleton(
  //   () => UpdateTextPropertiesUseCase(sl<CardRepository>()),
  // );

// Suggestions removed

// Notifications
  sl.registerLazySingleton(
    () => GetNotificationsUseCase(sl<NotificationsRepository>()),
  );
  sl.registerLazySingleton(
    () => MarkNotificationReadUseCase(sl<NotificationsRepository>()),
  );

// Settings
  sl.registerLazySingleton(
    () => LoadSettingsUseCase(sl<SettingsRepository>()),
  );
  sl.registerLazySingleton(
    () => SaveSettingsUseCase(sl<SettingsRepository>()),
  );
  sl.registerLazySingleton(
    () => UpdateThemeModeUseCase(sl<SettingsRepository>()),
  );
  sl.registerLazySingleton(
    () => UpdateLanguageUseCase(sl<SettingsRepository>()),
  );

// ========== Blocs ==========
  sl.registerLazySingleton(
    () => AuthBloc(
      getCurrentUserUseCase: sl<GetCurrentUserUseCase>(),
      logoutUseCase: sl<LogoutUseCase>(),
    ),
  );

  sl.registerFactory(
    () => LoginBloc(
      loginWithEmailUseCase: sl<LoginWithEmailUseCase>(),
    ),
  );

  sl.registerFactory(() => RegisterBloc(sl<RegisterWithEmailUseCase>()));

  // تم تعليق تسجيل CardsListBloc لأنه غير متوفر حاليًا
  // sl.registerFactory(
  //   () => CardsListBloc(
  //     getPopularCardsUseCase: sl<mashair_card.GetPopularCardsUseCase>(),
  //     getCardsByOccasionUseCase: sl<GetCardsByOccasionUseCase>(),
  //   ),
  // );

  // CreateCardBloc removed

  sl.registerFactory(
    () => HomeBloc(
      getBannersUseCase: sl<GetBannersUseCase>(),
      getOccasionsUseCase: sl<GetOccasionsUseCase>(),
      getPopularCardsUseCase: sl<GetPopularCardsUseCase>(),
      getBannersStreamUseCase: sl<GetBannersStreamUseCase>(),
      getOccasionsStreamUseCase: sl<GetOccasionsStreamUseCase>(),
      getPopularCardsStreamUseCase: sl<GetPopularCardsStreamUseCase>(),
    ),
  );

  sl.registerFactory(
    () => ProfileBloc(
      getProfileUseCase: sl<GetProfileUseCase>(),
      updateProfileUseCase: sl<UpdateProfileUseCase>(),
    ),
  );

  sl.registerFactory(() => AiBloc(
        generateAiTextUseCase: sl<GenerateAiTextUseCase>(),
        generateAiTextAdvancedUseCase: sl<GenerateAiTextAdvancedUseCase>(),
        getSuggestedPromptsUseCase: sl<GetSuggestedPromptsUseCase>(),
        getTextHistoryUseCase: sl<GetTextHistoryUseCase>(),
      ));

  // تسجيل CardEditorBloc
  sl.registerFactory(() => CardEditorBloc(
        cardRepository: sl<CardRepository>(),
        addTextElementUseCase: sl<AddTextElementUseCase>(),
        addStickerElementUseCase: sl<AddStickerElementUseCase>(),
        addImageElementUseCase: sl<AddImageElementUseCase>(),
        addQrCodeElementUseCase: sl<AddQrCodeElementUseCase>(),
        addSignatureElementUseCase: sl<AddSignatureElementUseCase>(),
        removeElementUseCase: sl<RemoveElementUseCase>(),
        updateElementPositionUseCase: sl<UpdateElementPositionUseCase>(),
        updateTextPropertiesUseCase: sl<UpdateTextPropertiesUseCase>(),
      ));

  sl.registerFactory(() => AiImageGeneratorBloc(
        generateAiImageUseCase: sl<GenerateAiImageUseCase>(),
        getImageHistoryUseCase: sl<GetImageHistoryUseCase>(),
      ));

  // CardEditorBloc is now registered in registerCardEditorDependencies

  // SuggestionsBloc removed

  sl.registerFactory(
    () => NotificationsBloc(
      getUseCase: sl<GetNotificationsUseCase>(),
      markReadUseCase: sl<MarkNotificationReadUseCase>(),
    ),
  );

  sl.registerLazySingleton(
    () => SettingsBloc(
      loadSettingsUseCase: sl<LoadSettingsUseCase>(),
      saveSettingsUseCase: sl<SaveSettingsUseCase>(),
      updateThemeModeUseCase: sl<UpdateThemeModeUseCase>(),
      updateLanguageUseCase: sl<UpdateLanguageUseCase>(),
    ),
  );

  // تم تعليق تسجيل بلوك المناسبات الخاصة لأنه غير متوفر حاليًا
  // sl.registerFactory(
  //   () => UserOccasionsBloc(
  //     repository: sl<UserOccasionsRepository>(),
  //   ),
  // );
}
