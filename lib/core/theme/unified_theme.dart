// lib/core/theme/unified_theme.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants/app_colors.dart';
import '../constants/app_sizes.dart';

/// ثيم موحد للتطبيق
class UnifiedTheme {
  /// الحصول على ثيم الوضع الفاتح
  static ThemeData getLightTheme({required bool isArabic}) {
    // تحديد TextTheme المناسب باستخدام Google Fonts
    final TextTheme customTextTheme = isArabic
        ? GoogleFonts.cairoTextTheme(ThemeData.light().textTheme)
        : GoogleFonts.poppinsTextTheme(ThemeData.light().textTheme);

    // استخدام الخط من Google Fonts
    final String fontFamily = isArabic
        ? GoogleFonts.cairo().fontFamily!
        : GoogleFonts.poppins().fontFamily!;

    return ThemeData(
      brightness: Brightness.light,
      primaryColor: AppColors.primaryColor,
      scaffoldBackgroundColor: AppColors.backgroundColor,
      colorScheme: const ColorScheme.light(
        primary: AppColors.primaryColor,
        secondary: AppColors.accentColor,
        error: AppColors.errorColor,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryColor,
        elevation: 4,
        shadowColor: const Color(0x40000000),
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: fontFamily,
        ),
        toolbarTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontFamily: fontFamily,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
          textStyle: TextStyle(
            fontFamily: fontFamily,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 24,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primaryColor,
          textStyle: TextStyle(
            fontFamily: fontFamily,
            fontWeight: FontWeight.bold,
          ),
          side: const BorderSide(
            color: AppColors.primaryColor,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 24,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primaryColor,
          textStyle: TextStyle(
            fontFamily: fontFamily,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 24,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 12,
          horizontal: 16,
        ),
        filled: true,
        fillColor: Colors.grey[100],
        hintStyle: TextStyle(
          color: Colors.grey[500],
          fontFamily: fontFamily,
        ),
        labelStyle: TextStyle(
          color: Colors.grey[700],
          fontFamily: fontFamily,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.primaryColor,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.errorColor,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.errorColor,
            width: 2,
          ),
        ),
      ),
      textTheme: customTextTheme.apply(
        bodyColor: AppColors.textColor,
        displayColor: AppColors.textColor,
      ),
      fontFamily: fontFamily,
      dividerTheme: const DividerThemeData(
        color: Colors.grey,
        thickness: 0.5,
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade400;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryColor;
          }
          return Colors.grey.shade50;
        }),
        trackColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade200;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.accentColor;
          }
          return Colors.grey.shade300;
        }),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade400;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryColor;
          }
          return Colors.white;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade400;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryColor;
          }
          return Colors.grey.shade700;
        }),
      ),
      dialogTheme: DialogTheme(
        backgroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
        titleTextStyle: TextStyle(
          color: AppColors.textColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: fontFamily,
        ),
        contentTextStyle: TextStyle(
          color: AppColors.textColor,
          fontSize: 16,
          fontFamily: fontFamily,
        ),
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: Colors.white,
        elevation: 8,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppSizes.borderRadius),
          ),
        ),
      ),
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: Colors.white,
        elevation: 4,
        indicatorColor: AppColors.primaryColor.withAlpha(51), // 0.2 * 255 = ~51
        labelTextStyle: WidgetStateProperty.resolveWith<TextStyle>((states) {
          return TextStyle(
            fontFamily: fontFamily,
            fontSize: 12,
            fontWeight: states.contains(WidgetState.selected)
                ? FontWeight.bold
                : FontWeight.normal,
          );
        }),
        iconTheme: WidgetStateProperty.resolveWith<IconThemeData>((states) {
          return IconThemeData(
            color: states.contains(WidgetState.selected)
                ? AppColors.primaryColor
                : Colors.grey[700],
            size: 24,
          );
        }),
      ),
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: Colors.white,
        elevation: 4,
        selectedIconTheme: const IconThemeData(
          color: AppColors.primaryColor,
          size: 24,
        ),
        unselectedIconTheme: IconThemeData(
          color: Colors.grey[700],
          size: 24,
        ),
        selectedLabelTextStyle: TextStyle(
          color: AppColors.primaryColor,
          fontFamily: fontFamily,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelTextStyle: TextStyle(
          color: Colors.grey[700],
          fontFamily: fontFamily,
          fontSize: 12,
        ),
      ),
      listTileTheme: ListTileThemeData(
        tileColor: Colors.white,
        selectedTileColor:
            AppColors.primaryColor.withAlpha(26), // 0.1 * 255 = ~26
        iconColor: AppColors.primaryColor,
        textColor: AppColors.textColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
      ),
      tabBarTheme: TabBarTheme(
        labelColor: AppColors.primaryColor,
        unselectedLabelColor: Colors.grey[700],
        labelStyle: TextStyle(
          fontFamily: fontFamily,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: fontFamily,
        ),
        indicator: const UnderlineTabIndicator(
          borderSide: BorderSide(
            color: AppColors.primaryColor,
            width: 2,
          ),
        ),
      ),
      chipTheme: ChipThemeData(
        backgroundColor: Colors.grey[200],
        disabledColor: Colors.grey[300],
        selectedColor: AppColors.primaryColor.withAlpha(51), // 0.2 * 255 = ~51
        secondarySelectedColor:
            AppColors.accentColor.withAlpha(51), // 0.2 * 255 = ~51
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        labelStyle: TextStyle(
          color: AppColors.textColor,
          fontFamily: fontFamily,
        ),
        secondaryLabelStyle: TextStyle(
          color: AppColors.textColor,
          fontFamily: fontFamily,
        ),
        brightness: Brightness.light,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
        textStyle: TextStyle(
          color: Colors.white,
          fontFamily: fontFamily,
          fontSize: 12,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: Colors.grey[800],
        contentTextStyle: TextStyle(
          color: Colors.white,
          fontFamily: fontFamily,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// الحصول على ثيم الوضع الداكن
  static ThemeData getDarkTheme({required bool isArabic}) {
    // ألوان الوضع الداكن المحسنة
    const darkBackgroundColor = Color(0xFF0D1117);
    const darkSurfaceColor = Color(0xFF161B22);
    const darkCardColor = Color(0xFF21262D);
    const darkTextColor = Color(0xFFF0F6FC);
    const darkSecondaryTextColor = Color(0xFF8B949E);

    // تحديد TextTheme المناسب باستخدام Google Fonts
    final TextTheme customTextTheme = isArabic
        ? GoogleFonts.cairoTextTheme(ThemeData.dark().textTheme)
        : GoogleFonts.poppinsTextTheme(ThemeData.dark().textTheme);

    // استخدام الخط من Google Fonts
    final String fontFamily = isArabic
        ? GoogleFonts.cairo().fontFamily!
        : GoogleFonts.poppins().fontFamily!;

    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: AppColors.primaryColor,
      scaffoldBackgroundColor: darkBackgroundColor,
      colorScheme: ColorScheme.dark(
        primary: AppColors.primaryColor,
        secondary: AppColors.accentColor,
        error: AppColors.errorColor,
        surface: darkSurfaceColor,
        onSurface: darkTextColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onError: Colors.white,
      ),
      // إضافة تنسيق واضح للنص المدخل في حقول النص
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: AppColors.primaryColor,
        selectionColor: AppColors.primaryColor.withValues(alpha: 0.3),
        selectionHandleColor: AppColors.primaryColor,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: darkSurfaceColor,
        elevation: 0,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        iconTheme: IconThemeData(color: darkTextColor),
        titleTextStyle: TextStyle(
          color: darkTextColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: fontFamily,
        ),
        toolbarTextStyle: TextStyle(
          color: darkTextColor,
          fontSize: 16,
          fontFamily: fontFamily,
        ),
      ),
      cardTheme: CardTheme(
        color: darkCardColor,
        elevation: 4,
        shadowColor: Colors.black.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
          textStyle: TextStyle(
            fontFamily: fontFamily,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 24,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primaryColor,
          textStyle: TextStyle(
            fontFamily: fontFamily,
            fontWeight: FontWeight.bold,
          ),
          side: const BorderSide(
            color: AppColors.primaryColor,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 24,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primaryColor,
          textStyle: TextStyle(
            fontFamily: fontFamily,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          ),
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 24,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 12,
          horizontal: 16,
        ),
        filled: true,
        fillColor: Colors.grey[800],
        hintStyle: TextStyle(
          color: Colors.grey[400],
          fontFamily: fontFamily,
        ),
        labelStyle: TextStyle(
          color: Colors.grey[300],
          fontFamily: fontFamily,
        ),

        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.primaryColor,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.errorColor,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          borderSide: const BorderSide(
            color: AppColors.errorColor,
            width: 2,
          ),
        ),
      ),
      textTheme: customTextTheme.apply(
        bodyColor: darkTextColor,
        displayColor: darkTextColor,
      ).copyWith(
        // تحسين تنسيق النص في حقول الإدخال
        bodyLarge: customTextTheme.bodyLarge?.copyWith(
          color: darkTextColor,
          fontFamily: fontFamily,
        ),
        bodyMedium: customTextTheme.bodyMedium?.copyWith(
          color: darkTextColor,
          fontFamily: fontFamily,
        ),
        bodySmall: customTextTheme.bodySmall?.copyWith(
          color: darkTextColor,
          fontFamily: fontFamily,
        ),
      ),
      fontFamily: fontFamily,
      dividerTheme: DividerThemeData(
        color: Colors.grey[700],
        thickness: 0.5,
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade700;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryColor;
          }
          return Colors.grey.shade400;
        }),
        trackColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade800;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.accentColor.withAlpha(128); // 0.5 * 255 = ~128
          }
          return Colors.grey.shade700;
        }),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade700;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryColor;
          }
          return Colors.grey.shade800;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade700;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryColor;
          }
          return Colors.grey.shade300;
        }),
      ),
      dialogTheme: DialogTheme(
        backgroundColor: darkSurfaceColor,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
        titleTextStyle: TextStyle(
          color: darkTextColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: fontFamily,
        ),
        contentTextStyle: TextStyle(
          color: darkTextColor,
          fontSize: 16,
          fontFamily: fontFamily,
        ),
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: Colors.black, // أسود كامل في الوضع المظلم
        elevation: 8,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppSizes.borderRadius),
          ),
        ),
      ),
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: darkSurfaceColor,
        elevation: 4,
        indicatorColor: AppColors.primaryColor.withAlpha(51), // 0.2 * 255 = ~51
        labelTextStyle: WidgetStateProperty.resolveWith<TextStyle>((states) {
          return TextStyle(
            fontFamily: fontFamily,
            fontSize: 12,
            color: states.contains(WidgetState.selected)
                ? AppColors.primaryColor
                : Colors.grey[300],
            fontWeight: states.contains(WidgetState.selected)
                ? FontWeight.bold
                : FontWeight.normal,
          );
        }),
        iconTheme: WidgetStateProperty.resolveWith<IconThemeData>((states) {
          return IconThemeData(
            color: states.contains(WidgetState.selected)
                ? AppColors.primaryColor
                : Colors.grey[300],
            size: 24,
          );
        }),
      ),
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: darkSurfaceColor,
        elevation: 4,
        selectedIconTheme: const IconThemeData(
          color: AppColors.primaryColor,
          size: 24,
        ),
        unselectedIconTheme: IconThemeData(
          color: Colors.grey[300],
          size: 24,
        ),
        selectedLabelTextStyle: TextStyle(
          color: AppColors.primaryColor,
          fontFamily: fontFamily,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelTextStyle: TextStyle(
          color: Colors.grey[300],
          fontFamily: fontFamily,
          fontSize: 12,
        ),
      ),
      listTileTheme: ListTileThemeData(
        tileColor: darkSurfaceColor,
        selectedTileColor:
            AppColors.primaryColor.withAlpha(26), // 0.1 * 255 = ~26
        iconColor: AppColors.primaryColor,
        textColor: darkTextColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
      ),
      tabBarTheme: TabBarTheme(
        labelColor: AppColors.primaryColor,
        unselectedLabelColor: Colors.grey[300],
        labelStyle: TextStyle(
          fontFamily: fontFamily,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: fontFamily,
        ),
        indicator: const UnderlineTabIndicator(
          borderSide: BorderSide(
            color: AppColors.primaryColor,
            width: 2,
          ),
        ),
      ),
      chipTheme: ChipThemeData(
        backgroundColor: Colors.grey[800],
        disabledColor: Colors.grey[700],
        selectedColor: AppColors.primaryColor.withAlpha(51), // 0.2 * 255 = ~51
        secondarySelectedColor:
            AppColors.accentColor.withAlpha(51), // 0.2 * 255 = ~51
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        labelStyle: TextStyle(
          color: darkTextColor,
          fontFamily: fontFamily,
        ),
        secondaryLabelStyle: TextStyle(
          color: darkTextColor,
          fontFamily: fontFamily,
        ),
        brightness: Brightness.dark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: darkCardColor,
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
        textStyle: TextStyle(
          color: darkTextColor,
          fontFamily: fontFamily,
          fontSize: 12,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: darkCardColor,
        contentTextStyle: TextStyle(
          color: darkTextColor,
          fontFamily: fontFamily,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        ),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
