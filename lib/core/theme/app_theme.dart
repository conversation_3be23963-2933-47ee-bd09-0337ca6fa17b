import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants/app_colors.dart';

/// Manages the application themes (light and dark).
///
/// This class provides methods to get theme data for both light and dark modes,
/// as well as for different languages (Arabic and English).
class AppTheme {
  /// Gets the light theme for the application.
  ///
  /// @param isArabic Whether the theme is for Arabic language
  /// @return ThemeData configured for light mode
  static ThemeData getLightTheme({required bool isArabic}) {
    // تحديد الخط الأساسي للتطبيق
    final String fontFamily = isArabic ? 'Cairo' : 'Poppins';

    // تحديد TextTheme المناسب باستخدام Google Fonts
    final TextTheme customTextTheme = isArabic
        ? GoogleFonts.cairoTextTheme(ThemeData.light().textTheme)
        : GoogleFonts.poppinsTextTheme(ThemeData.light().textTheme);

    return ThemeData(
      brightness: Brightness.light,
      primaryColor: AppColors.primaryColor,
      scaffoldBackgroundColor: AppColors.backgroundColor,
      colorScheme: const ColorScheme.light(
        primary: AppColors.primaryColor,
        secondary: AppColors.accentColor,
        error: AppColors.errorColor,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryColor,
        elevation: 4,
        shadowColor: const Color(0x40000000),
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: fontFamily,
        ),
        toolbarTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontFamily: fontFamily,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
          textStyle: TextStyle(
            fontFamily: fontFamily,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        ),
      ),
      textTheme: customTextTheme.apply(
        bodyColor: AppColors.textColor,
        displayColor: AppColors.textColor,
      ),
      fontFamily: fontFamily,
      dividerTheme: const DividerThemeData(
        color: Colors.grey,
        thickness: 0.5,
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade400;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryColor;
          }
          return Colors.grey.shade50;
        }),
        trackColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade200;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.accentColor;
          }
          return Colors.grey.shade300;
        }),
      ),
      inputDecorationTheme: InputDecorationTheme(
        labelStyle: TextStyle(
          fontFamily: fontFamily,
          color: AppColors.textColor.withAlpha(180),
        ),
        hintStyle: TextStyle(
          fontFamily: fontFamily,
          color: AppColors.textColor.withAlpha(120),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      drawerTheme: DrawerThemeData(
        backgroundColor: Colors.white,
        scrimColor: Colors.black54,
      ),
    );
  }

  /// Gets the dark theme for the application.
  ///
  /// @param isArabic Whether the theme is for Arabic language
  /// @return ThemeData configured for dark mode
  static ThemeData getDarkTheme({required bool isArabic}) {
    // Dark theme colors
    const darkBackgroundColor = Color(0xFF121212);
    const darkSurfaceColor = Color(0xFF1E1E1E);
    const darkTextColor = Colors.white;

    // تحديد الخط الأساسي للتطبيق
    final String fontFamily = isArabic ? 'Cairo' : 'Poppins';

    // تحديد TextTheme المناسب باستخدام Google Fonts
    final TextTheme customTextTheme = isArabic
        ? GoogleFonts.cairoTextTheme(ThemeData.dark().textTheme)
        : GoogleFonts.poppinsTextTheme(ThemeData.dark().textTheme);

    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: AppColors.primaryColor,
      scaffoldBackgroundColor: darkBackgroundColor,
      colorScheme: const ColorScheme.dark(
        primary: AppColors.primaryColor,
        secondary: AppColors.accentColor,
        error: AppColors.errorColor,
        surface: darkSurfaceColor,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryColor,
        elevation: 4,
        shadowColor: const Color(0x40000000),
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: fontFamily,
        ),
        toolbarTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontFamily: fontFamily,
        ),
      ),
      cardTheme: CardTheme(
        color: darkSurfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
          textStyle: TextStyle(
            fontFamily: fontFamily,
            fontWeight: FontWeight.bold,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        ),
      ),
      textTheme: customTextTheme.apply(
        bodyColor: darkTextColor,
        displayColor: darkTextColor,
      ),
      fontFamily: fontFamily,
      dividerTheme: const DividerThemeData(
        color: Colors.grey,
        thickness: 0.5,
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade700;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryColor;
          }
          return Colors.grey.shade400;
        }),
        trackColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade800;
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.accentColor.withValues(
              alpha: 128.0,
              red: AppColors.accentColor.r.toDouble(),
              green: AppColors.accentColor.g.toDouble(),
              blue: AppColors.accentColor.b.toDouble(),
            );
          }
          return Colors.grey.shade700;
        }),
      ),
      inputDecorationTheme: InputDecorationTheme(
        labelStyle: TextStyle(
          fontFamily: fontFamily,
          color: darkTextColor.withValues(
            alpha: (255 * 0.7).toDouble(),
            red: darkTextColor.r,
            green: darkTextColor.g,
            blue: darkTextColor.b,
          ),
        ),
        hintStyle: TextStyle(
          fontFamily: fontFamily,
          color: darkTextColor.withValues(
            alpha: (255 * 0.5).toDouble(),
            red: darkTextColor.r,
            green: darkTextColor.g,
            blue: darkTextColor.b,
          ),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade700),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      drawerTheme: DrawerThemeData(
        backgroundColor: darkSurfaceColor,
        scrimColor: Colors.black54,
      ),
    );
  }
}
