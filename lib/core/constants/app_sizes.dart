/// Contains size constants for UI elements.
///
/// This class defines standardized sizes for margins, paddings, font sizes,
/// and other UI-related measurements to ensure consistency throughout the app.
class AppSizes {
  /// Small padding value (8.0).
  ///
  /// Used for tight spacing between elements.
  static const double paddingSmall = 8.0;

  /// Medium padding value (16.0).
  ///
  /// Used for standard spacing between elements.
  static const double paddingMedium = 16.0;

  /// Large padding value (24.0).
  ///
  /// Used for generous spacing between major UI sections.
  static const double paddingLarge = 24.0;

  /// Small font size (14.0).
  ///
  /// Used for secondary text, captions, and hints.
  static const double fontSizeSmall = 14.0;

  /// Medium font size (16.0).
  ///
  /// Used for body text and most content.
  static const double fontSizeMedium = 16.0;

  /// Large font size (20.0).
  ///
  /// Used for headings and emphasized text.
  static const double fontSizeLarge = 20.0;

  /// Standard border radius (8.0).
  ///
  /// Used for rounding corners of UI elements like cards and buttons.
  static const double borderRadius = 8.0;

  /// Card dimensions in the mobile app
  /// هذه القيم يجب أن تكون ثابتة في جميع أنحاء التطبيق
  /// لضمان تناسق عرض البطاقات بين الأجهزة المختلفة
  /// نستخدم نفس أبعاد البطاقة في لوحة التحكم لضمان تناسق العرض
  static const double cardWidthMobile = 800.0;
  static const double cardHeightMobile = 1000.0;

  /// Card dimensions in the admin panel
  /// هذه القيم يجب أن تتطابق مع الأبعاد المستخدمة في لوحة التحكم
  /// لضمان تناسق عرض البطاقات بين لوحة التحكم والتطبيق
  static const double cardWidthAdmin = 800.0;
  static const double cardHeightAdmin = 1000.0;

  /// Scale factor between admin panel and mobile app
  /// يستخدم هذا العامل لتحويل العناصر من لوحة التحكم إلى التطبيق
  /// لضمان أن العناصر تظهر بنفس النسب في كل من لوحة التحكم والتطبيق
  /// بما أننا نستخدم نفس الأبعاد، فإن عامل التحويل هو 1.0
  static const double cardWidthScaleFactor = 1.0;
  static const double cardHeightScaleFactor = 1.0;

  /// Default values for card elements
  /// قيم افتراضية لعناصر البطاقة
  static const double defaultFontSize = 16.0;
  static const double defaultElementWidth = 0.3; // 30% من عرض البطاقة
  static const double defaultElementHeight = 0.2; // 20% من ارتفاع البطاقة
}
