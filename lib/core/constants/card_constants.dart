// lib/core/constants/card_constants.dart

/// ثوابت البطاقة الموحدة
class CardConstants {
  /// أبعاد البطاقة الموحدة في لوحة التحكم
  static const double ADMIN_PANEL_CARD_WIDTH = 800.0;
  static const double ADMIN_PANEL_CARD_HEIGHT = 1000.0;
  
  /// أبعاد البطاقة في التطبيق (يجب أن تحافظ على نفس النسبة)
  static const double APP_CARD_WIDTH = 360.0;
  static const double APP_CARD_HEIGHT = 450.0;
  
  /// نسبة التحويل من لوحة التحكم إلى التطبيق
  static const double WIDTH_SCALE_FACTOR = APP_CARD_WIDTH / ADMIN_PANEL_CARD_WIDTH;
  static const double HEIGHT_SCALE_FACTOR = APP_CARD_HEIGHT / ADMIN_PANEL_CARD_HEIGHT;
  
  /// التحقق من أن الإحداثيات نسبية
  static bool areCoordinatesRelative(double width, double height) {
    return width <= 1.0 && height <= 1.0;
  }
  
  /// تحويل الإحداثيات من مطلقة إلى نسبية
  static Map<String, double> convertAbsoluteToRelative({
    required double x,
    required double y,
    required double width,
    required double height,
    bool isFromAdminPanel = true,
  }) {
    final cardWidth = isFromAdminPanel ? ADMIN_PANEL_CARD_WIDTH : APP_CARD_WIDTH;
    final cardHeight = isFromAdminPanel ? ADMIN_PANEL_CARD_HEIGHT : APP_CARD_HEIGHT;
    
    return {
      'x': x / cardWidth,
      'y': y / cardHeight,
      'width': width / cardWidth,
      'height': height / cardHeight,
    };
  }
  
  /// تحويل الإحداثيات من نسبية إلى مطلقة
  static Map<String, double> convertRelativeToAbsolute({
    required double x,
    required double y,
    required double width,
    required double height,
    bool isForAdminPanel = false,
  }) {
    final cardWidth = isForAdminPanel ? ADMIN_PANEL_CARD_WIDTH : APP_CARD_WIDTH;
    final cardHeight = isForAdminPanel ? ADMIN_PANEL_CARD_HEIGHT : APP_CARD_HEIGHT;
    
    return {
      'x': x * cardWidth,
      'y': y * cardHeight,
      'width': width * cardWidth,
      'height': height * cardHeight,
    };
  }
}
