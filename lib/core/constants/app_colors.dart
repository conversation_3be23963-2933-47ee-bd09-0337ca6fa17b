import 'package:flutter/material.dart';

/// Contains the application's color constants.
///
/// This class defines all the colors used throughout the application
/// to ensure consistency in the UI design across all devices and themes.
/// All colors are explicitly defined to prevent system theme interference.
class AppColors {
  // منع إنشاء كائن من هذا الكلاس
  AppColors._();

  /// Primary color of the application - Purple shade (#976D9D).
  ///
  /// This color is used for primary elements like the app bar, primary buttons,
  /// and other key UI components. Fixed color that won't change with system themes.
  static const Color primaryColor = Color(0xFF976D9D);

  /// Accent color - Lighter shade of the primary color (#BFA2C7).
  ///
  /// This color is used for secondary elements and accents throughout the UI.
  /// Fixed color that won't change with system themes.
  static const Color accentColor = Color(0xFFBFA2C7);

  /// Background color - Pure White (#FFFFFF).
  ///
  /// This is the main background color used throughout the application.
  /// Fixed to pure white regardless of system theme.
  static const Color backgroundColor = Color(0xFFFFFFFF);

  /// Text color - Soft black (#212121).
  ///
  /// This color is used for most text content in the application.
  /// Fixed color that won't change with system themes.
  static const Color textColor = Color(0xFF212121);

  /// Secondary text color - Medium gray (#757575).
  ///
  /// Used for secondary text and descriptions.
  static const Color secondaryTextColor = Color(0xFF757575);

  /// Light text color - Light gray (#BDBDBD).
  ///
  /// Used for placeholder text and disabled elements.
  static const Color lightTextColor = Color(0xFFBDBDBD);

  /// Error color - Fixed red (#F44336).
  ///
  /// This color is used for error messages and warnings.
  /// Fixed color that won't change with system themes.
  static const Color errorColor = Color(0xFFF44336);

  /// Success color - Fixed green (#4CAF50).
  ///
  /// Used for success messages and positive actions.
  static const Color successColor = Color(0xFF4CAF50);

  /// Warning color - Fixed orange (#FF9800).
  ///
  /// Used for warning messages and caution indicators.
  static const Color warningColor = Color(0xFFFF9800);

  /// Card background color - Pure white (#FFFFFF).
  ///
  /// Used for card backgrounds to ensure consistency.
  static const Color cardBackgroundColor = Color(0xFFFFFFFF);

  /// Border color - Light gray (#E0E0E0).
  ///
  /// Used for borders and dividers.
  static const Color borderColor = Color(0xFFE0E0E0);

  /// Shadow color - Semi-transparent black.
  ///
  /// Used for shadows and elevation effects.
  static const Color shadowColor = Color(0x1A000000);

  /// Surface color - Pure white (#FFFFFF).
  ///
  /// Used for surface elements like app bars and bottom sheets.
  static const Color surfaceColor = Color(0xFFFFFFFF);

  /// Creates a fixed ColorScheme that won't change with system themes
  static ColorScheme get fixedColorScheme => const ColorScheme(
        brightness: Brightness.light,
        primary: primaryColor,
        onPrimary: Color(0xFFFFFFFF),
        secondary: accentColor,
        onSecondary: Color(0xFFFFFFFF),
        error: errorColor,
        onError: Color(0xFFFFFFFF),
        surface: surfaceColor,
        onSurface: textColor,
      );

  /// Creates a fixed ThemeData that won't change with system themes
  static ThemeData get fixedTheme => ThemeData(
        useMaterial3: true,
        colorScheme: fixedColorScheme,
        primaryColor: primaryColor,
        scaffoldBackgroundColor: backgroundColor,
        cardColor: cardBackgroundColor,
        dividerColor: borderColor,
        shadowColor: shadowColor,
        // تأكد من أن الألوان ثابتة في جميع المكونات
        appBarTheme: const AppBarTheme(
          backgroundColor: primaryColor,
          foregroundColor: Color(0xFFFFFFFF),
          elevation: 4,
          shadowColor: shadowColor,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: Color(0xFFFFFFFF),
            elevation: 4,
            shadowColor: shadowColor,
          ),
        ),
        textTheme: const TextTheme(
          bodyLarge: TextStyle(color: textColor),
          bodyMedium: TextStyle(color: textColor),
          bodySmall: TextStyle(color: secondaryTextColor),
          headlineLarge: TextStyle(color: textColor),
          headlineMedium: TextStyle(color: textColor),
          headlineSmall: TextStyle(color: textColor),
          titleLarge: TextStyle(color: textColor),
          titleMedium: TextStyle(color: textColor),
          titleSmall: TextStyle(color: textColor),
          labelLarge: TextStyle(color: textColor),
          labelMedium: TextStyle(color: textColor),
          labelSmall: TextStyle(color: secondaryTextColor),
        ),
      );
}
