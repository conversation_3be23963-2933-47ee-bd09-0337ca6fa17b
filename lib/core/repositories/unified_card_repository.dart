// lib/core/repositories/unified_card_repository.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:mashair/core/models/unified_card.dart';
import 'package:mashair/core/models/unified_card_element.dart';

/// مستودع البطاقات الموحد للتعامل مع البطاقات في كل من لوحة التحكم والتطبيق
class UnifiedCardRepository {
  final FirebaseFirestore _firestore;

  /// إنشاء مستودع البطاقات الموحد
  UnifiedCardRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// الحصول على بطاقة بواسطة المعرف
  Future<UnifiedCard?> getCardById(String cardId) async {
    try {
      // محاولة جلب البطاقة من مجموعة cards أولاً
      final cardDoc = await _firestore.collection('cards').doc(cardId).get();

      // إذا وجدنا البطاقة في مجموعة cards
      if (cardDoc.exists && cardDoc.data() != null) {
        debugPrint('تم العثور على البطاقة في مجموعة cards بالمعرف: $cardId');
        return UnifiedCard.fromMap(cardId, cardDoc.data()!);
      }

      // إذا لم نجد البطاقة في مجموعة cards، نحاول البحث في مجموعة greeting_cards
      final greetingDoc = await _firestore.collection('greeting_cards').doc(cardId).get();
      if (greetingDoc.exists && greetingDoc.data() != null) {
        debugPrint('تم العثور على البطاقة في مجموعة greeting_cards بالمعرف: $cardId');
        return UnifiedCard.fromMap(cardId, greetingDoc.data()!);
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على البطاقة: $e');
      return null;
    }
  }

  /// الحصول على عناصر البطاقة بواسطة معرف البطاقة
  Future<List<UnifiedCardElement>> getCardElements(String cardId) async {
    try {
      // أولاً، نتحقق من وجود البطاقة نفسها
      final cardDoc = await _firestore.collection('cards').doc(cardId).get();

      if (!cardDoc.exists) {
        debugPrint('البطاقة غير موجودة: $cardId');
        return [];
      }

      debugPrint('البطاقة موجودة، نستعلم عن العناصر');

      // استخراج لون الخلفية للبطاقة
      final cardData = cardDoc.data();
      String backgroundColor = 'FFFFFF'; // لون افتراضي

      if (cardData != null && cardData.containsKey('backgroundColor')) {
        if (cardData['backgroundColor'] is String) {
          backgroundColor = cardData['backgroundColor'] as String;
        } else if (cardData['backgroundColor'] is int) {
          backgroundColor = (cardData['backgroundColor'] as int).toRadixString(16).padLeft(8, '0').substring(2);
        }
      }

      // التحقق من وجود العناصر في وثيقة البطاقة نفسها
      if (cardData != null && cardData.containsKey('elements') && cardData['elements'] is List) {
        debugPrint('تم العثور على عناصر في وثيقة البطاقة نفسها');
        final elements = cardData['elements'] as List;
        debugPrint('تم العثور على ${elements.length} عنصر في وثيقة البطاقة');

        if (elements.isEmpty) {
          return [];
        }

        debugPrint('بدء تحليل ${elements.length} عنصر من وثيقة البطاقة');
        final List<UnifiedCardElement> result = [];

        for (int i = 0; i < elements.length; i++) {
          final element = elements[i];
          debugPrint('معالجة العنصر $i: $element');

          if (element is Map<String, dynamic>) {
            // طباعة معلومات العنصر للتصحيح
            debugPrint('=== معالجة العنصر $i من وثيقة البطاقة ===');
            debugPrint('البيانات الأصلية: $element');

            // إضافة معرف للعنصر إذا لم يكن موجوداً
            if (!element.containsKey('id') || element['id'] == null) {
              element['id'] = 'element_${cardId}_$i';
            }

            // إضافة معلومات إضافية للعنصر
            element['isFromAdminPanel'] = true;
            element['isRelativeCoordinates'] = true;

            // التأكد من أن الإحداثيات والأبعاد موجودة ونسبية (0-1)
            if (element.containsKey('x') && element.containsKey('y') &&
                element.containsKey('width') && element.containsKey('height')) {

              double x = (element['x'] as num).toDouble();
              double y = (element['y'] as num).toDouble();
              double width = (element['width'] as num).toDouble();
              double height = (element['height'] as num).toDouble();

              // إذا كانت الإحداثيات مطلقة (أكبر من 1)، نحولها إلى نسبية
              if (width > 1 || height > 1) {
                debugPrint('تحويل الإحداثيات المطلقة إلى نسبية: ($x, $y), (${width}x$height)');

                // استخدام أبعاد البطاقة في لوحة التحكم للتحويل
                final double cardWidth = 800.0;
                final double cardHeight = 1000.0;

                x = x / cardWidth;
                y = y / cardHeight;
                width = width / cardWidth;
                height = height / cardHeight;

                element['x'] = x;
                element['y'] = y;
                element['width'] = width;
                element['height'] = height;

                debugPrint('الإحداثيات بعد التحويل: ($x, $y), (${width}x$height)');
              }
            }

            // إنشاء عنصر موحد
            final unifiedElement = UnifiedCardElement.fromMap(element);
            result.add(unifiedElement);

            debugPrint('تم إضافة العنصر بنجاح');
          }
        }

        debugPrint('تم تحليل ${result.length} عنصر بنجاح من وثيقة البطاقة');
        return result;
      }

      // إذا لم نجد العناصر في وثيقة البطاقة، نبحث في المجموعة الفرعية
      final elementsSnapshot = await _firestore
          .collection('cards')
          .doc(cardId)
          .collection('elements')
          .get();

      if (elementsSnapshot.docs.isEmpty) {
        debugPrint('لم يتم العثور على عناصر في المجموعة الفرعية للبطاقة');
        return [];
      }

      debugPrint('تم العثور على ${elementsSnapshot.docs.length} عنصر في المجموعة الفرعية للبطاقة');
      final List<UnifiedCardElement> result = [];

      for (final doc in elementsSnapshot.docs) {
        final data = doc.data();
        debugPrint('=== معالجة عنصر من المجموعة الفرعية ===');
        debugPrint('البيانات الأصلية: $data');

        // إضافة معرف للعنصر
        data['id'] = doc.id;

        // إضافة معلومات إضافية للعنصر
        data['isFromAdminPanel'] = true;
        data['isRelativeCoordinates'] = true;

        // التأكد من أن الإحداثيات والأبعاد موجودة ونسبية (0-1)
        if (data.containsKey('x') && data.containsKey('y') &&
            data.containsKey('width') && data.containsKey('height')) {

          double x = (data['x'] as num).toDouble();
          double y = (data['y'] as num).toDouble();
          double width = (data['width'] as num).toDouble();
          double height = (data['height'] as num).toDouble();

          // إذا كانت الإحداثيات مطلقة (أكبر من 1)، نحولها إلى نسبية
          if (width > 1 || height > 1) {
            debugPrint('تحويل الإحداثيات المطلقة إلى نسبية: ($x, $y), (${width}x$height)');

            // استخدام أبعاد البطاقة في لوحة التحكم للتحويل
            final double cardWidth = 800.0;
            final double cardHeight = 1000.0;

            x = x / cardWidth;
            y = y / cardHeight;
            width = width / cardWidth;
            height = height / cardHeight;

            data['x'] = x;
            data['y'] = y;
            data['width'] = width;
            data['height'] = height;

            debugPrint('الإحداثيات بعد التحويل: ($x, $y), (${width}x$height)');
          }
        }

        final unifiedElement = UnifiedCardElement.fromMap(data);
        result.add(unifiedElement);

        debugPrint('تم إضافة العنصر بنجاح');
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في الحصول على عناصر البطاقة: $e');
      return [];
    }
  }

  /// حفظ بطاقة جديدة
  Future<String?> createCard(UnifiedCard card) async {
    try {
      final docRef = await _firestore.collection('cards').add(card.toMap());
      return docRef.id;
    } catch (e) {
      debugPrint('خطأ في إنشاء البطاقة: $e');
      return null;
    }
  }

  /// تحديث بطاقة موجودة
  Future<bool> updateCard(UnifiedCard card) async {
    try {
      await _firestore.collection('cards').doc(card.id).update(card.toMap());
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث البطاقة: $e');
      return false;
    }
  }

  /// حذف بطاقة
  Future<bool> deleteCard(String cardId) async {
    try {
      await _firestore.collection('cards').doc(cardId).delete();
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف البطاقة: $e');
      return false;
    }
  }

  /// زيادة عداد زيارات البطاقة
  Future<bool> incrementCardVisits(String cardId) async {
    try {
      await _firestore.collection('cards').doc(cardId).update({
        'visits': FieldValue.increment(1),
      });
      return true;
    } catch (e) {
      debugPrint('خطأ في زيادة عداد زيارات البطاقة: $e');
      return false;
    }
  }

  /// حفظ عناصر البطاقة
  Future<bool> saveCardElements(String cardId, List<UnifiedCardElement> elements) async {
    try {
      final batch = _firestore.batch();
      final cardRef = _firestore.collection('cards').doc(cardId);
      final elementsCollection = cardRef.collection('elements');

      // حذف العناصر الموجودة
      final existingElements = await elementsCollection.get();
      for (final doc in existingElements.docs) {
        batch.delete(doc.reference);
      }

      // إعداد قائمة العناصر المعيارية للحفظ في وثيقة البطاقة
      final List<Map<String, dynamic>> normalizedElements = [];

      // إضافة العناصر الجديدة
      for (final element in elements) {
        final data = element.toMap();
        normalizedElements.add(data);
        batch.set(elementsCollection.doc(element.id), data);
      }

      // تنفيذ عمليات الحذف والإضافة
      await batch.commit();

      // حفظ العناصر في وثيقة البطاقة نفسها
      await cardRef.update({
        'elements': normalizedElements,
        'lastModified': FieldValue.serverTimestamp(),
      });

      debugPrint('تم حفظ ${elements.length} عنصر للبطاقة $cardId');
      return true;
    } catch (e) {
      debugPrint('خطأ في حفظ عناصر البطاقة: $e');
      return false;
    }
  }
}
