// Custom exceptions for the application.
//
// This file contains exception classes that can be thrown in specific error scenarios.
// These exceptions are primarily used in data sources to differentiate
// between different types of errors that can occur during data operations.
// They are later converted to Failures in the repository layer.

/// Exception thrown when a server-related error occurs.
///
/// This exception is typically thrown when there's an error communicating
/// with a remote server, API, or cloud service like Firebase.
class ServerException implements Exception {
  /// Descriptive error message
  final String message;

  /// Creates a new server exception with the specified error message
  ServerException({required this.message});

  @override
  String toString() => message;
}

/// Exception thrown when a cache-related error occurs.
///
/// This exception is typically thrown when there's an error reading from
/// or writing to local storage or cache.
class CacheException implements Exception {
  /// Descriptive error message
  final String message;

  /// Creates a new cache exception with the specified error message
  CacheException({required this.message});

  @override
  String toString() => message;
}

/// Additional exceptions can be added as needed, such as:
/// - AuthException: For authentication errors
/// - NetworkException: For network connectivity issues
/// - ValidationException: For data validation errors
