import 'package:equatable/equatable.dart';

/// Abstract class representing a failure in business logic.
///
/// This class is used in the Either&lt;Failure, SuccessValue&gt; pattern to represent
/// the left (error) side of the Either type. It provides a consistent way to
/// handle errors across the application.
abstract class Failure extends Equatable {
  /// Descriptive error message
  final String message;

  /// Creates a new failure with the specified error message
  const Failure(this.message);

  @override
  List<Object?> get props => [message];
}

/// Failure that occurs when communicating with a server.
///
/// This failure typically represents errors from APIs, remote services,
/// or cloud platforms like Firebase.
class ServerFailure extends Failure {
  /// Creates a new server failure with the specified error message
  const ServerFailure(super.message);
}

/// Failure that occurs when working with local cache or storage.
///
/// This failure typically represents errors when reading from or
/// writing to local databases, shared preferences, or other local storage.
class CacheFailure extends Failure {
  /// Creates a new cache failure with the specified error message
  const CacheFailure(super.message);
}

/// Failure that occurs during authentication or authorization.
///
/// This failure typically represents errors like invalid credentials,
/// expired tokens, or insufficient permissions.
class AuthFailure extends Failure {
  /// Creates a new authentication failure with the specified error message
  const AuthFailure(super.message);
}

/// Failure that occurs during file processing operations.
///
/// This failure typically represents errors when reading, writing,
/// or manipulating files.
class FileProcessingFailure extends Failure {
  /// Creates a new file processing failure with the specified error message
  const FileProcessingFailure(super.message);
}

/// Additional failure types can be added as needed, such as:
/// - NetworkFailure: For network connectivity issues
/// - ValidationFailure: For data validation errors
/// - BusinessLogicFailure: For domain-specific business rule violations
