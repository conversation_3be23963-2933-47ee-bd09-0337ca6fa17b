// lib/core/services/user_guide_service.dart

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../widgets/user_guide/feature_highlight.dart';
import '../widgets/user_guide/guide_step.dart';
import '../widgets/user_guide/user_guide_manager.dart';

/// خدمة دليل المستخدم
class UserGuideService {
  /// مثيل واحد من الخدمة (Singleton)
  static final UserGuideService _instance = UserGuideService._internal();

  /// الحصول على مثيل الخدمة
  factory UserGuideService() => _instance;

  /// إنشاء خدمة دليل المستخدم
  UserGuideService._internal();

  /// مدير دليل المستخدم
  final UserGuideManager _guideManager = UserGuideManager();

  /// مفتاح التخزين المحلي لحالة الدليل
  static const String _prefsKeyPrefix = 'user_guide_shown_';

  /// التحقق مما إذا كان الدليل قد تم عرضه من قبل
  Future<bool> hasShownGuide(String guideId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('$_prefsKeyPrefix$guideId') ?? false;
  }

  /// تعيين حالة عرض الدليل
  Future<void> setGuideShown(String guideId, {bool shown = true}) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('$_prefsKeyPrefix$guideId', shown);
  }

  /// إعادة تعيين حالة عرض الدليل
  Future<void> resetGuideStatus(String guideId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('$_prefsKeyPrefix$guideId');
  }

  /// عرض دليل المستخدم إذا لم يتم عرضه من قبل
  ///
  /// ملاحظة: يجب استدعاء هذه الدالة من داخل StatefulWidget
  /// واستخدام mounted للتحقق من أن الـ widget لا يزال في الشجرة
  Future<bool> shouldShowGuide(String guideId) async {
    return !(await hasShownGuide(guideId));
  }

  /// عرض دليل المستخدم بعد التحقق من أنه لم يتم عرضه من قبل
  /// يجب استدعاء هذه الدالة بعد التحقق من shouldShowGuide
  void startGuideIfMounted(
    BuildContext context, {
    required String guideId,
    required List<GuideStep> steps,
    VoidCallback? onComplete,
  }) {
    _guideManager.startGuide(
      context,
      guideId: guideId,
      steps: steps,
      onComplete: () {
        setGuideShown(guideId);
        onComplete?.call();
      },
    );
  }

  /// عرض دليل المستخدم بغض النظر عما إذا كان قد تم عرضه من قبل
  void showGuide(
    BuildContext context, {
    required String guideId,
    required List<GuideStep> steps,
    VoidCallback? onComplete,
  }) {
    _guideManager.startGuide(
      context,
      guideId: guideId,
      steps: steps,
      onComplete: () {
        setGuideShown(guideId);
        onComplete?.call();
      },
    );
  }

  /// إيقاف دليل المستخدم
  void stopGuide() {
    _guideManager.stopGuide();
  }

  /// إنشاء دليل محرر البطاقات
  List<GuideStep> createCardEditorGuide({
    required GlobalKey appBarKey,
    required GlobalKey textToolKey,
    required GlobalKey imageToolKey,
    required GlobalKey stickerToolKey,
    required GlobalKey shapeToolKey,
    required GlobalKey saveButtonKey,
  }) {
    return [
      GuideStep(
        targetKey: appBarKey,
        title: 'محرر البطاقات',
        description:
            'مرحبًا بك في محرر البطاقات! هنا يمكنك إنشاء بطاقات مخصصة بسهولة.',
        position: HighlightPosition.bottom,
        nextButtonText: 'التالي',
        skipButtonText: 'تخطي الدليل',
      ),
      GuideStep(
        targetKey: textToolKey,
        title: 'إضافة نص',
        description:
            'انقر هنا لإضافة نص إلى البطاقة. يمكنك تخصيص الخط واللون والحجم.',
        position: HighlightPosition.top,
        nextButtonText: 'التالي',
        skipButtonText: 'تخطي',
      ),
      GuideStep(
        targetKey: imageToolKey,
        title: 'إضافة صورة',
        description: 'انقر هنا لإضافة صورة من معرض الصور أو التقاط صورة جديدة.',
        position: HighlightPosition.top,
        nextButtonText: 'التالي',
        skipButtonText: 'تخطي',
      ),
      GuideStep(
        targetKey: stickerToolKey,
        title: 'إضافة ملصق',
        description: 'انقر هنا لإضافة ملصقات وإيموجي لتزيين البطاقة.',
        position: HighlightPosition.top,
        nextButtonText: 'التالي',
        skipButtonText: 'تخطي',
      ),
      GuideStep(
        targetKey: shapeToolKey,
        title: 'إضافة أشكال',
        description: 'انقر هنا لإضافة أشكال مختلفة مثل المستطيلات والدوائر.',
        position: HighlightPosition.top,
        nextButtonText: 'التالي',
        skipButtonText: 'تخطي',
      ),
      GuideStep(
        targetKey: saveButtonKey,
        title: 'حفظ البطاقة',
        description:
            'بعد الانتهاء من تصميم البطاقة، انقر هنا لحفظها أو مشاركتها.',
        position: HighlightPosition.left,
        nextButtonText: 'إنهاء',
        skipButtonText: 'تخطي',
      ),
    ];
  }

  /// إنشاء دليل الصفحة الرئيسية
  List<GuideStep> createHomeGuide({
    required GlobalKey aiButtonKey,
    required GlobalKey createCardKey,
    required GlobalKey popularCardsKey,
    required GlobalKey settingsKey,
  }) {
    return [
      GuideStep(
        targetKey: aiButtonKey,
        title: 'الذكاء الاصطناعي',
        description: 'انقر هنا لإنشاء بطاقات باستخدام الذكاء الاصطناعي.',
        position: HighlightPosition.bottom,
        nextButtonText: 'التالي',
        skipButtonText: 'تخطي الدليل',
      ),
      GuideStep(
        targetKey: createCardKey,
        title: 'إنشاء بطاقة',
        description: 'انقر هنا لإنشاء بطاقة جديدة من الصفر.',
        position: HighlightPosition.bottom,
        nextButtonText: 'التالي',
        skipButtonText: 'تخطي',
      ),
      GuideStep(
        targetKey: popularCardsKey,
        title: 'البطاقات الشائعة',
        description: 'استعرض البطاقات الأكثر شيوعًا واختر منها.',
        position: HighlightPosition.top,
        nextButtonText: 'التالي',
        skipButtonText: 'تخطي',
      ),
      GuideStep(
        targetKey: settingsKey,
        title: 'الإعدادات',
        description: 'انقر هنا للوصول إلى إعدادات التطبيق وتخصيصه.',
        position: HighlightPosition.left,
        nextButtonText: 'إنهاء',
        skipButtonText: 'تخطي',
      ),
    ];
  }
}
