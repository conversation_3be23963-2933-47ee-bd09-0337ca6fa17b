// lib/core/services/feedback_service.dart

import 'dart:async';

import 'package:flutter/material.dart';

import '../widgets/feedback/confirmation_dialog.dart';
import '../widgets/feedback/operation_feedback.dart';
import '../widgets/feedback/progress_dialog.dart';

/// خدمة التغذية الراجعة
class FeedbackService {
  /// مثيل واحد من الخدمة (Singleton)
  static final FeedbackService _instance = FeedbackService._internal();

  /// الحصول على مثيل الخدمة
  factory FeedbackService() => _instance;

  /// إنشاء خدمة التغذية الراجعة
  FeedbackService._internal();

  /// عرض تغذية راجعة للعمليات
  void showOperationFeedback(
    BuildContext context, {
    required OperationType type,
    required String title,
    String? description,
    IconData? icon,
    bool showProgress = false,
    double? progress,
    VoidCallback? onActionPressed,
    String? actionText,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onDismiss,
  }) {
    OperationFeedback.show(
      context,
      type: type,
      title: title,
      description: description,
      icon: icon,
      showProgress: showProgress,
      progress: progress,
      onActionPressed: onActionPressed,
      actionText: actionText,
      duration: duration,
      onDismiss: onDismiss,
    );
  }

  /// عرض تغذية راجعة للنجاح
  void showSuccess(
    BuildContext context, {
    required String title,
    String? description,
    IconData? icon,
    VoidCallback? onActionPressed,
    String? actionText,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onDismiss,
  }) {
    showOperationFeedback(
      context,
      type: OperationType.success,
      title: title,
      description: description,
      icon: icon ?? Icons.check_circle,
      onActionPressed: onActionPressed,
      actionText: actionText,
      duration: duration,
      onDismiss: onDismiss,
    );
  }

  /// عرض تغذية راجعة للخطأ
  void showError(
    BuildContext context, {
    required String title,
    String? description,
    IconData? icon,
    VoidCallback? onActionPressed,
    String? actionText,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onDismiss,
  }) {
    showOperationFeedback(
      context,
      type: OperationType.error,
      title: title,
      description: description,
      icon: icon ?? Icons.error,
      onActionPressed: onActionPressed,
      actionText: actionText,
      duration: duration,
      onDismiss: onDismiss,
    );
  }

  /// عرض تغذية راجعة للتحذير
  void showWarning(
    BuildContext context, {
    required String title,
    String? description,
    IconData? icon,
    VoidCallback? onActionPressed,
    String? actionText,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onDismiss,
  }) {
    showOperationFeedback(
      context,
      type: OperationType.warning,
      title: title,
      description: description,
      icon: icon ?? Icons.warning,
      onActionPressed: onActionPressed,
      actionText: actionText,
      duration: duration,
      onDismiss: onDismiss,
    );
  }

  /// عرض تغذية راجعة للمعلومات
  void showInfo(
    BuildContext context, {
    required String title,
    String? description,
    IconData? icon,
    VoidCallback? onActionPressed,
    String? actionText,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onDismiss,
  }) {
    showOperationFeedback(
      context,
      type: OperationType.info,
      title: title,
      description: description,
      icon: icon ?? Icons.info,
      onActionPressed: onActionPressed,
      actionText: actionText,
      duration: duration,
      onDismiss: onDismiss,
    );
  }

  /// عرض تغذية راجعة للتحميل
  void showLoading(
    BuildContext context, {
    required String title,
    String? description,
    IconData? icon,
    bool showProgress = true,
    double? progress,
    VoidCallback? onActionPressed,
    String? actionText,
    VoidCallback? onDismiss,
  }) {
    showOperationFeedback(
      context,
      type: OperationType.loading,
      title: title,
      description: description,
      icon: icon ?? Icons.hourglass_empty,
      showProgress: showProgress,
      progress: progress,
      onActionPressed: onActionPressed,
      actionText: actionText,
      duration:
          const Duration(days: 1), // مدة طويلة لأن التحميل سيتم إغلاقه يدويًا
      onDismiss: onDismiss,
    );
  }

  /// تحديث تغذية راجعة التحميل
  void updateLoading({
    String? title,
    String? description,
    double? progress,
  }) {
    OperationFeedback.update(
      type: OperationType.loading,
      title: title,
      description: description,
      progress: progress,
    );
  }

  /// إغلاق تغذية راجعة التحميل
  void dismissLoading() {
    OperationFeedback.dismiss();
  }

  /// عرض حوار تقدم العملية
  Future<void> showProgressDialog(
    BuildContext context, {
    required String title,
    required String message,
    bool isDeterminate = false,
    double? progress,
    bool isCancelable = true,
    VoidCallback? onCancel,
  }) async {
    return ProgressDialog.show(
      context,
      title: title,
      message: message,
      isDeterminate: isDeterminate,
      progress: progress,
      isCancelable: isCancelable,
      onCancel: onCancel,
    );
  }

  /// تحديث حوار تقدم العملية
  void updateProgressDialog(
    BuildContext context, {
    String? message,
    double? progress,
  }) {
    ProgressDialog.update(
      context,
      message: message,
      progress: progress,
    );
  }

  /// إغلاق حوار تقدم العملية
  void dismissProgressDialog(BuildContext context) {
    ProgressDialog.dismiss(context);
  }

  /// عرض حوار تأكيد
  Future<bool?> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    Color confirmColor = Colors.red,
    IconData? icon,
    Color? iconColor,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) async {
    return ConfirmationDialog.show(
      context,
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: cancelText,
      confirmColor: confirmColor,
      icon: icon,
      iconColor: iconColor,
      onConfirm: onConfirm,
      onCancel: onCancel,
    );
  }
}
