import 'package:flutter/material.dart';

import '../widgets/animated_toast.dart';
import '../widgets/custom_snackbar.dart';
import '../widgets/success_overlay.dart';
import 'local_notifications_service.dart';

/// خدمة مركزية لإدارة جميع أنواع الإشعارات في التطبيق
class NotificationService {
  NotificationService._();

  /// مثيل واحد من الخدمة (Singleton)
  static final instance = NotificationService._();

  /// عرض إشعار نجاح
  ///
  /// [context] سياق البناء
  /// [message] نص الرسالة
  /// [duration] مدة ظهور الإشعار
  void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    CustomSnackbar.show(
      context: context,
      message: message,
      type: NotificationType.success,
      duration: duration,
    );
  }

  /// عرض إشعار خطأ
  ///
  /// [context] سياق البناء
  /// [message] نص الرسالة
  /// [duration] مدة ظهور الإشعار
  void showError(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    CustomSnackbar.show(
      context: context,
      message: message,
      type: NotificationType.error,
      duration: duration,
    );
  }

  /// عرض إشعار تحذير
  ///
  /// [context] سياق البناء
  /// [message] نص الرسالة
  /// [duration] مدة ظهور الإشعار
  void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    CustomSnackbar.show(
      context: context,
      message: message,
      type: NotificationType.warning,
      duration: duration,
    );
  }

  /// عرض إشعار معلومات
  ///
  /// [context] سياق البناء
  /// [message] نص الرسالة
  /// [duration] مدة ظهور الإشعار
  void showInfo(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    CustomSnackbar.show(
      context: context,
      message: message,
      type: NotificationType.info,
      duration: duration,
    );
  }

  /// عرض إشعار محلي (يظهر حتى عندما يكون التطبيق في الخلفية)
  ///
  /// [title] عنوان الإشعار
  /// [body] نص الإشعار
  /// [payload] بيانات إضافية للإشعار (اختياري)
  Future<void> showLocalNotification(
    String title,
    String body, {
    String? payload,
  }) async {
    await LocalNotificationsService.instance.show(
      title,
      body,
      payload: payload,
    );
  }

  /// تهيئة خدمة الإشعارات المحلية
  Future<void> initLocalNotifications() async {
    await LocalNotificationsService.instance.init();
  }

  /// طلب إذن الإشعارات (مهم لنظام iOS)
  Future<bool> requestNotificationPermission() async {
    // في هذه النسخة البسيطة، نفترض دائمًا أن الإذن ممنوح
    debugPrint('Notification permission requested (simplified version)');
    return true;
  }

  /// عرض إشعار منبثق متحرك
  ///
  /// [context] سياق البناء
  /// [message] نص الرسالة
  /// [type] نوع الإشعار
  /// [position] موقع ظهور الإشعار
  /// [duration] مدة ظهور الإشعار
  void showToast(
    BuildContext context,
    String message, {
    ToastType type = ToastType.info,
    ToastPosition position = ToastPosition.bottom,
    Duration duration = const Duration(seconds: 3),
  }) {
    AnimatedToast.show(
      context,
      message: message,
      type: type,
      position: position,
      duration: duration,
    );
  }

  /// عرض إشعار نجاح متحرك يغطي الشاشة
  ///
  /// [context] سياق البناء
  /// [message] نص الرسالة
  /// [onComplete] الإجراء الذي سيتم تنفيذه بعد انتهاء الإشعار
  /// [duration] مدة ظهور الإشعار
  void showSuccessOverlay(
    BuildContext context,
    String message, {
    VoidCallback? onComplete,
    Duration duration = const Duration(seconds: 2),
  }) {
    SuccessOverlay.show(
      context,
      message: message,
      onComplete: onComplete,
      duration: duration,
    );
  }

  /// عرض إشعار نجاح للعمليات
  ///
  /// يعرض إشعار نجاح متحرك يغطي الشاشة ثم يعرض إشعار منبثق
  ///
  /// [context] سياق البناء
  /// [message] نص الرسالة
  /// [toastMessage] نص الإشعار المنبثق (اختياري)
  /// [onComplete] الإجراء الذي سيتم تنفيذه بعد انتهاء الإشعار
  void showOperationSuccess(
    BuildContext context,
    String message, {
    String? toastMessage,
    VoidCallback? onComplete,
  }) {
    showSuccessOverlay(
      context,
      message,
      duration: const Duration(seconds: 1),
      onComplete: () {
        if (toastMessage != null) {
          showToast(
            context,
            toastMessage,
            type: ToastType.success,
          );
        }
        onComplete?.call();
      },
    );
  }
}
