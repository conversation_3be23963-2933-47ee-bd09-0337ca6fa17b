// lib/core/services/auth_persistence_service.dart

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة لحفظ بيانات المستخدم واستعادتها للتسجيل التلقائي
class AuthPersistenceService {
  static const String _rememberMeKey = 'auth_remember_me';
  static const String _emailKey = 'auth_email';
  static const String _passwordKey = 'auth_password';

  /// حفظ بيانات المستخدم للتسجيل التلقائي
  Future<bool> saveCredentials({
    required String email,
    required String password,
    required bool rememberMe,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // حفظ حالة "تذكرني"
      await prefs.setBool(_rememberMeKey, rememberMe);
      
      if (rememberMe) {
        // حفظ البريد الإلكتروني وكلمة المرور فقط إذا كان "تذكرني" مفعل
        await prefs.setString(_emailKey, email);
        await prefs.setString(_passwordKey, password);
        debugPrint('تم حفظ بيانات المستخدم للتسجيل التلقائي');
      } else {
        // إزالة البيانات المحفوظة إذا تم إلغاء تفعيل "تذكرني"
        await prefs.remove(_emailKey);
        await prefs.remove(_passwordKey);
        debugPrint('تم إزالة بيانات المستخدم المحفوظة');
      }
      
      return true;
    } catch (e) {
      debugPrint('خطأ في حفظ بيانات المستخدم: $e');
      return false;
    }
  }

  /// التحقق مما إذا كان المستخدم قد اختار "تذكرني"
  Future<bool> isRememberMeEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_rememberMeKey) ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة "تذكرني": $e');
      return false;
    }
  }

  /// الحصول على بيانات المستخدم المحفوظة
  Future<Map<String, String>?> getSavedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // التحقق من تفعيل "تذكرني"
      final rememberMe = prefs.getBool(_rememberMeKey) ?? false;
      if (!rememberMe) {
        return null;
      }
      
      final email = prefs.getString(_emailKey);
      final password = prefs.getString(_passwordKey);
      
      // التأكد من وجود البيانات المطلوبة
      if (email == null || password == null || email.isEmpty || password.isEmpty) {
        return null;
      }
      
      return {
        'email': email,
        'password': password,
      };
    } catch (e) {
      debugPrint('خطأ في استرجاع بيانات المستخدم: $e');
      return null;
    }
  }

  /// مسح بيانات المستخدم المحفوظة
  Future<bool> clearSavedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_rememberMeKey);
      await prefs.remove(_emailKey);
      await prefs.remove(_passwordKey);
      debugPrint('تم مسح بيانات المستخدم المحفوظة');
      return true;
    } catch (e) {
      debugPrint('خطأ في مسح بيانات المستخدم: $e');
      return false;
    }
  }
}
