import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة الذاكرة المؤقتة للتطبيق
/// تستخدم لتحسين الأداء وتقليل استخدام الموارد
class CacheService {
  static final CacheService _instance = CacheService._internal();

  /// مسار مجلد الذاكرة المؤقتة الرئيسي
  String? _cacheDirectory;

  /// مسار مجلد الصور المؤقتة
  String? _tempImagesPath;

  /// مسار مجلد الصور المضغوطة
  String? _compressedImagesPath;

  /// الحصول على نسخة وحيدة من الخدمة (Singleton)
  factory CacheService() {
    return _instance;
  }

  CacheService._internal() {
    // تهيئة مسارات المجلدات عند إنشاء الخدمة
    _initPaths();
  }

  /// تهيئة مسارات المجلدات
  Future<void> _initPaths() async {
    if (kIsWeb) return; // تخطي على الويب

    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = '${appDir.path}/cache';
      _tempImagesPath = '${appDir.path}/temp_images';
      _compressedImagesPath = '${appDir.path}/compressed_images';

      // إنشاء المجلدات إذا لم تكن موجودة
      await Directory(_cacheDirectory!).create(recursive: true);
      await Directory(_tempImagesPath!).create(recursive: true);
      await Directory(_compressedImagesPath!).create(recursive: true);

      debugPrint('Cache directories initialized:');
      debugPrint('Temp images: $_tempImagesPath');
      debugPrint('Compressed images: $_compressedImagesPath');
    } catch (e) {
      debugPrint('Error initializing cache directories: $e');
    }
  }

  /// تنظيف الذاكرة المؤقتة للصور والملفات
  Future<void> clearImageCache() async {
    if (kIsWeb) return; // تخطي على الويب

    try {
      // تنظيف مجلد الذاكرة المؤقتة الرئيسي
      if (_cacheDirectory != null) {
        final dir = Directory(_cacheDirectory!);
        if (await dir.exists()) {
          await for (final entity in dir.list()) {
            if (entity is File) {
              await entity.delete();
            }
          }
        }
      }

      // تنظيف مجلد الصور المؤقتة
      if (_tempImagesPath != null) {
        final dir = Directory(_tempImagesPath!);
        if (await dir.exists()) {
          await for (final entity in dir.list()) {
            if (entity is File) {
              await entity.delete();
            }
          }
        }
      }

      debugPrint('Image cache cleared successfully');
    } catch (e) {
      debugPrint('Error clearing image cache: $e');
    }
  }

  /// تنظيف ذاكرة التخزين المؤقت للتفضيلات
  Future<void> clearPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // حذف جميع المفاتيح باستثناء مفاتيح معينة مهمة
      final keys = prefs.getKeys();
      for (final key in keys) {
        // الاحتفاظ بمفاتيح معينة مثل إعدادات اللغة وحالة تسجيل الدخول
        if (!key.startsWith('language_') &&
            !key.startsWith('auth_') &&
            !key.startsWith('theme_')) {
          await prefs.remove(key);
        }
      }
      debugPrint('Preferences cache cleared successfully');
    } catch (e) {
      debugPrint('Error clearing preferences cache: $e');
    }
  }

  /// تنظيف جميع أنواع الذاكرة المؤقتة
  Future<void> clearAllCache() async {
    await clearImageCache();
    await clearPreferences();
    await clearCompressedImages();
    debugPrint('All cache cleared successfully');
  }

  /// تنظيف الصور المضغوطة
  Future<void> clearCompressedImages() async {
    if (kIsWeb) return; // تخطي على الويب

    try {
      if (_compressedImagesPath != null) {
        final dir = Directory(_compressedImagesPath!);
        if (await dir.exists()) {
          await for (final entity in dir.list()) {
            if (entity is File) {
              await entity.delete();
            }
          }
        }
      }
      debugPrint('Compressed images cleared successfully');
    } catch (e) {
      debugPrint('Error clearing compressed images: $e');
    }
  }

  /// تنظيف الذاكرة المؤقتة القديمة (أقدم من عدد معين من الأيام)
  Future<void> clearOldCache({int olderThan = 7}) async {
    if (kIsWeb) return; // تخطي على الويب

    try {
      // تنظيف الصور المؤقتة القديمة
      await _clearOldFiles(_tempImagesPath, olderThan);

      // تنظيف الصور المضغوطة القديمة
      await _clearOldFiles(_compressedImagesPath, olderThan);

      // حذف الملفات القديمة من التفضيلات
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final now = DateTime.now();

      for (final key in keys) {
        // تخطي المفاتيح المهمة
        if (key.startsWith('cache_timestamp_')) {
          final timestampStr = prefs.getString(key);
          if (timestampStr != null) {
            try {
              final timestamp = DateTime.parse(timestampStr);
              final difference = now.difference(timestamp).inDays;

              if (difference > olderThan) {
                // حذف المفتاح إذا كان أقدم من الحد المسموح
                final cacheKey = key.replaceFirst('cache_timestamp_', '');
                await prefs.remove('cache_$cacheKey');
                await prefs.remove(key);
              }
            } catch (e) {
              // تجاهل الأخطاء في تحليل التاريخ
            }
          }
        }
      }

      debugPrint('Old cache cleared successfully (older than $olderThan days)');
    } catch (e) {
      debugPrint('Error clearing old cache: $e');
    }
  }

  /// تنظيف الملفات القديمة من مجلد معين
  Future<void> _clearOldFiles(String? dirPath, int olderThan) async {
    if (dirPath == null) return;

    try {
      final dir = Directory(dirPath);
      if (await dir.exists()) {
        final now = DateTime.now();
        await for (final entity in dir.list()) {
          if (entity is File) {
            final stat = await entity.stat();
            final fileAge = now.difference(stat.modified).inDays;

            if (fileAge > olderThan) {
              await entity.delete();
              debugPrint('Deleted old file: ${entity.path}');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error clearing old files from $dirPath: $e');
    }
  }

  /// تخزين بيانات في الذاكرة المؤقتة مع طابع زمني
  Future<void> cacheData(String key, String data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('cache_$key', data);
      await prefs.setString(
          'cache_timestamp_$key', DateTime.now().toIso8601String());
      debugPrint('Data cached successfully for key: $key');
    } catch (e) {
      debugPrint('Error caching data for key $key: $e');
    }
  }

  /// استرجاع بيانات من الذاكرة المؤقتة
  Future<String?> getCachedData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('cache_$key');
    } catch (e) {
      debugPrint('Error retrieving cached data for key $key: $e');
      return null;
    }
  }

  /// التحقق من وجود بيانات في الذاكرة المؤقتة وعدم تجاوزها للعمر المحدد
  Future<bool> hasFreshCache(String key, {int maxAgeMinutes = 60}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestampStr = prefs.getString('cache_timestamp_$key');

      if (timestampStr == null) {
        return false;
      }

      final timestamp = DateTime.parse(timestampStr);
      final now = DateTime.now();
      final difference = now.difference(timestamp).inMinutes;

      return difference <= maxAgeMinutes;
    } catch (e) {
      debugPrint('Error checking cache freshness for key $key: $e');
      return false;
    }
  }

  /// تنظيف الذاكرة المؤقتة بشكل دوري
  /// يتم استدعاء هذه الدالة من خلال مؤقت دوري
  Future<void> scheduledCacheCleanup() async {
    try {
      // تنظيف الملفات القديمة (أكثر من 3 أيام)
      await clearOldCache(olderThan: 3);

      // تسجيل وقت آخر تنظيف
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'last_cache_cleanup', DateTime.now().toIso8601String());

      debugPrint('Scheduled cache cleanup completed successfully');
    } catch (e) {
      debugPrint('Error during scheduled cache cleanup: $e');
    }
  }

  /// حفظ ملف مؤقت في مجلد الصور المؤقتة
  /// يستخدم لحفظ الصور المؤقتة التي يتم تحميلها من الإنترنت
  Future<String?> saveTempFile(List<int> bytes, String extension) async {
    if (kIsWeb) return null; // تخطي على الويب

    try {
      if (_tempImagesPath == null) {
        await _initPaths();
      }

      if (_tempImagesPath == null) {
        return null;
      }

      final fileName =
          'temp_${DateTime.now().millisecondsSinceEpoch}.$extension';
      final filePath = '$_tempImagesPath/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(bytes);

      debugPrint('Temp file saved successfully: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('Error saving temp file: $e');
      return null;
    }
  }
}
