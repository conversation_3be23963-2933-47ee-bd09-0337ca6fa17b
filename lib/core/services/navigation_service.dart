import 'package:flutter/material.dart';

/// خدمة التنقل المحسنة للتطبيق
/// تستخدم لجعل الانتقال بين الصفحات أكثر سلاسة وأخف
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  
  factory NavigationService() {
    return _instance;
  }
  
  NavigationService._internal();
  
  /// مفتاح التنقل العام
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  /// الانتقال إلى صفحة جديدة بتأثير انتقالي سلس
  Future<dynamic> navigateTo(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamed(
      routeName,
      arguments: arguments,
    );
  }
  
  /// الانتقال إلى صفحة جديدة مع إزالة كل الصفحات السابقة
  Future<dynamic> navigateToAndRemoveUntil(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamedAndRemoveUntil(
      routeName,
      (Route<dynamic> route) => false,
      arguments: arguments,
    );
  }
  
  /// الانتقال إلى صفحة جديدة مع استبدال الصفحة الحالية
  Future<dynamic> navigateToReplacement(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushReplacementNamed(
      routeName,
      arguments: arguments,
    );
  }
  
  /// الرجوع للصفحة السابقة
  void goBack({dynamic result}) {
    return navigatorKey.currentState!.pop(result);
  }
  
  /// الانتقال إلى صفحة جديدة بتأثير انتقالي مخصص
  Future<dynamic> navigateWithCustomTransition(
    Widget page, {
    bool fullscreenDialog = false,
    Duration transitionDuration = const Duration(milliseconds: 300),
    TransitionType transitionType = TransitionType.fadeIn,
  }) {
    return navigatorKey.currentState!.push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionDuration: transitionDuration,
        fullscreenDialog: fullscreenDialog,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          switch (transitionType) {
            case TransitionType.fadeIn:
              return FadeTransition(opacity: animation, child: child);
            case TransitionType.slideFromRight:
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(1.0, 0.0),
                  end: Offset.zero,
                ).animate(animation),
                child: child,
              );
            case TransitionType.slideFromLeft:
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(-1.0, 0.0),
                  end: Offset.zero,
                ).animate(animation),
                child: child,
              );
            case TransitionType.slideFromBottom:
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.0, 1.0),
                  end: Offset.zero,
                ).animate(animation),
                child: child,
              );
            case TransitionType.scale:
              return ScaleTransition(
                scale: animation,
                child: child,
              );
          }
        },
      ),
    );
  }
}

/// أنواع التأثيرات الانتقالية المتاحة
enum TransitionType {
  fadeIn,
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  scale,
}
