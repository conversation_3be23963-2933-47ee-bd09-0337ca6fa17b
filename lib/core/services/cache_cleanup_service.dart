import 'dart:async';
import 'package:flutter/foundation.dart';
import 'cache_service.dart';

/// خدمة تنظيف الذاكرة المؤقتة بشكل دوري
/// تقوم بتنظيف الصور المؤقتة والملفات غير المستخدمة بشكل دوري
class CacheCleanupService {
  static final CacheCleanupService _instance = CacheCleanupService._internal();
  
  /// الحصول على نسخة وحيدة من الخدمة (Singleton)
  factory CacheCleanupService() {
    return _instance;
  }
  
  /// خدمة إدارة الذاكرة المؤقتة
  final CacheService _cacheService = CacheService();
  
  /// مؤقت لتنظيف الذاكرة المؤقتة بشكل دوري
  Timer? _cleanupTimer;
  
  /// الفترة الزمنية بين عمليات التنظيف (بالساعات)
  static const int _cleanupIntervalHours = 24;
  
  CacheCleanupService._internal();
  
  /// بدء خدمة تنظيف الذاكرة المؤقتة
  void startPeriodicCleanup() {
    if (_cleanupTimer != null) {
      _cleanupTimer!.cancel();
    }
    
    // تنظيف الذاكرة المؤقتة عند بدء التطبيق
    _performCleanup();
    
    // إعداد مؤقت لتنظيف الذاكرة المؤقتة بشكل دوري
    _cleanupTimer = Timer.periodic(
      Duration(hours: _cleanupIntervalHours),
      (_) => _performCleanup(),
    );
    
    debugPrint('Periodic cache cleanup service started');
  }
  
  /// إيقاف خدمة تنظيف الذاكرة المؤقتة
  void stopPeriodicCleanup() {
    if (_cleanupTimer != null) {
      _cleanupTimer!.cancel();
      _cleanupTimer = null;
      debugPrint('Periodic cache cleanup service stopped');
    }
  }
  
  /// تنفيذ عملية تنظيف الذاكرة المؤقتة
  Future<void> _performCleanup() async {
    if (kIsWeb) return; // تخطي على الويب
    
    debugPrint('Performing scheduled cache cleanup...');
    await _cacheService.scheduledCacheCleanup();
  }
  
  /// تنظيف الذاكرة المؤقتة يدويًا
  Future<void> cleanupNow() async {
    await _performCleanup();
  }
}
