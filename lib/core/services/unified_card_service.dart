// lib/core/services/unified_card_service.dart

import 'package:flutter/material.dart';
import 'package:mashair/core/models/unified_card.dart';
import 'package:mashair/core/models/unified_card_element.dart';
import 'package:mashair/core/repositories/unified_card_repository.dart';
import 'package:mashair/core/utils/card_converter.dart';
import 'package:mashair/features/create_card/domain/entities/card_element.dart';

/// خدمة البطاقات الموحدة للتعامل مع البطاقات في كل من لوحة التحكم والتطبيق
class UnifiedCardService {
  final UnifiedCardRepository _repository;

  /// إنشاء خدمة البطاقات الموحدة
  UnifiedCardService({UnifiedCardRepository? repository})
      : _repository = repository ?? UnifiedCardRepository();

  /// الحصول على بطاقة بواسطة المعرف
  Future<UnifiedCard?> getCardById(String cardId) async {
    return await _repository.getCardById(cardId);
  }

  /// الحصول على عناصر البطاقة بواسطة معرف البطاقة
  Future<List<UnifiedCardElement>> getCardElements(String cardId) async {
    return await _repository.getCardElements(cardId);
  }

  /// الحصول على عناصر البطاقة بواسطة معرف البطاقة وتحويلها إلى عناصر التطبيق
  Future<List<CardElement>> getCardElementsAsAppElements(String cardId) async {
    final unifiedElements = await _repository.getCardElements(cardId);
    return CardConverter.unifiedElementsToAppElements(unifiedElements);
  }

  /// حفظ بطاقة جديدة
  Future<String?> createCard(UnifiedCard card) async {
    return await _repository.createCard(card);
  }

  /// تحديث بطاقة موجودة
  Future<bool> updateCard(UnifiedCard card) async {
    return await _repository.updateCard(card);
  }

  /// حذف بطاقة
  Future<bool> deleteCard(String cardId) async {
    return await _repository.deleteCard(cardId);
  }

  /// زيادة عداد زيارات البطاقة
  Future<bool> incrementCardVisits(String cardId) async {
    return await _repository.incrementCardVisits(cardId);
  }

  /// حفظ عناصر البطاقة
  Future<bool> saveCardElements(String cardId, List<UnifiedCardElement> elements) async {
    return await _repository.saveCardElements(cardId, elements);
  }

  /// حفظ عناصر البطاقة من عناصر التطبيق
  /// يتم تحويل الإحداثيات إلى نسبية وتطبيق عامل التحويل المناسب
  Future<bool> saveAppElements(String cardId, List<CardElement> appElements) async {
    // طباعة معلومات العناصر قبل التحويل
    debugPrint('=== حفظ عناصر البطاقة من عناصر التطبيق ===');
    debugPrint('عدد العناصر: ${appElements.length}');

    // تحويل العناصر إلى نموذج البيانات الموحد
    final List<CardElement> normalizedElements = appElements.map((element) {
      // طباعة معلومات العنصر للتصحيح
      debugPrint('معالجة عنصر: النوع=${element.type}, '
          'الإحداثيات=(${element.x}, ${element.y}), '
          'الأبعاد=(${element.width}x${element.height}), '
          'isRelativeCoordinates=${element.isRelativeCoordinates}');

      // تحديد ما إذا كانت الإحداثيات نسبية
      bool isRelative = element.isRelativeCoordinates || (element.width <= 1 && element.height <= 1);

      // تحويل الإحداثيات إلى نسبية إذا لم تكن كذلك
      double x = element.x;
      double y = element.y;
      double width = element.width;
      double height = element.height;

      if (!isRelative) {
        // استخدام نفس أبعاد البطاقة في كل من لوحة التحكم والتطبيق
        // هذا يضمن توافق الإحداثيات والقياسات بين الواجهتين
        final double cardWidth = 800.0;  // أبعاد البطاقة الموحدة
        final double cardHeight = 1000.0; // أبعاد البطاقة الموحدة

        x = x / cardWidth;
        y = y / cardHeight;
        width = width / cardWidth;
        height = height / cardHeight;

        debugPrint('تم تحويل الإحداثيات من مطلقة إلى نسبية: '
            'من (${element.x}, ${element.y}, ${element.width}x${element.height}) '
            'إلى ($x, $y, ${width}x$height)');
      }

      // إنشاء نسخة من العنصر مع تحديث الإحداثيات
      return element.copyWith(
        x: x,
        y: y,
        width: width,
        height: height,
        isRelativeCoordinates: true,
      );
    }).toList();

    debugPrint('============================');

    // تحويل العناصر المعيارية إلى نموذج البيانات الموحد
    final unifiedElements = CardConverter.appElementsToUnifiedElements(normalizedElements);

    // حفظ العناصر في قاعدة البيانات
    return await _repository.saveCardElements(cardId, unifiedElements);
  }

  /// إنشاء عنصر نص جديد
  UnifiedCardElement createTextElement({
    required String text,
    required double x,
    required double y,
    double width = 0.3,
    double height = 0.1,
    double fontSize = 16.0,
    bool isBold = false,
    bool isItalic = false,
    bool isUnderline = false,
    String? fontFamily,
    String? fontColor,
    TextAlign? textAlign,
  }) {
    return UnifiedCardElement(
      id: CardConverter.generateElementId(),
      type: UnifiedElementType.text,
      content: text,
      x: x,
      y: y,
      width: width,
      height: height,
      fontSize: fontSize,
      isBold: isBold,
      isItalic: isItalic,
      isUnderline: isUnderline,
      fontFamily: fontFamily,
      fontColor: fontColor,
      textAlign: textAlign ?? TextAlign.center, // توسيط النص كتنسيق افتراضي
      isRelativeCoordinates: true,
    );
  }

  /// إنشاء عنصر صورة جديد
  UnifiedCardElement createImageElement({
    required String imageUrl,
    required double x,
    required double y,
    double width = 0.3,
    double height = 0.3,
  }) {
    return UnifiedCardElement(
      id: CardConverter.generateElementId(),
      type: UnifiedElementType.image,
      content: imageUrl,
      x: x,
      y: y,
      width: width,
      height: height,
      isRelativeCoordinates: true,
    );
  }

  /// إنشاء عنصر ملصق جديد
  UnifiedCardElement createStickerElement({
    required String stickerUrl,
    required double x,
    required double y,
    double width = 0.2,
    double height = 0.2,
  }) {
    return UnifiedCardElement(
      id: CardConverter.generateElementId(),
      type: UnifiedElementType.sticker,
      content: stickerUrl,
      x: x,
      y: y,
      width: width,
      height: height,
      isRelativeCoordinates: true,
    );
  }

  /// إنشاء عنصر توقيع جديد
  UnifiedCardElement createSignatureElement({
    required String signatureData,
    required double x,
    required double y,
    double width = 0.4,
    double height = 0.2,
    String? signatureText,
  }) {
    return UnifiedCardElement(
      id: CardConverter.generateElementId(),
      type: UnifiedElementType.signature,
      content: signatureData,
      x: x,
      y: y,
      width: width,
      height: height,
      fontFamily: signatureText, // تخزين اسم التوقيع في حقل fontFamily
      isRelativeCoordinates: true,
    );
  }

  /// إنشاء بطاقة جديدة فارغة
  UnifiedCard createEmptyCard({
    required String title,
    required String occasionId,
    String? occasionName,
    Color backgroundColor = Colors.white,
  }) {
    return UnifiedCard(
      id: '',
      title: title,
      description: '',
      occasionId: occasionId,
      occasionName: occasionName,
      imageUrl: '',
      backgroundColor: backgroundColor,
      elements: [],
    );
  }
}
