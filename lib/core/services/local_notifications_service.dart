import 'package:flutter/material.dart';

import '../../../main.dart' show navigatorKey;

/// خدمة الإشعارات المحلية البديلة (بدون استخدام مكتبة flutter_local_notifications)
class LocalNotificationsService {
  LocalNotificationsService._();

  /// مثيل واحد من الخدمة (Singleton)
  static final instance = LocalNotificationsService._();

  /// تهيئة خدمة الإشعارات المحلية
  Future<void> init() async {
    // لا شيء للتهيئة في هذه النسخة البسيطة
    debugPrint('LocalNotificationsService initialized (simplified version)');
  }

  /// عرض إشعار محلي
  ///
  /// [title] عنوان الإشعار
  /// [body] نص الإشعار
  /// [payload] بيانات إضافية للإشعار (اختياري)
  /// [channelType] نوع قناة الإشعار (عام، معاملات، تذكيرات)
  /// [showBadge] عرض شارة على أيقونة التطبيق
  Future<void> show(
    String title,
    String body, {
    String? payload,
    NotificationChannelType channelType = NotificationChannelType.general,
    bool showBadge = true,
  }) async {
    // في هذه النسخة البسيطة، نقوم فقط بطباعة الإشعار في وحدة التحكم
    debugPrint('LOCAL NOTIFICATION (simplified):');
    debugPrint('Title: $title');
    debugPrint('Body: $body');
    debugPrint('Payload: $payload');
    debugPrint('Channel: $channelType');

    // يمكن إضافة منطق لعرض الإشعار داخل التطبيق هنا إذا كان التطبيق في المقدمة
    // على سبيل المثال، يمكن استخدام SnackBar أو Dialog

    // إذا كان هناك payload، يمكن معالجته هنا
    if (payload != null) {
      _handlePayload(payload);
    }
  }

  /// معالجة البيانات الإضافية للإشعار
  void _handlePayload(String payload) {
    if (payload == 'notifications') {
      navigatorKey.currentState?.pushNamed('/notifications');
    } else if (payload.startsWith('card:')) {
      // استخراج معرف البطاقة من البيانات
      final cardId = payload.split(':')[1];
      navigatorKey.currentState?.pushNamed('/card_details', arguments: cardId);
    } else if (payload == 'settings') {
      navigatorKey.currentState?.pushNamed('/settings');
    }
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAll() async {
    // لا شيء للإلغاء في هذه النسخة البسيطة
    debugPrint('All notifications cancelled (simplified version)');
  }

  /// إلغاء إشعار محدد
  Future<void> cancel(int id) async {
    // لا شيء للإلغاء في هذه النسخة البسيطة
    debugPrint('Notification with ID $id cancelled (simplified version)');
  }
}

/// أنواع قنوات الإشعارات
enum NotificationChannelType {
  /// إشعارات عامة
  general,

  /// إشعارات المعاملات والعمليات
  transactions,

  /// إشعارات التذكيرات
  reminders,
}
