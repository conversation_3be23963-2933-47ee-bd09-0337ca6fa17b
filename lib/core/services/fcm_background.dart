import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import '../../firebase_options.dart';

@pragma('vm:entry-point')
Future<void> fcmBackgroundHandler(RemoteMessage msg) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final uid = msg.data['uid'];
  if (uid == null) return;

  await FirebaseFirestore.instance
      .collection('users')
      .doc(uid)
      .collection('notifications')
      .add({
    'title': msg.notification?.title ?? msg.data['title'] ?? '',
    'body': msg.notification?.body ?? msg.data['body'] ?? '',
    'ts': FieldValue.serverTimestamp(),
    'read': false,
  });
}
