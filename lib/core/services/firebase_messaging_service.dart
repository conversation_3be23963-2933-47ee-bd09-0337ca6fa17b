import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

import '../../../main.dart' show navigator<PERSON>ey;
import 'local_notifications_service.dart';

class FirebaseMessagingService {
  FirebaseMessagingService._();
  static final instance = FirebaseMessagingService._();

  Future<void> init() async {
    await FirebaseMessaging.instance.requestPermission();
    FirebaseMessaging.onMessage.listen(_handleForeground);
  }

  Future<void> _handleForeground(RemoteMessage msg) async {
    try {
      await LocalNotificationsService.instance.show(
        msg.notification?.title ?? 'تنبيه',
        msg.notification?.body ?? '',
      );

      /* فتح الصفحة من داخل التطبيق دون ضغط على الإشعار */
      if (msg.data['route'] == 'notifications') {
        navigatorKey.currentState?.pushNamed('/notifications');
      }

      /* تخزين الإشعار فى Firestore */
      try {
        final uid = FirebaseAuth.instance.currentUser?.uid;
        if (uid == null) return;
        await FirebaseFirestore.instance
            .collection('users')
            .doc(uid)
            .collection('notifications')
            .add({
          'title': msg.notification?.title ?? msg.data['title'] ?? '',
          'body': msg.notification?.body ?? msg.data['body'] ?? '',
          'ts': FieldValue.serverTimestamp(),
          'read': false,
        });
      } catch (firestoreError) {
        // تجاهل أخطاء Firestore
        debugPrint('خطأ في حفظ الإشعار في Firestore: $firestoreError');
      }
    } catch (e) {
      // تجاهل أي أخطاء في معالج الإشعارات
      debugPrint('خطأ في معالج الإشعارات: $e');
    }
  }
}
