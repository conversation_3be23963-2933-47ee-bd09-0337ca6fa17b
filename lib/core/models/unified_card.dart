// lib/core/models/unified_card.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'unified_card_element.dart';

/// نموذج موحد للبطاقة المستخدمة في كل من لوحة التحكم والتطبيق
/// يضمن هذا النموذج تناسق البيانات بين الواجهتين
class UnifiedCard {
  /// معرف البطاقة
  final String id;

  /// عنوان البطاقة
  final String title;

  /// وصف البطاقة
  final String description;

  /// معرف المناسبة
  final String occasionId;

  /// اسم المناسبة
  final String? occasionName;

  /// رابط صورة البطاقة
  final String imageUrl;

  /// شعبية البطاقة
  final int popularity;

  /// عدد زيارات البطاقة
  final int visits;

  /// لون خلفية البطاقة
  final Color backgroundColor;

  /// عناصر البطاقة
  final List<UnifiedCardElement> elements;

  /// تاريخ إنشاء البطاقة
  final DateTime createdAt;

  /// تاريخ آخر تعديل للبطاقة
  final DateTime? lastModified;

  /// إنشاء بطاقة موحدة
  UnifiedCard({
    required this.id,
    required this.title,
    this.description = '',
    required this.occasionId,
    this.occasionName,
    required this.imageUrl,
    this.popularity = 0,
    this.visits = 0,
    this.backgroundColor = Colors.white,
    required this.elements,
    DateTime? createdAt,
    this.lastModified,
  }) : createdAt = createdAt ?? DateTime.now();

  /// إنشاء نسخة من البطاقة مع تعديل بعض الخصائص
  UnifiedCard copyWith({
    String? id,
    String? title,
    String? description,
    String? occasionId,
    String? occasionName,
    String? imageUrl,
    int? popularity,
    int? visits,
    Color? backgroundColor,
    List<UnifiedCardElement>? elements,
    DateTime? createdAt,
    DateTime? lastModified,
  }) {
    return UnifiedCard(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      occasionId: occasionId ?? this.occasionId,
      occasionName: occasionName ?? this.occasionName,
      imageUrl: imageUrl ?? this.imageUrl,
      popularity: popularity ?? this.popularity,
      visits: visits ?? this.visits,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      elements: elements ?? this.elements,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  /// تحويل البطاقة إلى Map لتخزينها في Firestore
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'occasionId': occasionId,
      'occasionName': occasionName,
      'imageUrl': imageUrl,
      'popularity': popularity,
      'visits': visits,
      'backgroundColor': backgroundColor.value.toRadixString(16).padLeft(8, '0').toUpperCase(),
      'elements': elements.map((e) => e.toMap()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      if (lastModified != null) 'lastModified': Timestamp.fromDate(lastModified!),
    };
  }

  /// إنشاء بطاقة من Map من Firestore
  factory UnifiedCard.fromMap(String id, Map<String, dynamic> map) {
    // استخراج تاريخ الإنشاء
    DateTime createdAt = DateTime.now();
    if (map['createdAt'] != null) {
      if (map['createdAt'] is Timestamp) {
        createdAt = (map['createdAt'] as Timestamp).toDate();
      } else if (map['createdAt'] is int) {
        createdAt = DateTime.fromMillisecondsSinceEpoch(map['createdAt'] as int);
      }
    }

    // استخراج تاريخ آخر تعديل
    DateTime? lastModified;
    if (map['lastModified'] != null) {
      if (map['lastModified'] is Timestamp) {
        lastModified = (map['lastModified'] as Timestamp).toDate();
      } else if (map['lastModified'] is int) {
        lastModified = DateTime.fromMillisecondsSinceEpoch(map['lastModified'] as int);
      }
    }

    // استخراج لون الخلفية
    Color backgroundColor = Colors.white;
    if (map['backgroundColor'] != null) {
      if (map['backgroundColor'] is int) {
        backgroundColor = Color(map['backgroundColor'] as int);
      } else if (map['backgroundColor'] is String) {
        final colorStr = map['backgroundColor'] as String;
        if (colorStr.startsWith('#')) {
          backgroundColor = Color(int.parse('FF${colorStr.substring(1)}', radix: 16));
        } else {
          backgroundColor = Color(int.parse('FF$colorStr', radix: 16));
        }
      }
    }

    // استخراج عناصر البطاقة
    List<UnifiedCardElement> elements = [];
    if (map['elements'] != null && map['elements'] is List) {
      elements = (map['elements'] as List)
          .map((e) => UnifiedCardElement.fromMap(e as Map<String, dynamic>))
          .toList();
    }

    return UnifiedCard(
      id: id,
      title: map['title'] as String? ?? '',
      description: map['description'] as String? ?? '',
      occasionId: map['occasionId'] as String? ?? '',
      occasionName: map['occasionName'] as String?,
      imageUrl: map['imageUrl'] as String? ?? '',
      popularity: map['popularity'] is num ? (map['popularity'] as num).toInt() : 0,
      visits: map['visits'] is num ? (map['visits'] as num).toInt() : 0,
      backgroundColor: backgroundColor,
      elements: elements,
      createdAt: createdAt,
      lastModified: lastModified,
    );
  }

  /// تحويل البطاقة إلى JSON
  Map<String, dynamic> toJson() => toMap();

  /// إنشاء بطاقة من JSON
  factory UnifiedCard.fromJson(String id, Map<String, dynamic> json) => UnifiedCard.fromMap(id, json);
}
