// lib/core/models/unified_card_element.dart

import 'dart:math' as math;
import 'package:flutter/material.dart';

/// نموذج موحد لعناصر البطاقة المستخدمة في كل من لوحة التحكم والتطبيق
/// يضمن هذا النموذج تناسق البيانات بين الواجهتين
class UnifiedCardElement {
  /// معرف العنصر
  final String id;

  /// نوع العنصر (نص، صورة، ملصق، توقيع)
  final UnifiedElementType type;

  /// محتوى العنصر (نص أو رابط صورة)
  final String content;

  /// الموقع الأفقي (نسبي من 0 إلى 1)
  final double x;

  /// الموقع الرأسي (نسبي من 0 إلى 1)
  final double y;

  /// العرض (نسبي من 0 إلى 1)
  final double width;

  /// الارتفاع (نسبي من 0 إلى 1)
  final double height;

  /// زاوية الدوران (بالراديان)
  final double rotation;

  /// مقياس العنصر
  final double scale;

  /// هل الإحداثيات نسبية (0-1) أم مطلقة (بالبكسل)
  final bool isRelativeCoordinates;

  /// هل العنصر من لوحة التحكم
  final bool isFromAdminPanel;

  // خصائص النص
  /// حجم الخط
  final double? fontSize;

  /// هل النص عريض
  final bool? isBold;

  /// هل النص مائل
  final bool? isItalic;

  /// هل النص تحته خط
  final bool? isUnderline;

  /// نوع الخط
  final String? fontFamily;

  /// لون النص
  final String? fontColor;

  /// محاذاة النص
  final TextAlign? textAlign;

  /// تباعد الأحرف
  final double? letterSpacing;

  /// ارتفاع السطر
  final double? lineHeight;

  /// هل العنصر معكوس أفقياً
  final bool? flippedHorizontally;

  /// الموقع الأفقي النهائي بعد التعديل (نسبي من 0 إلى 1)
  final double? finalX;

  /// الموقع الرأسي النهائي بعد التعديل (نسبي من 0 إلى 1)
  final double? finalY;

  /// العرض النهائي بعد التعديل (نسبي من 0 إلى 1)
  final double? finalWidth;

  /// الارتفاع النهائي بعد التعديل (نسبي من 0 إلى 1)
  final double? finalHeight;

  /// إنشاء عنصر بطاقة موحد
  UnifiedCardElement({
    required this.id,
    required this.type,
    required this.content,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.rotation = 0.0,
    this.scale = 1.0,
    this.isRelativeCoordinates = true,
    this.isFromAdminPanel = false,
    this.fontSize,
    this.isBold,
    this.isItalic,
    this.isUnderline,
    this.fontFamily,
    this.fontColor,
    this.textAlign,
    this.letterSpacing,
    this.lineHeight,
    this.flippedHorizontally,
    this.finalX,
    this.finalY,
    this.finalWidth,
    this.finalHeight,
  });

  /// إنشاء نسخة من العنصر مع تعديل بعض الخصائص
  UnifiedCardElement copyWith({
    String? id,
    UnifiedElementType? type,
    String? content,
    double? x,
    double? y,
    double? width,
    double? height,
    double? rotation,
    double? scale,
    bool? isRelativeCoordinates,
    bool? isFromAdminPanel,
    double? fontSize,
    bool? isBold,
    bool? isItalic,
    bool? isUnderline,
    String? fontFamily,
    String? fontColor,
    TextAlign? textAlign,
    double? letterSpacing,
    double? lineHeight,
    bool? flippedHorizontally,
    double? finalX,
    double? finalY,
    double? finalWidth,
    double? finalHeight,
  }) {
    return UnifiedCardElement(
      id: id ?? this.id,
      type: type ?? this.type,
      content: content ?? this.content,
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      rotation: rotation ?? this.rotation,
      scale: scale ?? this.scale,
      isRelativeCoordinates: isRelativeCoordinates ?? this.isRelativeCoordinates,
      isFromAdminPanel: isFromAdminPanel ?? this.isFromAdminPanel,
      fontSize: fontSize ?? this.fontSize,
      isBold: isBold ?? this.isBold,
      isItalic: isItalic ?? this.isItalic,
      isUnderline: isUnderline ?? this.isUnderline,
      fontFamily: fontFamily ?? this.fontFamily,
      fontColor: fontColor ?? this.fontColor,
      textAlign: textAlign ?? this.textAlign,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineHeight: lineHeight ?? this.lineHeight,
      flippedHorizontally: flippedHorizontally ?? this.flippedHorizontally,
      finalX: finalX ?? this.finalX,
      finalY: finalY ?? this.finalY,
      finalWidth: finalWidth ?? this.finalWidth,
      finalHeight: finalHeight ?? this.finalHeight,
    );
  }

  /// تحويل العنصر إلى Map لتخزينه في Firestore
  /// يتم تخزين فقط البيانات الضرورية لكل نوع من العناصر
  Map<String, dynamic> toMap() {
    // طباعة معلومات العنصر قبل التحويل إلى Map
    debugPrint('=== تحويل العنصر إلى Map لتخزينه في Firestore ===');
    debugPrint('المعرف: $id');
    debugPrint('النوع: $type');
    debugPrint('الإحداثيات الأصلية: ($x, $y)');
    debugPrint('الأبعاد الأصلية: (${width}x$height)');
    debugPrint('زاوية الدوران: $rotation');
    debugPrint('المقياس: $scale');
    debugPrint('الإحداثيات النهائية: ($finalX, $finalY)');
    debugPrint('الأبعاد النهائية: (${finalWidth}x$finalHeight)');

    // البيانات الأساسية المشتركة بين جميع أنواع العناصر
    final Map<String, dynamic> data = {
      'id': id,
      'typeIndex': type.index,
      'type': type.name,
      'content': content,
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'rotation': rotation,
      'scale': scale,
      'isRelativeCoordinates': true, // دائمًا نخزن الإحداثيات بشكل نسبي
      'isFromAdminPanel': isFromAdminPanel,
      // إضافة معلومات عن الإحداثيات والقياسات النهائية للصورة
      // هذه القيم مهمة جداً لضمان ظهور العناصر بنفس الحجم والموقع في التطبيق
      'finalX': finalX ?? x,
      'finalY': finalY ?? y,
      'finalWidth': finalWidth ?? width,
      'finalHeight': finalHeight ?? height,
    };

    // إضافة البيانات الخاصة بالنص فقط إذا كان العنصر من نوع النص
    if (type == UnifiedElementType.text) {
      if (fontSize != null) data['fontSize'] = fontSize;
      if (isBold != null) data['isBold'] = isBold;
      if (isItalic != null) data['isItalic'] = isItalic;
      if (isUnderline != null) data['isUnderline'] = isUnderline;
      if (fontFamily != null) data['fontFamily'] = fontFamily;
      if (fontColor != null) data['fontColor'] = fontColor;
      if (textAlign != null) data['textAlign'] = textAlign?.name;
      if (letterSpacing != null) data['letterSpacing'] = letterSpacing;
      if (lineHeight != null) data['lineHeight'] = lineHeight;
    }

    // إضافة البيانات الخاصة بالصور والملصقات فقط إذا كان العنصر من هذه الأنواع
    if (type == UnifiedElementType.image || type == UnifiedElementType.sticker) {
      if (flippedHorizontally != null) data['flippedHorizontally'] = flippedHorizontally;
    }

    return data;
  }

  /// إنشاء عنصر من Map من Firestore
  /// يتم قراءة البيانات الضرورية لكل نوع من العناصر
  factory UnifiedCardElement.fromMap(Map<String, dynamic> map) {
    // طباعة البيانات الواردة للتصحيح
    debugPrint('=== بيانات العنصر الواردة من Firestore ===');
    debugPrint('البيانات: $map');

    // تحديد نوع العنصر
    UnifiedElementType elementType;
    if (map.containsKey('type') && map['type'] is String) {
      elementType = _parseElementTypeFromString(map['type'] as String);
    } else if (map.containsKey('typeIndex') && map['typeIndex'] is int) {
      elementType = UnifiedElementType.values[map['typeIndex'] as int];
    } else {
      elementType = UnifiedElementType.text;
    }

    debugPrint('نوع العنصر: $elementType');

    // تحديد محتوى العنصر
    String content = '';
    if (map.containsKey('content') && map['content'] != null) {
      content = map['content'] as String;
    } else if (map.containsKey('text') && map['text'] != null) {
      content = map['text'] as String;
    } else if (map.containsKey('remoteUrl') && map['remoteUrl'] != null) {
      content = map['remoteUrl'] as String;
    }

    // تحديد محاذاة النص
    TextAlign? textAlign;
    if (map.containsKey('textAlign') && map['textAlign'] != null) {
      if (map['textAlign'] is String) {
        textAlign = _parseTextAlignFromString(map['textAlign'] as String);
      }
    }

    // استخراج الإحداثيات والأبعاد
    double x = (map['x'] as num?)?.toDouble() ?? 0.0;
    double y = (map['y'] as num?)?.toDouble() ?? 0.0;
    double width = (map['width'] as num?)?.toDouble() ?? 0.3;
    double height = (map['height'] as num?)?.toDouble() ?? 0.2;

    // استخراج الإحداثيات والأبعاد النهائية
    // هذه القيم مهمة جداً لضمان ظهور العناصر بنفس الحجم والموقع في التطبيق
    double? finalX = (map['finalX'] as num?)?.toDouble();
    double? finalY = (map['finalY'] as num?)?.toDouble();
    double? finalWidth = (map['finalWidth'] as num?)?.toDouble();
    double? finalHeight = (map['finalHeight'] as num?)?.toDouble();

    // طباعة معلومات الإحداثيات والأبعاد النهائية للتصحيح
    debugPrint('الإحداثيات والأبعاد النهائية من Firebase: ($finalX, $finalY), (${finalWidth}x$finalHeight)');

    // التحقق من وجود زاوية الدوران والمقياس
    double rotation = (map['rotation'] as num?)?.toDouble() ?? 0.0;
    double scale = (map['scale'] as num?)?.toDouble() ?? 1.0;

    debugPrint('زاوية الدوران: $rotation');
    debugPrint('المقياس: $scale');

    // التأكد من أن الإحداثيات نسبية
    bool isRelativeCoordinates = map['isRelativeCoordinates'] as bool? ?? true;
    if (!isRelativeCoordinates) {
      // تحويل الإحداثيات المطلقة إلى نسبية
      // نستخدم أبعاد البطاقة القياسية في التطبيق
      final double cardWidth = map['cardWidth'] as double? ?? 360.0;
      final double cardHeight = map['cardHeight'] as double? ?? 500.0;

      x = x / cardWidth;
      y = y / cardHeight;
      width = width / cardWidth;
      height = height / cardHeight;

      // تحديث علامة الإحداثيات النسبية
      isRelativeCoordinates = true;
    }

    // تحديد ما إذا كان العنصر من لوحة التحكم
    bool isFromAdminPanel = map['isFromAdminPanel'] as bool? ?? true; // نفترض أن العنصر من لوحة التحكم افتراضيًا

    debugPrint('الإحداثيات نسبية: $isRelativeCoordinates');
    debugPrint('من لوحة التحكم: $isFromAdminPanel');

    // تصحيح الإحداثيات السالبة والأبعاد الصغيرة جداً
    if (x < 0) x = 0;
    if (y < 0) y = 0;

    // معالجة خاصة للصور
    if (elementType == UnifiedElementType.image) {
      // للصور، نقوم بفحص شامل للإحداثيات والأبعاد
      debugPrint('معالجة خاصة للصورة: ($x, $y), (${width}x$height)');

      // تحقق ما إذا كان العنصر يتجاوز حدود البطاقة
      bool isElementExtendingBeyondBounds = x < 0 || y < 0 || x + width > 1 || y + height > 1;

      if (isElementExtendingBeyondBounds) {
        debugPrint('الصورة تتجاوز حدود البطاقة: ($x, $y), (${width}x$height)');
      }

      // حالة 1: صورة صغيرة جداً أو في الزاوية العلوية اليسرى
      if (width < 0.1 || height < 0.1 || (x < 0.01 && y < 0.01)) {
        debugPrint('تم اكتشاف صورة صغيرة جداً أو في الزاوية العلوية اليسرى');

        // وضع الصورة في بداية البطاقة بحجم كامل
        x = 0.0; // بداية البطاقة من اليسار
        y = 0.0; // بداية البطاقة من الأعلى
        width = 1.0; // عرض البطاقة كاملاً
        height = 1.0; // ارتفاع البطاقة كاملاً

        debugPrint('تم تصحيح الصورة لتغطي البطاقة كاملة: ($x, $y), (${width}x$height)');
      }
      // حالة 2: صورة كبيرة تتجاوز حدود البطاقة
      else if (width > 0.9 || height > 0.9 || isElementExtendingBeyondBounds) {
        debugPrint('تم اكتشاف صورة كبيرة تتجاوز حدود البطاقة');

        // التأكد من أن الصورة تغطي البطاقة كاملة
        // نحافظ على الإحداثيات الأصلية إذا كانت الصورة تتجاوز الحدود
        // لكن نضمن أن الصورة تغطي البطاقة كاملة
        if (x > 0.1 || y > 0.1) {
          // إذا كانت الصورة ليست في بداية البطاقة، نحافظ على موقعها
          // ولكن نزيد حجمها لتغطي البطاقة كاملة
          width = math.max(width, 1.0);
          height = math.max(height, 1.0);
        } else {
          // إذا كانت الصورة في بداية البطاقة، نضعها لتغطي البطاقة كاملة
          x = 0.0;
          y = 0.0;
          width = 1.0;
          height = 1.0;
        }

        debugPrint('تم تصحيح الصورة لتغطي البطاقة كاملة: ($x, $y), (${width}x$height)');
      }
    } else if (elementType == UnifiedElementType.sticker) {
      // للملصقات، نستخدم قيم أصغر ونحافظ على نسبة العرض إلى الارتفاع
      if (width < 0.01) width = 0.2;
      if (height < 0.01) height = 0.2;

      // حساب نسبة العرض إلى الارتفاع
      double aspectRatio = (width > 0 && height > 0) ? width / height : 1.0;

      // ضمان أن الملصق له حجم مناسب
      if (width < 0.1 || height < 0.1) {
        width = 0.2;
        height = width / aspectRatio;
      }
    } else {
      // للنصوص، نستخدم قيم أصغر
      if (width < 0.01) width = 0.3;
      if (height < 0.01) height = 0.3;
    }

    // تحديث الإحداثيات والقياسات النهائية إذا لم تكن متوفرة
    finalX ??= x;
    finalY ??= y;
    finalWidth ??= width;
    finalHeight ??= height;

    // طباعة معلومات الإحداثيات والقياسات النهائية للتصحيح
    debugPrint('الإحداثيات والقياسات النهائية المستخدمة: ($finalX, $finalY), (${finalWidth}x$finalHeight)');
    debugPrint('الإحداثيات والقياسات المصححة: ($x, $y), (${width}x$height)');

    // إنشاء العنصر مع البيانات المناسبة لنوعه
    return UnifiedCardElement(
      id: map['id'] as String? ?? DateTime.now().millisecondsSinceEpoch.toString(),
      type: elementType,
      content: content,
      x: x,
      y: y,
      width: width,
      height: height,
      rotation: (map['rotation'] as num?)?.toDouble() ?? 0.0,
      scale: (map['scale'] as num?)?.toDouble() ?? 1.0,
      isRelativeCoordinates: isRelativeCoordinates,
      isFromAdminPanel: isFromAdminPanel,
      // إضافة الإحداثيات والقياسات النهائية
      finalX: finalX,
      finalY: finalY,
      finalWidth: finalWidth,
      finalHeight: finalHeight,
      // البيانات الخاصة بالنص فقط إذا كان العنصر من نوع النص
      fontSize: elementType == UnifiedElementType.text ? (map['fontSize'] as num?)?.toDouble() : null,
      isBold: elementType == UnifiedElementType.text ? map['isBold'] as bool? : null,
      isItalic: elementType == UnifiedElementType.text ? map['isItalic'] as bool? : null,
      isUnderline: elementType == UnifiedElementType.text ? map['isUnderline'] as bool? : null,
      fontFamily: elementType == UnifiedElementType.text ? map['fontFamily'] as String? : null,
      fontColor: elementType == UnifiedElementType.text ? map['fontColor'] as String? : null,
      textAlign: elementType == UnifiedElementType.text ? textAlign : null,
      letterSpacing: elementType == UnifiedElementType.text ? (map['letterSpacing'] as num?)?.toDouble() : null,
      lineHeight: elementType == UnifiedElementType.text ? (map['lineHeight'] as num?)?.toDouble() : null,
      // البيانات الخاصة بالصور والملصقات فقط إذا كان العنصر من هذه الأنواع
      flippedHorizontally: (elementType == UnifiedElementType.image || elementType == UnifiedElementType.sticker)
          ? map['flippedHorizontally'] as bool? : null,
      // الإحداثيات والأبعاد النهائية تم إضافتها بالفعل أعلاه
    );
  }

  /// تحليل نوع العنصر من نص
  static UnifiedElementType _parseElementTypeFromString(String typeStr) {
    if (typeStr.contains('text')) return UnifiedElementType.text;
    if (typeStr.contains('image')) return UnifiedElementType.image;
    if (typeStr.contains('sticker')) return UnifiedElementType.sticker;
    if (typeStr.contains('signature')) return UnifiedElementType.signature;
    if (typeStr.contains('qrCode')) return UnifiedElementType.qrCode;
    return UnifiedElementType.text;
  }

  /// تحليل محاذاة النص من نص
  static TextAlign _parseTextAlignFromString(String alignStr) {
    if (alignStr.contains('start')) return TextAlign.start;
    if (alignStr.contains('center')) return TextAlign.center;
    if (alignStr.contains('end')) return TextAlign.end;
    if (alignStr.contains('left')) return TextAlign.left;
    if (alignStr.contains('right')) return TextAlign.right;
    if (alignStr.contains('justify')) return TextAlign.justify;
    return TextAlign.start;
  }
}

/// أنواع عناصر البطاقة
enum UnifiedElementType {
  text,
  image,
  sticker,
  signature,
  qrCode,
}
