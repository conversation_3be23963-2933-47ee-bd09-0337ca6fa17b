import 'package:dartz/dartz.dart';

import '../errors/failures.dart';

/// Abstract interface representing the UseCase concept in Clean Architecture.
///
/// This generic interface defines the contract for all use cases in the application.
/// - Type: The return type of the use case (e.g., entity, list, etc.)
/// - Params: The parameters required to execute the use case
///
/// Use cases are callable classes that encapsulate a single business operation.
abstract class UseCase<Type, Params> {
  /// Executes the use case with the provided parameters.
  ///
  /// @param params The parameters required for this use case
  /// @return A Future that resolves to either a [Failure] or the result of type [Type]
  Future<Either<Failure, Type>> call(Params params);
}

/// Empty parameters class for use cases that don't require any parameters.
///
/// This class can be used with use cases that don't need any input parameters.
/// Example: `class GetCurrentUserUseCase implements UseCase<User, NoParams>`
class NoParams {
  /// Creates a new instance of NoParams
  const NoParams();
}
