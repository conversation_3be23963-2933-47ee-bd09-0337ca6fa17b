// lib/core/error/failures.dart

import 'package:equatable/equatable.dart';

/// فئة الفشل الأساسية
abstract class Failure extends Equatable {
  /// رسالة الخطأ
  String get message => 'حدث خطأ غير متوقع';

  @override
  List<Object> get props => [];
}

/// فشل الخادم
class ServerFailure extends Failure {
  /// رسالة الخطأ
  @override
  final String message;

  /// إنشاء فشل الخادم
  ServerFailure([this.message = 'حدث خطأ في الخادم']);

  @override
  List<Object> get props => [message];
}

/// فشل التخزين المؤقت
class CacheFailure extends Failure {
  /// رسالة الخطأ
  @override
  final String message;

  /// إنشاء فشل التخزين المؤقت
  CacheFailure([this.message = 'حدث خطأ في التخزين المؤقت']);

  @override
  List<Object> get props => [message];
}

/// فشل الاتصال
class ConnectionFailure extends Failure {
  /// رسالة الخطأ
  @override
  final String message;

  /// إنشاء فشل الاتصال
  ConnectionFailure([this.message = 'لا يوجد اتصال بالإنترنت']);

  @override
  List<Object> get props => [message];
}

/// فشل المصادقة
class AuthFailure extends Failure {
  /// رسالة الخطأ
  @override
  final String message;

  /// إنشاء فشل المصادقة
  AuthFailure([this.message = 'فشل في المصادقة']);

  @override
  List<Object> get props => [message];
}

/// فشل التحقق من الصحة
class ValidationFailure extends Failure {
  /// رسالة الخطأ
  @override
  final String message;

  /// إنشاء فشل التحقق من الصحة
  ValidationFailure([this.message = 'بيانات غير صالحة']);

  @override
  List<Object> get props => [message];
}
