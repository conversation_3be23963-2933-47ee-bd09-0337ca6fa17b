import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants/app_colors.dart';

/// مكون عرض إشعار نجاح متحرك يغطي الشاشة
class SuccessOverlay extends StatefulWidget {
  /// نص الإشعار
  final String message;

  /// الإجراء الذي سيتم تنفيذه بعد انتهاء الإشعار
  final VoidCallback? onComplete;

  /// مدة ظهور الإشعار
  final Duration duration;

  /// إنشاء إشعار نجاح متحرك
  const SuccessOverlay({
    super.key,
    required this.message,
    this.onComplete,
    this.duration = const Duration(seconds: 2),
  });

  /// عرض إشعار نجاح متحرك
  static void show(
    BuildContext context, {
    required String message,
    VoidCallback? onComplete,
    Duration duration = const Duration(seconds: 2),
  }) {
    // إنشاء مفتاح عام للإشعار
    final overlayKey = GlobalKey<_SuccessOverlayState>();

    // إنشاء إشعار جديد
    final overlayEntry = OverlayEntry(
      builder: (context) => SuccessOverlay(
        key: overlayKey,
        message: message,
        onComplete: onComplete,
        duration: duration,
      ),
    );

    // عرض الإشعار
    Overlay.of(context).insert(overlayEntry);

    // إغلاق الإشعار بعد المدة المحددة
    Future.delayed(duration, () {
      if (overlayKey.currentState?.mounted == true) {
        overlayKey.currentState?.closeOverlay().then((_) {
          overlayEntry.remove();
          onComplete?.call();
        });
      }
    });
  }

  @override
  State<SuccessOverlay> createState() => _SuccessOverlayState();
}

class _SuccessOverlayState extends State<SuccessOverlay>
    with SingleTickerProviderStateMixin {
  /// متحكم الحركة
  late final AnimationController _controller;

  /// حركة الظهور والاختفاء
  late final Animation<double> _fadeAnimation;

  /// حركة الحجم
  late final Animation<double> _scaleAnimation;

  /// حركة دوران الأيقونة
  late final Animation<double> _rotateAnimation;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الحركة
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // إنشاء حركة الظهور والاختفاء
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
        reverseCurve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
      ),
    );

    // إنشاء حركة الحجم
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.elasticOut),
        reverseCurve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
      ),
    );

    // إنشاء حركة دوران الأيقونة
    _rotateAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
      ),
    );

    // بدء الحركة
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// إغلاق الإشعار
  Future<void> closeOverlay() async {
    return _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      child: Material(
        type: MaterialType.transparency,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            color: Colors.black54,
            child: Center(
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(51), // 0.2 * 255 = ~51
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      RotationTransition(
                        turns: _rotateAnimation,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor
                                .withAlpha(26), // 0.1 * 255 = ~26
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Icon(
                              Icons.check_circle,
                              color: Colors.green.shade700,
                              size: 60,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          widget.message,
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
