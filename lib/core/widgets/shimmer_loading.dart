import 'package:flutter/material.dart';

class ShimmerLoading extends StatefulWidget {
  final Widget child;
  final bool isLoading;
  final Color baseColor;
  final Color highlightColor;
  final Duration duration;

  const ShimmerLoading({
    super.key,
    required this.child,
    required this.isLoading,
    this.baseColor = const Color(0xFFE0E0E0),
    this.highlightColor = const Color(0xFFF5F5F5),
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<ShimmerLoading> createState() => _ShimmerLoadingState();
}

class _ShimmerLoadingState extends State<ShimmerLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOutSine,
      ),
    );

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          blendMode: BlendMode.srcATop,
          shaderCallback: (bounds) {
            return LinearGradient(
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: const [0.0, 0.5, 1.0],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              transform: _SlidingGradientTransform(
                slidePercent: _animation.value,
              ),
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

class _SlidingGradientTransform extends GradientTransform {
  const _SlidingGradientTransform({
    required this.slidePercent,
  });

  final double slidePercent;

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    return Matrix4.translationValues(
      bounds.width * slidePercent,
      0.0,
      0.0,
    );
  }
}

// مكونات جاهزة للاستخدام

// مكون لعرض مستطيل بتأثير الشيمر
class ShimmerBox extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;
  final Color baseColor;
  final Color highlightColor;

  const ShimmerBox({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = 8.0,
    this.baseColor = const Color(0xFFE0E0E0),
    this.highlightColor = const Color(0xFFF5F5F5),
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final actualBaseColor = isDarkMode ? const Color(0xFF303030) : baseColor;
    final actualHighlightColor =
        isDarkMode ? const Color(0xFF404040) : highlightColor;

    return ShimmerLoading(
      isLoading: true,
      baseColor: actualBaseColor,
      highlightColor: actualHighlightColor,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: actualBaseColor,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }
}

// مكون لعرض بطاقة بتأثير الشيمر
class ShimmerCard extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;
  final bool showTitle;
  final bool showSubtitle;
  final EdgeInsetsGeometry padding;
  final Color baseColor;
  final Color highlightColor;

  const ShimmerCard({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = 8.0,
    this.showTitle = true,
    this.showSubtitle = false,
    this.padding = const EdgeInsets.all(16.0),
    this.baseColor = const Color(0xFFE0E0E0),
    this.highlightColor = const Color(0xFFF5F5F5),
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final actualBaseColor = isDarkMode ? const Color(0xFF303030) : baseColor;
    final actualHighlightColor =
        isDarkMode ? const Color(0xFF404040) : highlightColor;

    return ShimmerLoading(
      isLoading: true,
      baseColor: actualBaseColor,
      highlightColor: actualHighlightColor,
      child: Container(
        width: width,
        height: height,
        padding: padding,
        decoration: BoxDecoration(
          color: actualBaseColor,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              height: height -
                  (showTitle ? 50 : 0) -
                  (showSubtitle ? 30 : 0) -
                  padding.vertical,
              decoration: BoxDecoration(
                color: actualBaseColor,
                borderRadius: BorderRadius.circular(borderRadius - 4),
              ),
            ),
            if (showTitle) ...[
              const SizedBox(height: 12),
              Container(
                width: width * 0.7,
                height: 20,
                decoration: BoxDecoration(
                  color: actualBaseColor,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
            if (showSubtitle) ...[
              const SizedBox(height: 8),
              Container(
                width: width * 0.5,
                height: 14,
                decoration: BoxDecoration(
                  color: actualBaseColor,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// مكون لعرض بانر إعلاني بتأثير الشيمر
class ShimmerBanner extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;
  final Color baseColor;
  final Color highlightColor;

  const ShimmerBanner({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = 12.0,
    this.baseColor = const Color(0xFFE0E0E0),
    this.highlightColor = const Color(0xFFF5F5F5),
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final actualBaseColor = isDarkMode ? const Color(0xFF303030) : baseColor;
    final actualHighlightColor =
        isDarkMode ? const Color(0xFF404040) : highlightColor;

    return ShimmerLoading(
      isLoading: true,
      baseColor: actualBaseColor,
      highlightColor: actualHighlightColor,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: actualBaseColor,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }
}

// مكون لعرض قائمة من البطاقات بتأثير الشيمر
class ShimmerCardsList extends StatelessWidget {
  final int itemCount;
  final double itemWidth;
  final double itemHeight;
  final double spacing;
  final bool horizontal;
  final bool showTitle;
  final bool showSubtitle;
  final double borderRadius;
  final Color baseColor;
  final Color highlightColor;

  const ShimmerCardsList({
    super.key,
    required this.itemCount,
    required this.itemWidth,
    required this.itemHeight,
    this.spacing = 16.0,
    this.horizontal = true,
    this.showTitle = true,
    this.showSubtitle = false,
    this.borderRadius = 8.0,
    this.baseColor = const Color(0xFFE0E0E0),
    this.highlightColor = const Color(0xFFF5F5F5),
  });

  @override
  Widget build(BuildContext context) {
    if (horizontal) {
      return SizedBox(
        height: itemHeight,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: itemCount,
          padding: EdgeInsets.symmetric(horizontal: spacing),
          separatorBuilder: (context, index) => SizedBox(width: spacing),
          itemBuilder: (context, index) {
            return ShimmerCard(
              width: itemWidth,
              height: itemHeight,
              showTitle: showTitle,
              showSubtitle: showSubtitle,
              borderRadius: borderRadius,
              baseColor: baseColor,
              highlightColor: highlightColor,
            );
          },
        ),
      );
    } else {
      return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: itemCount,
        separatorBuilder: (context, index) => SizedBox(height: spacing),
        itemBuilder: (context, index) {
          return ShimmerCard(
            width: itemWidth,
            height: itemHeight,
            showTitle: showTitle,
            showSubtitle: showSubtitle,
            borderRadius: borderRadius,
            baseColor: baseColor,
            highlightColor: highlightColor,
          );
        },
      );
    }
  }
}
