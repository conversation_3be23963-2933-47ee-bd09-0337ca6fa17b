import 'package:flutter/material.dart';
import 'dart:math' as math;

/// أنواع المقابض المختلفة
enum HandleType {
  corner,
  side,
  rotation,
}

/// مواضع المقابض
enum HandlePosition {
  topLeft,
  topCenter,
  topRight,
  centerLeft,
  centerRight,
  bottomLeft,
  bottomCenter,
  bottomRight,
  rotation,
}

/// مقبض تحكم احترافي
class ProfessionalHandle extends StatefulWidget {
  final HandlePosition position;
  final HandleType type;
  final VoidCallback? onTap;
  final Function(DragUpdateDetails)? onPanUpdate;
  final Function(DragEndDetails)? onPanEnd;
  final bool isSelected;
  final Color primaryColor;
  final Color secondaryColor;
  final double size;
  final bool showAnimation;

  const ProfessionalHandle({
    Key? key,
    required this.position,
    required this.type,
    this.onTap,
    this.onPanUpdate,
    this.onPanEnd,
    this.isSelected = false,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.white,
    this.size = 12.0,
    this.showAnimation = true,
  }) : super(key: key);

  @override
  State<ProfessionalHandle> createState() => _ProfessionalHandleState();
}

class _ProfessionalHandleState extends State<ProfessionalHandle>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isHovered = false;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHoverEnter() {
    if (!_isDragging) {
      setState(() => _isHovered = true);
      if (widget.showAnimation) {
        _animationController.forward();
      }
    }
  }

  void _onHoverExit() {
    if (!_isDragging) {
      setState(() => _isHovered = false);
      if (widget.showAnimation) {
        _animationController.reverse();
      }
    }
  }

  void _onPanStart(DragStartDetails details) {
    setState(() => _isDragging = true);
    if (widget.showAnimation) {
      _animationController.forward();
    }
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() => _isDragging = false);
    if (!_isHovered && widget.showAnimation) {
      _animationController.reverse();
    }
    widget.onPanEnd?.call(details);
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHoverEnter(),
      onExit: (_) => _onHoverExit(),
      cursor: _getCursor(),
      child: GestureDetector(
        onTap: widget.onTap,
        onPanStart: _onPanStart,
        onPanUpdate: widget.onPanUpdate,
        onPanEnd: _onPanEnd,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  color: widget.secondaryColor,
                  border: Border.all(
                    color: widget.primaryColor,
                    width: 2.0,
                  ),
                  borderRadius: _getBorderRadius(),
                  boxShadow: [
                    BoxShadow(
                      color: widget.primaryColor.withOpacity(0.3 * _glowAnimation.value),
                      blurRadius: 8.0 * _glowAnimation.value,
                      spreadRadius: 2.0 * _glowAnimation.value,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 2.0,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: _buildHandleIcon(),
              ),
            );
          },
        ),
      ),
    );
  }

  BorderRadius _getBorderRadius() {
    switch (widget.type) {
      case HandleType.corner:
        return BorderRadius.circular(2.0);
      case HandleType.side:
        return BorderRadius.circular(widget.size / 2);
      case HandleType.rotation:
        return BorderRadius.circular(widget.size / 2);
    }
  }

  Widget? _buildHandleIcon() {
    if (widget.type == HandleType.rotation) {
      return Icon(
        Icons.refresh,
        size: widget.size * 0.6,
        color: widget.primaryColor,
      );
    }
    return null;
  }

  MouseCursor _getCursor() {
    switch (widget.position) {
      case HandlePosition.topLeft:
      case HandlePosition.bottomRight:
        return SystemMouseCursors.resizeUpLeftDownRight;
      case HandlePosition.topRight:
      case HandlePosition.bottomLeft:
        return SystemMouseCursors.resizeUpRightDownLeft;
      case HandlePosition.topCenter:
      case HandlePosition.bottomCenter:
        return SystemMouseCursors.resizeUpDown;
      case HandlePosition.centerLeft:
      case HandlePosition.centerRight:
        return SystemMouseCursors.resizeLeftRight;
      case HandlePosition.rotation:
        return SystemMouseCursors.grab;
    }
  }
}

/// مجموعة المقابض الاحترافية
class ProfessionalHandleSet extends StatelessWidget {
  final Rect bounds;
  final bool isSelected;
  final Function(HandlePosition, DragUpdateDetails)? onHandleDrag;
  final Function(HandlePosition, DragEndDetails)? onHandleDragEnd;
  final Color primaryColor;
  final Color secondaryColor;
  final double handleSize;
  final bool showRotationHandle;
  final bool showAnimation;

  const ProfessionalHandleSet({
    Key? key,
    required this.bounds,
    this.isSelected = false,
    this.onHandleDrag,
    this.onHandleDragEnd,
    this.primaryColor = Colors.blue,
    this.secondaryColor = Colors.white,
    this.handleSize = 12.0,
    this.showRotationHandle = true,
    this.showAnimation = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isSelected) return const SizedBox.shrink();

    return Stack(
      children: [
        // إطار التحديد
        Positioned.fromRect(
          rect: bounds,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: primaryColor,
                width: 2.0,
              ),
            ),
          ),
        ),
        
        // مقابض الزوايا
        ..._buildCornerHandles(),
        
        // مقابض الجوانب
        ..._buildSideHandles(),
        
        // مقبض الدوران
        if (showRotationHandle) _buildRotationHandle(),
      ],
    );
  }

  List<Widget> _buildCornerHandles() {
    return [
      _buildHandle(HandlePosition.topLeft, HandleType.corner),
      _buildHandle(HandlePosition.topRight, HandleType.corner),
      _buildHandle(HandlePosition.bottomLeft, HandleType.corner),
      _buildHandle(HandlePosition.bottomRight, HandleType.corner),
    ];
  }

  List<Widget> _buildSideHandles() {
    return [
      _buildHandle(HandlePosition.topCenter, HandleType.side),
      _buildHandle(HandlePosition.centerLeft, HandleType.side),
      _buildHandle(HandlePosition.centerRight, HandleType.side),
      _buildHandle(HandlePosition.bottomCenter, HandleType.side),
    ];
  }

  Widget _buildRotationHandle() {
    final rotationOffset = Offset(bounds.center.dx, bounds.top - 30);
    
    return Positioned(
      left: rotationOffset.dx - handleSize / 2,
      top: rotationOffset.dy - handleSize / 2,
      child: ProfessionalHandle(
        position: HandlePosition.rotation,
        type: HandleType.rotation,
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
        size: handleSize,
        showAnimation: showAnimation,
        onPanUpdate: (details) => onHandleDrag?.call(HandlePosition.rotation, details),
        onPanEnd: (details) => onHandleDragEnd?.call(HandlePosition.rotation, details),
      ),
    );
  }

  Widget _buildHandle(HandlePosition position, HandleType type) {
    final offset = _getHandleOffset(position);
    
    return Positioned(
      left: offset.dx - handleSize / 2,
      top: offset.dy - handleSize / 2,
      child: ProfessionalHandle(
        position: position,
        type: type,
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
        size: handleSize,
        showAnimation: showAnimation,
        onPanUpdate: (details) => onHandleDrag?.call(position, details),
        onPanEnd: (details) => onHandleDragEnd?.call(position, details),
      ),
    );
  }

  Offset _getHandleOffset(HandlePosition position) {
    switch (position) {
      case HandlePosition.topLeft:
        return bounds.topLeft;
      case HandlePosition.topCenter:
        return Offset(bounds.center.dx, bounds.top);
      case HandlePosition.topRight:
        return bounds.topRight;
      case HandlePosition.centerLeft:
        return Offset(bounds.left, bounds.center.dy);
      case HandlePosition.centerRight:
        return Offset(bounds.right, bounds.center.dy);
      case HandlePosition.bottomLeft:
        return bounds.bottomLeft;
      case HandlePosition.bottomCenter:
        return Offset(bounds.center.dx, bounds.bottom);
      case HandlePosition.bottomRight:
        return bounds.bottomRight;
      case HandlePosition.rotation:
        return Offset(bounds.center.dx, bounds.top - 30);
    }
  }
}
