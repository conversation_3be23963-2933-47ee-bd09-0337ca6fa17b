// lib/core/widgets/enhanced_tooltip.dart

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// تلميح أدوات محسن مع دعم للأيقونات والصور
class EnhancedTooltip extends StatefulWidget {
  /// النص المراد عرضه في التلميح
  final String message;

  /// أيقونة التلميح (اختياري)
  final IconData? icon;

  /// لون الخلفية
  final Color backgroundColor;

  /// لون النص
  final Color textColor;

  /// العنصر الذي سيظهر التلميح عند تحويم المؤشر عليه
  final Widget child;

  /// ما إذا كان التلميح يحتوي على سهم يشير إلى العنصر
  final bool showArrow;

  /// موقع التلميح بالنسبة للعنصر
  final TooltipPosition position;

  /// المدة التي يستغرقها ظهور التلميح
  final Duration showDuration;

  /// إنشاء تلميح أدوات محسن
  const EnhancedTooltip({
    super.key,
    required this.message,
    required this.child,
    this.icon,
    this.backgroundColor = const Color(0xFF333333),
    this.textColor = Colors.white,
    this.showArrow = true,
    this.position = TooltipPosition.auto,
    this.showDuration = const Duration(seconds: 3),
  });

  @override
  State<EnhancedTooltip> createState() => _EnhancedTooltipState();
}

/// موقع التلميح بالنسبة للعنصر
enum TooltipPosition {
  /// تلقائي (يتم تحديده بناءً على موقع العنصر في الشاشة)
  auto,

  /// فوق العنصر
  top,

  /// أسفل العنصر
  bottom,

  /// يسار العنصر
  left,

  /// يمين العنصر
  right,
}

class _EnhancedTooltipState extends State<EnhancedTooltip> {
  /// ما إذا كان التلميح مرئيًا
  bool _isVisible = false;

  /// مؤقت إخفاء التلميح
  Timer? _hideTimer;

  /// مفتاح العنصر المستهدف
  final GlobalKey _targetKey = GlobalKey();

  /// مدخل الطبقة العلوية
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _hideTimer?.cancel();
    _overlayEntry?.remove();
    super.dispose();
  }

  /// إظهار التلميح
  void _showTooltip() {
    if (_isVisible) return;

    setState(() {
      _isVisible = true;
    });

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);

    _hideTimer = Timer(widget.showDuration, _hideTooltip);
  }

  /// إخفاء التلميح
  void _hideTooltip() {
    if (!_isVisible) return;

    setState(() {
      _isVisible = false;
    });

    _overlayEntry?.remove();
    _overlayEntry = null;
    _hideTimer?.cancel();
    _hideTimer = null;
  }

  /// إنشاء مدخل الطبقة العلوية
  OverlayEntry _createOverlayEntry() {
    // الحصول على موقع وحجم العنصر المستهدف
    final RenderBox renderBox =
        _targetKey.currentContext!.findRenderObject() as RenderBox;
    final Size targetSize = renderBox.size;
    final Offset targetPosition = renderBox.localToGlobal(Offset.zero);

    // حساب موقع التلميح
    final Size screenSize = MediaQuery.of(context).size;
    final double tooltipWidth = 200.0;
    final double tooltipHeight = 60.0;

    // تحديد موقع التلميح بناءً على الموقع المحدد أو تلقائيًا
    TooltipPosition position = widget.position;
    if (position == TooltipPosition.auto) {
      // تحديد الموقع الأفضل بناءً على المساحة المتاحة
      final double spaceAbove = targetPosition.dy;
      final double spaceBelow =
          screenSize.height - (targetPosition.dy + targetSize.height);
      final double spaceLeft = targetPosition.dx;
      final double spaceRight =
          screenSize.width - (targetPosition.dx + targetSize.width);

      final List<MapEntry<TooltipPosition, double>> spaces = [
        MapEntry(TooltipPosition.top, spaceAbove),
        MapEntry(TooltipPosition.bottom, spaceBelow),
        MapEntry(TooltipPosition.left, spaceLeft),
        MapEntry(TooltipPosition.right, spaceRight),
      ];

      spaces.sort((a, b) => b.value.compareTo(a.value));
      position = spaces.first.key;
    }

    // حساب موقع التلميح بناءً على الموقع المحدد
    double left = 0.0;
    double top = 0.0;

    switch (position) {
      case TooltipPosition.top:
        left = targetPosition.dx + (targetSize.width / 2) - (tooltipWidth / 2);
        top = targetPosition.dy - tooltipHeight - 10.0;
        break;
      case TooltipPosition.bottom:
        left = targetPosition.dx + (targetSize.width / 2) - (tooltipWidth / 2);
        top = targetPosition.dy + targetSize.height + 10.0;
        break;
      case TooltipPosition.left:
        left = targetPosition.dx - tooltipWidth - 10.0;
        top = targetPosition.dy + (targetSize.height / 2) - (tooltipHeight / 2);
        break;
      case TooltipPosition.right:
        left = targetPosition.dx + targetSize.width + 10.0;
        top = targetPosition.dy + (targetSize.height / 2) - (tooltipHeight / 2);
        break;
      case TooltipPosition.auto:
        // لن يتم الوصول إلى هذه الحالة، ولكن نضيفها لتجنب الأخطاء
        break;
    }

    // التأكد من أن التلميح لا يخرج عن حدود الشاشة
    left = left.clamp(10.0, screenSize.width - tooltipWidth - 10.0);
    top = top.clamp(10.0, screenSize.height - tooltipHeight - 10.0);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: left,
        top: top,
        child: Material(
          color: Colors.transparent,
          child: _TooltipContent(
            message: widget.message,
            icon: widget.icon,
            backgroundColor: widget.backgroundColor,
            textColor: widget.textColor,
            position: position,
            showArrow: widget.showArrow,
            targetPosition: targetPosition,
            targetSize: targetSize,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: _targetKey,
      onTap: _showTooltip,
      onLongPress: _showTooltip,
      child: MouseRegion(
        onEnter: (_) => _showTooltip(),
        onExit: (_) => _hideTooltip(),
        child: widget.child,
      ),
    );
  }
}

/// محتوى التلميح
class _TooltipContent extends StatelessWidget {
  /// النص المراد عرضه في التلميح
  final String message;

  /// أيقونة التلميح (اختياري)
  final IconData? icon;

  /// لون الخلفية
  final Color backgroundColor;

  /// لون النص
  final Color textColor;

  /// موقع التلميح بالنسبة للعنصر
  final TooltipPosition position;

  /// ما إذا كان التلميح يحتوي على سهم يشير إلى العنصر
  final bool showArrow;

  /// موقع العنصر المستهدف
  final Offset targetPosition;

  /// حجم العنصر المستهدف
  final Size targetSize;

  /// إنشاء محتوى التلميح
  const _TooltipContent({
    required this.message,
    this.icon,
    required this.backgroundColor,
    required this.textColor,
    required this.position,
    required this.showArrow,
    required this.targetPosition,
    required this.targetSize,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 200),
      opacity: 1.0,
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 200.0,
          minHeight: 40.0,
        ),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(51), // 0.2 * 255 = ~51
              blurRadius: 5.0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: textColor,
                size: 18.0,
              ),
              const SizedBox(width: 8.0),
            ],
            Flexible(
              child: Text(
                message,
                style: GoogleFonts.cairo(
                  color: textColor,
                  fontSize: 14.0,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
