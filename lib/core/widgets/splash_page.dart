import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mashair/routes.dart';
import 'package:mashair/injection_container.dart' as di;
import 'package:mashair/core/services/auth_persistence_service.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    _checkAuthAndNavigate();
  }

  /// Verificar el estado de autenticación y navegar a la pantalla correspondiente
  Future<void> _checkAuthAndNavigate() async {
    // Esperar 2 segundos para mostrar la pantalla splash
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    // Verificar si el usuario tiene la opción "Recuérdame" activada
    final authService = di.sl<AuthPersistenceService>();
    final isRememberMeEnabled = await authService.isRememberMeEnabled();

    // Verificar si hay un usuario autenticado actualmente
    final currentUser = FirebaseAuth.instance.currentUser;

    // Verificar que el widget sigue montado antes de usar el contexto
    if (!mounted) return;

    if (isRememberMeEnabled && currentUser != null) {
      // Si "Recuérdame" está activado y hay un usuario autenticado,
      // navegar directamente a la página principal
      Navigator.pushReplacementNamed(context, AppRoutes.home);
    } else {
      // En caso contrario, navegar a la página de inicio de sesión
      Navigator.pushReplacementNamed(context, AppRoutes.login);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Image.asset(
          'assets/logos/logo.png',
          width: 150,
          height: 150,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
