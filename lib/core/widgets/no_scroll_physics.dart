// lib/core/widgets/no_scroll_physics.dart

import 'package:flutter/widgets.dart';

/// فيزياء تمنع التمرير
class NoScrollPhysics extends ScrollPhysics {
  /// إنشاء فيزياء تمنع التمرير
  const NoScrollPhysics({super.parent});

  @override
  NoScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return NoScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  bool shouldAcceptUserOffset(ScrollMetrics position) {
    return false;
  }
}
