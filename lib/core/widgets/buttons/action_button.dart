// lib/core/widgets/buttons/action_button.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// زر إجراء محسن
class ActionButton extends StatelessWidget {
  /// نص الزر
  final String text;

  /// أيقونة الزر (اختياري)
  final IconData? icon;

  /// دالة يتم استدعاؤها عند النقر على الزر
  final VoidCallback? onPressed;

  /// لون خلفية الزر
  final Color? backgroundColor;

  /// لون النص والأيقونة
  final Color? foregroundColor;

  /// حجم الزر
  final ActionButtonSize size;

  /// نوع الزر
  final ActionButtonType type;

  /// ما إذا كان الزر ممتدًا ليملأ العرض المتاح
  final bool isExpanded;

  /// ما إذا كان الزر قيد التحميل
  final bool isLoading;

  /// نص التلميح
  final String? tooltip;

  /// إنشاء زر إجراء محسن
  const ActionButton({
    super.key,
    required this.text,
    this.icon,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.size = ActionButtonSize.medium,
    this.type = ActionButtonType.filled,
    this.isExpanded = false,
    this.isLoading = false,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان بناءً على نوع الزر
    final Color defaultBackgroundColor;
    final Color defaultForegroundColor;

    switch (type) {
      case ActionButtonType.filled:
        defaultBackgroundColor = Theme.of(context).primaryColor;
        defaultForegroundColor = Colors.white;
        break;
      case ActionButtonType.outlined:
        defaultBackgroundColor = Colors.transparent;
        defaultForegroundColor = Theme.of(context).primaryColor;
        break;
      case ActionButtonType.text:
        defaultBackgroundColor = Colors.transparent;
        defaultForegroundColor = Theme.of(context).primaryColor;
        break;
    }

    // تحديد الحجم
    final double height;
    final double iconSize;
    final double fontSize;
    final EdgeInsetsGeometry padding;

    switch (size) {
      case ActionButtonSize.small:
        height = 32.0;
        iconSize = 16.0;
        fontSize = 12.0;
        padding = const EdgeInsets.symmetric(horizontal: 12.0);
        break;
      case ActionButtonSize.medium:
        height = 40.0;
        iconSize = 20.0;
        fontSize = 14.0;
        padding = const EdgeInsets.symmetric(horizontal: 16.0);
        break;
      case ActionButtonSize.large:
        height = 48.0;
        iconSize = 24.0;
        fontSize = 16.0;
        padding = const EdgeInsets.symmetric(horizontal: 24.0);
        break;
    }

    // بناء محتوى الزر
    Widget buttonContent = Row(
      mainAxisSize: isExpanded ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading)
          SizedBox(
            width: iconSize,
            height: iconSize,
            child: CircularProgressIndicator(
              strokeWidth: 2.0,
              valueColor: AlwaysStoppedAnimation<Color>(
                foregroundColor ?? defaultForegroundColor,
              ),
            ),
          )
        else if (icon != null)
          Icon(
            icon,
            size: iconSize,
          ),
        if ((icon != null || isLoading) && text.isNotEmpty)
          const SizedBox(width: 8.0),
        if (text.isNotEmpty)
          Text(
            text,
            style: GoogleFonts.cairo(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
      ],
    );

    // تطبيق التلميح إذا كان موجودًا
    if (tooltip != null) {
      buttonContent = Tooltip(
        message: tooltip!,
        child: buttonContent,
      );
    }

    // بناء الزر بناءً على النوع
    switch (type) {
      case ActionButtonType.filled:
        return SizedBox(
          height: height,
          width: isExpanded ? double.infinity : null,
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor ?? defaultBackgroundColor,
              foregroundColor: foregroundColor ?? defaultForegroundColor,
              padding: padding,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              elevation: 2.0,
            ),
            child: buttonContent,
          ),
        );
      case ActionButtonType.outlined:
        return SizedBox(
          height: height,
          width: isExpanded ? double.infinity : null,
          child: OutlinedButton(
            onPressed: isLoading ? null : onPressed,
            style: OutlinedButton.styleFrom(
              foregroundColor: foregroundColor ?? defaultForegroundColor,
              padding: padding,
              side: BorderSide(
                color: (foregroundColor ?? defaultForegroundColor)
                    .withAlpha(128), // 0.5 * 255 = ~128
                width: 1.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            child: buttonContent,
          ),
        );
      case ActionButtonType.text:
        return SizedBox(
          height: height,
          width: isExpanded ? double.infinity : null,
          child: TextButton(
            onPressed: isLoading ? null : onPressed,
            style: TextButton.styleFrom(
              foregroundColor: foregroundColor ?? defaultForegroundColor,
              padding: padding,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            child: buttonContent,
          ),
        );
    }
  }
}

/// حجم زر الإجراء
enum ActionButtonSize {
  /// صغير
  small,

  /// متوسط
  medium,

  /// كبير
  large,
}

/// نوع زر الإجراء
enum ActionButtonType {
  /// زر ممتلئ
  filled,

  /// زر محاط
  outlined,

  /// زر نصي
  text,
}
