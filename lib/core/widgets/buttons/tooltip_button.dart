// lib/core/widgets/buttons/tooltip_button.dart

import 'package:flutter/material.dart';

import '../enhanced_tooltip.dart';

/// زر مع تلميح أدوات محسن
class TooltipButton extends StatelessWidget {
  /// أيقونة الزر
  final IconData icon;
  
  /// نص التلميح
  final String tooltip;
  
  /// دالة يتم استدعاؤها عند النقر على الزر
  final VoidCallback? onPressed;
  
  /// لون الزر
  final Color? color;
  
  /// حجم الأيقونة
  final double iconSize;
  
  /// لون خلفية التلميح
  final Color tooltipBackgroundColor;
  
  /// لون نص التلميح
  final Color tooltipTextColor;
  
  /// موقع التلميح
  final TooltipPosition tooltipPosition;
  
  /// إنشاء زر مع تلميح أدوات محسن
  const TooltipButton({
    super.key,
    required this.icon,
    required this.tooltip,
    this.onPressed,
    this.color,
    this.iconSize = 24.0,
    this.tooltipBackgroundColor = const Color(0xFF333333),
    this.tooltipTextColor = Colors.white,
    this.tooltipPosition = TooltipPosition.auto,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedTooltip(
      message: tooltip,
      backgroundColor: tooltipBackgroundColor,
      textColor: tooltipTextColor,
      position: tooltipPosition,
      child: IconButton(
        icon: Icon(icon),
        onPressed: onPressed,
        color: color,
        iconSize: iconSize,
        splashRadius: 24.0,
      ),
    );
  }
}
