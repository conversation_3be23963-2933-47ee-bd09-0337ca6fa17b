// lib/core/widgets/optimized/unified_optimized_list.dart

import 'package:flutter/material.dart';

import 'unified_optimized_widget.dart';

/// Unified Optimized List
/// 
/// This class combines functionality from:
/// - OptimizedList
/// 
/// An optimized list widget that supports freezing invisible items
/// and other performance optimizations.
class UnifiedOptimizedList extends StatefulWidget {
  /// The number of items in the list
  final int itemCount;
  
  /// The builder for the items
  final IndexedWidgetBuilder itemBuilder;
  
  /// Whether to shrink wrap the list
  final bool shrinkWrap;
  
  /// The scroll physics
  final ScrollPhysics? physics;
  
  /// The padding around the list
  final EdgeInsetsGeometry? padding;
  
  /// The scroll controller
  final ScrollController? controller;
  
  /// Whether to freeze invisible items
  final bool freezeInvisibleItems;
  
  /// Whether to use lazy loading
  final bool useLazyLoading;
  
  /// The cache extent
  final double cacheExtent;
  
  /// Whether to add repaint boundaries
  final bool addRepaintBoundaries;
  
  /// Whether to add automatic keep alives
  final bool addAutomaticKeepAlives;
  
  /// Whether to add semantic indexes
  final bool addSemanticIndexes;
  
  /// Create an optimized list
  const UnifiedOptimizedList({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.controller,
    this.freezeInvisibleItems = true,
    this.useLazyLoading = true,
    this.cacheExtent = 500.0,
    this.addRepaintBoundaries = true,
    this.addAutomaticKeepAlives = false,
    this.addSemanticIndexes = false,
  });

  @override
  State<UnifiedOptimizedList> createState() => _UnifiedOptimizedListState();
}

class _UnifiedOptimizedListState extends State<UnifiedOptimizedList> {
  // The scroll controller
  late ScrollController _scrollController;
  
  // The visible items range
  int _firstVisibleIndex = 0;
  int _lastVisibleIndex = 0;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize the scroll controller
    _scrollController = widget.controller ?? ScrollController();
    
    // Add a listener to update the visible items range
    _scrollController.addListener(_updateVisibleItemsRange);
  }
  
  @override
  void didUpdateWidget(UnifiedOptimizedList oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update the scroll controller if needed
    if (widget.controller != oldWidget.controller) {
      _scrollController.removeListener(_updateVisibleItemsRange);
      
      _scrollController = widget.controller ?? ScrollController();
      _scrollController.addListener(_updateVisibleItemsRange);
    }
  }
  
  @override
  void dispose() {
    // Dispose the scroll controller if we created it
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_updateVisibleItemsRange);
    }
    
    super.dispose();
  }
  
  /// Update the visible items range
  void _updateVisibleItemsRange() {
    if (!_scrollController.hasClients) return;
    
    final viewportHeight = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;
    
    // Estimate the item height (assuming all items have the same height)
    // This is a simplification, in a real app you might want to use a more accurate approach
    final estimatedItemHeight = 100.0; // Adjust this based on your actual item height
    
    // Calculate the visible range
    final firstVisible = (scrollOffset / estimatedItemHeight).floor();
    final lastVisible = ((scrollOffset + viewportHeight) / estimatedItemHeight).ceil();
    
    // Update the visible range if it changed
    if (firstVisible != _firstVisibleIndex || lastVisible != _lastVisibleIndex) {
      setState(() {
        _firstVisibleIndex = firstVisible;
        _lastVisibleIndex = lastVisible;
      });
    }
  }
  
  /// Check if an item is visible
  bool _isItemVisible(int index) {
    if (!widget.freezeInvisibleItems) return true;
    
    // Add a buffer to prevent flickering
    const buffer = 5;
    return index >= (_firstVisibleIndex - buffer) && index <= (_lastVisibleIndex + buffer);
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        // Wrap the item with an optimized widget
        return UnifiedOptimizedWidget(
          id: 'list_item_$index',
          freezeWhenInactive: widget.freezeInvisibleItems,
          isActive: _isItemVisible(index),
          useRepaintBoundary: widget.addRepaintBoundaries,
          child: widget.itemBuilder(context, index),
        );
      },
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      padding: widget.padding,
      controller: _scrollController,
      cacheExtent: widget.cacheExtent,
      addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
      addRepaintBoundaries: false, // We use OptimizedWidget instead
      addSemanticIndexes: widget.addSemanticIndexes,
    );
  }
}
