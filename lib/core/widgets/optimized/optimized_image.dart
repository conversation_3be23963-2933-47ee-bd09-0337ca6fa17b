// lib/core/widgets/optimized/optimized_image.dart

import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:octo_image/octo_image.dart';

import 'optimized_widget.dart';

/// مدير ذاكرة التخزين المؤقت المخصص للصور
final _customCacheManager = CacheManager(
  Config(
    'optimizedImageCache',
    stalePeriod: const Duration(days: 14),
    maxNrOfCacheObjects: 200,
    repo: JsonCacheInfoRepository(databaseName: 'optimizedImageCache'),
    fileService: HttpFileService(),
  ),
);

/// صورة محسنة تستخدم تقنيات متقدمة لتحسين الأداء
class OptimizedImage extends StatelessWidget {
  /// مسار أو رابط الصورة
  final String imageUrl;

  /// عرض الصورة
  final double? width;

  /// ارتفاع الصورة
  final double? height;

  /// كيفية ملاءمة الصورة للمساحة المتاحة
  final BoxFit fit;

  /// معرف فريد للصورة
  final String? id;

  /// ما إذا كان يجب تجميد الصورة عندما تكون غير نشطة
  final bool freezeWhenInactive;

  /// ما إذا كانت الصورة نشطة حاليًا
  final bool isActive;

  /// ما إذا كان يجب استخدام التحميل التدريجي
  final bool useProgressiveLoading;

  /// ما إذا كان يجب استخدام التحميل الكسول
  final bool useLazyLoading;

  /// قيمة BlurHash للصورة
  final String? blurHash;

  /// عنصر يعرض أثناء تحميل الصورة
  final Widget? placeholder;

  /// عنصر يعرض في حالة حدوث خطأ
  final Widget? errorWidget;

  /// إنشاء صورة محسنة
  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.id,
    this.freezeWhenInactive = false,
    this.isActive = true,
    this.useProgressiveLoading = true,
    this.useLazyLoading = true,
    this.blurHash,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد نوع الصورة (شبكة، محلية، أصول)
    final Widget imageWidget = _buildImage();

    // تغليف الصورة بعنصر محسن
    return OptimizedWidget(
      id: id ?? 'optimized_image_${imageUrl.hashCode}',
      freezeWhenInactive: freezeWhenInactive,
      isActive: isActive,
      useRepaintBoundary: true,
      child: imageWidget,
    );
  }

  /// بناء عنصر الصورة المناسب
  Widget _buildImage() {
    // تحديد عنصر الخطأ
    final Widget defaultErrorWidget = errorWidget ??
        Container(
          color: Colors.grey[300],
          child: const Center(
            child: Icon(Icons.error_outline, color: Colors.red),
          ),
        );

    // تحديد عنصر التحميل
    final Widget defaultPlaceholder = placeholder ??
        (blurHash != null
            ? BlurHash(hash: blurHash!)
            : Container(color: Colors.grey[200]));

    // صورة من الإنترنت
    if (imageUrl.startsWith('http')) {
      return _buildNetworkImage(defaultPlaceholder, defaultErrorWidget);
    }
    // صورة من الأصول
    else if (imageUrl.startsWith('assets/')) {
      return _buildAssetImage(defaultPlaceholder, defaultErrorWidget);
    }
    // صورة محلية
    else {
      return _buildLocalImage(defaultPlaceholder, defaultErrorWidget);
    }
  }

  /// بناء صورة من الإنترنت
  Widget _buildNetworkImage(
      Widget defaultPlaceholder, Widget defaultErrorWidget) {
    // استخدام OctoImage للتحميل التدريجي
    if (useProgressiveLoading) {
      return OctoImage(
        image: CachedNetworkImageProvider(
          imageUrl,
          cacheManager: _customCacheManager,
          maxWidth: width != null && width!.isFinite ? width!.toInt() : null,
          maxHeight:
              height != null && height!.isFinite ? height!.toInt() : null,
        ),
        width: width,
        height: height,
        fit: fit,
        // Usar solo progressIndicatorBuilder, no ambos
        errorBuilder: (_, __, ___) => defaultErrorWidget,
        fadeInDuration: const Duration(milliseconds: 300),
        fadeOutDuration: const Duration(milliseconds: 300),
        progressIndicatorBuilder: (context, progress) {
          if (progress == null || progress.expectedTotalBytes == null) {
            return defaultPlaceholder;
          }

          return Center(
            child: CircularProgressIndicator(
              value:
                  progress.cumulativeBytesLoaded / progress.expectedTotalBytes!,
              strokeWidth: 2,
              color: Theme.of(context).primaryColor,
            ),
          );
        },
      );
    }

    // استخدام CachedNetworkImage العادي
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: _customCacheManager,
      placeholder: (_, __) => defaultPlaceholder,
      errorWidget: (_, __, ___) => defaultErrorWidget,
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
      memCacheWidth: width != null && width!.isFinite ? width!.toInt() : null,
      memCacheHeight:
          height != null && height!.isFinite ? height!.toInt() : null,
    );
  }

  /// بناء صورة من الأصول
  Widget _buildAssetImage(
      Widget defaultPlaceholder, Widget defaultErrorWidget) {
    return Image.asset(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width != null && width!.isFinite ? width!.toInt() : null,
      cacheHeight: height != null && height!.isFinite ? height!.toInt() : null,
      filterQuality: FilterQuality.medium,
      gaplessPlayback: true,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return frame != null
            ? AnimatedOpacity(
                opacity: 1.0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
                child: child,
              )
            : defaultPlaceholder;
      },
      errorBuilder: (_, __, ___) => defaultErrorWidget,
    );
  }

  /// بناء صورة محلية
  Widget _buildLocalImage(
      Widget defaultPlaceholder, Widget defaultErrorWidget) {
    final file = File(imageUrl);
    if (!file.existsSync()) {
      return defaultErrorWidget;
    }

    return Image.file(
      file,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width != null && width!.isFinite ? width!.toInt() : null,
      cacheHeight: height != null && height!.isFinite ? height!.toInt() : null,
      filterQuality: FilterQuality.medium,
      gaplessPlayback: true,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return frame != null
            ? AnimatedOpacity(
                opacity: 1.0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
                child: child,
              )
            : defaultPlaceholder;
      },
      errorBuilder: (_, __, ___) => defaultErrorWidget,
    );
  }
}
