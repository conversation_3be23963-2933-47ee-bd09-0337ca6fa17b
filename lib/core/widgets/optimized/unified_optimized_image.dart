// lib/core/widgets/optimized/unified_optimized_image.dart

import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:octo_image/octo_image.dart';

import '../../utils/unified_image_cache_manager.dart';
import 'unified_optimized_widget.dart';

/// Unified Optimized Image
///
/// This class combines functionality from:
/// - OptimizedImage
///
/// An optimized image widget that supports network, asset, and local images
/// with advanced features like progressive loading, blur hash, and freezing.
class UnifiedOptimizedImage extends StatelessWidget {
  /// The image URL or path
  final String imageUrl;

  /// The width of the image
  final double? width;

  /// The height of the image
  final double? height;

  /// How to fit the image in the box
  final BoxFit fit;

  /// Unique identifier for the image
  final String? id;

  /// Whether to freeze the image when inactive
  final bool freezeWhenInactive;

  /// Whether the image is currently active
  final bool isActive;

  /// Whether to use progressive loading
  final bool useProgressiveLoading;

  /// Whether to use lazy loading
  final bool useLazyLoading;

  /// The blur hash for the image
  final String? blurHash;

  /// Custom placeholder widget
  final Widget? placeholder;

  /// Custom error widget
  final Widget? errorWidget;

  /// The image cache manager
  static final _imageCacheManager = UnifiedImageCacheManager();

  /// Create an optimized image
  const UnifiedOptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.id,
    this.freezeWhenInactive = false,
    this.isActive = true,
    this.useProgressiveLoading = true,
    this.useLazyLoading = true,
    this.blurHash,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    // Determine the type of image (network, local, asset)
    final Widget imageWidget = _buildImage();

    // Wrap the image with an optimized widget
    return UnifiedOptimizedWidget(
      id: id ?? 'optimized_image_${imageUrl.hashCode}',
      freezeWhenInactive: freezeWhenInactive,
      isActive: isActive,
      useRepaintBoundary: true,
      child: imageWidget,
    );
  }

  /// Build the appropriate image widget
  Widget _buildImage() {
    // Determine the error widget
    final Widget defaultErrorWidget = errorWidget ??
        Container(
          color: Colors.grey[300],
          child: const Center(
            child: Icon(Icons.error_outline, color: Colors.red),
          ),
        );

    // Determine the placeholder widget
    final Widget defaultPlaceholder = placeholder ??
        (blurHash != null
            ? BlurHash(hash: blurHash!)
            : Container(color: Colors.grey[200]));

    // Network image
    if (imageUrl.startsWith('http')) {
      return _buildNetworkImage(defaultPlaceholder, defaultErrorWidget);
    }
    // Asset image
    else if (imageUrl.startsWith('assets/')) {
      return _buildAssetImage(defaultPlaceholder, defaultErrorWidget);
    }
    // Local image
    else {
      return _buildLocalImage(defaultPlaceholder, defaultErrorWidget);
    }
  }

  /// Build a network image
  Widget _buildNetworkImage(
      Widget defaultPlaceholder, Widget defaultErrorWidget) {
    // Use OctoImage for progressive loading
    if (useProgressiveLoading) {
      return OctoImage(
        image: CachedNetworkImageProvider(
          imageUrl,
          cacheManager: _imageCacheManager.cacheManager,
          maxWidth: width?.toInt(),
          maxHeight: height?.toInt(),
        ),
        width: width,
        height: height,
        fit: fit,
        placeholderBuilder: (_) => defaultPlaceholder,
        errorBuilder: (_, __, ___) => defaultErrorWidget,
        fadeInDuration: const Duration(milliseconds: 300),
        fadeOutDuration: const Duration(milliseconds: 300),
        progressIndicatorBuilder: (context, imageChunkEvent) {
          // حساب نسبة التقدم من imageChunkEvent
          final double? progress = imageChunkEvent?.expectedTotalBytes != null
              ? imageChunkEvent!.cumulativeBytesLoaded /
                  imageChunkEvent.expectedTotalBytes!
              : null;

          return Center(
            child: CircularProgressIndicator(
              value: progress,
              strokeWidth: 2,
              color: Theme.of(context).primaryColor,
            ),
          );
        },
      );
    }

    // Use CachedNetworkImage for basic caching
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: _imageCacheManager.cacheManager,
      placeholder: (context, url) => defaultPlaceholder,
      errorWidget: (context, url, error) => defaultErrorWidget,
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
    );
  }

  /// Build an asset image
  Widget _buildAssetImage(
      Widget defaultPlaceholder, Widget defaultErrorWidget) {
    return Image.asset(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      filterQuality: FilterQuality.medium,
      gaplessPlayback: true,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return frame != null
            ? AnimatedOpacity(
                opacity: 1.0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
                child: child,
              )
            : defaultPlaceholder;
      },
      errorBuilder: (_, __, ___) => defaultErrorWidget,
    );
  }

  /// Build a local image
  Widget _buildLocalImage(
      Widget defaultPlaceholder, Widget defaultErrorWidget) {
    final file = File(imageUrl);
    if (!file.existsSync()) {
      return defaultErrorWidget;
    }

    return Image.file(
      file,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      filterQuality: FilterQuality.medium,
      gaplessPlayback: true,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return frame != null
            ? AnimatedOpacity(
                opacity: 1.0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
                child: child,
              )
            : defaultPlaceholder;
      },
      errorBuilder: (_, __, ___) => defaultErrorWidget,
    );
  }
}
