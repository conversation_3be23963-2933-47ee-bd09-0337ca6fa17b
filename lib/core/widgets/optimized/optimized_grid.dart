// lib/core/widgets/optimized/optimized_grid.dart

import 'package:flutter/material.dart';

import 'optimized_widget.dart';

/// شبكة محسنة تستخدم تقنيات متقدمة لتحسين الأداء
class OptimizedGrid extends StatefulWidget {
  /// عدد العناصر في الشبكة
  final int itemCount;

  /// باني العناصر
  final IndexedWidgetBuilder itemBuilder;

  /// عدد الأعمدة
  final int crossAxisCount;

  /// نسبة العرض إلى الارتفاع للعناصر
  final double childAspectRatio;

  /// المسافة بين العناصر أفقيًا
  final double crossAxisSpacing;

  /// المسافة بين العناصر رأسيًا
  final double mainAxisSpacing;

  /// ما إذا كان يجب تقليص الشبكة
  final bool shrinkWrap;

  /// فيزياء التمرير
  final ScrollPhysics? physics;

  /// الحشو حول الشبكة
  final EdgeInsetsGeometry? padding;

  /// متحكم التمرير
  final ScrollController? controller;

  /// ما إذا كان يجب تجميد العناصر غير المرئية
  final bool freezeInvisibleItems;

  /// ما إذا كان يجب استخدام التحميل الكسول
  final bool useLazyLoading;

  /// مدى التخزين المؤقت
  final double cacheExtent;

  /// ما إذا كان يجب إضافة حدود RepaintBoundary
  final bool addRepaintBoundaries;

  /// ما إذا كان يجب إضافة مؤشرات الحفاظ التلقائي
  final bool addAutomaticKeepAlives;

  /// ما إذا كان يجب إضافة فهارس دلالية
  final bool addSemanticIndexes;

  /// إنشاء شبكة محسنة
  const OptimizedGrid({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.crossAxisCount,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 0.0,
    this.mainAxisSpacing = 0.0,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.controller,
    this.freezeInvisibleItems = true,
    this.useLazyLoading = true,
    this.cacheExtent = 500.0,
    this.addRepaintBoundaries = true,
    this.addAutomaticKeepAlives = false,
    this.addSemanticIndexes = false,
  });

  @override
  State<OptimizedGrid> createState() => _OptimizedGridState();
}

class _OptimizedGridState extends State<OptimizedGrid> {
  // مجموعة العناصر المرئية
  final Set<int> _visibleItems = {};

  // متحكم التمرير
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();

    // إضافة مستمع للتمرير لتحديث العناصر المرئية
    if (widget.freezeInvisibleItems) {
      _scrollController.addListener(_updateVisibleItems);
    }
  }

  @override
  void dispose() {
    // إزالة مستمع التمرير إذا كان متحكم التمرير داخليًا
    if (widget.controller == null) {
      _scrollController.dispose();
    } else if (widget.freezeInvisibleItems) {
      _scrollController.removeListener(_updateVisibleItems);
    }
    super.dispose();
  }

  /// تحديث مجموعة العناصر المرئية
  void _updateVisibleItems() {
    // لا يمكن تحديث العناصر المرئية إذا لم يكن هناك سياق
    if (!mounted) return;

    // الحصول على معلومات التمرير
    final ScrollPosition position = _scrollController.position;
    final double viewportStart = position.pixels;
    final double viewportEnd = viewportStart + position.viewportDimension;

    // تقدير متوسط ارتفاع الصف
    final double estimatedRowHeight = 100.0 / widget.childAspectRatio;

    // تقدير عدد العناصر في الصف الواحد
    final int itemsPerRow = widget.crossAxisCount;

    // تقدير الصفوف المرئية
    final int startRow = (viewportStart / estimatedRowHeight).floor();
    final int endRow = (viewportEnd / estimatedRowHeight).ceil();

    // إضافة هامش أمان
    final int safeStartRow =
        (startRow - 2).clamp(0, (widget.itemCount / itemsPerRow).ceil());
    final int safeEndRow =
        (endRow + 2).clamp(0, (widget.itemCount / itemsPerRow).ceil());

    // تحديث مجموعة العناصر المرئية
    final Set<int> newVisibleItems = {};
    for (int row = safeStartRow; row <= safeEndRow; row++) {
      for (int col = 0; col < itemsPerRow; col++) {
        final int index = row * itemsPerRow + col;
        if (index < widget.itemCount) {
          newVisibleItems.add(index);
        }
      }
    }

    // تحديث الحالة إذا تغيرت العناصر المرئية
    bool areEqual = _visibleItems.length == newVisibleItems.length &&
        _visibleItems.toSet().containsAll(newVisibleItems);
    if (!areEqual) {
      setState(() {
        _visibleItems.clear();
        _visibleItems.addAll(newVisibleItems);
      });
    }
  }

  /// التحقق مما إذا كان العنصر مرئيًا
  bool _isItemVisible(int index) {
    if (!widget.freezeInvisibleItems) return true;
    return _visibleItems.contains(index);
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
      ),
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        // تغليف العنصر بعنصر محسن
        return OptimizedWidget(
          id: 'grid_item_$index',
          freezeWhenInactive: widget.freezeInvisibleItems,
          isActive: _isItemVisible(index),
          useRepaintBoundary: widget.addRepaintBoundaries,
          child: widget.itemBuilder(context, index),
        );
      },
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      padding: widget.padding,
      controller: _scrollController,
      cacheExtent: widget.cacheExtent,
      addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
      addRepaintBoundaries: false, // نستخدم OptimizedWidget بدلاً من ذلك
      addSemanticIndexes: widget.addSemanticIndexes,
    );
  }
}
