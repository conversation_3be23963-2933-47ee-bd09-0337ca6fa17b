// lib/core/widgets/optimized/optimized_draggable.dart

import 'package:flutter/material.dart';

import '../../utils/drag_throttler.dart';

/// عنصر قابل للسحب محسن
/// يستخدم تقنيات متقدمة لتحسين أداء عمليات السحب
class OptimizedDraggable extends StatefulWidget {
  /// العنصر المراد جعله قابلاً للسحب
  final Widget child;

  /// الموضع الأولي للعنصر
  final Offset initialOffset;

  /// حجم العنصر
  final Size size;

  /// عرض الحاوية
  final double containerWidth;

  /// ارتفاع الحاوية
  final double containerHeight;

  /// معرف فريد للعنصر
  final String elementId;

  /// دالة يتم استدعاؤها عند تحديث موضع العنصر
  final ValueChanged<Offset>? onDragUpdate;

  /// دالة يتم استدعاؤها عند بدء السحب
  final VoidCallback? onDragStart;

  /// دالة يتم استدعاؤها عند انتهاء السحب
  final VoidCallback? onDragEnd;

  /// ما إذا كان العنصر نشطًا
  final bool isActive;

  /// ما إذا كان العنصر قابلاً للسحب
  final bool isDraggable;

  /// الفاصل الزمني بين تحديثات السحب بالمللي ثانية
  final int throttleInterval;

  /// نسبة الجزء المرئي الأدنى من العنصر
  final double minVisiblePart;

  /// إنشاء عنصر قابل للسحب محسن
  const OptimizedDraggable({
    super.key,
    required this.child,
    required this.initialOffset,
    required this.size,
    required this.containerWidth,
    required this.containerHeight,
    required this.elementId,
    this.onDragUpdate,
    this.onDragStart,
    this.onDragEnd,
    this.isActive = true,
    this.isDraggable = true,
    this.throttleInterval = 16, // ~60fps
    this.minVisiblePart = 0.1,
  });

  @override
  State<OptimizedDraggable> createState() => _OptimizedDraggableState();
}

class _OptimizedDraggableState extends State<OptimizedDraggable> {
  // موضع العنصر الحالي
  late Offset _offset;

  // مخنق السحب
  late DragThrottler _dragThrottler;

  // ملاحظة: يمكن استخدام محسن التحويلات في المستقبل لتحسين الأداء

  // موضع بداية السحب
  Offset? _dragStartOffset;

  @override
  void initState() {
    super.initState();

    // تهيئة موضع العنصر
    _offset = widget.initialOffset;

    // تهيئة مخنق السحب
    _dragThrottler = DragThrottler(
      throttleInterval: widget.throttleInterval,
      onUpdate: _handleDragUpdate,
    );
  }

  @override
  void didUpdateWidget(OptimizedDraggable oldWidget) {
    super.didUpdateWidget(oldWidget);

    // تحديث موضع العنصر إذا تغير الموضع الأولي
    if (widget.initialOffset != oldWidget.initialOffset) {
      _offset = widget.initialOffset;
    }

    // تحديث مخنق السحب إذا تغير الفاصل الزمني
    if (widget.throttleInterval != oldWidget.throttleInterval) {
      _dragThrottler.dispose();
      _dragThrottler = DragThrottler(
        throttleInterval: widget.throttleInterval,
        onUpdate: _handleDragUpdate,
      );
    }
  }

  @override
  void dispose() {
    // التخلص من مخنق السحب
    _dragThrottler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _offset.dx,
      top: _offset.dy,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onPanStart: widget.isDraggable ? _onDragStart : null,
        onPanUpdate: widget.isDraggable ? _onDragUpdate : null,
        onPanEnd: widget.isDraggable ? _onDragEnd : null,
        child: widget.child,
      ),
    );
  }

  /// معالجة بدء السحب
  void _onDragStart(DragStartDetails details) {
    _dragStartOffset = details.globalPosition;

    // استدعاء دالة بدء السحب
    widget.onDragStart?.call();
  }

  /// معالجة تحديث السحب
  void _onDragUpdate(DragUpdateDetails details) {
    if (_dragStartOffset == null) return;

    // استخدام details.delta بدلاً من حساب الفرق يدويًا للحصول على حركة أكثر سلاسة
    final dx = details.delta.dx;
    final dy = details.delta.dy;

    // حساب الموضع الجديد
    double newX = _offset.dx + dx;
    double newY = _offset.dy + dy;

    // تطبيق قيود الحاوية
    newX = _constrainX(newX);
    newY = _constrainY(newY);

    // تحديث الموضع
    final newOffset = Offset(newX, newY);

    // استخدام مخنق السحب لتقليل عدد التحديثات
    _dragThrottler.update(newOffset);
  }

  /// معالجة انتهاء السحب
  void _onDragEnd(DragEndDetails details) {
    _dragStartOffset = null;

    // تنفيذ أي تحديث معلق
    _dragThrottler.end();

    // استدعاء دالة انتهاء السحب
    widget.onDragEnd?.call();
  }

  /// معالجة تحديث السحب
  void _handleDragUpdate(Offset newOffset) {
    setState(() {
      _offset = newOffset;
    });

    // استدعاء دالة تحديث السحب
    widget.onDragUpdate?.call(newOffset);
  }

  /// تقييد قيمة X ضمن حدود الحاوية
  double _constrainX(double x) {
    // نسمح للعناصر بالخروج عن حدود البطاقة بنسبة معينة، لكن نتأكد من أن جزءًا منها يبقى داخل البطاقة
    if (x + widget.size.width * widget.minVisiblePart > widget.containerWidth) {
      return widget.containerWidth - widget.size.width * widget.minVisiblePart;
    }

    // التأكد من أن العنصر لا يخرج بالكامل من الجهة اليسرى
    if (x + widget.size.width * (1 - widget.minVisiblePart) < 0) {
      return -widget.size.width * (1 - widget.minVisiblePart);
    }

    return x;
  }

  /// تقييد قيمة Y ضمن حدود الحاوية
  double _constrainY(double y) {
    // نسمح للعناصر بالخروج عن حدود البطاقة بنسبة معينة، لكن نتأكد من أن جزءًا منها يبقى داخل البطاقة
    if (y + widget.size.height * widget.minVisiblePart >
        widget.containerHeight) {
      return widget.containerHeight -
          widget.size.height * widget.minVisiblePart;
    }

    // التأكد من أن العنصر لا يخرج بالكامل من الجهة العليا
    if (y + widget.size.height * (1 - widget.minVisiblePart) < 0) {
      return -widget.size.height * (1 - widget.minVisiblePart);
    }

    return y;
  }
}
