// lib/core/widgets/optimized/optimized_resizable.dart

import 'package:flutter/material.dart';

import '../../utils/transform_throttler.dart';

/// زاوية تغيير الحجم
enum ResizeHandlePosition {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
}

/// عنصر قابل لتغيير الحجم محسن
/// يستخدم تقنيات متقدمة لتحسين أداء عمليات تغيير الحجم
class OptimizedResizable extends StatefulWidget {
  /// العنصر المراد جعله قابلاً لتغيير الحجم
  final Widget child;

  /// الحجم الأولي للعنصر
  final Size initialSize;

  /// الموضع الأولي للعنصر
  final Offset initialOffset;

  /// معرف فريد للعنصر
  final String elementId;

  /// دالة يتم استدعاؤها عند تحديث حجم العنصر
  final ValueChanged<Size>? onResizeUpdate;

  /// دالة يتم استدعاؤها عند تحديث موضع العنصر
  final ValueChanged<Offset>? onOffsetUpdate;

  /// دالة يتم استدعاؤها عند بدء تغيير الحجم
  final VoidCallback? onResizeStart;

  /// دالة يتم استدعاؤها عند انتهاء تغيير الحجم
  final VoidCallback? onResizeEnd;

  /// الحد الأدنى لعرض العنصر
  final double minWidth;

  /// الحد الأدنى لارتفاع العنصر
  final double minHeight;

  /// الحد الأقصى لعرض العنصر
  final double? maxWidth;

  /// الحد الأقصى لارتفاع العنصر
  final double? maxHeight;

  /// ما إذا كان العنصر نشطًا
  final bool isActive;

  /// ما إذا كان العنصر قابلاً لتغيير الحجم
  final bool isResizable;

  /// ما إذا كان يجب عرض مقابض تغيير الحجم
  final bool showResizeHandles;

  /// الفاصل الزمني بين تحديثات تغيير الحجم بالمللي ثانية
  final int throttleInterval;

  /// حجم مقبض تغيير الحجم
  final double handleSize;

  /// لون مقبض تغيير الحجم
  final Color handleColor;

  /// إنشاء عنصر قابل لتغيير الحجم محسن
  const OptimizedResizable({
    super.key,
    required this.child,
    required this.initialSize,
    required this.initialOffset,
    required this.elementId,
    this.onResizeUpdate,
    this.onOffsetUpdate,
    this.onResizeStart,
    this.onResizeEnd,
    this.minWidth = 20.0,
    this.minHeight = 20.0,
    this.maxWidth,
    this.maxHeight,
    this.isActive = true,
    this.isResizable = true,
    this.showResizeHandles = true,
    this.throttleInterval = 16, // ~60fps
    this.handleSize = 20.0,
    this.handleColor = Colors.blue,
  });

  @override
  State<OptimizedResizable> createState() => _OptimizedResizableState();
}

class _OptimizedResizableState extends State<OptimizedResizable> {
  // حجم العنصر الحالي
  late Size _size;

  // موضع العنصر الحالي
  late Offset _offset;

  // مخنق تغيير الحجم
  late TransformThrottler<Size> _resizeThrottler;

  // مخنق تحديث الموضع
  late TransformThrottler<Offset> _offsetThrottler;

  // ملاحظة: يمكن استخدام محسن التحويلات في المستقبل لتحسين الأداء

  // الحجم الأولي عند بدء تغيير الحجم
  Size? _initialSize;

  // الموضع الأولي عند بدء تغيير الحجم
  Offset? _dragStartOffset;

  // الزاوية المستخدمة لتغيير الحجم
  ResizeHandlePosition? _activeCorner;

  @override
  void initState() {
    super.initState();

    // تهيئة حجم وموضع العنصر
    _size = widget.initialSize;
    _offset = widget.initialOffset;

    // تهيئة مخنقات التحويل
    _resizeThrottler = TransformThrottler<Size>(
      throttleInterval: widget.throttleInterval,
      onUpdate: _handleResizeUpdate,
    );

    _offsetThrottler = TransformThrottler<Offset>(
      throttleInterval: widget.throttleInterval,
      onUpdate: _handleOffsetUpdate,
    );
  }

  @override
  void didUpdateWidget(OptimizedResizable oldWidget) {
    super.didUpdateWidget(oldWidget);

    // تحديث حجم وموضع العنصر إذا تغيرت القيم الأولية
    if (widget.initialSize != oldWidget.initialSize) {
      _size = widget.initialSize;
    }

    if (widget.initialOffset != oldWidget.initialOffset) {
      _offset = widget.initialOffset;
    }

    // تحديث مخنقات التحويل إذا تغير الفاصل الزمني
    if (widget.throttleInterval != oldWidget.throttleInterval) {
      _resizeThrottler.dispose();
      _offsetThrottler.dispose();

      _resizeThrottler = TransformThrottler<Size>(
        throttleInterval: widget.throttleInterval,
        onUpdate: _handleResizeUpdate,
      );

      _offsetThrottler = TransformThrottler<Offset>(
        throttleInterval: widget.throttleInterval,
        onUpdate: _handleOffsetUpdate,
      );
    }
  }

  @override
  void dispose() {
    // التخلص من مخنقات التحويل
    _resizeThrottler.dispose();
    _offsetThrottler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // العنصر الأساسي
        Container(
          width: _size.width,
          height: _size.height,
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.isActive ? widget.handleColor : Colors.transparent,
              width: widget.isActive ? 1.0 : 0.0,
            ),
          ),
          child: widget.child,
        ),

        // مقابض تغيير الحجم
        if (widget.isResizable && widget.showResizeHandles && widget.isActive)
          ..._buildResizeHandles(),
      ],
    );
  }

  /// بناء مقابض تغيير الحجم (الأركان الأربعة فقط)
  List<Widget> _buildResizeHandles() {
    return [
      // الزاوية العلوية اليسرى
      _buildResizeHandle(
        corner: ResizeHandlePosition.topLeft,
        alignment: Alignment.topLeft,
        cursor: SystemMouseCursors.resizeUpLeft,
      ),

      // الزاوية العلوية اليمنى
      _buildResizeHandle(
        corner: ResizeHandlePosition.topRight,
        alignment: Alignment.topRight,
        cursor: SystemMouseCursors.resizeUpRight,
      ),

      // الزاوية السفلية اليسرى
      _buildResizeHandle(
        corner: ResizeHandlePosition.bottomLeft,
        alignment: Alignment.bottomLeft,
        cursor: SystemMouseCursors.resizeDownLeft,
      ),

      // الزاوية السفلية اليمنى
      _buildResizeHandle(
        corner: ResizeHandlePosition.bottomRight,
        alignment: Alignment.bottomRight,
        cursor: SystemMouseCursors.resizeDownRight,
      ),
    ];
  }

  /// بناء مقبض تغيير الحجم
  Widget _buildResizeHandle({
    required ResizeHandlePosition corner,
    required Alignment alignment,
    required MouseCursor cursor,
  }) {
    return Positioned(
      left: alignment.x < 0 ? -widget.handleSize / 2 : null,
      right: alignment.x > 0 ? -widget.handleSize / 2 : null,
      top: alignment.y < 0 ? -widget.handleSize / 2 : null,
      bottom: alignment.y > 0 ? -widget.handleSize / 2 : null,
      child: MouseRegion(
        cursor: cursor,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onPanStart: (details) => _onResizeStart(details, corner),
          onPanUpdate: _onResizeUpdate,
          onPanEnd: _onResizeEnd,
          child: Container(
            width: widget.handleSize,
            height: widget.handleSize,
            decoration: BoxDecoration(
              color: Colors.blue.shade600,
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 2,
              ),
            ),
            child: Icon(
              _getIconForCorner(corner),
              size: widget.handleSize * 0.4,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// معالجة بدء تغيير الحجم
  void _onResizeStart(DragStartDetails details, ResizeHandlePosition corner) {
    _initialSize = _size;
    _dragStartOffset = details.globalPosition;
    _activeCorner = corner;

    // استدعاء دالة بدء تغيير الحجم
    widget.onResizeStart?.call();
  }

  /// معالجة تحديث تغيير الحجم
  void _onResizeUpdate(DragUpdateDetails details) {
    if (_initialSize == null ||
        _dragStartOffset == null ||
        _activeCorner == null) {
      return;
    }

    // حساب التغيير في الموضع
    final dx = details.globalPosition.dx - _dragStartOffset!.dx;
    final dy = details.globalPosition.dy - _dragStartOffset!.dy;

    // حساب الحجم والموضع الجديدين بناءً على الزاوية النشطة
    double newWidth = _initialSize!.width;
    double newHeight = _initialSize!.height;
    double newX = _offset.dx;
    double newY = _offset.dy;

    switch (_activeCorner) {
      case ResizeHandlePosition.topLeft:
        newWidth = _initialSize!.width - dx;
        newHeight = _initialSize!.height - dy;
        newX = _offset.dx + dx;
        newY = _offset.dy + dy;
        break;
      case ResizeHandlePosition.topRight:
        newWidth = _initialSize!.width + dx;
        newHeight = _initialSize!.height - dy;
        newY = _offset.dy + dy;
        break;
      case ResizeHandlePosition.bottomLeft:
        newWidth = _initialSize!.width - dx;
        newHeight = _initialSize!.height + dy;
        newX = _offset.dx + dx;
        break;
      case ResizeHandlePosition.bottomRight:
        newWidth = _initialSize!.width + dx;
        newHeight = _initialSize!.height + dy;
        break;
      default:
        break;
    }

    // تطبيق قيود الحجم
    newWidth = _constrainWidth(newWidth);
    newHeight = _constrainHeight(newHeight);

    // تحديث الحجم والموضع
    final newSize = Size(newWidth, newHeight);
    final newOffset = Offset(newX, newY);

    // استخدام مخنقات التحويل لتقليل عدد التحديثات
    _resizeThrottler.update(newSize);
    _offsetThrottler.update(newOffset);
  }

  /// معالجة انتهاء تغيير الحجم
  void _onResizeEnd(DragEndDetails details) {
    _initialSize = null;
    _dragStartOffset = null;
    _activeCorner = null;

    // تنفيذ أي تحديث معلق
    _resizeThrottler.end();
    _offsetThrottler.end();

    // استدعاء دالة انتهاء تغيير الحجم
    widget.onResizeEnd?.call();
  }

  /// معالجة تحديث الحجم
  void _handleResizeUpdate(Size newSize) {
    setState(() {
      _size = newSize;
    });

    // استدعاء دالة تحديث الحجم
    widget.onResizeUpdate?.call(newSize);
  }

  /// معالجة تحديث الموضع
  void _handleOffsetUpdate(Offset newOffset) {
    setState(() {
      _offset = newOffset;
    });

    // استدعاء دالة تحديث الموضع
    widget.onOffsetUpdate?.call(newOffset);
  }

  /// تقييد العرض ضمن الحدود المسموح بها
  double _constrainWidth(double width) {
    if (width < widget.minWidth) {
      return widget.minWidth;
    }

    if (widget.maxWidth != null && width > widget.maxWidth!) {
      return widget.maxWidth!;
    }

    return width;
  }

  /// تقييد الارتفاع ضمن الحدود المسموح بها
  double _constrainHeight(double height) {
    if (height < widget.minHeight) {
      return widget.minHeight;
    }

    if (widget.maxHeight != null && height > widget.maxHeight!) {
      return widget.maxHeight!;
    }

    return height;
  }

  /// تحديد الأيقونة المناسبة لكل زاوية
  IconData _getIconForCorner(ResizeHandlePosition corner) {
    switch (corner) {
      case ResizeHandlePosition.topLeft:
        return Icons.north_west;
      case ResizeHandlePosition.topRight:
        return Icons.north_east;
      case ResizeHandlePosition.bottomLeft:
        return Icons.south_west;
      case ResizeHandlePosition.bottomRight:
        return Icons.south_east;
    }
  }
}
