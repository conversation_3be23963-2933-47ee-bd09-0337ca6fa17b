// lib/core/widgets/optimized/unified_optimized_grid.dart

import 'package:flutter/material.dart';

import 'unified_optimized_widget.dart';

/// Unified Optimized Grid
///
/// This class combines functionality from:
/// - OptimizedGrid
///
/// An optimized grid widget that supports freezing invisible items
/// and other performance optimizations.
class UnifiedOptimizedGrid extends StatefulWidget {
  /// The number of items in the grid
  final int itemCount;

  /// The builder for the items
  final IndexedWidgetBuilder itemBuilder;

  /// The number of items in the cross axis
  final int crossAxisCount;

  /// The aspect ratio of the items
  final double childAspectRatio;

  /// The spacing between items in the cross axis
  final double crossAxisSpacing;

  /// The spacing between items in the main axis
  final double mainAxisSpacing;

  /// Whether to shrink wrap the grid
  final bool shrinkWrap;

  /// The scroll physics
  final ScrollPhysics? physics;

  /// The padding around the grid
  final EdgeInsetsGeometry? padding;

  /// The scroll controller
  final ScrollController? controller;

  /// Whether to freeze invisible items
  final bool freezeInvisibleItems;

  /// Whether to use lazy loading
  final bool useLazyLoading;

  /// The cache extent
  final double cacheExtent;

  /// Whether to add repaint boundaries
  final bool addRepaintBoundaries;

  /// Whether to add automatic keep alives
  final bool addAutomaticKeepAlives;

  /// Whether to add semantic indexes
  final bool addSemanticIndexes;

  /// Create an optimized grid
  const UnifiedOptimizedGrid({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.crossAxisCount,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 0.0,
    this.mainAxisSpacing = 0.0,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.controller,
    this.freezeInvisibleItems = true,
    this.useLazyLoading = true,
    this.cacheExtent = 500.0,
    this.addRepaintBoundaries = true,
    this.addAutomaticKeepAlives = false,
    this.addSemanticIndexes = false,
  });

  @override
  State<UnifiedOptimizedGrid> createState() => _UnifiedOptimizedGridState();
}

class _UnifiedOptimizedGridState extends State<UnifiedOptimizedGrid> {
  // The scroll controller
  late ScrollController _scrollController;

  // The visible items range
  int _firstVisibleIndex = 0;
  int _lastVisibleIndex = 0;

  @override
  void initState() {
    super.initState();

    // Initialize the scroll controller
    _scrollController = widget.controller ?? ScrollController();

    // Add a listener to update the visible items range
    _scrollController.addListener(_updateVisibleItemsRange);
  }

  @override
  void didUpdateWidget(UnifiedOptimizedGrid oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update the scroll controller if needed
    if (widget.controller != oldWidget.controller) {
      _scrollController.removeListener(_updateVisibleItemsRange);

      _scrollController = widget.controller ?? ScrollController();
      _scrollController.addListener(_updateVisibleItemsRange);
    }
  }

  @override
  void dispose() {
    // Dispose the scroll controller if we created it
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_updateVisibleItemsRange);
    }

    super.dispose();
  }

  /// Update the visible items range
  void _updateVisibleItemsRange() {
    if (!_scrollController.hasClients) return;

    final viewportHeight = _scrollController.position.viewportDimension;
    final scrollOffset = _scrollController.offset;

    // Estimate the item height (assuming all items have the same height)
    // This is a simplification, in a real app you might want to use a more accurate approach
    final estimatedItemHeight = widget.childAspectRatio *
        100.0; // Adjust this based on your actual item height

    // Calculate the number of items per row
    final itemsPerRow = widget.crossAxisCount;

    // Calculate the visible range
    final firstVisibleRow =
        (scrollOffset / (estimatedItemHeight + widget.mainAxisSpacing)).floor();
    final lastVisibleRow = ((scrollOffset + viewportHeight) /
            (estimatedItemHeight + widget.mainAxisSpacing))
        .ceil();

    // Calculate the visible items range
    final firstVisible = firstVisibleRow * itemsPerRow;
    final lastVisible = (lastVisibleRow + 1) * itemsPerRow - 1;

    // Update the visible range if it changed
    if (firstVisible != _firstVisibleIndex ||
        lastVisible != _lastVisibleIndex) {
      setState(() {
        _firstVisibleIndex = firstVisible;
        _lastVisibleIndex = lastVisible;
      });
    }
  }

  /// Check if an item is visible
  bool _isItemVisible(int index) {
    if (!widget.freezeInvisibleItems) return true;

    // Add a buffer to prevent flickering
    final buffer = widget.crossAxisCount * 2;
    return index >= (_firstVisibleIndex - buffer) &&
        index <= (_lastVisibleIndex + buffer);
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
      ),
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        // Wrap the item with an optimized widget
        return UnifiedOptimizedWidget(
          id: 'grid_item_$index',
          freezeWhenInactive: widget.freezeInvisibleItems,
          isActive: _isItemVisible(index),
          useRepaintBoundary: widget.addRepaintBoundaries,
          child: widget.itemBuilder(context, index),
        );
      },
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      padding: widget.padding,
      controller: _scrollController,
      cacheExtent: widget.cacheExtent,
      addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
      addRepaintBoundaries: false, // We use OptimizedWidget instead
      addSemanticIndexes: widget.addSemanticIndexes,
    );
  }
}
