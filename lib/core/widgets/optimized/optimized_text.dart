// lib/core/widgets/optimized/optimized_text.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'optimized_widget.dart';

/// نص محسن يستخدم تقنيات متقدمة لتحسين الأداء
class OptimizedText extends StatelessWidget {
  /// النص المراد عرضه
  final String text;
  
  /// نمط النص
  final TextStyle? style;
  
  /// محاذاة النص
  final TextAlign? textAlign;
  
  /// اتجاه النص
  final TextDirection? textDirection;
  
  /// عدد الأسطر الأقصى
  final int? maxLines;
  
  /// كيفية التعامل مع النص الزائد
  final TextOverflow? overflow;
  
  /// ما إذا كان النص قابلاً للتحديد
  final bool? softWrap;
  
  /// معرف فريد للنص
  final String? id;
  
  /// ما إذا كان يجب تجميد النص عندما يكون غير نشط
  final bool freezeWhenInactive;
  
  /// ما إذا كان النص نشطًا حاليًا
  final bool isActive;
  
  /// اسم الخط
  final String? fontFamily;
  
  /// إنشاء نص محسن
  const OptimizedText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.textDirection,
    this.maxLines,
    this.overflow,
    this.softWrap,
    this.id,
    this.freezeWhenInactive = false,
    this.isActive = true,
    this.fontFamily,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد النص المناسب
    Widget textWidget;
    
    // استخدام Google Fonts إذا تم تحديد اسم الخط
    if (fontFamily != null) {
      textWidget = Text(
        text,
        style: GoogleFonts.getFont(
          fontFamily!,
          textStyle: style,
        ),
        textAlign: textAlign,
        textDirection: textDirection,
        maxLines: maxLines,
        overflow: overflow,
        softWrap: softWrap,
      );
    } else {
      textWidget = Text(
        text,
        style: style,
        textAlign: textAlign,
        textDirection: textDirection,
        maxLines: maxLines,
        overflow: overflow,
        softWrap: softWrap,
      );
    }
    
    // تغليف النص بعنصر محسن
    return OptimizedWidget(
      id: id ?? 'optimized_text_${text.hashCode}',
      freezeWhenInactive: freezeWhenInactive,
      isActive: isActive,
      child: textWidget,
    );
  }
}
