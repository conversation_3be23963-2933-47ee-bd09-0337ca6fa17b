// lib/core/widgets/optimized/unified_optimized_widget.dart

import 'package:flutter/material.dart';

import '../../utils/unified_element_freeze_manager.dart';

/// Unified Optimized Widget
/// 
/// This class combines functionality from:
/// - OptimizedWidget
/// 
/// An optimized widget that uses RepaintBoundary consistently
/// and can be frozen when inactive to improve performance.
class UnifiedOptimizedWidget extends StatefulWidget {
  /// The child widget to optimize
  final Widget child;
  
  /// Unique identifier for the widget
  final String? id;
  
  /// Whether to freeze the widget when inactive
  final bool freezeWhenInactive;
  
  /// Whether the widget is currently active
  final bool isActive;
  
  /// Whether to use RepaintBoundary
  final bool useRepaintBoundary;
  
  /// Callback when the widget is frozen
  final Function(String id)? onFreeze;
  
  /// Callback when the widget is unfrozen
  final Function(String id)? onUnfreeze;
  
  /// Create an optimized widget
  const UnifiedOptimizedWidget({
    super.key,
    required this.child,
    this.id,
    this.freezeWhenInactive = false,
    this.isActive = true,
    this.useRepaintBoundary = true,
    this.onFreeze,
    this.onUnfreeze,
  });

  @override
  State<UnifiedOptimizedWidget> createState() => _UnifiedOptimizedWidgetState();
}

class _UnifiedOptimizedWidgetState extends State<UnifiedOptimizedWidget> {
  // Key to access the widget
  final GlobalKey _repaintKey = GlobalKey();
  
  // Widget ID
  late String _widgetId;
  
  // Previous freeze state
  bool _wasFrozen = false;
  
  // Element freeze manager
  final _freezeManager = UnifiedElementFreezeManager();

  @override
  void initState() {
    super.initState();
    _widgetId = widget.id ?? 'optimized_widget_${identityHashCode(this)}';
    
    // Register the key with the freeze manager
    if (widget.freezeWhenInactive) {
      _freezeManager.registerElementKey(_widgetId, _repaintKey);
    }
  }
  
  @override
  void didUpdateWidget(UnifiedOptimizedWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update ID if changed
    if (widget.id != oldWidget.id) {
      if (oldWidget.freezeWhenInactive) {
        _freezeManager.unregisterElementKey(_widgetId);
      }
      
      _widgetId = widget.id ?? 'optimized_widget_${identityHashCode(this)}';
      
      if (widget.freezeWhenInactive) {
        _freezeManager.registerElementKey(_widgetId, _repaintKey);
      }
    }
    
    // Update freeze state based on active state
    _updateFreezeState();
  }
  
  @override
  void dispose() {
    // Unregister the key when disposing
    if (widget.freezeWhenInactive) {
      _freezeManager.unregisterElementKey(_widgetId);
    }
    super.dispose();
  }
  
  /// Update freeze state based on active state
  void _updateFreezeState() {
    if (!widget.freezeWhenInactive) return;
    
    final isFrozen = _freezeManager.isElementFrozen(_widgetId);
    
    if (!widget.isActive && !isFrozen) {
      // Freeze the widget if inactive and not frozen
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _freezeManager.freezeElement(_widgetId).then((success) {
          if (success && widget.onFreeze != null) {
            widget.onFreeze!(_widgetId);
          }
        });
      });
      _wasFrozen = true;
    } else if (widget.isActive && isFrozen) {
      // Unfreeze the widget if active and frozen
      _freezeManager.unfreezeElement(_widgetId);
      if (widget.onUnfreeze != null) {
        widget.onUnfreeze!(_widgetId);
      }
      _wasFrozen = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Update freeze state
    if (_wasFrozen != !widget.isActive) {
      _updateFreezeState();
    }
    
    // Use RepaintBoundary if required
    if (widget.useRepaintBoundary) {
      return RepaintBoundary(
        key: _repaintKey,
        child: widget.child,
      );
    }
    
    return widget.child;
  }
}
