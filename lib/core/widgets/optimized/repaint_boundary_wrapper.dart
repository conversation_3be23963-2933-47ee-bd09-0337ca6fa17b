import 'package:flutter/material.dart';

/// مغلف RepaintBoundary لتحسين الأداء
/// يستخدم لتحسين أداء العناصر التي لا تتغير كثيرًا
class RepaintBoundaryWrapper extends StatelessWidget {
  /// المفتاح المستخدم للوصول إلى هذا العنصر
  final Key? boundaryKey;

  /// العنصر الذي سيتم تغليفه
  final Widget child;

  /// إنشاء مغلف RepaintBoundary
  const RepaintBoundaryWrapper({
    super.key,
    this.boundaryKey,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: boundaryKey,
      child: child,
    );
  }
}
