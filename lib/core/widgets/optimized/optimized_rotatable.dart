// lib/core/widgets/optimized/optimized_rotatable.dart

import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../../utils/transform_throttler.dart';

/// عنصر قابل للتدوير محسن
/// يستخدم تقنيات متقدمة لتحسين أداء عمليات التدوير
class OptimizedRotatable extends StatefulWidget {
  /// العنصر المراد جعله قابلاً للتدوير
  final Widget child;

  /// زاوية التدوير الأولية بالدرجات
  final double initialAngle;

  /// معرف فريد للعنصر
  final String elementId;

  /// دالة يتم استدعاؤها عند تحديث زاوية التدوير
  final ValueChanged<double>? onRotateUpdate;

  /// دالة يتم استدعاؤها عند بدء التدوير
  final VoidCallback? onRotateStart;

  /// دالة يتم استدعاؤها عند انتهاء التدوير
  final VoidCallback? onRotateEnd;

  /// ما إذا كان العنصر نشطًا
  final bool isActive;

  /// ما إذا كان العنصر قابلاً للتدوير
  final bool isRotatable;

  /// ما إذا كان يجب عرض مقبض التدوير
  final bool showRotateHandle;

  /// الفاصل الزمني بين تحديثات التدوير بالمللي ثانية
  final int throttleInterval;

  /// حجم مقبض التدوير
  final double handleSize;

  /// لون مقبض التدوير
  final Color handleColor;

  /// المسافة بين مقبض التدوير والعنصر
  final double handleDistance;

  /// إنشاء عنصر قابل للتدوير محسن
  const OptimizedRotatable({
    super.key,
    required this.child,
    required this.initialAngle,
    required this.elementId,
    this.onRotateUpdate,
    this.onRotateStart,
    this.onRotateEnd,
    this.isActive = true,
    this.isRotatable = true,
    this.showRotateHandle = true,
    this.throttleInterval = 16, // ~60fps
    this.handleSize = 20.0,
    this.handleColor = Colors.green,
    this.handleDistance = 30.0,
  });

  @override
  State<OptimizedRotatable> createState() => _OptimizedRotatableState();
}

class _OptimizedRotatableState extends State<OptimizedRotatable> {
  // زاوية التدوير الحالية بالدرجات
  late double _angle;

  // مخنق التدوير
  late TransformThrottler<double> _rotateThrottler;

  // ملاحظة: يمكن استخدام محسن التحويلات في المستقبل لتحسين الأداء

  // مركز العنصر
  Offset _center = Offset.zero;

  // موضع بداية التدوير
  Offset? _rotateStartPosition;

  // زاوية بداية التدوير
  double? _rotateStartAngle;

  @override
  void initState() {
    super.initState();

    // تهيئة زاوية التدوير
    _angle = widget.initialAngle;

    // تهيئة مخنق التدوير
    _rotateThrottler = TransformThrottler<double>(
      throttleInterval: widget.throttleInterval,
      onUpdate: _handleRotateUpdate,
    );
  }

  @override
  void didUpdateWidget(OptimizedRotatable oldWidget) {
    super.didUpdateWidget(oldWidget);

    // تحديث زاوية التدوير إذا تغيرت الزاوية الأولية
    if (widget.initialAngle != oldWidget.initialAngle) {
      _angle = widget.initialAngle;
    }

    // تحديث مخنق التدوير إذا تغير الفاصل الزمني
    if (widget.throttleInterval != oldWidget.throttleInterval) {
      _rotateThrottler.dispose();

      _rotateThrottler = TransformThrottler<double>(
        throttleInterval: widget.throttleInterval,
        onUpdate: _handleRotateUpdate,
      );
    }
  }

  @override
  void dispose() {
    // التخلص من مخنق التدوير
    _rotateThrottler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب مركز العنصر
        _center = Offset(
          constraints.maxWidth / 2,
          constraints.maxHeight / 2,
        );

        return Stack(
          clipBehavior: Clip.none,
          children: [
            // العنصر المدور
            Transform.rotate(
              angle: _angle * math.pi / 180,
              child: widget.child,
            ),

            // مقبض التدوير
            if (widget.isRotatable &&
                widget.showRotateHandle &&
                widget.isActive)
              _buildRotateHandle(),
          ],
        );
      },
    );
  }

  /// بناء مقبض التدوير
  Widget _buildRotateHandle() {
    // حساب موضع مقبض التدوير
    final handleAngle = _angle * math.pi / 180;
    final handleX = _center.dx + math.cos(handleAngle) * widget.handleDistance;
    final handleY = _center.dy + math.sin(handleAngle) * widget.handleDistance;

    return Positioned(
      left: handleX - widget.handleSize / 2,
      top: handleY - widget.handleSize / 2,
      child: MouseRegion(
        cursor: SystemMouseCursors.grabbing,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onPanStart: _onRotateStart,
          onPanUpdate: _onRotateUpdate,
          onPanEnd: _onRotateEnd,
          child: Container(
            width: widget.handleSize,
            height: widget.handleSize,
            decoration: BoxDecoration(
              color: widget.handleColor.withAlpha(128),
              shape: BoxShape.circle,
              border: Border.all(
                color: widget.handleColor,
                width: 1.5,
              ),
            ),
            child: const Icon(
              Icons.rotate_right,
              size: 12,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// معالجة بدء التدوير
  void _onRotateStart(DragStartDetails details) {
    _rotateStartPosition = details.globalPosition;
    _rotateStartAngle = _angle;

    // استدعاء دالة بدء التدوير
    widget.onRotateStart?.call();
  }

  /// معالجة تحديث التدوير
  void _onRotateUpdate(DragUpdateDetails details) {
    if (_rotateStartPosition == null || _rotateStartAngle == null) {
      return;
    }

    // حساب الزاوية الجديدة
    final currentPosition = details.globalPosition;

    // حساب الزاوية بين المركز والموضع الحالي
    final startAngle = _calculateAngle(_center, _rotateStartPosition!);
    final currentAngle = _calculateAngle(_center, currentPosition);

    // حساب الفرق بين الزاويتين
    final angleDiff = currentAngle - startAngle;

    // حساب الزاوية الجديدة
    final newAngle = _normalizeAngle(_rotateStartAngle! + angleDiff);

    // استخدام مخنق التدوير لتقليل عدد التحديثات
    _rotateThrottler.update(newAngle);
  }

  /// معالجة انتهاء التدوير
  void _onRotateEnd(DragEndDetails details) {
    _rotateStartPosition = null;
    _rotateStartAngle = null;

    // تنفيذ أي تحديث معلق
    _rotateThrottler.end();

    // استدعاء دالة انتهاء التدوير
    widget.onRotateEnd?.call();
  }

  /// معالجة تحديث زاوية التدوير
  void _handleRotateUpdate(double newAngle) {
    setState(() {
      _angle = newAngle;
    });

    // استدعاء دالة تحديث زاوية التدوير
    widget.onRotateUpdate?.call(newAngle);
  }

  /// حساب الزاوية بين نقطتين بالدرجات
  double _calculateAngle(Offset center, Offset position) {
    final deltaX = position.dx - center.dx;
    final deltaY = position.dy - center.dy;

    // حساب الزاوية بالراديان
    final angleRadians = math.atan2(deltaY, deltaX);

    // تحويل الزاوية إلى درجات
    return angleRadians * 180 / math.pi;
  }

  /// تطبيع الزاوية لتكون بين 0 و 360 درجة
  double _normalizeAngle(double angle) {
    // تطبيع الزاوية لتكون بين 0 و 360 درجة
    double normalizedAngle = angle % 360;
    if (normalizedAngle < 0) {
      normalizedAngle += 360;
    }

    return normalizedAngle;
  }
}
