// lib/core/widgets/optimized/optimized_list.dart

import 'package:flutter/material.dart';

import 'optimized_widget.dart';

/// قائمة محسنة تستخدم تقنيات متقدمة لتحسين الأداء
class OptimizedList extends StatefulWidget {
  /// عدد العناصر في القائمة
  final int itemCount;

  /// باني العناصر
  final IndexedWidgetBuilder itemBuilder;

  /// ما إذا كان يجب تقليص القائمة
  final bool shrinkWrap;

  /// فيزياء التمرير
  final ScrollPhysics? physics;

  /// الحشو حول القائمة
  final EdgeInsetsGeometry? padding;

  /// متحكم التمرير
  final ScrollController? controller;

  /// ما إذا كان يجب تجميد العناصر غير المرئية
  final bool freezeInvisibleItems;

  /// ما إذا كان يجب استخدام التحميل الكسول
  final bool useLazyLoading;

  /// مدى التخزين المؤقت
  final double cacheExtent;

  /// ما إذا كان يجب إضافة حدود RepaintBoundary
  final bool addRepaintBoundaries;

  /// ما إذا كان يجب إضافة مؤشرات الحفاظ التلقائي
  final bool addAutomaticKeepAlives;

  /// ما إذا كان يجب إضافة فهارس دلالية
  final bool addSemanticIndexes;

  /// إنشاء قائمة محسنة
  const OptimizedList({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.controller,
    this.freezeInvisibleItems = true,
    this.useLazyLoading = true,
    this.cacheExtent = 500.0,
    this.addRepaintBoundaries = true,
    this.addAutomaticKeepAlives = false,
    this.addSemanticIndexes = false,
  });

  @override
  State<OptimizedList> createState() => _OptimizedListState();
}

class _OptimizedListState extends State<OptimizedList> {
  // مجموعة العناصر المرئية
  final Set<int> _visibleItems = {};

  // متحكم التمرير
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();

    // إضافة مستمع للتمرير لتحديث العناصر المرئية
    if (widget.freezeInvisibleItems) {
      _scrollController.addListener(_updateVisibleItems);
    }
  }

  @override
  void dispose() {
    // إزالة مستمع التمرير إذا كان متحكم التمرير داخليًا
    if (widget.controller == null) {
      _scrollController.dispose();
    } else if (widget.freezeInvisibleItems) {
      _scrollController.removeListener(_updateVisibleItems);
    }
    super.dispose();
  }

  /// تحديث مجموعة العناصر المرئية
  void _updateVisibleItems() {
    // لا يمكن تحديث العناصر المرئية إذا لم يكن هناك سياق
    if (!mounted) return;

    // الحصول على معلومات التمرير
    final ScrollPosition position = _scrollController.position;
    final double viewportStart = position.pixels;
    final double viewportEnd = viewportStart + position.viewportDimension;

    // تقدير متوسط ارتفاع العنصر
    final double estimatedItemHeight =
        100.0; // يمكن تعديل هذه القيمة حسب الحاجة

    // تقدير العناصر المرئية
    final int startIndex = (viewportStart / estimatedItemHeight).floor();
    final int endIndex = (viewportEnd / estimatedItemHeight).ceil();

    // إضافة هامش أمان
    final int safeStartIndex = (startIndex - 5).clamp(0, widget.itemCount - 1);
    final int safeEndIndex = (endIndex + 5).clamp(0, widget.itemCount - 1);

    // تحديث مجموعة العناصر المرئية
    final Set<int> newVisibleItems = {};
    for (int i = safeStartIndex; i <= safeEndIndex; i++) {
      newVisibleItems.add(i);
    }

    // تحديث الحالة إذا تغيرت العناصر المرئية
    bool areEqual = _visibleItems.length == newVisibleItems.length &&
        _visibleItems.toSet().containsAll(newVisibleItems);
    if (!areEqual) {
      setState(() {
        _visibleItems.clear();
        _visibleItems.addAll(newVisibleItems);
      });
    }
  }

  /// التحقق مما إذا كان العنصر مرئيًا
  bool _isItemVisible(int index) {
    if (!widget.freezeInvisibleItems) return true;
    return _visibleItems.contains(index);
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        // تغليف العنصر بعنصر محسن
        return OptimizedWidget(
          id: 'list_item_$index',
          freezeWhenInactive: widget.freezeInvisibleItems,
          isActive: _isItemVisible(index),
          useRepaintBoundary: widget.addRepaintBoundaries,
          child: widget.itemBuilder(context, index),
        );
      },
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      padding: widget.padding,
      controller: _scrollController,
      cacheExtent: widget.cacheExtent,
      addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
      addRepaintBoundaries: false, // نستخدم OptimizedWidget بدلاً من ذلك
      addSemanticIndexes: widget.addSemanticIndexes,
    );
  }
}
