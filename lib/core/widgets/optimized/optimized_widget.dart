// lib/core/widgets/optimized/optimized_widget.dart

import 'package:flutter/material.dart';

import '../../utils/render_optimization_service.dart';

/// عنصر محسن يستخدم RepaintBoundary بشكل متسق
/// يمكن استخدامه لتحسين أداء أي عنصر في التطبيق
class OptimizedWidget extends StatefulWidget {
  /// العنصر المراد تحسينه
  final Widget child;
  
  /// معرف فريد للعنصر
  final String? id;
  
  /// ما إذا كان يجب تجميد العنصر عندما يكون غير نشط
  final bool freezeWhenInactive;
  
  /// ما إذا كان العنصر نشطًا حاليًا
  final bool isActive;
  
  /// ما إذا كان يجب استخدام RepaintBoundary
  final bool useRepaintBoundary;
  
  /// دالة يتم استدعاؤها عند تجميد العنصر
  final Function(String id)? onFreeze;
  
  /// دالة يتم استدعاؤها عند إلغاء تجميد العنصر
  final Function(String id)? onUnfreeze;
  
  /// إنشاء عنصر محسن
  const OptimizedWidget({
    super.key,
    required this.child,
    this.id,
    this.freezeWhenInactive = false,
    this.isActive = true,
    this.useRepaintBoundary = true,
    this.onFreeze,
    this.onUnfreeze,
  });

  @override
  State<OptimizedWidget> createState() => _OptimizedWidgetState();
}

class _OptimizedWidgetState extends State<OptimizedWidget> {
  // مفتاح للوصول إلى العنصر
  final GlobalKey _repaintKey = GlobalKey();
  
  // معرف العنصر
  late String _widgetId;
  
  // حالة التجميد السابقة
  bool _wasFrozen = false;

  @override
  void initState() {
    super.initState();
    _widgetId = widget.id ?? 'optimized_widget_${identityHashCode(this)}';
    
    // تسجيل المفتاح في خدمة تحسين الرسم
    if (widget.freezeWhenInactive) {
      RenderOptimizationService.registerWidgetKey(_widgetId, _repaintKey);
    }
  }
  
  @override
  void didUpdateWidget(OptimizedWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // تحديث المعرف إذا تغير
    if (widget.id != oldWidget.id) {
      if (oldWidget.freezeWhenInactive) {
        RenderOptimizationService.unregisterWidgetKey(_widgetId);
      }
      
      _widgetId = widget.id ?? 'optimized_widget_${identityHashCode(this)}';
      
      if (widget.freezeWhenInactive) {
        RenderOptimizationService.registerWidgetKey(_widgetId, _repaintKey);
      }
    }
    
    // تحديث حالة التجميد بناءً على حالة النشاط
    _updateFreezeState();
  }
  
  @override
  void dispose() {
    // إلغاء تسجيل المفتاح عند التخلص من العنصر
    if (widget.freezeWhenInactive) {
      RenderOptimizationService.unregisterWidgetKey(_widgetId);
      RenderOptimizationService.unfreezeWidget(_widgetId);
    }
    super.dispose();
  }
  
  /// تحديث حالة التجميد بناءً على حالة النشاط
  void _updateFreezeState() {
    if (!widget.freezeWhenInactive) return;
    
    final isFrozen = RenderOptimizationService.isWidgetFrozen(_widgetId);
    
    if (!widget.isActive && !isFrozen) {
      // تجميد العنصر إذا كان غير نشط وغير مجمد
      WidgetsBinding.instance.addPostFrameCallback((_) {
        RenderOptimizationService.freezeWidget(_widgetId).then((success) {
          if (success && widget.onFreeze != null) {
            widget.onFreeze!(_widgetId);
          }
        });
      });
      _wasFrozen = true;
    } else if (widget.isActive && isFrozen) {
      // إلغاء تجميد العنصر إذا كان نشطًا ومجمدًا
      RenderOptimizationService.unfreezeWidget(_widgetId);
      if (widget.onUnfreeze != null) {
        widget.onUnfreeze!(_widgetId);
      }
      _wasFrozen = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحديث حالة التجميد
    if (_wasFrozen != !widget.isActive) {
      _updateFreezeState();
    }
    
    // استخدام RepaintBoundary إذا كان مطلوبًا
    if (widget.useRepaintBoundary) {
      return RepaintBoundary(
        key: _repaintKey,
        child: widget.child,
      );
    }
    
    return widget.child;
  }
}
