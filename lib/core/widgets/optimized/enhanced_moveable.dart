// lib/core/widgets/optimized/enhanced_moveable.dart

import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../../utils/drag_throttler.dart';
import '../../utils/transform_cache.dart';
import '../../utils/transform_optimizer.dart';
import '../../utils/transform_throttler.dart';
import 'optimized_widget.dart';

/// عنصر قابل للتحريك محسن
/// يجمع بين السحب وتغيير الحجم والتدوير في عنصر واحد
class EnhancedMoveable extends StatefulWidget {
  /// العنصر المراد جعله قابلاً للتحريك
  final Widget child;

  /// الموضع الأولي للعنصر
  final Offset offset;

  /// الحجم الأولي للعنصر
  final Size size;

  /// زاوية التدوير الأولية بالدرجات
  final double rotationAngle;

  /// معامل المقياس الأولي
  final double scale;

  /// عرض الحاوية
  final double containerWidth;

  /// ارتفاع الحاوية
  final double containerHeight;

  /// معرف فريد للعنصر
  final String elementId;

  /// دالة يتم استدعاؤها عند تحديث موضع العنصر
  final ValueChanged<Offset>? onDragUpdate;

  /// دالة يتم استدعاؤها عند تحديث حجم العنصر
  final ValueChanged<Size>? onResizeUpdate;

  /// دالة يتم استدعاؤها عند تحديث زاوية التدوير
  final ValueChanged<double>? onRotateUpdate;

  /// دالة يتم استدعاؤها عند تحديث معامل المقياس
  final ValueChanged<double>? onScaleUpdate;

  /// ما إذا كان العنصر نشطًا
  final bool isActive;

  /// ما إذا كان العنصر قابلاً للسحب
  final bool withDragging;

  /// ما إذا كان العنصر قابلاً لتغيير الحجم
  final bool withResizing;

  /// ما إذا كان العنصر قابلاً للتدوير
  final bool withRotating;

  /// ما إذا كان يجب عرض مقابض تغيير الحجم
  final bool showResizeHandles;

  /// ما إذا كان يجب عرض مقبض التدوير
  final bool showRotateHandle;

  /// الحد الأدنى لعرض العنصر
  final double minWidth;

  /// الحد الأدنى لارتفاع العنصر
  final double minHeight;

  /// الحد الأقصى لعرض العنصر
  final double? maxWidth;

  /// الحد الأقصى لارتفاع العنصر
  final double? maxHeight;

  /// الفاصل الزمني بين التحديثات بالمللي ثانية
  final int throttleInterval;

  /// نسبة الجزء المرئي الأدنى من العنصر
  final double minVisiblePart;

  /// إنشاء عنصر قابل للتحريك محسن
  const EnhancedMoveable({
    super.key,
    required this.child,
    required this.offset,
    required this.size,
    required this.rotationAngle,
    required this.containerWidth,
    required this.containerHeight,
    required this.elementId,
    this.scale = 1.0,
    this.onDragUpdate,
    this.onResizeUpdate,
    this.onRotateUpdate,
    this.onScaleUpdate,
    this.isActive = false,
    this.withDragging = true,
    this.withResizing = true,
    this.withRotating = true,
    this.showResizeHandles = true,
    this.showRotateHandle = true,
    this.minWidth = 20.0,
    this.minHeight = 20.0,
    this.maxWidth,
    this.maxHeight,
    this.throttleInterval = 16, // ~60fps
    this.minVisiblePart = 0.1,
  });

  @override
  State<EnhancedMoveable> createState() => _EnhancedMoveableState();
}

class _EnhancedMoveableState extends State<EnhancedMoveable> {
  // موضع العنصر الحالي
  late Offset _offset;

  // حجم العنصر الحالي
  late Size _size;

  // زاوية التدوير الحالية بالدرجات
  late double _rotationAngle;

  // معامل المقياس الحالي
  late double _scale;

  // مخنق السحب
  late DragThrottler _dragThrottler;

  // مخنق تغيير الحجم
  late TransformThrottler<Size> _resizeThrottler;

  // مخنق التدوير
  late TransformThrottler<double> _rotateThrottler;

  // مخنق تغيير المقياس
  late TransformThrottler<double> _scaleThrottler;

  // محسن التحويلات
  final _transformOptimizer = TransformOptimizer();

  // ذاكرة التخزين المؤقت للتحويلات
  final _transformCache = TransformCache();

  // مفتاح للوصول إلى العنصر
  final GlobalKey _containerKey = GlobalKey();

  // موضع بداية السحب
  Offset? _dragStartOffset;

  // الحجم الأولي عند بدء تغيير الحجم
  Size? _initialSize;

  // موضع بداية التدوير
  Offset? _rotateStartPosition;

  // زاوية بداية التدوير
  double? _rotateStartAngle;

  // الزاوية المستخدمة لتغيير الحجم
  ResizeCorner? _activeCorner;

  // مركز العنصر
  Offset _center = Offset.zero;

  @override
  void initState() {
    super.initState();

    // تهيئة موضع وحجم وزاوية تدوير ومقياس العنصر
    _offset = widget.offset;
    _size = widget.size;
    _rotationAngle = widget.rotationAngle;
    _scale = widget.scale;

    // تهيئة مخنقات التحويل
    _dragThrottler = DragThrottler(
      throttleInterval: widget.throttleInterval,
      onUpdate: _handleDragUpdate,
    );

    _resizeThrottler = TransformThrottler<Size>(
      throttleInterval: widget.throttleInterval,
      onUpdate: _handleResizeUpdate,
    );

    _rotateThrottler = TransformThrottler<double>(
      throttleInterval: widget.throttleInterval,
      onUpdate: _handleRotateUpdate,
    );

    _scaleThrottler = TransformThrottler<double>(
      throttleInterval: widget.throttleInterval,
      onUpdate: _handleScaleUpdate,
    );

    // حساب مركز العنصر
    _updateCenter();
  }

  @override
  void didUpdateWidget(EnhancedMoveable oldWidget) {
    super.didUpdateWidget(oldWidget);

    // تحديث موضع وحجم وزاوية تدوير ومقياس العنصر إذا تغيرت القيم الأولية
    if (widget.offset != oldWidget.offset) {
      _offset = widget.offset;
    }

    if (widget.size != oldWidget.size) {
      _size = widget.size;
    }

    if (widget.rotationAngle != oldWidget.rotationAngle) {
      _rotationAngle = widget.rotationAngle;
    }

    if (widget.scale != oldWidget.scale) {
      _scale = widget.scale;
    }

    // تحديث مخنقات التحويل إذا تغير الفاصل الزمني
    if (widget.throttleInterval != oldWidget.throttleInterval) {
      _dragThrottler.dispose();
      _resizeThrottler.dispose();
      _rotateThrottler.dispose();
      _scaleThrottler.dispose();

      _dragThrottler = DragThrottler(
        throttleInterval: widget.throttleInterval,
        onUpdate: _handleDragUpdate,
      );

      _resizeThrottler = TransformThrottler<Size>(
        throttleInterval: widget.throttleInterval,
        onUpdate: _handleResizeUpdate,
      );

      _rotateThrottler = TransformThrottler<double>(
        throttleInterval: widget.throttleInterval,
        onUpdate: _handleRotateUpdate,
      );

      _scaleThrottler = TransformThrottler<double>(
        throttleInterval: widget.throttleInterval,
        onUpdate: _handleScaleUpdate,
      );
    }

    // تحديث مركز العنصر
    _updateCenter();
  }

  @override
  void dispose() {
    // التخلص من مخنقات التحويل
    _dragThrottler.dispose();
    _resizeThrottler.dispose();
    _rotateThrottler.dispose();
    _scaleThrottler.dispose();

    // تنظيف ذاكرة التخزين المؤقت للتحويلات
    _transformCache.clearCacheForElement(widget.elementId);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // حساب مصفوفة التحويل
    final transformMatrix = _transformOptimizer.calculateTransformMatrix(
      elementId: widget.elementId,
      offset: _offset,
      size: _size,
      angle: _rotationAngle,
      scale: _scale,
    );

    // بناء العنصر المحول
    Widget transformedContent = Transform(
      transform: transformMatrix,
      alignment: Alignment.center,
      child: _buildContent(),
    );

    // تطبيق قيود الحاوية
    final safeX = _constrainX(_offset.dx);
    final safeY = _constrainY(_offset.dy);

    return Positioned(
      left: safeX,
      top: safeY,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onPanStart: widget.withDragging ? _onDragStart : null,
        onPanUpdate: widget.withDragging ? _onDragUpdate : null,
        onPanEnd: widget.withDragging ? _onDragEnd : null,
        child: OptimizedWidget(
          id: 'moveable_${widget.elementId}',
          freezeWhenInactive: true,
          isActive: widget.isActive,
          child: transformedContent,
        ),
      ),
    );
  }

  /// بناء محتوى العنصر
  Widget _buildContent() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // العنصر الأساسي
        RepaintBoundary(
          key: _containerKey,
          child: Container(
            width: _size.width,
            height: _size.height,
            decoration: BoxDecoration(
              border: Border.all(
                color: widget.isActive ? Colors.blue : Colors.transparent,
                width: widget.isActive ? 2.0 : 0.0,
              ),
            ),
            child: widget.child,
          ),
        ),

        // مقابض تغيير الحجم
        if (widget.withResizing && widget.showResizeHandles && widget.isActive)
          ..._buildResizeHandles(),

        // مقبض التدوير
        if (widget.withRotating && widget.showRotateHandle && widget.isActive)
          _buildRotateHandle(),
      ],
    );
  }

  /// بناء مقابض تغيير الحجم (الأركان الأربعة فقط)
  List<Widget> _buildResizeHandles() {
    return [
      // الزاوية العلوية اليسرى
      _buildResizeHandle(
        corner: ResizeCorner.topLeft,
        alignment: Alignment.topLeft,
        cursor: SystemMouseCursors.resizeUpLeft,
      ),

      // الزاوية العلوية اليمنى
      _buildResizeHandle(
        corner: ResizeCorner.topRight,
        alignment: Alignment.topRight,
        cursor: SystemMouseCursors.resizeUpRight,
      ),

      // الزاوية السفلية اليسرى
      _buildResizeHandle(
        corner: ResizeCorner.bottomLeft,
        alignment: Alignment.bottomLeft,
        cursor: SystemMouseCursors.resizeDownLeft,
      ),

      // الزاوية السفلية اليمنى
      _buildResizeHandle(
        corner: ResizeCorner.bottomRight,
        alignment: Alignment.bottomRight,
        cursor: SystemMouseCursors.resizeDownRight,
      ),
    ];
  }

  /// بناء مقبض تغيير الحجم
  Widget _buildResizeHandle({
    required ResizeCorner corner,
    required Alignment alignment,
    required MouseCursor cursor,
  }) {
    const handleSize = 20.0;

    return Positioned(
      left: alignment.x < 0 ? -handleSize / 2 : null,
      right: alignment.x > 0 ? -handleSize / 2 : null,
      top: alignment.y < 0 ? -handleSize / 2 : null,
      bottom: alignment.y > 0 ? -handleSize / 2 : null,
      child: MouseRegion(
        cursor: cursor,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onPanStart: (details) => _onResizeStart(details, corner),
          onPanUpdate: _onResizeUpdate,
          onPanEnd: _onResizeEnd,
          child: Container(
            width: handleSize,
            height: handleSize,
            decoration: BoxDecoration(
              color: Colors.blue.shade600,
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 2,
              ),
            ),
            child: Icon(
              _getIconForCorner(corner),
              size: handleSize * 0.4,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء مقبض التدوير
  Widget _buildRotateHandle() {
    const handleSize = 20.0;
    const handleDistance = 30.0;

    // حساب موضع مقبض التدوير
    final handleAngle = _rotationAngle * math.pi / 180;
    final handleX = _size.width / 2 + math.cos(handleAngle) * handleDistance;
    final handleY = _size.height / 2 + math.sin(handleAngle) * handleDistance;

    return Positioned(
      left: handleX - handleSize / 2,
      top: handleY - handleSize / 2,
      child: MouseRegion(
        cursor: SystemMouseCursors.grabbing,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onPanStart: _onRotateStart,
          onPanUpdate: _onRotateUpdate,
          onPanEnd: _onRotateEnd,
          child: Container(
            width: handleSize,
            height: handleSize,
            decoration: BoxDecoration(
              color: Colors.green.withAlpha(128), // 0.5 * 255 = 128
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.green,
                width: 1.5,
              ),
            ),
            child: const Icon(
              Icons.rotate_right,
              size: 12,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// معالجة بدء السحب
  void _onDragStart(DragStartDetails details) {
    _dragStartOffset = details.globalPosition;
  }

  /// معالجة تحديث السحب
  void _onDragUpdate(DragUpdateDetails details) {
    if (_dragStartOffset == null) return;

    // استخدام details.delta بدلاً من حساب الفرق يدويًا للحصول على حركة أكثر سلاسة
    final dx = details.delta.dx;
    final dy = details.delta.dy;

    // حساب الموضع الجديد
    double newX = _offset.dx + dx;
    double newY = _offset.dy + dy;

    // تطبيق قيود الحاوية
    newX = _constrainX(newX);
    newY = _constrainY(newY);

    // تحديث الموضع
    final newOffset = Offset(newX, newY);

    // استخدام مخنق السحب لتقليل عدد التحديثات
    _dragThrottler.update(newOffset);
  }

  /// معالجة انتهاء السحب
  void _onDragEnd(DragEndDetails details) {
    _dragStartOffset = null;

    // تنفيذ أي تحديث معلق
    _dragThrottler.end();
  }

  /// معالجة بدء تغيير الحجم
  void _onResizeStart(DragStartDetails details, ResizeCorner corner) {
    _initialSize = _size;
    _dragStartOffset = details.globalPosition;
    _activeCorner = corner;
  }

  /// معالجة تحديث تغيير الحجم
  void _onResizeUpdate(DragUpdateDetails details) {
    if (_initialSize == null ||
        _dragStartOffset == null ||
        _activeCorner == null) {
      return;
    }

    // حساب التغيير في الموضع
    final dx = details.globalPosition.dx - _dragStartOffset!.dx;
    final dy = details.globalPosition.dy - _dragStartOffset!.dy;

    // حساب الحجم والموضع الجديدين بناءً على الزاوية النشطة
    double newWidth = _initialSize!.width;
    double newHeight = _initialSize!.height;
    double newX = _offset.dx;
    double newY = _offset.dy;

    switch (_activeCorner) {
      case ResizeCorner.topLeft:
        newWidth = _initialSize!.width - dx;
        newHeight = _initialSize!.height - dy;
        newX = _offset.dx + dx;
        newY = _offset.dy + dy;
        break;
      case ResizeCorner.topRight:
        newWidth = _initialSize!.width + dx;
        newHeight = _initialSize!.height - dy;
        newY = _offset.dy + dy;
        break;
      case ResizeCorner.bottomLeft:
        newWidth = _initialSize!.width - dx;
        newHeight = _initialSize!.height + dy;
        newX = _offset.dx + dx;
        break;
      case ResizeCorner.bottomRight:
        newWidth = _initialSize!.width + dx;
        newHeight = _initialSize!.height + dy;
        break;
      default:
        break;
    }

    // تطبيق قيود الحجم
    newWidth = _constrainWidth(newWidth);
    newHeight = _constrainHeight(newHeight);

    // تحديث الحجم والموضع
    final newSize = Size(newWidth, newHeight);
    final newOffset = Offset(newX, newY);

    // استخدام مخنقات التحويل لتقليل عدد التحديثات
    _resizeThrottler.update(newSize);
    _dragThrottler.update(newOffset);
  }

  /// معالجة انتهاء تغيير الحجم
  void _onResizeEnd(DragEndDetails details) {
    _initialSize = null;
    _dragStartOffset = null;
    _activeCorner = null;

    // تنفيذ أي تحديث معلق
    _resizeThrottler.end();
    _dragThrottler.end();
  }

  /// معالجة بدء التدوير
  void _onRotateStart(DragStartDetails details) {
    _rotateStartPosition = details.globalPosition;
    _rotateStartAngle = _rotationAngle;

    // تحديث مركز العنصر
    _updateCenter();
  }

  /// معالجة تحديث التدوير
  void _onRotateUpdate(DragUpdateDetails details) {
    if (_rotateStartPosition == null || _rotateStartAngle == null) {
      return;
    }

    // حساب الزاوية الجديدة
    final currentPosition = details.globalPosition;

    // حساب الزاوية بين المركز والموضع الحالي
    final startAngle = _calculateAngle(_center, _rotateStartPosition!);
    final currentAngle = _calculateAngle(_center, currentPosition);

    // حساب الفرق بين الزاويتين
    final angleDiff = currentAngle - startAngle;

    // حساب الزاوية الجديدة
    final newAngle = _normalizeAngle(_rotateStartAngle! + angleDiff);

    // استخدام مخنق التدوير لتقليل عدد التحديثات
    _rotateThrottler.update(newAngle);
  }

  /// معالجة انتهاء التدوير
  void _onRotateEnd(DragEndDetails details) {
    _rotateStartPosition = null;
    _rotateStartAngle = null;

    // تنفيذ أي تحديث معلق
    _rotateThrottler.end();
  }

  /// معالجة تحديث السحب
  void _handleDragUpdate(Offset newOffset) {
    setState(() {
      _offset = newOffset;
    });

    // تحديث مركز العنصر
    _updateCenter();

    // استدعاء دالة تحديث السحب
    widget.onDragUpdate?.call(newOffset);
  }

  /// معالجة تحديث الحجم
  void _handleResizeUpdate(Size newSize) {
    setState(() {
      _size = newSize;
    });

    // تحديث مركز العنصر
    _updateCenter();

    // استدعاء دالة تحديث الحجم
    widget.onResizeUpdate?.call(newSize);
  }

  /// معالجة تحديث زاوية التدوير
  void _handleRotateUpdate(double newAngle) {
    setState(() {
      _rotationAngle = newAngle;
    });

    // استدعاء دالة تحديث زاوية التدوير
    widget.onRotateUpdate?.call(newAngle);
  }

  /// معالجة تحديث معامل المقياس
  void _handleScaleUpdate(double newScale) {
    setState(() {
      _scale = newScale;
    });

    // استدعاء دالة تحديث معامل المقياس
    widget.onScaleUpdate?.call(newScale);
  }

  /// تحديث مركز العنصر
  void _updateCenter() {
    _center = Offset(
      _offset.dx + _size.width / 2,
      _offset.dy + _size.height / 2,
    );
  }

  /// تقييد قيمة X ضمن حدود الحاوية
  double _constrainX(double x) {
    // نسمح للعناصر بالخروج عن حدود البطاقة بنسبة معينة، لكن نتأكد من أن جزءًا منها يبقى داخل البطاقة
    if (x + _size.width * widget.minVisiblePart > widget.containerWidth) {
      return widget.containerWidth - _size.width * widget.minVisiblePart;
    }

    // التأكد من أن العنصر لا يخرج بالكامل من الجهة اليسرى
    if (x + _size.width * (1 - widget.minVisiblePart) < 0) {
      return -_size.width * (1 - widget.minVisiblePart);
    }

    return x;
  }

  /// تقييد قيمة Y ضمن حدود الحاوية
  double _constrainY(double y) {
    // نسمح للعناصر بالخروج عن حدود البطاقة بنسبة معينة، لكن نتأكد من أن جزءًا منها يبقى داخل البطاقة
    if (y + _size.height * widget.minVisiblePart > widget.containerHeight) {
      return widget.containerHeight - _size.height * widget.minVisiblePart;
    }

    // التأكد من أن العنصر لا يخرج بالكامل من الجهة العليا
    if (y + _size.height * (1 - widget.minVisiblePart) < 0) {
      return -_size.height * (1 - widget.minVisiblePart);
    }

    return y;
  }

  /// تقييد العرض ضمن الحدود المسموح بها
  double _constrainWidth(double width) {
    if (width < widget.minWidth) {
      return widget.minWidth;
    }

    if (widget.maxWidth != null && width > widget.maxWidth!) {
      return widget.maxWidth!;
    }

    return width;
  }

  /// تقييد الارتفاع ضمن الحدود المسموح بها
  double _constrainHeight(double height) {
    if (height < widget.minHeight) {
      return widget.minHeight;
    }

    if (widget.maxHeight != null && height > widget.maxHeight!) {
      return widget.maxHeight!;
    }

    return height;
  }

  /// حساب الزاوية بين نقطتين بالدرجات
  double _calculateAngle(Offset center, Offset position) {
    final deltaX = position.dx - center.dx;
    final deltaY = position.dy - center.dy;

    // حساب الزاوية بالراديان
    final angleRadians = math.atan2(deltaY, deltaX);

    // تحويل الزاوية إلى درجات
    return angleRadians * 180 / math.pi;
  }

  /// تطبيع الزاوية لتكون بين 0 و 360 درجة
  double _normalizeAngle(double angle) {
    // تطبيع الزاوية لتكون بين 0 و 360 درجة
    double normalizedAngle = angle % 360;
    if (normalizedAngle < 0) {
      normalizedAngle += 360;
    }

    return normalizedAngle;
  }

  /// تحديد الأيقونة المناسبة لكل زاوية
  IconData _getIconForCorner(ResizeCorner corner) {
    switch (corner) {
      case ResizeCorner.topLeft:
        return Icons.north_west;
      case ResizeCorner.topRight:
        return Icons.north_east;
      case ResizeCorner.bottomLeft:
        return Icons.south_west;
      case ResizeCorner.bottomRight:
        return Icons.south_east;
    }
  }
}

/// زاوية تغيير الحجم
enum ResizeCorner {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
}
