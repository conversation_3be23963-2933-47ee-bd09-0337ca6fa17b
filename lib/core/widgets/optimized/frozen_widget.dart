// lib/core/widgets/optimized/frozen_widget.dart

import 'dart:ui' as ui;
import 'package:flutter/material.dart';

import '../../utils/render_optimization_service.dart';

/// عنصر مجمد يعرض صورة ثابتة للعنصر الأصلي
/// يستخدم لتحسين الأداء عن طريق تقليل إعادة الرسم
class FrozenWidget extends StatelessWidget {
  /// معرف العنصر المجمد
  final String widgetId;
  
  /// العنصر الاحتياطي في حالة عدم وجود صورة مجمدة
  final Widget fallbackChild;
  
  /// حجم العنصر
  final Size size;
  
  /// دالة يتم استدعاؤها عند النقر على العنصر
  final VoidCallback? onTap;
  
  /// إنشاء عنصر مجمد
  const FrozenWidget({
    super.key,
    required this.widgetId,
    required this.fallbackChild,
    required this.size,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // التحقق مما إذا كان العنصر مجمدًا
    final isFrozen = RenderOptimizationService.isWidgetFrozen(widgetId);
    
    // الحصول على صورة العنصر المجمد
    final frozenImage = isFrozen
        ? RenderOptimizationService.getFrozenWidgetImage(widgetId)
        : null;
    
    // إذا لم يكن العنصر مجمدًا أو لم تكن هناك صورة، عرض العنصر الاحتياطي
    if (!isFrozen || frozenImage == null) {
      return fallbackChild;
    }
    
    // عرض صورة العنصر المجمد
    return GestureDetector(
      onTap: onTap,
      child: CustomPaint(
        size: size,
        painter: _FrozenImagePainter(frozenImage),
      ),
    );
  }
}

/// رسام لعرض صورة العنصر المجمد
class _FrozenImagePainter extends CustomPainter {
  /// صورة العنصر المجمد
  final ui.Image image;
  
  /// إنشاء رسام لصورة مجمدة
  _FrozenImagePainter(this.image);

  @override
  void paint(Canvas canvas, Size size) {
    // رسم الصورة بحجم العنصر
    paintImage(
      canvas: canvas,
      rect: Offset.zero & size,
      image: image,
      fit: BoxFit.fill,
    );
  }

  @override
  bool shouldRepaint(_FrozenImagePainter oldDelegate) {
    return image != oldDelegate.image;
  }
}
