import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// ويدجت لتتبع تغييرات الحجم بكفاءة
/// يستخدم لتحسين أداء العناصر التي تتغير أحجامها
class SizeChangedLayoutNotifier extends SingleChildRenderObjectWidget {
  /// دالة يتم استدعاؤها عند تغيير الحجم
  final ValueChanged<Size> onSizeChanged;

  const SizeChangedLayoutNotifier({
    super.key,
    required this.onSizeChanged,
    required Widget child,
  }) : super(child: child);

  @override
  RenderObject createRenderObject(BuildContext context) {
    return _SizeChangedRenderBox(onSizeChanged: onSizeChanged);
  }

  @override
  void updateRenderObject(
      BuildContext context, _SizeChangedRenderBox renderObject) {
    renderObject.onSizeChanged = onSizeChanged;
  }
}

/// صندوق عرض مخصص لتتبع تغييرات الحجم
class _SizeChangedRenderBox extends RenderProxyBox {
  _SizeChangedRenderBox({
    RenderBox? child,
    required this.onSizeChanged,
  }) : super(child);

  /// دالة يتم استدعاؤها عند تغيير الحجم
  ValueChanged<Size> onSizeChanged;

  /// الحجم السابق
  Size? _oldSize;

  @override
  void performLayout() {
    super.performLayout();
    
    // التحقق مما إذا كان الحجم قد تغير
    if (_oldSize == null || _oldSize != size) {
      _oldSize = size;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onSizeChanged(size);
      });
    }
  }
}

/// امتداد لـ BuildContext لتسهيل استخدام SizeChangedLayoutNotifier
extension SizeChangedLayoutNotifierExtension on BuildContext {
  /// إضافة مستمع لتغييرات الحجم
  Widget withSizeChangedCallback(
    Widget child,
    ValueChanged<Size> onSizeChanged,
  ) {
    return SizeChangedLayoutNotifier(
      onSizeChanged: onSizeChanged,
      child: child,
    );
  }
}
