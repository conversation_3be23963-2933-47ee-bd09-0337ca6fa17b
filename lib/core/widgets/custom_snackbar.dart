import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants/app_colors.dart';

/// نوع الإشعار الذي يحدد لون وأيقونة الإشعار
enum NotificationType {
  /// إشعار نجاح (أخضر)
  success,

  /// إشعار خطأ (أحمر)
  error,

  /// إشعار تحذير (برتقالي)
  warning,

  /// إشعار معلومات (أزرق)
  info,
}

/// عرض إشعار مخصص جميل في أسفل الشاشة
class CustomSnackbar {
  /// عرض إشعار مخصص
  ///
  /// [context] سياق البناء
  /// [message] نص الرسالة
  /// [type] نوع الإشعار
  /// [duration] مدة ظهور الإشعار
  static void show({
    required BuildContext context,
    required String message,
    NotificationType type = NotificationType.info,
    Duration duration = const Duration(seconds: 3),
  }) {
    // إلغاء أي إشعار سابق
    ScaffoldMessenger.of(context).hideCurrentSnackBar();

    // تحديد لون وأيقونة الإشعار بناءً على النوع
    final Color backgroundColor;
    final Color iconColor;
    final IconData icon;

    switch (type) {
      case NotificationType.success:
        backgroundColor = Colors.green.shade800;
        iconColor = Colors.white;
        icon = Icons.check_circle;
        break;
      case NotificationType.error:
        backgroundColor = Colors.red.shade800;
        iconColor = Colors.white;
        icon = Icons.error;
        break;
      case NotificationType.warning:
        backgroundColor = Colors.orange.shade800;
        iconColor = Colors.white;
        icon = Icons.warning;
        break;
      case NotificationType.info:
        backgroundColor = AppColors.primaryColor;
        iconColor = Colors.white;
        icon = Icons.info;
        break;
    }

    // إنشاء الإشعار
    final snackBar = SnackBar(
      content: Row(
        children: [
          Icon(
            icon,
            color: iconColor,
            size: 28,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontSize: 16,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
      backgroundColor: backgroundColor,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      margin: const EdgeInsets.all(10),
      duration: duration,
      dismissDirection: DismissDirection.horizontal,
      action: SnackBarAction(
        label: 'إغلاق',
        textColor: Colors.white,
        onPressed: () {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
        },
      ),
    );

    // عرض الإشعار
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}
