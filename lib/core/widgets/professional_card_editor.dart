import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../utils/professional_performance_optimizer.dart';
import '../utils/professional_snapping_system.dart';
import '../utils/professional_history_manager.dart';
import 'professional_handles.dart';

/// أنواع العناصر
enum ElementType {
  text,
  image,
  shape,
  sticker,
}

/// عنصر البطاقة
class CardElement {
  String id;
  ElementType type;
  Rect bounds;
  Map<String, dynamic> properties;
  double rotation;
  double opacity;

  CardElement({
    required this.id,
    required this.type,
    required this.bounds,
    this.properties = const {},
    this.rotation = 0.0,
    this.opacity = 1.0,
  });

  /// نسخ العنصر
  CardElement copy() {
    return CardElement(
      id: id,
      type: type,
      bounds: bounds,
      properties: Map.from(properties),
      rotation: rotation,
      opacity: opacity,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'bounds': {
        'left': bounds.left,
        'top': bounds.top,
        'width': bounds.width,
        'height': bounds.height,
      },
      'properties': properties,
      'rotation': rotation,
      'opacity': opacity,
    };
  }

  /// إنشاء من Map
  factory CardElement.fromMap(Map<String, dynamic> map) {
    return CardElement(
      id: map['id'],
      type: ElementType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => ElementType.text,
      ),
      bounds: Rect.fromLTWH(
        map['bounds']['left'],
        map['bounds']['top'],
        map['bounds']['width'],
        map['bounds']['height'],
      ),
      properties: Map<String, dynamic>.from(map['properties'] ?? {}),
      rotation: map['rotation'] ?? 0.0,
      opacity: map['opacity'] ?? 1.0,
    );
  }
}

/// محرر البطاقات الاحترافي
class ProfessionalCardEditor extends StatefulWidget {
  final Size cardSize;
  final List<CardElement> initialElements;
  final Function(List<CardElement>)? onElementsChanged;
  final Color backgroundColor;
  final bool showGrid;
  final bool enableSnapping;
  final bool enableHistory;

  const ProfessionalCardEditor({
    Key? key,
    this.cardSize = const Size(400, 600),
    this.initialElements = const [],
    this.onElementsChanged,
    this.backgroundColor = Colors.white,
    this.showGrid = true,
    this.enableSnapping = true,
    this.enableHistory = true,
  }) : super(key: key);

  @override
  State<ProfessionalCardEditor> createState() => _ProfessionalCardEditorState();
}

class _ProfessionalCardEditorState extends State<ProfessionalCardEditor>
    with TickerProviderStateMixin {
  
  // المتغيرات الأساسية
  List<CardElement> _elements = [];
  CardElement? _selectedElement;
  final TransformationController _transformController = TransformationController();
  
  // مدراء النظام
  late final ProfessionalPerformanceOptimizer _performanceOptimizer;
  late final CardHistoryManager _historyManager;
  
  // متغيرات الالتصاق
  List<SnapLine> _snapLines = [];
  bool _isSnapping = false;
  
  // متغيرات الرسوم المتحركة
  late AnimationController _selectionAnimationController;
  late Animation<double> _selectionAnimation;
  
  // متغيرات السحب
  Offset? _dragStartPosition;
  bool _isDragging = false;
  
  @override
  void initState() {
    super.initState();
    _initializeComponents();
    _elements = List.from(widget.initialElements);
  }
  
  void _initializeComponents() {
    // تهيئة محسن الأداء
    _performanceOptimizer = ProfessionalPerformanceOptimizer();
    
    // تهيئة مدير التاريخ
    if (widget.enableHistory) {
      _historyManager = CardHistoryManager();
      _saveCurrentState('الحالة الأولية');
    }
    
    // تهيئة الرسوم المتحركة
    _selectionAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _selectionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _selectionAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }
  
  @override
  void dispose() {
    _performanceOptimizer.dispose();
    _selectionAnimationController.dispose();
    _transformController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Column(
        children: [
          // شريط الأدوات
          _buildToolbar(),
          
          // منطقة التحرير
          Expanded(
            child: _buildEditorArea(),
          ),
        ],
      ),
    );
  }
  
  Widget _buildToolbar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أدوات التاريخ
          if (widget.enableHistory)
            HistoryControlWidget(
              historyManager: _historyManager,
              onUndo: _handleUndo,
              onRedo: _handleRedo,
            ),
          
          const SizedBox(width: 16),
          
          // أدوات الإضافة
          IconButton(
            onPressed: () => _addTextElement(),
            icon: const Icon(Icons.text_fields),
            tooltip: 'إضافة نص',
          ),
          
          IconButton(
            onPressed: () => _addImageElement(),
            icon: const Icon(Icons.image),
            tooltip: 'إضافة صورة',
          ),
          
          const SizedBox(width: 16),
          
          // أدوات التحكم
          IconButton(
            onPressed: _selectedElement != null ? _duplicateElement : null,
            icon: const Icon(Icons.copy),
            tooltip: 'تكرار',
          ),
          
          IconButton(
            onPressed: _selectedElement != null ? _deleteElement : null,
            icon: const Icon(Icons.delete),
            tooltip: 'حذف',
          ),
          
          const Spacer(),
          
          // أدوات العرض
          IconButton(
            onPressed: () => setState(() {}), // تبديل الشبكة
            icon: Icon(widget.showGrid ? Icons.grid_on : Icons.grid_off),
            tooltip: 'إظهار/إخفاء الشبكة',
          ),
        ],
      ),
    );
  }
  
  Widget _buildEditorArea() {
    return Center(
      child: Container(
        width: widget.cardSize.width + 100,
        height: widget.cardSize.height + 100,
        child: Stack(
          children: [
            // الخلفية والشبكة
            if (widget.showGrid)
              Positioned.fill(
                child: CustomPaint(
                  painter: GridPainter(
                    gridSize: 20,
                    gridColor: Colors.grey,
                    showGrid: widget.showGrid,
                  ),
                ),
              ),
            
            // منطقة البطاقة
            Positioned(
              left: 50,
              top: 50,
              child: _buildCardCanvas(),
            ),
            
            // خطوط الالتصاق
            if (_snapLines.isNotEmpty)
              Positioned.fill(
                child: CustomPaint(
                  painter: SnapLinePainter(snapLines: _snapLines),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCardCanvas() {
    return RepaintBoundary(
      child: Container(
        width: widget.cardSize.width,
        height: widget.cardSize.height,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // العناصر
              ..._elements.map((element) => _buildElement(element)),
              
              // مقابض التحكم للعنصر المحدد
              if (_selectedElement != null)
                ProfessionalHandleSet(
                  bounds: _selectedElement!.bounds,
                  isSelected: true,
                  onHandleDrag: _handleHandleDrag,
                  onHandleDragEnd: _handleHandleDragEnd,
                  primaryColor: Colors.blue,
                  showRotationHandle: true,
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildElement(CardElement element) {
    final isSelected = element == _selectedElement;
    
    return Positioned.fromRect(
      rect: element.bounds,
      child: RepaintBoundary(
        child: GestureDetector(
          onTap: () => _selectElement(element),
          onPanStart: (details) => _startDragging(element, details),
          onPanUpdate: (details) => _updateDragging(element, details),
          onPanEnd: (details) => _endDragging(element, details),
          child: AnimatedBuilder(
            animation: _selectionAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: isSelected ? 1.0 + (0.02 * _selectionAnimation.value) : 1.0,
                child: Container(
                  decoration: BoxDecoration(
                    border: isSelected 
                      ? Border.all(
                          color: Colors.blue.withOpacity(_selectionAnimation.value),
                          width: 2,
                        )
                      : null,
                  ),
                  child: _buildElementContent(element),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
  
  Widget _buildElementContent(CardElement element) {
    switch (element.type) {
      case ElementType.text:
        return Container(
          alignment: Alignment.center,
          child: Text(
            element.properties['text'] ?? 'نص',
            style: TextStyle(
              fontSize: element.properties['fontSize'] ?? 16.0,
              color: element.properties['color'] ?? Colors.black,
              fontWeight: element.properties['fontWeight'] ?? FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        );

      case ElementType.image:
        return Container(
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(Icons.image, size: 40, color: Colors.grey),
        );

      default:
        return Container(
          color: Colors.grey[300],
          child: const Center(child: Text('عنصر')),
        );
    }
  }

  // وظائف التحكم في العناصر
  void _selectElement(CardElement element) {
    if (_selectedElement != element) {
      setState(() {
        _selectedElement = element;
      });
      _selectionAnimationController.forward();
    }
  }

  void _addTextElement() {
    final newElement = CardElement(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: ElementType.text,
      bounds: Rect.fromLTWH(50, 50, 150, 40),
      properties: {
        'text': 'نص جديد',
        'fontSize': 16.0,
        'color': Colors.black,
        'fontWeight': FontWeight.normal,
      },
    );

    setState(() {
      _elements.add(newElement);
      _selectedElement = newElement;
    });

    if (widget.enableHistory) {
      _historyManager.saveAddElementState(_serializeState(), 'نص');
    }

    _selectionAnimationController.forward();
    widget.onElementsChanged?.call(_elements);
  }

  void _addImageElement() {
    final newElement = CardElement(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: ElementType.image,
      bounds: Rect.fromLTWH(100, 100, 100, 100),
      properties: {},
    );

    setState(() {
      _elements.add(newElement);
      _selectedElement = newElement;
    });

    if (widget.enableHistory) {
      _historyManager.saveAddElementState(_serializeState(), 'صورة');
    }

    _selectionAnimationController.forward();
    widget.onElementsChanged?.call(_elements);
  }

  void _duplicateElement() {
    if (_selectedElement == null) return;

    final original = _selectedElement!;
    final duplicate = CardElement(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: original.type,
      bounds: original.bounds.translate(20, 20),
      properties: Map.from(original.properties),
    );

    setState(() {
      _elements.add(duplicate);
      _selectedElement = duplicate;
    });

    if (widget.enableHistory) {
      _historyManager.saveAddElementState(_serializeState(), 'تكرار عنصر');
    }

    widget.onElementsChanged?.call(_elements);
  }

  void _deleteElement() {
    if (_selectedElement == null) return;

    final elementType = _selectedElement!.type.toString();

    setState(() {
      _elements.remove(_selectedElement);
      _selectedElement = null;
    });

    if (widget.enableHistory) {
      _historyManager.saveDeleteElementState(_serializeState(), elementType);
    }

    _selectionAnimationController.reverse();
    widget.onElementsChanged?.call(_elements);
  }

  // وظائف السحب
  void _startDragging(CardElement element, DragStartDetails details) {
    _selectElement(element);
    _dragStartPosition = details.localPosition;
    _isDragging = true;
    _snapLines.clear();
  }

  void _updateDragging(CardElement element, DragUpdateDetails details) {
    if (!_isDragging || !_performanceOptimizer.shouldUpdate()) return;

    final delta = details.localPosition - _dragStartPosition!;
    var newBounds = element.bounds.translate(delta.dx, delta.dy);

    // تطبيق الالتصاق
    if (widget.enableSnapping) {
      final snapResult = _applySnapping(newBounds, element);
      newBounds = newBounds.translate(snapResult.offset.dx, snapResult.offset.dy);
      _snapLines = snapResult.snapLines;
    }

    // التأكد من البقاء داخل حدود البطاقة
    newBounds = _constrainToBounds(newBounds);

    setState(() {
      element.bounds = newBounds;
    });

    _dragStartPosition = details.localPosition;
  }

  void _endDragging(CardElement element, DragEndDetails details) {
    _isDragging = false;
    _snapLines.clear();

    if (widget.enableHistory) {
      _historyManager.saveMoveElementState(_serializeState(), element.id);
    }

    setState(() {});
    widget.onElementsChanged?.call(_elements);
  }

  // وظائف مقابض التحكم
  void _handleHandleDrag(HandlePosition position, DragUpdateDetails details) {
    if (_selectedElement == null || !_performanceOptimizer.shouldUpdate()) return;

    final element = _selectedElement!;
    var newBounds = element.bounds;

    switch (position) {
      case HandlePosition.topLeft:
        newBounds = Rect.fromLTRB(
          newBounds.left + details.delta.dx,
          newBounds.top + details.delta.dy,
          newBounds.right,
          newBounds.bottom,
        );
        break;
      case HandlePosition.topRight:
        newBounds = Rect.fromLTRB(
          newBounds.left,
          newBounds.top + details.delta.dy,
          newBounds.right + details.delta.dx,
          newBounds.bottom,
        );
        break;
      case HandlePosition.bottomLeft:
        newBounds = Rect.fromLTRB(
          newBounds.left + details.delta.dx,
          newBounds.top,
          newBounds.right,
          newBounds.bottom + details.delta.dy,
        );
        break;
      case HandlePosition.bottomRight:
        newBounds = Rect.fromLTRB(
          newBounds.left,
          newBounds.top,
          newBounds.right + details.delta.dx,
          newBounds.bottom + details.delta.dy,
        );
        break;
      case HandlePosition.topCenter:
        newBounds = Rect.fromLTRB(
          newBounds.left,
          newBounds.top + details.delta.dy,
          newBounds.right,
          newBounds.bottom,
        );
        break;
      case HandlePosition.bottomCenter:
        newBounds = Rect.fromLTRB(
          newBounds.left,
          newBounds.top,
          newBounds.right,
          newBounds.bottom + details.delta.dy,
        );
        break;
      case HandlePosition.centerLeft:
        newBounds = Rect.fromLTRB(
          newBounds.left + details.delta.dx,
          newBounds.top,
          newBounds.right,
          newBounds.bottom,
        );
        break;
      case HandlePosition.centerRight:
        newBounds = Rect.fromLTRB(
          newBounds.left,
          newBounds.top,
          newBounds.right + details.delta.dx,
          newBounds.bottom,
        );
        break;
      case HandlePosition.rotation:
        // تطبيق الدوران (سيتم تطبيقه لاحقاً)
        break;
    }

    // التأكد من الحد الأدنى للحجم
    if (newBounds.width < 20 || newBounds.height < 20) return;

    // التأكد من البقاء داخل حدود البطاقة
    newBounds = _constrainToBounds(newBounds);

    setState(() {
      element.bounds = newBounds;
    });
  }

  void _handleHandleDragEnd(HandlePosition position, DragEndDetails details) {
    if (_selectedElement == null) return;

    if (widget.enableHistory) {
      _historyManager.saveResizeElementState(_serializeState(), _selectedElement!.id);
    }

    widget.onElementsChanged?.call(_elements);
  }

  // وظائف مساعدة
  SnapResult _applySnapping(Rect bounds, CardElement currentElement) {
    final otherRects = _elements
        .where((e) => e != currentElement)
        .map((e) => e.bounds)
        .toList();

    final containerRect = Rect.fromLTWH(0, 0, widget.cardSize.width, widget.cardSize.height);

    // الالتصاق بالعناصر الأخرى
    var snapResult = ProfessionalSnappingSystem.snapToElements(bounds, otherRects);

    // الالتصاق بحدود الحاوية
    final containerSnap = ProfessionalSnappingSystem.snapToContainer(bounds, containerRect);
    if (containerSnap != Offset.zero) {
      snapResult = SnapResult(
        offset: containerSnap,
        snapLines: [],
        snapped: true,
      );
    }

    // الالتصاق بالمركز
    final centerSnap = ProfessionalSnappingSystem.snapToCenter(bounds, containerRect);
    if (centerSnap != Offset.zero) {
      snapResult = SnapResult(
        offset: centerSnap,
        snapLines: [],
        snapped: true,
      );
    }

    return snapResult;
  }

  Rect _constrainToBounds(Rect bounds) {
    final cardRect = Rect.fromLTWH(0, 0, widget.cardSize.width, widget.cardSize.height);

    double left = math.max(0, math.min(bounds.left, cardRect.width - bounds.width));
    double top = math.max(0, math.min(bounds.top, cardRect.height - bounds.height));

    return Rect.fromLTWH(left, top, bounds.width, bounds.height);
  }

  Map<String, dynamic> _serializeState() {
    return {
      'elements': _elements.map((e) => e.toMap()).toList(),
      'selectedElementId': _selectedElement?.id,
    };
  }

  void _saveCurrentState(String description) {
    if (widget.enableHistory) {
      _historyManager.saveState(_serializeState(), description);
    }
  }

  void _handleUndo() {
    final state = _historyManager.undo();
    if (state != null) {
      _restoreState(state);
    }
  }

  void _handleRedo() {
    final state = _historyManager.redo();
    if (state != null) {
      _restoreState(state);
    }
  }

  void _restoreState(Map<String, dynamic> state) {
    final elementsList = state['elements'] as List;
    final selectedId = state['selectedElementId'] as String?;

    setState(() {
      _elements = elementsList.map((e) => CardElement.fromMap(e)).toList();
      _selectedElement = selectedId != null
          ? _elements.firstWhere((e) => e.id == selectedId, orElse: () => _elements.first)
          : null;
    });

    widget.onElementsChanged?.call(_elements);
  }
}
