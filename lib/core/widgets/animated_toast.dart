import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants/app_colors.dart';

/// نوع الإشعار المنبثق
enum ToastType {
  /// نجاح (أخضر)
  success,

  /// خطأ (أحمر)
  error,

  /// تحذير (برتقالي)
  warning,

  /// معلومات (أزرق)
  info,
}

/// موقع ظهور الإشعار المنبثق
enum ToastPosition {
  /// أعلى الشاشة
  top,

  /// وسط الشاشة
  center,

  /// أسفل الشاشة
  bottom,
}

/// مكون إشعار منبثق متحرك
class AnimatedToast extends StatefulWidget {
  /// نص الإشعار
  final String message;

  /// نوع الإشعار
  final ToastType type;

  /// موقع ظهور الإشعار
  final ToastPosition position;

  /// مدة ظهور الإشعار
  final Duration duration;

  /// إنشاء إشعار منبثق متحرك
  const AnimatedToast({
    super.key,
    required this.message,
    this.type = ToastType.info,
    this.position = ToastPosition.bottom,
    this.duration = const Duration(seconds: 3),
  });

  /// عرض إشعار منبثق متحرك
  static void show(
    BuildContext context, {
    required String message,
    ToastType type = ToastType.info,
    ToastPosition position = ToastPosition.bottom,
    Duration duration = const Duration(seconds: 3),
  }) {
    // إلغاء أي إشعار سابق
    _dismissCurrentToast();

    // إنشاء مفتاح عام للإشعار
    final overlayKey = GlobalKey<_AnimatedToastState>();

    // إنشاء إشعار جديد
    final overlayEntry = OverlayEntry(
      builder: (context) => AnimatedToast(
        key: overlayKey,
        message: message,
        type: type,
        position: position,
        duration: duration,
      ),
    );

    // حفظ الإشعار الحالي
    _currentToast = overlayEntry;

    // عرض الإشعار
    Overlay.of(context).insert(overlayEntry);

    // إغلاق الإشعار بعد المدة المحددة
    Future.delayed(duration, () {
      if (overlayKey.currentState?.mounted == true) {
        overlayKey.currentState?.closeToast();
      }
    });
  }

  /// الإشعار الحالي
  static OverlayEntry? _currentToast;

  /// إلغاء الإشعار الحالي
  static void _dismissCurrentToast() {
    _currentToast?.remove();
    _currentToast = null;
  }

  @override
  State<AnimatedToast> createState() => _AnimatedToastState();
}

class _AnimatedToastState extends State<AnimatedToast>
    with SingleTickerProviderStateMixin {
  /// متحكم الحركة
  late final AnimationController _controller;

  /// حركة الظهور والاختفاء
  late final Animation<double> _fadeAnimation;

  /// حركة الموقع
  late final Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الحركة
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // إنشاء حركة الظهور والاختفاء
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    // تحديد اتجاه الحركة بناءً على الموقع
    final Offset beginOffset;
    switch (widget.position) {
      case ToastPosition.top:
        beginOffset = const Offset(0.0, -1.0);
        break;
      case ToastPosition.center:
        beginOffset = const Offset(0.0, -0.2);
        break;
      case ToastPosition.bottom:
        beginOffset = const Offset(0.0, 1.0);
        break;
    }

    // إنشاء حركة الموقع
    _slideAnimation = Tween<Offset>(
      begin: beginOffset,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutBack,
      ),
    );

    // بدء الحركة
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// إغلاق الإشعار
  Future<void> closeToast() async {
    // بدء حركة الاختفاء
    await _controller.reverse();

    // إزالة الإشعار من الشاشة
    if (AnimatedToast._currentToast != null) {
      AnimatedToast._currentToast!.remove();
      AnimatedToast._currentToast = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحديد لون وأيقونة الإشعار بناءً على النوع
    final Color backgroundColor;
    final IconData icon;

    switch (widget.type) {
      case ToastType.success:
        backgroundColor = Colors.green.shade800;
        icon = Icons.check_circle;
        break;
      case ToastType.error:
        backgroundColor = Colors.red.shade800;
        icon = Icons.error;
        break;
      case ToastType.warning:
        backgroundColor = Colors.orange.shade800;
        icon = Icons.warning;
        break;
      case ToastType.info:
        backgroundColor = AppColors.primaryColor;
        icon = Icons.info;
        break;
    }

    // تحديد موقع الإشعار
    final Alignment alignment;
    switch (widget.position) {
      case ToastPosition.top:
        alignment = Alignment.topCenter;
        break;
      case ToastPosition.center:
        alignment = Alignment.center;
        break;
      case ToastPosition.bottom:
        alignment = Alignment.bottomCenter;
        break;
    }

    return Positioned(
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      child: IgnorePointer(
        child: Material(
          type: MaterialType.transparency,
          child: Align(
            alignment: alignment,
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    constraints: const BoxConstraints(
                      maxWidth: 400,
                    ),
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(51), // 0.2 * 255 = ~51
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            icon,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Flexible(
                            child: Text(
                              widget.message,
                              style: GoogleFonts.cairo(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
