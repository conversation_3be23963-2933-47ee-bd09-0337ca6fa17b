// lib/core/widgets/feedback/confirmation_dialog.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// حوار تأكيد محسن
class ConfirmationDialog extends StatelessWidget {
  /// عنوان الحوار
  final String title;
  
  /// رسالة الحوار
  final String message;
  
  /// نص زر التأكيد
  final String confirmText;
  
  /// نص زر الإلغاء
  final String cancelText;
  
  /// لون زر التأكيد
  final Color confirmColor;
  
  /// أيقونة الحوار
  final IconData? icon;
  
  /// لون الأيقونة
  final Color? iconColor;
  
  /// دالة يتم استدعاؤها عند النقر على زر التأكيد
  final VoidCallback? onConfirm;
  
  /// دالة يتم استدعاؤها عند النقر على زر الإلغاء
  final VoidCallback? onCancel;
  
  /// إنشاء حوار تأكيد محسن
  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmText = 'تأكيد',
    this.cancelText = 'إلغاء',
    this.confirmColor = Colors.red,
    this.icon,
    this.iconColor,
    this.onConfirm,
    this.onCancel,
  });

  /// عرض حوار تأكيد محسن
  static Future<bool?> show(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    Color confirmColor = Colors.red,
    IconData? icon,
    Color? iconColor,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => ConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        confirmColor: confirmColor,
        icon: icon,
        iconColor: iconColor,
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: iconColor ?? confirmColor,
              size: 28.0,
            ),
            const SizedBox(width: 12.0),
          ],
          Expanded(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                fontSize: 18.0,
              ),
            ),
          ),
        ],
      ),
      content: Text(
        message,
        style: GoogleFonts.cairo(
          fontSize: 16.0,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            onCancel?.call();
            Navigator.of(context).pop(false);
          },
          child: Text(
            cancelText,
            style: GoogleFonts.cairo(
              color: Colors.grey[700],
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            onConfirm?.call();
            Navigator.of(context).pop(true);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: confirmColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
          ),
          child: Text(
            confirmText,
            style: GoogleFonts.cairo(),
          ),
        ),
      ],
    );
  }
}
