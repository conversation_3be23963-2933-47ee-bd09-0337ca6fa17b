// lib/core/widgets/feedback/operation_feedback.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// نوع عملية التغذية الراجعة
enum OperationType {
  /// عملية جارية
  loading,

  /// عملية ناجحة
  success,

  /// عملية فاشلة
  error,

  /// عملية تحذير
  warning,

  /// عملية معلومات
  info,
}

/// مكون لعرض تغذية راجعة للعمليات
class OperationFeedback extends StatefulWidget {
  /// نوع العملية
  final OperationType type;

  /// عنوان العملية
  final String title;

  /// وصف العملية
  final String? description;

  /// أيقونة العملية (اختياري)
  final IconData? icon;

  /// ما إذا كان يتم عرض مؤشر التقدم
  final bool showProgress;

  /// قيمة التقدم (من 0.0 إلى 1.0)
  final double? progress;

  /// دالة يتم استدعاؤها عند النقر على زر الإجراء
  final VoidCallback? onActionPressed;

  /// نص زر الإجراء
  final String? actionText;

  /// مدة ظهور التغذية الراجعة
  final Duration duration;

  /// دالة يتم استدعاؤها عند إغلاق التغذية الراجعة
  final VoidCallback? onDismiss;

  /// إنشاء مكون تغذية راجعة للعمليات
  const OperationFeedback({
    super.key,
    required this.type,
    required this.title,
    this.description,
    this.icon,
    this.showProgress = false,
    this.progress,
    this.onActionPressed,
    this.actionText,
    this.duration = const Duration(seconds: 3),
    this.onDismiss,
  });

  /// عرض تغذية راجعة للعمليات
  static void show(
    BuildContext context, {
    required OperationType type,
    required String title,
    String? description,
    IconData? icon,
    bool showProgress = false,
    double? progress,
    VoidCallback? onActionPressed,
    String? actionText,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onDismiss,
  }) {
    // إلغاء أي تغذية راجعة سابقة
    _dismissCurrentFeedback();

    // إنشاء مفتاح عام للتغذية الراجعة
    final overlayKey = GlobalKey<_OperationFeedbackState>();

    // إنشاء تغذية راجعة جديدة
    final overlayEntry = OverlayEntry(
      builder: (context) => OperationFeedback(
        key: overlayKey,
        type: type,
        title: title,
        description: description,
        icon: icon,
        showProgress: showProgress,
        progress: progress,
        onActionPressed: onActionPressed,
        actionText: actionText,
        duration: duration,
        onDismiss: onDismiss,
      ),
    );

    // عرض التغذية الراجعة
    Overlay.of(context).insert(overlayEntry);

    // تخزين مدخل الطبقة العلوية
    _currentOverlayEntry = overlayEntry;

    // إغلاق التغذية الراجعة بعد المدة المحددة
    if (type != OperationType.loading) {
      Future.delayed(duration, () {
        if (overlayKey.currentState?.mounted == true) {
          overlayKey.currentState?.closeFeedback().then((_) {
            overlayEntry.remove();
            _currentOverlayEntry = null;
            onDismiss?.call();
          });
        }
      });
    }
  }

  /// إغلاق التغذية الراجعة الحالية
  static void dismiss() {
    _dismissCurrentFeedback();
  }

  /// مدخل الطبقة العلوية الحالي
  static OverlayEntry? _currentOverlayEntry;

  /// إلغاء التغذية الراجعة الحالية
  static void _dismissCurrentFeedback() {
    _currentOverlayEntry?.remove();
    _currentOverlayEntry = null;
  }

  /// تحديث التغذية الراجعة الحالية
  static void update({
    OperationType? type,
    String? title,
    String? description,
    IconData? icon,
    bool? showProgress,
    double? progress,
    VoidCallback? onActionPressed,
    String? actionText,
  }) {
    if (_currentOverlayEntry == null) return;

    // إعادة بناء التغذية الراجعة
    _currentOverlayEntry!.markNeedsBuild();
  }

  @override
  State<OperationFeedback> createState() => _OperationFeedbackState();
}

class _OperationFeedbackState extends State<OperationFeedback>
    with SingleTickerProviderStateMixin {
  /// متحكم الرسوم المتحركة
  late AnimationController _animationController;

  /// رسوم متحركة للتلاشي
  late Animation<double> _fadeAnimation;

  /// رسوم متحركة للحجم
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // تهيئة الرسوم المتحركة
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // تشغيل الرسوم المتحركة
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// إغلاق التغذية الراجعة
  Future<void> closeFeedback() async {
    await _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 20.0,
      left: 20.0,
      right: 20.0,
      child: SafeArea(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: _buildFeedbackCard(),
              ),
            );
          },
        ),
      ),
    );
  }

  /// بناء بطاقة التغذية الراجعة
  Widget _buildFeedbackCard() {
    // تحديد لون وأيقونة التغذية الراجعة بناءً على النوع
    final Color backgroundColor;
    final Color iconColor;
    final IconData icon;

    switch (widget.type) {
      case OperationType.loading:
        backgroundColor = Colors.blue[700]!;
        iconColor = Colors.white;
        icon = widget.icon ?? Icons.hourglass_empty;
        break;
      case OperationType.success:
        backgroundColor = Colors.green[700]!;
        iconColor = Colors.white;
        icon = widget.icon ?? Icons.check_circle;
        break;
      case OperationType.error:
        backgroundColor = Colors.red[700]!;
        iconColor = Colors.white;
        icon = widget.icon ?? Icons.error;
        break;
      case OperationType.warning:
        backgroundColor = Colors.orange[700]!;
        iconColor = Colors.white;
        icon = widget.icon ?? Icons.warning;
        break;
      case OperationType.info:
        backgroundColor = Colors.blue[700]!;
        iconColor = Colors.white;
        icon = widget.icon ?? Icons.info;
        break;
    }

    return Card(
      color: backgroundColor,
      elevation: 8.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                if (widget.type == OperationType.loading && widget.showProgress)
                  SizedBox(
                    width: 24.0,
                    height: 24.0,
                    child: CircularProgressIndicator(
                      value: widget.progress,
                      valueColor: AlwaysStoppedAnimation<Color>(iconColor),
                      strokeWidth: 2.0,
                    ),
                  )
                else
                  Icon(
                    icon,
                    color: iconColor,
                    size: 24.0,
                  ),
                const SizedBox(width: 16.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontSize: 16.0,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (widget.description != null) ...[
                        const SizedBox(height: 4.0),
                        Text(
                          widget.description!,
                          style: GoogleFonts.cairo(
                            color:
                                Colors.white.withAlpha(204), // 0.8 * 255 = ~204
                            fontSize: 14.0,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20.0,
                  ),
                  onPressed: () {
                    closeFeedback().then((_) {
                      OperationFeedback._currentOverlayEntry?.remove();
                      OperationFeedback._currentOverlayEntry = null;
                      widget.onDismiss?.call();
                    });
                  },
                ),
              ],
            ),
            if (widget.showProgress && widget.progress != null) ...[
              const SizedBox(height: 12.0),
              LinearProgressIndicator(
                value: widget.progress,
                valueColor: AlwaysStoppedAnimation<Color>(iconColor),
                backgroundColor: Colors.white.withAlpha(77), // 0.3 * 255 = ~77
              ),
            ],
            if (widget.onActionPressed != null &&
                widget.actionText != null) ...[
              const SizedBox(height: 12.0),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: widget.onActionPressed,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    widget.actionText!,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
