// lib/core/widgets/feedback/progress_dialog.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// حوار تقدم العملية
class ProgressDialog extends StatefulWidget {
  /// عنوان الحوار
  final String title;
  
  /// وصف العملية
  final String message;
  
  /// ما إذا كان مؤشر التقدم محدد القيمة
  final bool isDeterminate;
  
  /// قيمة التقدم (من 0.0 إلى 1.0)
  final double? progress;
  
  /// ما إذا كان يمكن إلغاء العملية
  final bool isCancelable;
  
  /// دالة يتم استدعاؤها عند إلغاء العملية
  final VoidCallback? onCancel;
  
  /// إنشاء حوار تقدم العملية
  const ProgressDialog({
    super.key,
    required this.title,
    required this.message,
    this.isDeterminate = false,
    this.progress,
    this.isCancelable = true,
    this.onCancel,
  });

  /// عرض حوار تقدم العملية
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    bool isDeterminate = false,
    double? progress,
    bool isCancelable = true,
    VoidCallback? onCancel,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ProgressDialog(
        title: title,
        message: message,
        isDeterminate: isDeterminate,
        progress: progress,
        isCancelable: isCancelable,
        onCancel: onCancel,
      ),
    );
  }
  
  /// تحديث حوار تقدم العملية
  static void update(
    BuildContext context, {
    String? message,
    double? progress,
  }) {
    final dialog = _ProgressDialogState.currentState;
    if (dialog != null) {
      dialog.updateDialog(message: message, progress: progress);
    }
  }
  
  /// إغلاق حوار تقدم العملية
  static void dismiss(BuildContext context) {
    Navigator.of(context, rootNavigator: true).pop();
    _ProgressDialogState.currentState = null;
  }

  @override
  State<ProgressDialog> createState() => _ProgressDialogState();
}

class _ProgressDialogState extends State<ProgressDialog> {
  /// حالة الحوار الحالي
  static _ProgressDialogState? currentState;
  
  /// رسالة الحوار
  late String _message;
  
  /// قيمة التقدم
  double? _progress;

  @override
  void initState() {
    super.initState();
    currentState = this;
    _message = widget.message;
    _progress = widget.progress;
  }

  /// تحديث الحوار
  void updateDialog({String? message, double? progress}) {
    setState(() {
      if (message != null) _message = message;
      if (progress != null) _progress = progress;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.title,
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _message,
            style: GoogleFonts.cairo(),
          ),
          const SizedBox(height: 20.0),
          widget.isDeterminate
              ? LinearProgressIndicator(
                  value: _progress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                )
              : CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
        ],
      ),
      actions: widget.isCancelable
          ? [
              TextButton(
                onPressed: () {
                  widget.onCancel?.call();
                  Navigator.of(context).pop();
                  currentState = null;
                },
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(),
                ),
              ),
            ]
          : null,
    );
  }
}
