import 'package:flutter/gestures.dart';
import 'package:flutter/widgets.dart';

/// A widget that disables scrolling for its child.
///
/// This widget prevents scrolling by absorbing scroll events.
class DisableScroll extends StatelessWidget {
  /// The child widget that will have scrolling disabled.
  final Widget child;

  /// Creates a new [DisableScroll] widget.
  const DisableScroll({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (_) => true, // Absorb all scroll notifications
      child: ScrollConfiguration(
        behavior: _DisableScrollBehavior(),
        child: child,
      ),
    );
  }
}

/// A scroll behavior that disables scrolling.
class _DisableScrollBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    return child;
  }

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const NeverScrollableScrollPhysics();
  }

  @override
  Set<PointerDeviceKind> get dragDevices => {};
}
