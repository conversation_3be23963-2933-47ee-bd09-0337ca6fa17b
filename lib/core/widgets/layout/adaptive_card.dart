// lib/core/widgets/layout/adaptive_card.dart

import 'package:flutter/material.dart';

import '../../constants/app_sizes.dart';
import '../../utils/responsive_utils.dart';

/// بطاقة متكيفة تعدل مظهرها بناءً على حجم الشاشة
class AdaptiveCard extends StatelessWidget {
  /// عنوان البطاقة
  final String? title;
  
  /// وصف البطاقة
  final String? subtitle;
  
  /// محتوى البطاقة
  final Widget? child;
  
  /// أيقونة البطاقة
  final IconData? icon;
  
  /// صورة البطاقة
  final Widget? image;
  
  /// ارتفاع الصورة
  final double? imageHeight;
  
  /// أزرار البطاقة
  final List<Widget>? actions;
  
  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;
  
  /// ما إذا كانت البطاقة قابلة للتوسيع
  final bool isExpandable;
  
  /// ما إذا كانت البطاقة موسعة افتراضيًا
  final bool initiallyExpanded;
  
  /// لون خلفية البطاقة
  final Color? backgroundColor;
  
  /// لون العنوان
  final Color? titleColor;
  
  /// لون الوصف
  final Color? subtitleColor;
  
  /// لون الأيقونة
  final Color? iconColor;
  
  /// الحشو داخل البطاقة
  final EdgeInsetsGeometry? padding;
  
  /// الهوامش حول البطاقة
  final EdgeInsetsGeometry? margin;
  
  /// ارتفاع البطاقة
  final double? height;
  
  /// عرض البطاقة
  final double? width;
  
  /// ارتفاع البطاقة على الهاتف المحمول
  final double? mobileHeight;
  
  /// عرض البطاقة على الهاتف المحمول
  final double? mobileWidth;
  
  /// ارتفاع البطاقة على الجهاز اللوحي
  final double? tabletHeight;
  
  /// عرض البطاقة على الجهاز اللوحي
  final double? tabletWidth;
  
  /// ارتفاع البطاقة على الحاسوب المكتبي
  final double? desktopHeight;
  
  /// عرض البطاقة على الحاسوب المكتبي
  final double? desktopWidth;
  
  /// إنشاء بطاقة متكيفة
  const AdaptiveCard({
    super.key,
    this.title,
    this.subtitle,
    this.child,
    this.icon,
    this.image,
    this.imageHeight,
    this.actions,
    this.onTap,
    this.isExpandable = false,
    this.initiallyExpanded = false,
    this.backgroundColor,
    this.titleColor,
    this.subtitleColor,
    this.iconColor,
    this.padding,
    this.margin,
    this.height,
    this.width,
    this.mobileHeight,
    this.mobileWidth,
    this.tabletHeight,
    this.tabletWidth,
    this.desktopHeight,
    this.desktopWidth,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    
    // تحديد الأبعاد بناءً على نوع الجهاز
    double? currentHeight;
    double? currentWidth;
    
    switch (deviceType) {
      case DeviceType.mobile:
        currentHeight = mobileHeight ?? height;
        currentWidth = mobileWidth ?? width;
        break;
      case DeviceType.tablet:
        currentHeight = tabletHeight ?? height;
        currentWidth = tabletWidth ?? width;
        break;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        currentHeight = desktopHeight ?? height;
        currentWidth = desktopWidth ?? width;
        break;
    }
    
    // تحديد الحشو المناسب بناءً على حجم الشاشة
    final EdgeInsetsGeometry responsivePadding = padding ??
        ResponsiveUtils.getPadding(
          context,
          basePadding: const EdgeInsets.all(AppSizes.paddingMedium),
          tabletPadding: const EdgeInsets.all(AppSizes.paddingMedium),
          desktopPadding: const EdgeInsets.all(AppSizes.paddingLarge),
        );
    
    // بناء محتوى البطاقة
    Widget cardContent;
    
    if (isExpandable) {
      // بطاقة قابلة للتوسيع
      cardContent = ExpansionTile(
        title: title != null
            ? Text(
                title!,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: titleColor,
                ),
              )
            : const SizedBox.shrink(),
        subtitle: subtitle != null
            ? Text(
                subtitle!,
                style: TextStyle(
                  color: subtitleColor,
                ),
              )
            : null,
        leading: icon != null
            ? Icon(
                icon,
                color: iconColor ?? Theme.of(context).primaryColor,
              )
            : null,
        initiallyExpanded: initiallyExpanded,
        children: [
          if (image != null)
            SizedBox(
              height: imageHeight,
              width: double.infinity,
              child: image,
            ),
          if (child != null)
            Padding(
              padding: const EdgeInsets.all(AppSizes.paddingMedium),
              child: child,
            ),
          if (actions != null && actions!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(AppSizes.paddingSmall),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: actions!,
              ),
            ),
        ],
      );
    } else {
      // بطاقة عادية
      cardContent = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (image != null)
            SizedBox(
              height: imageHeight,
              width: double.infinity,
              child: image,
            ),
          Padding(
            padding: responsivePadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null || icon != null)
                  Row(
                    children: [
                      if (icon != null)
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Icon(
                            icon,
                            color: iconColor ?? Theme.of(context).primaryColor,
                          ),
                        ),
                      if (title != null)
                        Expanded(
                          child: Text(
                            title!,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ResponsiveUtils.getFontSize(
                                context,
                                baseFontSize: 16.0,
                              ),
                              color: titleColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                if (title != null && subtitle != null)
                  const SizedBox(height: AppSizes.paddingSmall),
                if (subtitle != null)
                  Text(
                    subtitle!,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.getFontSize(
                        context,
                        baseFontSize: 14.0,
                      ),
                      color: subtitleColor ?? Colors.grey[600],
                    ),
                  ),
                if ((title != null || subtitle != null) && child != null)
                  const SizedBox(height: AppSizes.paddingMedium),
                if (child != null) child!,
                if (actions != null && actions!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: AppSizes.paddingMedium),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: actions!,
                    ),
                  ),
              ],
            ),
          ),
        ],
      );
    }
    
    // بناء البطاقة
    final card = Card(
      color: backgroundColor,
      margin: margin ?? const EdgeInsets.all(AppSizes.paddingSmall),
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.borderRadius),
      ),
      child: SizedBox(
        height: currentHeight,
        width: currentWidth,
        child: cardContent,
      ),
    );
    
    // إضافة تفاعل إذا كان مطلوبًا
    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.borderRadius),
        child: card,
      );
    }
    
    return card;
  }
}
