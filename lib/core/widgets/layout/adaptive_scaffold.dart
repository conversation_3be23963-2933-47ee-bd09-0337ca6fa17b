// lib/core/widgets/layout/adaptive_scaffold.dart

import 'package:flutter/material.dart';

import '../../utils/responsive_utils.dart';

/// سقالة متكيفة تعدل تخطيطها بناءً على حجم الشاشة
class AdaptiveScaffold extends StatelessWidget {
  /// عنوان الصفحة
  final String title;
  
  /// محتوى الصفحة
  final Widget body;
  
  /// أزرار شريط التطبيقات
  final List<Widget>? actions;
  
  /// زر العودة
  final Widget? leading;
  
  /// عناصر التنقل
  final List<NavigationDestination>? destinations;
  
  /// الفهرس المحدد للتنقل
  final int? selectedIndex;
  
  /// دالة يتم استدعاؤها عند تغيير الفهرس المحدد
  final ValueChanged<int>? onDestinationSelected;
  
  /// عنصر الشريط السفلي
  final Widget? bottomNavigationBar;
  
  /// عنصر الزر العائم
  final Widget? floatingActionButton;
  
  /// موقع الزر العائم
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  
  /// عنصر الدرج
  final Widget? drawer;
  
  /// عنصر الدرج النهائي
  final Widget? endDrawer;
  
  /// لون خلفية الصفحة
  final Color? backgroundColor;
  
  /// لون خلفية شريط التطبيقات
  final Color? appBarColor;
  
  /// ما إذا كان يجب عرض شريط التطبيقات
  final bool showAppBar;
  
  /// ما إذا كان يجب استخدام شريط جانبي على الشاشات الكبيرة
  final bool useSidebarOnLargeScreens;
  
  /// عرض الشريط الجانبي
  final double sidebarWidth;
  
  /// ما إذا كان الشريط الجانبي موسعًا
  final bool isExtended;
  
  /// إنشاء سقالة متكيفة
  const AdaptiveScaffold({
    super.key,
    required this.title,
    required this.body,
    this.actions,
    this.leading,
    this.destinations,
    this.selectedIndex,
    this.onDestinationSelected,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
    this.appBarColor,
    this.showAppBar = true,
    this.useSidebarOnLargeScreens = true,
    this.sidebarWidth = 240.0,
    this.isExtended = true,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isLargeScreen = deviceType == DeviceType.desktop || 
                          deviceType == DeviceType.largeDesktop;
    
    // تحديد ما إذا كان يجب استخدام الشريط الجانبي
    final bool useSidebar = isLargeScreen && 
                           useSidebarOnLargeScreens && 
                           destinations != null && 
                           destinations!.isNotEmpty;
    
    // بناء شريط التطبيقات
    PreferredSizeWidget? appBar;
    if (showAppBar) {
      appBar = AppBar(
        title: Text(title),
        actions: actions,
        leading: leading,
        backgroundColor: appBarColor,
        elevation: 4,
      );
    }
    
    // بناء شريط التنقل السفلي
    Widget? navigationBar;
    if (!useSidebar && destinations != null && destinations!.isNotEmpty) {
      navigationBar = NavigationBar(
        destinations: destinations!,
        selectedIndex: selectedIndex ?? 0,
        onDestinationSelected: onDestinationSelected,
      );
    }
    
    // بناء الشريط الجانبي
    Widget? sidebar;
    if (useSidebar) {
      sidebar = NavigationRail(
        extended: isExtended,
        destinations: destinations!.map((destination) {
          return NavigationRailDestination(
            icon: destination.icon,
            selectedIcon: destination.selectedIcon,
            label: Text(destination.label),
          );
        }).toList(),
        selectedIndex: selectedIndex ?? 0,
        onDestinationSelected: onDestinationSelected,
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 4,
      );
    }
    
    // بناء السقالة
    if (useSidebar) {
      return Scaffold(
        appBar: appBar,
        body: Row(
          children: [
            sidebar!,
            Expanded(child: body),
          ],
        ),
        backgroundColor: backgroundColor,
        floatingActionButton: floatingActionButton,
        floatingActionButtonLocation: floatingActionButtonLocation,
        drawer: drawer,
        endDrawer: endDrawer,
      );
    } else {
      return Scaffold(
        appBar: appBar,
        body: body,
        backgroundColor: backgroundColor,
        bottomNavigationBar: navigationBar ?? bottomNavigationBar,
        floatingActionButton: floatingActionButton,
        floatingActionButtonLocation: floatingActionButtonLocation,
        drawer: drawer,
        endDrawer: endDrawer,
      );
    }
  }
}
