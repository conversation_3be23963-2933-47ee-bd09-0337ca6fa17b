// lib/core/widgets/layout/responsive_grid.dart

import 'package:flutter/material.dart';

import '../../utils/responsive_utils.dart';

/// شبكة متجاوبة تعدل عدد الأعمدة بناءً على حجم الشاشة
class ResponsiveGrid extends StatelessWidget {
  /// قائمة العناصر المراد عرضها في الشبكة
  final List<Widget> children;
  
  /// المسافة بين العناصر أفقيًا
  final double crossAxisSpacing;
  
  /// المسافة بين العناصر رأسيًا
  final double mainAxisSpacing;
  
  /// نسبة العرض إلى الارتفاع للعناصر
  final double childAspectRatio;
  
  /// عدد الأعمدة للهاتف المحمول
  final int mobileColumns;
  
  /// عدد الأعمدة للجهاز اللوحي
  final int tabletColumns;
  
  /// عدد الأعمدة للحاسوب المكتبي
  final int desktopColumns;
  
  /// عدد الأعمدة للحاسوب المكتبي الكبير
  final int largeDesktopColumns;
  
  /// الحشو حول الشبكة
  final EdgeInsetsGeometry padding;
  
  /// ما إذا كانت الشبكة قابلة للتمرير
  final bool shrinkWrap;
  
  /// فيزياء التمرير
  final ScrollPhysics? physics;
  
  /// إنشاء شبكة متجاوبة
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.crossAxisSpacing = 10,
    this.mainAxisSpacing = 10,
    this.childAspectRatio = 1.0,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.largeDesktopColumns = 4,
    this.padding = EdgeInsets.zero,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        // تحديد عدد الأعمدة بناءً على العرض
        int crossAxisCount;
        if (width < ResponsiveUtils.mobileBreakpoint) {
          crossAxisCount = mobileColumns;
        } else if (width < ResponsiveUtils.tabletBreakpoint) {
          crossAxisCount = tabletColumns;
        } else if (width < ResponsiveUtils.desktopBreakpoint) {
          crossAxisCount = desktopColumns;
        } else {
          crossAxisCount = largeDesktopColumns;
        }
        
        return GridView.builder(
          padding: padding,
          shrinkWrap: shrinkWrap,
          physics: physics,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: crossAxisSpacing,
            mainAxisSpacing: mainAxisSpacing,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}
