// lib/core/widgets/layout/page_template.dart

import 'package:flutter/material.dart';

import '../../constants/app_sizes.dart';
import '../../utils/responsive_utils.dart';

/// قالب صفحة موحد للتطبيق
class PageTemplate extends StatelessWidget {
  /// عنوان الصفحة
  final String title;
  
  /// محتوى الصفحة
  final Widget body;
  
  /// أزرار شريط التطبيقات
  final List<Widget>? actions;
  
  /// زر العودة
  final Widget? leading;
  
  /// ما إذا كان يجب عرض زر العودة
  final bool showBackButton;
  
  /// ما إذا كان يجب تمكين التمرير
  final bool enableScrolling;
  
  /// ما إذا كان يجب استخدام حشو آمن
  final bool useSafePadding;
  
  /// الحشو حول المحتوى
  final EdgeInsetsGeometry? padding;
  
  /// لون خلفية الصفحة
  final Color? backgroundColor;
  
  /// لون خلفية شريط التطبيقات
  final Color? appBarColor;
  
  /// ارتفاع شريط التطبيقات
  final double? appBarHeight;
  
  /// ما إذا كان يجب عرض شريط التطبيقات
  final bool showAppBar;
  
  /// عنصر الشريط السفلي
  final Widget? bottomNavigationBar;
  
  /// عنصر الزر العائم
  final Widget? floatingActionButton;
  
  /// موقع الزر العائم
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  
  /// عنصر الدرج
  final Widget? drawer;
  
  /// عنصر الدرج النهائي
  final Widget? endDrawer;
  
  /// إنشاء قالب صفحة
  const PageTemplate({
    super.key,
    required this.title,
    required this.body,
    this.actions,
    this.leading,
    this.showBackButton = true,
    this.enableScrolling = true,
    this.useSafePadding = true,
    this.padding,
    this.backgroundColor,
    this.appBarColor,
    this.appBarHeight,
    this.showAppBar = true,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.drawer,
    this.endDrawer,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد الحشو المناسب بناءً على حجم الشاشة
    final EdgeInsetsGeometry responsivePadding = padding ??
        ResponsiveUtils.getPadding(
          context,
          basePadding: const EdgeInsets.all(AppSizes.paddingMedium),
          tabletPadding: const EdgeInsets.all(AppSizes.paddingLarge),
          desktopPadding: const EdgeInsets.symmetric(
            horizontal: AppSizes.paddingLarge * 2,
            vertical: AppSizes.paddingLarge,
          ),
        );
    
    // بناء المحتوى
    Widget content = enableScrolling
        ? SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: responsivePadding,
              child: body,
            ),
          )
        : Padding(
            padding: responsivePadding,
            child: body,
          );
    
    // تطبيق الحشو الآمن إذا كان مطلوبًا
    if (useSafePadding) {
      content = SafeArea(child: content);
    }
    
    // بناء شريط التطبيقات
    PreferredSizeWidget? appBar;
    if (showAppBar) {
      appBar = AppBar(
        title: Text(title),
        actions: actions,
        leading: leading ??
            (showBackButton && Navigator.of(context).canPop()
                ? IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () => Navigator.of(context).pop(),
                  )
                : null),
        backgroundColor: appBarColor,
        toolbarHeight: appBarHeight,
        elevation: 4,
      );
    }
    
    return Scaffold(
      appBar: appBar,
      body: content,
      backgroundColor: backgroundColor,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      drawer: drawer,
      endDrawer: endDrawer,
    );
  }
}
