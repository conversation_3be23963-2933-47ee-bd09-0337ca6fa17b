// lib/core/widgets/layout/responsive_layout.dart

import 'package:flutter/material.dart';

import '../../utils/responsive_utils.dart';

/// تخطيط متجاوب يعرض محتوى مختلفًا بناءً على حجم الشاشة
class ResponsiveLayout extends StatelessWidget {
  /// محتوى للهاتف المحمول
  final Widget mobile;
  
  /// محتوى للجهاز اللوحي (اختياري)
  final Widget? tablet;
  
  /// محتوى للحاسوب المكتبي (اختياري)
  final Widget? desktop;
  
  /// محتوى للحاسوب المكتبي الكبير (اختياري)
  final Widget? largeDesktop;
  
  /// إنشاء تخطيط متجاوب
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        // تحديد نوع الجهاز بناءً على العرض
        if (width < ResponsiveUtils.mobileBreakpoint) {
          return mobile;
        } else if (width < ResponsiveUtils.tabletBreakpoint) {
          return tablet ?? mobile;
        } else if (width < ResponsiveUtils.desktopBreakpoint) {
          return desktop ?? tablet ?? mobile;
        } else {
          return largeDesktop ?? desktop ?? tablet ?? mobile;
        }
      },
    );
  }
}
