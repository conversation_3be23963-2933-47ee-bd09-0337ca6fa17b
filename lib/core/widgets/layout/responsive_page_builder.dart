// lib/core/widgets/layout/responsive_page_builder.dart

import 'package:flutter/material.dart';

import '../../utils/responsive_utils.dart';
import 'adaptive_scaffold.dart';
import 'page_template.dart';

/// باني صفحة متجاوب يختار التخطيط المناسب بناءً على حجم الشاشة
class ResponsivePageBuilder extends StatelessWidget {
  /// عنوان الصفحة
  final String title;
  
  /// محتوى الصفحة
  final Widget body;
  
  /// أزرار شريط التطبيقات
  final List<Widget>? actions;
  
  /// زر العودة
  final Widget? leading;
  
  /// عناصر التنقل
  final List<NavigationDestination>? destinations;
  
  /// الفهرس المحدد للتنقل
  final int? selectedIndex;
  
  /// دالة يتم استدعاؤها عند تغيير الفهرس المحدد
  final ValueChanged<int>? onDestinationSelected;
  
  /// عنصر الشريط السفلي
  final Widget? bottomNavigationBar;
  
  /// عنصر الزر العائم
  final Widget? floatingActionButton;
  
  /// موقع الزر العائم
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  
  /// عنصر الدرج
  final Widget? drawer;
  
  /// عنصر الدرج النهائي
  final Widget? endDrawer;
  
  /// لون خلفية الصفحة
  final Color? backgroundColor;
  
  /// لون خلفية شريط التطبيقات
  final Color? appBarColor;
  
  /// ما إذا كان يجب عرض شريط التطبيقات
  final bool showAppBar;
  
  /// ما إذا كان يجب استخدام شريط جانبي على الشاشات الكبيرة
  final bool useSidebarOnLargeScreens;
  
  /// ما إذا كان يجب تمكين التمرير
  final bool enableScrolling;
  
  /// ما إذا كان يجب استخدام حشو آمن
  final bool useSafePadding;
  
  /// الحشو حول المحتوى
  final EdgeInsetsGeometry? padding;
  
  /// إنشاء باني صفحة متجاوب
  const ResponsivePageBuilder({
    super.key,
    required this.title,
    required this.body,
    this.actions,
    this.leading,
    this.destinations,
    this.selectedIndex,
    this.onDestinationSelected,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
    this.appBarColor,
    this.showAppBar = true,
    this.useSidebarOnLargeScreens = true,
    this.enableScrolling = true,
    this.useSafePadding = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isLargeScreen = deviceType == DeviceType.desktop || 
                          deviceType == DeviceType.largeDesktop;
    
    // استخدام AdaptiveScaffold للشاشات الكبيرة إذا كان هناك عناصر تنقل
    if (isLargeScreen && 
        useSidebarOnLargeScreens && 
        destinations != null && 
        destinations!.isNotEmpty) {
      return AdaptiveScaffold(
        title: title,
        body: body,
        actions: actions,
        leading: leading,
        destinations: destinations,
        selectedIndex: selectedIndex,
        onDestinationSelected: onDestinationSelected,
        bottomNavigationBar: bottomNavigationBar,
        floatingActionButton: floatingActionButton,
        floatingActionButtonLocation: floatingActionButtonLocation,
        drawer: drawer,
        endDrawer: endDrawer,
        backgroundColor: backgroundColor,
        appBarColor: appBarColor,
        showAppBar: showAppBar,
        useSidebarOnLargeScreens: useSidebarOnLargeScreens,
      );
    } else {
      // استخدام PageTemplate للشاشات الصغيرة أو إذا لم تكن هناك عناصر تنقل
      return PageTemplate(
        title: title,
        body: body,
        actions: actions,
        leading: leading,
        showBackButton: true,
        enableScrolling: enableScrolling,
        useSafePadding: useSafePadding,
        padding: padding,
        backgroundColor: backgroundColor,
        appBarColor: appBarColor,
        showAppBar: showAppBar,
        bottomNavigationBar: destinations != null && destinations!.isNotEmpty
            ? NavigationBar(
                destinations: destinations!,
                selectedIndex: selectedIndex ?? 0,
                onDestinationSelected: onDestinationSelected,
              )
            : bottomNavigationBar,
        floatingActionButton: floatingActionButton,
        floatingActionButtonLocation: floatingActionButtonLocation,
        drawer: drawer,
        endDrawer: endDrawer,
      );
    }
  }
}
