// lib/core/widgets/layout/responsive_row.dart

import 'package:flutter/material.dart';

import '../../utils/responsive_utils.dart';

/// صف متجاوب يتحول إلى عمود على الشاشات الصغيرة
class ResponsiveRow extends StatelessWidget {
  /// العناصر المراد عرضها في الصف
  final List<Widget> children;
  
  /// المسافة بين العناصر
  final double spacing;
  
  /// محاذاة العناصر أفقيًا
  final MainAxisAlignment mainAxisAlignment;
  
  /// محاذاة العناصر رأسيًا
  final CrossAxisAlignment crossAxisAlignment;
  
  /// عرض الشاشة الذي يتحول عنده الصف إلى عمود
  final double breakpoint;
  
  /// المسافة بين العناصر عندما تكون في عمود
  final double columnSpacing;
  
  /// محاذاة العناصر أفقيًا عندما تكون في عمود
  final MainAxisAlignment columnMainAxisAlignment;
  
  /// محاذاة العناصر رأسيًا عندما تكون في عمود
  final CrossAxisAlignment columnCrossAxisAlignment;
  
  /// إنشاء صف متجاوب
  const ResponsiveRow({
    super.key,
    required this.children,
    this.spacing = 16.0,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.breakpoint = ResponsiveUtils.mobileBreakpoint,
    this.columnSpacing = 16.0,
    this.columnMainAxisAlignment = MainAxisAlignment.start,
    this.columnCrossAxisAlignment = CrossAxisAlignment.stretch,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        // تحويل الصف إلى عمود إذا كان العرض أقل من نقطة الفصل
        if (width < breakpoint) {
          return Column(
            mainAxisAlignment: columnMainAxisAlignment,
            crossAxisAlignment: columnCrossAxisAlignment,
            children: _addSpacingToChildren(
              children,
              columnSpacing,
              isColumn: true,
            ),
          );
        } else {
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: _addSpacingToChildren(
              children,
              spacing,
              isColumn: false,
            ),
          );
        }
      },
    );
  }
  
  /// إضافة مسافات بين العناصر
  List<Widget> _addSpacingToChildren(
    List<Widget> children,
    double spacing, {
    required bool isColumn,
  }) {
    if (children.isEmpty) return [];
    if (children.length == 1) return children;
    
    final List<Widget> result = [];
    for (int i = 0; i < children.length; i++) {
      result.add(children[i]);
      if (i < children.length - 1) {
        if (isColumn) {
          result.add(SizedBox(height: spacing));
        } else {
          result.add(SizedBox(width: spacing));
        }
      }
    }
    
    return result;
  }
}
