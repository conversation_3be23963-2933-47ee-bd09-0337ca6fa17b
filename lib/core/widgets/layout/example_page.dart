// lib/core/widgets/layout/example_page.dart

import 'package:flutter/material.dart';

import '../../constants/app_sizes.dart';
import 'adaptive_card.dart';
import 'adaptive_list_item.dart';
import 'responsive_container.dart';
import 'responsive_grid.dart';
import 'responsive_page_builder.dart';
import 'responsive_row.dart';
import 'responsive_wrap.dart';

/// صفحة مثال لعرض استخدام المكونات المتجاوبة
class ResponsiveDesignExamplePage extends StatelessWidget {
  /// إنشاء صفحة مثال
  const ResponsiveDesignExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsivePageBuilder(
      title: 'مثال التصميم المتجاوب',
      destinations: [
        const NavigationDestination(
          icon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        const NavigationDestination(
          icon: Icon(Icons.grid_view),
          label: 'الشبكة',
        ),
        const NavigationDestination(
          icon: Icon(Icons.view_list),
          label: 'القائمة',
        ),
        const NavigationDestination(
          icon: Icon(Icons.settings),
          label: 'الإعدادات',
        ),
      ],
      selectedIndex: 0,
      onDestinationSelected: (index) {
        // التعامل مع تغيير التنقل
      },
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مثال على التصميم المتجاوب',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingMedium),
            const Text(
              'هذه الصفحة تعرض مثالًا على استخدام المكونات المتجاوبة في التطبيق. جرب تغيير حجم النافذة لرؤية كيف تتكيف المكونات مع أحجام الشاشات المختلفة.',
              style: TextStyle(
                fontSize: 16,
              ),
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            
            // مثال على ResponsiveContainer
            const Text(
              'ResponsiveContainer',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingSmall),
            ResponsiveContainer(
              maxWidth: 800,
              mobileMaxWidth: 300,
              tabletMaxWidth: 500,
              desktopMaxWidth: 800,
              padding: const EdgeInsets.all(AppSizes.paddingMedium),
              backgroundColor: Colors.blue[100],
              child: const Text(
                'هذه حاوية متجاوبة تغير حجمها بناءً على حجم الشاشة. على الهاتف المحمول، العرض الأقصى هو 300 بكسل. على الجهاز اللوحي، العرض الأقصى هو 500 بكسل. على الحاسوب المكتبي، العرض الأقصى هو 800 بكسل.',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            
            // مثال على ResponsiveRow
            const Text(
              'ResponsiveRow',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingSmall),
            ResponsiveRow(
              children: [
                Expanded(
                  child: Container(
                    height: 100,
                    color: Colors.red[300],
                    alignment: Alignment.center,
                    child: const Text('العنصر 1'),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 100,
                    color: Colors.green[300],
                    alignment: Alignment.center,
                    child: const Text('العنصر 2'),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 100,
                    color: Colors.blue[300],
                    alignment: Alignment.center,
                    child: const Text('العنصر 3'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            
            // مثال على ResponsiveGrid
            const Text(
              'ResponsiveGrid',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingSmall),
            SizedBox(
              height: 300,
              child: ResponsiveGrid(
                mobileColumns: 1,
                tabletColumns: 2,
                desktopColumns: 3,
                largeDesktopColumns: 4,
                children: List.generate(
                  6,
                  (index) => Container(
                    color: Colors.purple[100 * ((index % 5) + 1)],
                    alignment: Alignment.center,
                    child: Text('العنصر ${index + 1}'),
                  ),
                ),
              ),
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            
            // مثال على ResponsiveWrap
            const Text(
              'ResponsiveWrap',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingSmall),
            ResponsiveWrap(
              spacing: 8,
              runSpacing: 8,
              mobileSpacing: 4,
              tabletSpacing: 8,
              desktopSpacing: 16,
              children: List.generate(
                10,
                (index) => Chip(
                  label: Text('الوسم ${index + 1}'),
                  backgroundColor: Colors.orange[100 * ((index % 5) + 1)],
                ),
              ),
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            
            // مثال على AdaptiveCard
            const Text(
              'AdaptiveCard',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingSmall),
            AdaptiveCard(
              title: 'بطاقة متكيفة',
              subtitle: 'هذه بطاقة تتكيف مع حجم الشاشة',
              icon: Icons.info,
              actions: [
                TextButton(
                  onPressed: () {},
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('موافق'),
                ),
              ],
              child: const Text(
                'محتوى البطاقة يمكن أن يكون أي عنصر. هذه البطاقة تتكيف مع حجم الشاشة وتغير مظهرها بناءً على ذلك.',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            
            // مثال على AdaptiveListItem
            const Text(
              'AdaptiveListItem',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingSmall),
            Column(
              children: [
                AdaptiveListItem(
                  title: 'العنصر الأول',
                  subtitle: 'وصف العنصر الأول',
                  icon: Icons.star,
                  onTap: () {},
                ),
                AdaptiveListItem(
                  title: 'العنصر الثاني',
                  subtitle: 'وصف العنصر الثاني',
                  icon: Icons.favorite,
                  isSelected: true,
                  onTap: () {},
                ),
                AdaptiveListItem(
                  title: 'العنصر الثالث',
                  subtitle: 'وصف العنصر الثالث',
                  icon: Icons.bookmark,
                  isDisabled: true,
                  onTap: () {},
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
