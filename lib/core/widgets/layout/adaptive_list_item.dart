// lib/core/widgets/layout/adaptive_list_item.dart

import 'package:flutter/material.dart';

import '../../constants/app_sizes.dart';
import '../../utils/responsive_utils.dart';

/// عنصر قائمة متكيف يعدل مظهره بناءً على حجم الشاشة
class AdaptiveListItem extends StatelessWidget {
  /// عنوان العنصر
  final String title;

  /// وصف العنصر
  final String? subtitle;

  /// أيقونة العنصر
  final IconData? icon;

  /// صورة العنصر
  final Widget? leading;

  /// عنصر في نهاية العنصر
  final Widget? trailing;

  /// دالة يتم استدعاؤها عند النقر على العنصر
  final VoidCallback? onTap;

  /// دالة يتم استدعاؤها عند النقر المطول على العنصر
  final VoidCallback? onLongPress;

  /// لون خلفية العنصر
  final Color? backgroundColor;

  /// لون العنوان
  final Color? titleColor;

  /// لون الوصف
  final Color? subtitleColor;

  /// لون الأيقونة
  final Color? iconColor;

  /// الحشو داخل العنصر
  final EdgeInsetsGeometry? padding;

  /// ارتفاع العنصر
  final double? height;

  /// ما إذا كان العنصر مختارًا
  final bool isSelected;

  /// لون العنصر عند اختياره
  final Color? selectedColor;

  /// ما إذا كان العنصر معطلًا
  final bool isDisabled;

  /// ما إذا كان العنصر يعرض كبطاقة على الشاشات الكبيرة
  final bool useCardOnLargeScreens;

  /// إنشاء عنصر قائمة متكيف
  const AdaptiveListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.leading,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.backgroundColor,
    this.titleColor,
    this.subtitleColor,
    this.iconColor,
    this.padding,
    this.height,
    this.isSelected = false,
    this.selectedColor,
    this.isDisabled = false,
    this.useCardOnLargeScreens = true,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isLargeScreen = deviceType == DeviceType.desktop ||
        deviceType == DeviceType.largeDesktop;

    // تحديد الحشو المناسب بناءً على حجم الشاشة
    final EdgeInsetsGeometry responsivePadding = padding ??
        ResponsiveUtils.getPadding(
          context,
          basePadding: const EdgeInsets.symmetric(
            horizontal: AppSizes.paddingMedium,
            vertical: AppSizes.paddingSmall,
          ),
          tabletPadding: const EdgeInsets.symmetric(
            horizontal: AppSizes.paddingMedium,
            vertical: AppSizes.paddingMedium,
          ),
        );

    // بناء محتوى العنصر
    Widget itemContent = ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: isDisabled
              ? Colors.grey
              : (isSelected
                  ? selectedColor ?? Theme.of(context).primaryColor
                  : titleColor),
          fontSize: ResponsiveUtils.getFontSize(
            context,
            baseFontSize: 16.0,
          ),
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: TextStyle(
                color: isDisabled
                    ? Colors.grey[400]
                    : (subtitleColor ?? Colors.grey[600]),
                fontSize: ResponsiveUtils.getFontSize(
                  context,
                  baseFontSize: 14.0,
                ),
              ),
            )
          : null,
      leading: leading ??
          (icon != null
              ? Icon(
                  icon,
                  color: isDisabled
                      ? Colors.grey
                      : (iconColor ?? Theme.of(context).primaryColor),
                  size: ResponsiveUtils.getIconSize(
                    context,
                    baseIconSize: 24.0,
                  ),
                )
              : null),
      trailing: trailing,
      onTap: isDisabled ? null : onTap,
      onLongPress: isDisabled ? null : onLongPress,
      selected: isSelected,
      selectedTileColor: isSelected
          ? (selectedColor ?? Theme.of(context).primaryColor)
              .withAlpha(26) // 0.1 * 255 = ~26
          : null,
      contentPadding: responsivePadding,
      dense: deviceType == DeviceType.mobile,
    );

    // على الشاشات الكبيرة، يمكن عرض العنصر كبطاقة
    if (isLargeScreen && useCardOnLargeScreens) {
      return Card(
        margin: const EdgeInsets.symmetric(
          horizontal: AppSizes.paddingMedium,
          vertical: AppSizes.paddingSmall / 2,
        ),
        color: isSelected
            ? (selectedColor ?? Theme.of(context).primaryColor)
                .withAlpha(26) // 0.1 * 255 = ~26
            : backgroundColor,
        elevation: isSelected ? 2.0 : 1.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.borderRadius),
          side: isSelected
              ? BorderSide(
                  color: selectedColor ?? Theme.of(context).primaryColor,
                  width: 1.0,
                )
              : BorderSide.none,
        ),
        child: SizedBox(
          height: height,
          child: itemContent,
        ),
      );
    }

    // على الشاشات الصغيرة، عرض العنصر كعنصر قائمة عادي
    return Container(
      height: height,
      color: isSelected
          ? (selectedColor ?? Theme.of(context).primaryColor)
              .withAlpha(26) // 0.1 * 255 = ~26
          : backgroundColor,
      child: itemContent,
    );
  }
}
