// lib/core/widgets/layout/adaptive_dialog.dart

import 'package:flutter/material.dart';

import '../../constants/app_sizes.dart';
import '../../utils/responsive_utils.dart';

/// حوار متكيف يعدل حجمه ومظهره بناءً على حجم الشاشة
class AdaptiveDialog extends StatelessWidget {
  /// عنوان الحوار
  final String title;
  
  /// محتوى الحوار
  final Widget content;
  
  /// أزرار الحوار
  final List<Widget>? actions;
  
  /// أيقونة الحوار
  final IconData? icon;
  
  /// لون الأيقونة
  final Color? iconColor;
  
  /// العرض الأقصى للحوار
  final double? maxWidth;
  
  /// الارتفاع الأقصى للحوار
  final double? maxHeight;
  
  /// العرض الأقصى للحوار على الهاتف المحمول
  final double? mobileMaxWidth;
  
  /// الارتفاع الأقصى للحوار على الهاتف المحمول
  final double? mobileMaxHeight;
  
  /// العرض الأقصى للحوار على الجهاز اللوحي
  final double? tabletMaxWidth;
  
  /// الارتفاع الأقصى للحوار على الجهاز اللوحي
  final double? tabletMaxHeight;
  
  /// العرض الأقصى للحوار على الحاسوب المكتبي
  final double? desktopMaxWidth;
  
  /// الارتفاع الأقصى للحوار على الحاسوب المكتبي
  final double? desktopMaxHeight;
  
  /// الحشو داخل الحوار
  final EdgeInsetsGeometry? padding;
  
  /// لون خلفية الحوار
  final Color? backgroundColor;
  
  /// ما إذا كان الحوار قابلًا للإغلاق بالنقر خارجه
  final bool barrierDismissible;
  
  /// لون الحاجز
  final Color? barrierColor;
  
  /// إنشاء حوار متكيف
  const AdaptiveDialog({
    super.key,
    required this.title,
    required this.content,
    this.actions,
    this.icon,
    this.iconColor,
    this.maxWidth,
    this.maxHeight,
    this.mobileMaxWidth,
    this.mobileMaxHeight,
    this.tabletMaxWidth,
    this.tabletMaxHeight,
    this.desktopMaxWidth,
    this.desktopMaxHeight,
    this.padding,
    this.backgroundColor,
    this.barrierDismissible = true,
    this.barrierColor,
  });

  /// عرض الحوار
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required Widget content,
    List<Widget>? actions,
    IconData? icon,
    Color? iconColor,
    double? maxWidth,
    double? maxHeight,
    double? mobileMaxWidth,
    double? mobileMaxHeight,
    double? tabletMaxWidth,
    double? tabletMaxHeight,
    double? desktopMaxWidth,
    double? desktopMaxHeight,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    bool barrierDismissible = true,
    Color? barrierColor,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      builder: (context) => AdaptiveDialog(
        title: title,
        content: content,
        actions: actions,
        icon: icon,
        iconColor: iconColor,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        mobileMaxWidth: mobileMaxWidth,
        mobileMaxHeight: mobileMaxHeight,
        tabletMaxWidth: tabletMaxWidth,
        tabletMaxHeight: tabletMaxHeight,
        desktopMaxWidth: desktopMaxWidth,
        desktopMaxHeight: desktopMaxHeight,
        padding: padding,
        backgroundColor: backgroundColor,
        barrierDismissible: barrierDismissible,
        barrierColor: barrierColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    
    // تحديد الأبعاد بناءً على نوع الجهاز
    double? currentMaxWidth;
    double? currentMaxHeight;
    
    switch (deviceType) {
      case DeviceType.mobile:
        currentMaxWidth = mobileMaxWidth ?? maxWidth ?? 320.0;
        currentMaxHeight = mobileMaxHeight ?? maxHeight;
        break;
      case DeviceType.tablet:
        currentMaxWidth = tabletMaxWidth ?? maxWidth ?? 480.0;
        currentMaxHeight = tabletMaxHeight ?? maxHeight;
        break;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        currentMaxWidth = desktopMaxWidth ?? maxWidth ?? 640.0;
        currentMaxHeight = desktopMaxHeight ?? maxHeight;
        break;
    }
    
    // تحديد الحشو المناسب بناءً على حجم الشاشة
    final EdgeInsetsGeometry responsivePadding = padding ??
        ResponsiveUtils.getPadding(
          context,
          basePadding: const EdgeInsets.all(AppSizes.paddingMedium),
          tabletPadding: const EdgeInsets.all(AppSizes.paddingLarge),
        );
    
    return Dialog(
      backgroundColor: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.borderRadius),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: currentMaxWidth,
          maxHeight: currentMaxHeight ?? double.infinity,
        ),
        child: Padding(
          padding: responsivePadding,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الحوار
              Row(
                children: [
                  if (icon != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: Icon(
                        icon,
                        color: iconColor ?? Theme.of(context).primaryColor,
                        size: ResponsiveUtils.getIconSize(
                          context,
                          baseIconSize: 24.0,
                        ),
                      ),
                    ),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: ResponsiveUtils.getFontSize(
                          context,
                          baseFontSize: 18.0,
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    iconSize: ResponsiveUtils.getIconSize(
                      context,
                      baseIconSize: 20.0,
                    ),
                  ),
                ],
              ),
              const Divider(),
              // محتوى الحوار
              Flexible(
                child: SingleChildScrollView(
                  child: content,
                ),
              ),
              // أزرار الحوار
              if (actions != null && actions!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: AppSizes.paddingMedium),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: actions!,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
