// lib/core/widgets/layout/responsive_container.dart

import 'package:flutter/material.dart';

import '../../utils/responsive_utils.dart';

/// حاوية متجاوبة تعدل حجمها وخصائصها بناءً على حجم الشاشة
class ResponsiveContainer extends StatelessWidget {
  /// العنصر الذي سيتم عرضه داخل الحاوية
  final Widget child;
  
  /// العرض الأقصى للحاوية
  final double? maxWidth;
  
  /// العرض الأقصى للحاوية على الهاتف المحمول
  final double? mobileMaxWidth;
  
  /// العرض الأقصى للحاوية على الجهاز اللوحي
  final double? tabletMaxWidth;
  
  /// العرض الأقصى للحاوية على الحاسوب المكتبي
  final double? desktopMaxWidth;
  
  /// العرض الأقصى للحاوية على الحاسوب المكتبي الكبير
  final double? largeDesktopMaxWidth;
  
  /// الحشو حول الحاوية
  final EdgeInsetsGeometry? padding;
  
  /// الحشو حول الحاوية على الهاتف المحمول
  final EdgeInsetsGeometry? mobilePadding;
  
  /// الحشو حول الحاوية على الجهاز اللوحي
  final EdgeInsetsGeometry? tabletPadding;
  
  /// الحشو حول الحاوية على الحاسوب المكتبي
  final EdgeInsetsGeometry? desktopPadding;
  
  /// الحشو حول الحاوية على الحاسوب المكتبي الكبير
  final EdgeInsetsGeometry? largeDesktopPadding;
  
  /// الهوامش حول الحاوية
  final EdgeInsetsGeometry? margin;
  
  /// الهوامش حول الحاوية على الهاتف المحمول
  final EdgeInsetsGeometry? mobileMargin;
  
  /// الهوامش حول الحاوية على الجهاز اللوحي
  final EdgeInsetsGeometry? tabletMargin;
  
  /// الهوامش حول الحاوية على الحاسوب المكتبي
  final EdgeInsetsGeometry? desktopMargin;
  
  /// الهوامش حول الحاوية على الحاسوب المكتبي الكبير
  final EdgeInsetsGeometry? largeDesktopMargin;
  
  /// محاذاة العنصر داخل الحاوية
  final Alignment? alignment;
  
  /// لون خلفية الحاوية
  final Color? backgroundColor;
  
  /// زخرفة الحاوية
  final BoxDecoration? decoration;
  
  /// إنشاء حاوية متجاوبة
  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.mobileMaxWidth,
    this.tabletMaxWidth,
    this.desktopMaxWidth,
    this.largeDesktopMaxWidth,
    this.padding,
    this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
    this.largeDesktopPadding,
    this.margin,
    this.mobileMargin,
    this.tabletMargin,
    this.desktopMargin,
    this.largeDesktopMargin,
    this.alignment,
    this.backgroundColor,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        // تحديد نوع الجهاز بناءً على العرض
        DeviceType deviceType;
        if (width < ResponsiveUtils.mobileBreakpoint) {
          deviceType = DeviceType.mobile;
        } else if (width < ResponsiveUtils.tabletBreakpoint) {
          deviceType = DeviceType.tablet;
        } else if (width < ResponsiveUtils.desktopBreakpoint) {
          deviceType = DeviceType.desktop;
        } else {
          deviceType = DeviceType.largeDesktop;
        }
        
        // تحديد العرض الأقصى بناءً على نوع الجهاز
        double? currentMaxWidth;
        switch (deviceType) {
          case DeviceType.mobile:
            currentMaxWidth = mobileMaxWidth ?? maxWidth;
            break;
          case DeviceType.tablet:
            currentMaxWidth = tabletMaxWidth ?? maxWidth;
            break;
          case DeviceType.desktop:
            currentMaxWidth = desktopMaxWidth ?? maxWidth;
            break;
          case DeviceType.largeDesktop:
            currentMaxWidth = largeDesktopMaxWidth ?? maxWidth;
            break;
        }
        
        // تحديد الحشو بناءً على نوع الجهاز
        EdgeInsetsGeometry? currentPadding;
        switch (deviceType) {
          case DeviceType.mobile:
            currentPadding = mobilePadding ?? padding;
            break;
          case DeviceType.tablet:
            currentPadding = tabletPadding ?? padding;
            break;
          case DeviceType.desktop:
            currentPadding = desktopPadding ?? padding;
            break;
          case DeviceType.largeDesktop:
            currentPadding = largeDesktopPadding ?? padding;
            break;
        }
        
        // تحديد الهوامش بناءً على نوع الجهاز
        EdgeInsetsGeometry? currentMargin;
        switch (deviceType) {
          case DeviceType.mobile:
            currentMargin = mobileMargin ?? margin;
            break;
          case DeviceType.tablet:
            currentMargin = tabletMargin ?? margin;
            break;
          case DeviceType.desktop:
            currentMargin = desktopMargin ?? margin;
            break;
          case DeviceType.largeDesktop:
            currentMargin = largeDesktopMargin ?? margin;
            break;
        }
        
        return Container(
          constraints: BoxConstraints(
            maxWidth: currentMaxWidth ?? double.infinity,
          ),
          padding: currentPadding,
          margin: currentMargin,
          alignment: alignment,
          color: decoration == null ? backgroundColor : null,
          decoration: decoration,
          child: child,
        );
      },
    );
  }
}
