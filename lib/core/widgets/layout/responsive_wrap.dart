// lib/core/widgets/layout/responsive_wrap.dart

import 'package:flutter/material.dart';

import '../../utils/responsive_utils.dart';

/// غلاف متجاوب يعدل المسافات والمحاذاة بناءً على حجم الشاشة
class ResponsiveWrap extends StatelessWidget {
  /// العناصر المراد عرضها في الغلاف
  final List<Widget> children;
  
  /// المسافة بين العناصر أفقيًا
  final double spacing;
  
  /// المسافة بين العناصر أفقيًا على الهاتف المحمول
  final double? mobileSpacing;
  
  /// المسافة بين العناصر أفقيًا على الجهاز اللوحي
  final double? tabletSpacing;
  
  /// المسافة بين العناصر أفقيًا على الحاسوب المكتبي
  final double? desktopSpacing;
  
  /// المسافة بين العناصر رأسيًا
  final double runSpacing;
  
  /// المسافة بين العناصر رأسيًا على الهاتف المحمول
  final double? mobileRunSpacing;
  
  /// المسافة بين العناصر رأسيًا على الجهاز اللوحي
  final double? tabletRunSpacing;
  
  /// المسافة بين العناصر رأسيًا على الحاسوب المكتبي
  final double? desktopRunSpacing;
  
  /// محاذاة العناصر أفقيًا
  final WrapAlignment alignment;
  
  /// محاذاة العناصر رأسيًا
  final WrapAlignment runAlignment;
  
  /// محاذاة العناصر داخل المساحة المتاحة
  final WrapCrossAlignment crossAxisAlignment;
  
  /// اتجاه العناصر
  final Axis direction;
  
  /// الحشو حول الغلاف
  final EdgeInsetsGeometry? padding;
  
  /// إنشاء غلاف متجاوب
  const ResponsiveWrap({
    super.key,
    required this.children,
    this.spacing = 8.0,
    this.mobileSpacing,
    this.tabletSpacing,
    this.desktopSpacing,
    this.runSpacing = 8.0,
    this.mobileRunSpacing,
    this.tabletRunSpacing,
    this.desktopRunSpacing,
    this.alignment = WrapAlignment.start,
    this.runAlignment = WrapAlignment.start,
    this.crossAxisAlignment = WrapCrossAlignment.start,
    this.direction = Axis.horizontal,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final deviceType = ResponsiveUtils.getDeviceType(context);
        
        // تحديد المسافات بناءً على نوع الجهاز
        double currentSpacing;
        double currentRunSpacing;
        
        switch (deviceType) {
          case DeviceType.mobile:
            currentSpacing = mobileSpacing ?? spacing;
            currentRunSpacing = mobileRunSpacing ?? runSpacing;
            break;
          case DeviceType.tablet:
            currentSpacing = tabletSpacing ?? spacing;
            currentRunSpacing = tabletRunSpacing ?? runSpacing;
            break;
          case DeviceType.desktop:
          case DeviceType.largeDesktop:
            currentSpacing = desktopSpacing ?? spacing;
            currentRunSpacing = desktopRunSpacing ?? runSpacing;
            break;
        }
        
        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Wrap(
            spacing: currentSpacing,
            runSpacing: currentRunSpacing,
            alignment: alignment,
            runAlignment: runAlignment,
            crossAxisAlignment: crossAxisAlignment,
            direction: direction,
            children: children,
          ),
        );
      },
    );
  }
}
