// lib/core/widgets/user_guide/feature_highlight.dart

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// مكون لتسليط الضوء على ميزة معينة في التطبيق
class FeatureHighlight extends StatefulWidget {
  /// العنصر المراد تسليط الضوء عليه
  final GlobalKey targetKey;

  /// عنوان الشرح
  final String title;

  /// نص الشرح
  final String description;

  /// موقع النص بالنسبة للعنصر المستهدف
  final HighlightPosition position;

  /// دالة يتم استدعاؤها عند إغلاق الشرح
  final VoidCallback? onDismiss;

  /// دالة يتم استدعاؤها عند النقر على زر "التالي"
  final VoidCallback? onNext;

  /// نص زر "التالي"
  final String? nextButtonText;

  /// نص زر "تخطي"
  final String? skipButtonText;

  /// إنشاء مكون تسليط الضوء على ميزة
  const FeatureHighlight({
    super.key,
    required this.targetKey,
    required this.title,
    required this.description,
    this.position = HighlightPosition.bottom,
    this.onDismiss,
    this.onNext,
    this.nextButtonText,
    this.skipButtonText,
  });

  @override
  State<FeatureHighlight> createState() => _FeatureHighlightState();
}

/// موقع النص بالنسبة للعنصر المستهدف
enum HighlightPosition {
  /// فوق العنصر
  top,

  /// أسفل العنصر
  bottom,

  /// يسار العنصر
  left,

  /// يمين العنصر
  right,
}

class _FeatureHighlightState extends State<FeatureHighlight>
    with SingleTickerProviderStateMixin {
  /// متحكم الرسوم المتحركة
  late AnimationController _animationController;

  /// رسوم متحركة للتلاشي
  late Animation<double> _fadeAnimation;

  /// رسوم متحركة للحجم
  late Animation<double> _scaleAnimation;

  /// موقع العنصر المستهدف
  Rect? _targetRect;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // تهيئة الرسوم المتحركة
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // تشغيل الرسوم المتحركة
    _animationController.forward();

    // جدولة حساب موقع العنصر المستهدف بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateTargetRect();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// حساب موقع العنصر المستهدف
  void _calculateTargetRect() {
    final RenderBox? renderBox =
        widget.targetKey.currentContext?.findRenderObject() as RenderBox?;

    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      setState(() {
        _targetRect = Rect.fromLTWH(
          offset.dx,
          offset.dy,
          renderBox.size.width,
          renderBox.size.height,
        );
      });
    }
  }

  /// إغلاق الشرح
  void _dismiss() {
    _animationController.reverse().then((_) {
      widget.onDismiss?.call();
    });
  }

  /// الانتقال إلى الخطوة التالية
  void _next() {
    _animationController.reverse().then((_) {
      widget.onNext?.call();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_targetRect == null) {
      return const SizedBox.shrink();
    }

    return Material(
      type: MaterialType.transparency,
      child: Stack(
        children: [
          // طبقة شفافة للنقر عليها لإغلاق الشرح
          GestureDetector(
            onTap: _dismiss,
            child: Container(
              color: Colors.black54,
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
            ),
          ),

          // رسم دائرة حول العنصر المستهدف
          CustomPaint(
            painter: HighlightPainter(
              targetRect: _targetRect!,
              animation: _fadeAnimation,
            ),
            size: MediaQuery.of(context).size,
          ),

          // مربع الشرح
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: _buildDescriptionBox(context),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// بناء مربع الشرح
  Widget _buildDescriptionBox(BuildContext context) {
    if (_targetRect == null) return const SizedBox.shrink();

    // حساب موقع مربع الشرح بناءً على موقع العنصر المستهدف
    final double boxWidth = MediaQuery.of(context).size.width * 0.8;
    final double boxHeight = 150.0;

    double left = 0.0;
    double top = 0.0;

    switch (widget.position) {
      case HighlightPosition.bottom:
        left = _targetRect!.center.dx - (boxWidth / 2);
        top = _targetRect!.bottom + 20.0;
        break;
      case HighlightPosition.top:
        left = _targetRect!.center.dx - (boxWidth / 2);
        top = _targetRect!.top - boxHeight - 20.0;
        break;
      case HighlightPosition.left:
        left = _targetRect!.left - boxWidth - 20.0;
        top = _targetRect!.center.dy - (boxHeight / 2);
        break;
      case HighlightPosition.right:
        left = _targetRect!.right + 20.0;
        top = _targetRect!.center.dy - (boxHeight / 2);
        break;
    }

    // تأكد من أن مربع الشرح لا يخرج عن حدود الشاشة
    left =
        left.clamp(10.0, MediaQuery.of(context).size.width - boxWidth - 10.0);
    top =
        top.clamp(10.0, MediaQuery.of(context).size.height - boxHeight - 10.0);

    return Positioned(
      left: left,
      top: top,
      child: Container(
        width: boxWidth,
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(51), // 0.2 * 255 = ~51
              blurRadius: 10.0,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: GoogleFonts.cairo(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8.0),
            Text(
              widget.description,
              style: GoogleFonts.cairo(
                fontSize: 14.0,
                color: Colors.black54,
              ),
            ),
            const SizedBox(height: 16.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (widget.skipButtonText != null)
                  TextButton(
                    onPressed: _dismiss,
                    child: Text(
                      widget.skipButtonText!,
                      style: GoogleFonts.cairo(
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                const SizedBox(width: 8.0),
                if (widget.onNext != null)
                  ElevatedButton(
                    onPressed: _next,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    child: Text(
                      widget.nextButtonText ?? 'التالي',
                      style: GoogleFonts.cairo(),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// رسام لتسليط الضوء على العنصر المستهدف
class HighlightPainter extends CustomPainter {
  /// مستطيل العنصر المستهدف
  final Rect targetRect;

  /// رسوم متحركة للتلاشي
  final Animation<double> animation;

  /// إنشاء رسام لتسليط الضوء
  HighlightPainter({
    required this.targetRect,
    required this.animation,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.black.withAlpha((0.7 * animation.value * 255).toInt())
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.dstOut;

    // رسم دائرة حول العنصر المستهدف
    final Path path = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(
        RRect.fromRectAndRadius(
          targetRect.inflate(4.0),
          const Radius.circular(8.0),
        ),
      );

    canvas.drawPath(path, paint);

    // رسم حدود حول العنصر المستهدف
    final Paint borderPaint = Paint()
      ..color = Colors.white.withAlpha((animation.value * 255).toInt())
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        targetRect.inflate(4.0),
        const Radius.circular(8.0),
      ),
      borderPaint,
    );
  }

  @override
  bool shouldRepaint(covariant HighlightPainter oldDelegate) {
    return targetRect != oldDelegate.targetRect ||
        animation != oldDelegate.animation;
  }
}
