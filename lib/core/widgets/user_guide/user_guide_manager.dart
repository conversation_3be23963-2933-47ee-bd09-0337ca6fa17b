// lib/core/widgets/user_guide/user_guide_manager.dart

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'feature_highlight.dart';
import 'guide_step.dart';

/// مدير دليل المستخدم
class UserGuideManager {
  /// مثيل واحد من المدير (Singleton)
  static final UserGuideManager _instance = UserGuideManager._internal();

  /// الحصول على مثيل المدير
  factory UserGuideManager() => _instance;

  /// إنشاء مدير دليل المستخدم
  UserGuideManager._internal();

  /// مفتاح التخزين المحلي لحالة الدليل
  static const String _prefsKeyPrefix = 'user_guide_completed_';

  /// قائمة خطوات الدليل الحالية
  List<GuideStep> _steps = [];

  /// مؤشر الخطوة الحالية
  int _currentStepIndex = 0;

  /// مدخل الطبقة العلوية
  OverlayEntry? _overlayEntry;

  /// دالة يتم استدعاؤها عند اكتمال الدليل
  VoidCallback? _onComplete;

  /// التحقق مما إذا كان الدليل قد تم عرضه من قبل
  Future<bool> hasCompletedGuide(String guideId) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('$_prefsKeyPrefix$guideId') ?? false;
  }

  /// تعيين حالة اكتمال الدليل
  Future<void> setGuideCompleted(String guideId,
      {bool completed = true}) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('$_prefsKeyPrefix$guideId', completed);
  }

  /// إعادة تعيين حالة اكتمال الدليل
  Future<void> resetGuideStatus(String guideId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('$_prefsKeyPrefix$guideId');
  }

  /// بدء عرض دليل المستخدم
  void startGuide(
    BuildContext context, {
    required String guideId,
    required List<GuideStep> steps,
    VoidCallback? onComplete,
  }) async {
    // التحقق من اكتمال الدليل
    final hasCompleted = await hasCompletedGuide(guideId);
    if (hasCompleted) {
      return;
    }

    // تهيئة الدليل
    _steps = steps;
    _currentStepIndex = 0;
    _onComplete = onComplete;

    // عرض الخطوة الأولى
    if (_steps.isNotEmpty) {
      // تخزين سياق البناء في متغير محلي لتجنب استخدامه عبر فجوات غير متزامنة
      final BuildContext localContext = context;

      // استخدام ميكروتاسك لتجنب استخدام BuildContext عبر فجوات غير متزامنة
      Future.microtask(() {
        _showCurrentStep(localContext);
      });
    }
  }

  /// عرض الخطوة الحالية
  void _showCurrentStep(BuildContext context) {
    // إزالة الطبقة العلوية السابقة إن وجدت
    _overlayEntry?.remove();
    _overlayEntry = null;

    // التحقق من وجود خطوات
    if (_currentStepIndex >= _steps.length) {
      _completeGuide();
      return;
    }

    // الحصول على الخطوة الحالية
    final currentStep = _steps[_currentStepIndex];

    // الحصول على OverlayState مباشرة لتجنب استخدام BuildContext عبر فجوات غير متزامنة
    final OverlayState overlayState = Overlay.of(context);

    // إنشاء طبقة علوية جديدة
    _overlayEntry = OverlayEntry(
      builder: (builderContext) => FeatureHighlight(
        targetKey: currentStep.targetKey,
        title: currentStep.title,
        description: currentStep.description,
        position: currentStep.position,
        onDismiss: () {
          _completeGuide();
        },
        onNext: () {
          // استخدام نسخة محلية من BuildContext
          final BuildContext localContext = builderContext;
          _showNextStep(localContext);
        },
        nextButtonText: currentStep.nextButtonText,
        skipButtonText: currentStep.skipButtonText,
      ),
    );

    // عرض الطبقة العلوية
    overlayState.insert(_overlayEntry!);
  }

  /// عرض الخطوة التالية
  void _showNextStep(BuildContext context) {
    _currentStepIndex++;
    _showCurrentStep(context);
  }

  /// إكمال الدليل
  void _completeGuide() {
    // إزالة الطبقة العلوية
    _overlayEntry?.remove();
    _overlayEntry = null;

    // استدعاء دالة الاكتمال
    _onComplete?.call();
  }

  /// إيقاف الدليل
  void stopGuide() {
    _completeGuide();
  }
}
