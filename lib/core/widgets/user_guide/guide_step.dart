// lib/core/widgets/user_guide/guide_step.dart

import 'package:flutter/material.dart';

import 'feature_highlight.dart';

/// خطوة في دليل المستخدم
class GuideStep {
  /// مفتاح العنصر المستهدف
  final GlobalKey targetKey;
  
  /// عنوان الخطوة
  final String title;
  
  /// وصف الخطوة
  final String description;
  
  /// موقع النص بالنسبة للعنصر المستهدف
  final HighlightPosition position;
  
  /// نص زر "التالي"
  final String? nextButtonText;
  
  /// نص زر "تخطي"
  final String? skipButtonText;
  
  /// إنشاء خطوة في دليل المستخدم
  const GuideStep({
    required this.targetKey,
    required this.title,
    required this.description,
    this.position = HighlightPosition.bottom,
    this.nextButtonText,
    this.skipButtonText,
  });
}
