import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for managing the application language
class LanguageProvider extends ChangeNotifier {
  /// Current locale of the application
  Locale _locale = const Locale('ar'); // Default to Arabic

  /// Key for storing the language code in SharedPreferences
  static const String _languageCodeKey = 'language_code';

  /// Get the current locale
  Locale get locale => _locale;

  /// Initialize the provider by loading the saved language
  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLanguageCode = prefs.getString(_languageCodeKey);

    if (savedLanguageCode != null) {
      _locale = Locale(savedLanguageCode);
    }
  }

  /// Flag to prevent multiple simultaneous language changes
  bool _isChangingLanguage = false;

  /// Set the application locale
  Future<void> setLocale(Locale locale) async {
    // Prevent multiple simultaneous changes, but allow changing to different locale
    if (_isChangingLanguage) {
      debugPrint('Language change already in progress, ignoring request');
      return;
    }

    // Skip if same locale to prevent unnecessary rebuilds
    if (_locale == locale) {
      debugPrint('Same locale requested, skipping change');
      return;
    }

    debugPrint('Changing language from ${_locale.languageCode} to ${locale.languageCode}');
    _isChangingLanguage = true;

    try {
      _locale = locale;

      // Save the language code to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageCodeKey, locale.languageCode);

      debugPrint('Language changed successfully to ${_locale.languageCode}');

      // Single notification after all changes are complete
      notifyListeners();
    } catch (e) {
      debugPrint('Error changing language: $e');
    } finally {
      _isChangingLanguage = false;
    }
  }

  /// Check if the current locale is RTL (Right-to-Left)
  bool get isRtl => _locale.languageCode == 'ar';

  /// Check if language change is in progress
  bool get isChangingLanguage => _isChangingLanguage;

  /// Get the appropriate font family based on the current locale
  /// Always returns 'Cairo' for Arabic language
  String get fontFamily => _locale.languageCode == 'ar' ? 'Cairo' : 'Poppins';
}
