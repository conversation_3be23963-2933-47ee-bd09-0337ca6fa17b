// lib/core/utils/unified_export_service.dart

import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:uuid/uuid.dart';

/// Export file format
enum ExportFileFormat {
  png,
  jpg,
  pdf,
}

/// Unified Export Service
/// 
/// This class combines functionality from:
/// - ExportService
/// - EnhancedExportService
/// 
/// Provides tools for exporting widgets as images or PDFs.
class UnifiedExportService {
  // Singleton instance
  static final UnifiedExportService _instance = UnifiedExportService._internal();
  
  // Get singleton instance
  factory UnifiedExportService() => _instance;
  
  // Private constructor
  UnifiedExportService._internal();
  
  /// Capture a widget as an image
  Future<Uint8List?> captureWidget(GlobalKey key, {double pixelRatio = 3.0}) async {
    try {
      // Get the RenderObject
      final RenderRepaintBoundary? boundary = key.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        debugPrint('Error: No RenderRepaintBoundary found');
        return null;
      }
      
      // Capture the image
      final ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        debugPrint('Error: Failed to convert image to bytes');
        return null;
      }
      
      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error capturing widget: $e');
      return null;
    }
  }
  
  /// Export a widget as an image
  Future<String?> exportWidgetAsImage({
    required GlobalKey key,
    required ExportFileFormat format,
    String? fileName,
    double pixelRatio = 3.0,
  }) async {
    try {
      // Capture the widget
      final Uint8List? imageData = await captureWidget(key, pixelRatio: pixelRatio);
      if (imageData == null) {
        return null;
      }
      
      // Save the image
      return saveToFile(
        data: imageData,
        format: format,
        customFileName: fileName,
      );
    } catch (e) {
      debugPrint('Error exporting widget as image: $e');
      return null;
    }
  }
  
  /// Export a widget as a PDF
  Future<String?> exportWidgetAsPdf({
    required GlobalKey key,
    String? fileName,
    double pixelRatio = 3.0,
    PdfPageFormat pageFormat = PdfPageFormat.a4,
    String title = 'Exported Document',
  }) async {
    try {
      // Capture the widget
      final Uint8List? imageData = await captureWidget(key, pixelRatio: pixelRatio);
      if (imageData == null) {
        return null;
      }
      
      // Create a PDF document
      final pdf = pw.Document(
        title: title,
        author: 'Mashair App',
        creator: 'Mashair App',
        subject: 'Exported Card',
      );
      
      // Create a PDF image
      final pdfImage = pw.MemoryImage(imageData);
      
      // Add a page with the image
      pdf.addPage(
        pw.Page(
          pageFormat: pageFormat,
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Image(pdfImage, fit: pw.BoxFit.contain),
            );
          },
        ),
      );
      
      // Save the PDF
      final pdfData = await pdf.save();
      
      return saveToFile(
        data: pdfData,
        format: ExportFileFormat.pdf,
        customFileName: fileName,
      );
    } catch (e) {
      debugPrint('Error exporting widget as PDF: $e');
      return null;
    }
  }
  
  /// Save data to a file
  Future<String?> saveToFile({
    required Uint8List data,
    required ExportFileFormat format,
    String? customFileName,
  }) async {
    try {
      // Get the export directory
      final dir = await getApplicationDocumentsDirectory();
      final exportDir = Directory('${dir.path}/exports');
      
      // Create the directory if it doesn't exist
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }
      
      // Create a unique file name
      final fileName = customFileName ??
          'export_${const Uuid().v4()}${_getFileExtension(format)}';
      final filePath = '${exportDir.path}/$fileName';
      
      // Save the file
      final file = File(filePath);
      await file.writeAsBytes(data);
      
      return filePath;
    } catch (e) {
      debugPrint('Error saving file: $e');
      return null;
    }
  }
  
  /// Get the file extension for a format
  String _getFileExtension(ExportFileFormat format) {
    switch (format) {
      case ExportFileFormat.png:
        return '.png';
      case ExportFileFormat.jpg:
        return '.jpg';
      case ExportFileFormat.pdf:
        return '.pdf';
    }
  }
  
  /// Share a file
  Future<void> shareFile(String filePath, {String? subject, String? text}) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        subject: subject,
        text: text,
      );
    } catch (e) {
      debugPrint('Error sharing file: $e');
    }
  }
  
  /// Share data
  Future<void> shareData(Uint8List data, ExportFileFormat format, {String? subject, String? text}) async {
    try {
      // Save the data to a temporary file
      final filePath = await saveToFile(
        data: data,
        format: format,
        customFileName: 'share_temp${_getFileExtension(format)}',
      );
      
      if (filePath != null) {
        await shareFile(filePath, subject: subject, text: text);
      }
    } catch (e) {
      debugPrint('Error sharing data: $e');
    }
  }
  
  /// Enhance image quality
  Future<Uint8List?> enhanceImageQuality(Uint8List imageData) async {
    try {
      // This is a placeholder for image enhancement
      // In a real app, you would use a library like image or flutter_image_compress
      // to enhance the image quality
      
      return imageData;
    } catch (e) {
      debugPrint('Error enhancing image quality: $e');
      return null;
    }
  }
}
