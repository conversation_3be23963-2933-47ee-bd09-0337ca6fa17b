import 'package:flutter/material.dart';
import 'dart:math' as math;

/// محسن التحويلات - يحسن أداء العمليات الحسابية للتحويلات
class TransformOptimizer {
  /// التحقق من صحة القيم الرقمية
  static bool isValidNumber(double value) {
    return value.isFinite && !value.isNaN;
  }
  
  /// التحقق من صحة الإحداثيات
  static bool isValidOffset(Offset offset) {
    return isValidNumber(offset.dx) && isValidNumber(offset.dy);
  }
  
  /// التحقق من صحة الحجم
  static bool isValidSize(Size size) {
    return isValidNumber(size.width) && 
           isValidNumber(size.height) && 
           size.width > 0 && 
           size.height > 0;
  }
  
  /// تطبيق حدود آمنة على القيم
  static double clampSafe(double value, double min, double max) {
    if (!isValidNumber(value)) return min;
    if (!isValidNumber(min)) min = 0.0;
    if (!isValidNumber(max)) max = 1.0;
    
    return math.max(min, math.min(max, value));
  }
  
  /// تحويل آمن للإحداثيات النسبية إلى مطلقة
  static Offset relativeToAbsoluteSafe(
    double relativeX, 
    double relativeY, 
    double containerWidth, 
    double containerHeight
  ) {
    // التحقق من صحة المدخلات
    if (!isValidNumber(relativeX)) relativeX = 0.0;
    if (!isValidNumber(relativeY)) relativeY = 0.0;
    if (!isValidNumber(containerWidth) || containerWidth <= 0) containerWidth = 1.0;
    if (!isValidNumber(containerHeight) || containerHeight <= 0) containerHeight = 1.0;
    
    // تطبيق الحدود الآمنة
    relativeX = clampSafe(relativeX, 0.0, 1.0);
    relativeY = clampSafe(relativeY, 0.0, 1.0);
    
    return Offset(
      relativeX * containerWidth,
      relativeY * containerHeight,
    );
  }
  
  /// تحويل آمن للإحداثيات المطلقة إلى نسبية
  static Offset absoluteToRelativeSafe(
    double absoluteX, 
    double absoluteY, 
    double containerWidth, 
    double containerHeight
  ) {
    // التحقق من صحة المدخلات
    if (!isValidNumber(absoluteX)) absoluteX = 0.0;
    if (!isValidNumber(absoluteY)) absoluteY = 0.0;
    if (!isValidNumber(containerWidth) || containerWidth <= 0) containerWidth = 1.0;
    if (!isValidNumber(containerHeight) || containerHeight <= 0) containerHeight = 1.0;
    
    return Offset(
      absoluteX / containerWidth,
      absoluteY / containerHeight,
    );
  }
  
  /// تحويل آمن للحجم النسبي إلى مطلق
  static Size relativeSizeToAbsoluteSafe(
    double relativeWidth, 
    double relativeHeight, 
    double containerWidth, 
    double containerHeight
  ) {
    // التحقق من صحة المدخلات
    if (!isValidNumber(relativeWidth)) relativeWidth = 0.1;
    if (!isValidNumber(relativeHeight)) relativeHeight = 0.1;
    if (!isValidNumber(containerWidth) || containerWidth <= 0) containerWidth = 1.0;
    if (!isValidNumber(containerHeight) || containerHeight <= 0) containerHeight = 1.0;
    
    // تطبيق الحدود الآمنة
    relativeWidth = clampSafe(relativeWidth, 0.01, 1.0);
    relativeHeight = clampSafe(relativeHeight, 0.01, 1.0);
    
    return Size(
      relativeWidth * containerWidth,
      relativeHeight * containerHeight,
    );
  }
  
  /// تحويل آمن للحجم المطلق إلى نسبي
  static Size absoluteSizeToRelativeSafe(
    double absoluteWidth, 
    double absoluteHeight, 
    double containerWidth, 
    double containerHeight
  ) {
    // التحقق من صحة المدخلات
    if (!isValidNumber(absoluteWidth) || absoluteWidth <= 0) absoluteWidth = 10.0;
    if (!isValidNumber(absoluteHeight) || absoluteHeight <= 0) absoluteHeight = 10.0;
    if (!isValidNumber(containerWidth) || containerWidth <= 0) containerWidth = 1.0;
    if (!isValidNumber(containerHeight) || containerHeight <= 0) containerHeight = 1.0;
    
    return Size(
      absoluteWidth / containerWidth,
      absoluteHeight / containerHeight,
    );
  }
  
  /// تحسين زاوية الدوران
  static double optimizeRotation(double rotation) {
    if (!isValidNumber(rotation)) return 0.0;
    
    // تطبيع الزاوية إلى النطاق [0, 2π]
    while (rotation < 0) {
      rotation += 2 * math.pi;
    }
    while (rotation >= 2 * math.pi) {
      rotation -= 2 * math.pi;
    }
    
    return rotation;
  }
  
  /// تحسين المقياس
  static double optimizeScale(double scale) {
    if (!isValidNumber(scale) || scale <= 0) return 1.0;
    
    // تطبيق حدود معقولة للمقياس
    return clampSafe(scale, 0.1, 10.0);
  }
  
  /// حساب المسافة بين نقطتين
  static double distanceBetween(Offset point1, Offset point2) {
    if (!isValidOffset(point1) || !isValidOffset(point2)) return 0.0;
    
    final dx = point2.dx - point1.dx;
    final dy = point2.dy - point1.dy;
    
    return math.sqrt(dx * dx + dy * dy);
  }
  
  /// حساب الزاوية بين نقطتين
  static double angleBetween(Offset point1, Offset point2) {
    if (!isValidOffset(point1) || !isValidOffset(point2)) return 0.0;
    
    final dx = point2.dx - point1.dx;
    final dy = point2.dy - point1.dy;
    
    return math.atan2(dy, dx);
  }
  
  /// تحسين موضع العنصر ليبقى داخل الحدود
  static Offset constrainToContainer(
    Offset position, 
    Size elementSize, 
    Size containerSize,
    {double margin = 0.0}
  ) {
    if (!isValidOffset(position)) position = Offset.zero;
    if (!isValidSize(elementSize)) elementSize = const Size(50, 50);
    if (!isValidSize(containerSize)) containerSize = const Size(300, 400);
    
    final double minX = margin;
    final double minY = margin;
    final double maxX = containerSize.width - elementSize.width - margin;
    final double maxY = containerSize.height - elementSize.height - margin;
    
    return Offset(
      clampSafe(position.dx, minX, math.max(minX, maxX)),
      clampSafe(position.dy, minY, math.max(minY, maxY)),
    );
  }
  
  /// تحسين حجم العنصر ليناسب الحاوية
  static Size constrainSizeToContainer(
    Size size,
    Size containerSize,
    {double minWidth = 20.0, double minHeight = 20.0}
  ) {
    if (!isValidSize(size)) size = Size(minWidth, minHeight);
    if (!isValidSize(containerSize)) containerSize = const Size(300, 400);

    return Size(
      clampSafe(size.width, minWidth, containerSize.width),
      clampSafe(size.height, minHeight, containerSize.height),
    );
  }



  /// حساب مصفوفة التحويل
  static Matrix4 calculateTransformMatrix({
    required Offset position,
    required Size size,
    required double rotation,
    required double scale,
    Offset? anchor,
  }) {
    final transform = Matrix4.identity();

    // التحقق من صحة القيم
    if (!isValidOffset(position) || !isValidSize(size) ||
        !isValidNumber(rotation) || !isValidNumber(scale)) {
      return transform;
    }

    // تطبيق الترجمة
    transform.translate(position.dx, position.dy);

    // تطبيق الدوران حول النقطة المرجعية
    if (anchor != null && isValidOffset(anchor)) {
      transform.translate(anchor.dx, anchor.dy);
      transform.rotateZ(rotation);
      transform.translate(-anchor.dx, -anchor.dy);
    } else {
      // الدوران حول المركز
      final centerX = size.width / 2;
      final centerY = size.height / 2;
      transform.translate(centerX, centerY);
      transform.rotateZ(rotation);
      transform.translate(-centerX, -centerY);
    }

    // تطبيق المقياس
    transform.scale(scale, scale);

    return transform;
  }
}
