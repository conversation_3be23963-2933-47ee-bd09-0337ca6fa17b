// lib/core/utils/coordinate_system.dart
import 'package:flutter/material.dart';
import '../constants/app_sizes.dart';

/// نظام تحويل الإحداثيات الموحد
/// يستخدم لتحويل الإحداثيات بين النظام النسبي والمطلق
/// لضمان عرض العناصر بشكل صحيح بغض النظر عن حجم الشاشة
class CoordinateSystem {
  /// عرض الحاوية
  final double containerWidth;

  /// ارتفاع الحاوية
  final double containerHeight;

  /// الأبعاد القياسية للبطاقة في التطبيق
  static const double standardCardWidth = AppSizes.cardWidthMobile;
  static const double standardCardHeight = AppSizes.cardHeightMobile;

  /// الأبعاد القياسية للبطاقة في لوحة التحكم
  static const double adminCardWidth = AppSizes.cardWidthAdmin;
  static const double adminCardHeight = AppSizes.cardHeightAdmin;

  /// عوامل التحويل بين لوحة التحكم والتطبيق
  /// هذه العوامل تضمن أن العناصر تظهر بنفس النسب في كل من لوحة التحكم والتطبيق
  static const double widthScaleFactor = AppSizes.cardWidthScaleFactor;
  static const double heightScaleFactor = AppSizes.cardHeightScaleFactor;

  /// إنشاء نظام تحويل الإحداثيات
  CoordinateSystem({
    required this.containerWidth,
    required this.containerHeight,
  });

  /// تحويل من إحداثيات نسبية إلى مطلقة
  /// يستخدم لتحويل الإحداثيات من النظام النسبي (0-1) إلى النظام المطلق (بالبكسل)
  Offset relativeToAbsolute(double relativeX, double relativeY) {
    return Offset(
      relativeX * containerWidth,
      relativeY * containerHeight,
    );
  }

  /// تحويل من أبعاد نسبية إلى مطلقة
  /// يستخدم لتحويل الأبعاد من النظام النسبي (0-1) إلى النظام المطلق (بالبكسل)
  Size relativeSizeToAbsolute(double relativeWidth, double relativeHeight) {
    return Size(
      relativeWidth * containerWidth,
      relativeHeight * containerHeight,
    );
  }

  /// تحويل من إحداثيات مطلقة إلى نسبية
  /// يستخدم لتحويل الإحداثيات من النظام المطلق (بالبكسل) إلى النظام النسبي (0-1)
  Offset absoluteToRelative(double absoluteX, double absoluteY) {
    return Offset(
      absoluteX / containerWidth,
      absoluteY / containerHeight,
    );
  }

  /// تحويل من أبعاد مطلقة إلى نسبية
  /// يستخدم لتحويل الأبعاد من النظام المطلق (بالبكسل) إلى النظام النسبي (0-1)
  Size absoluteSizeToRelative(double absoluteWidth, double absoluteHeight) {
    return Size(
      absoluteWidth / containerWidth,
      absoluteHeight / containerHeight,
    );
  }

  /// تحديد ما إذا كانت القيمة نسبية (بين 0 و 1)
  bool isRelativeValue(double value) {
    return value >= 0 && value <= 1;
  }

  /// تحديد ما إذا كانت الإحداثيات نسبية
  bool areRelativeCoordinates(double width, double height) {
    return isRelativeValue(width) && isRelativeValue(height);
  }

  /// تحويل عنصر من لوحة التحكم إلى التطبيق
  /// يستخدم لتحويل الإحداثيات من لوحة التحكم إلى التطبيق
  /// مع تطبيق عامل التحويل بين لوحة التحكم والتطبيق
  static Map<String, dynamic> normalizeElementForStorage(
      Map<String, dynamic> element) {
    // التأكد من أن الإحداثيات نسبية
    double x = (element['x'] as num).toDouble();
    double y = (element['y'] as num).toDouble();
    double width = (element['width'] as num).toDouble();
    double height = (element['height'] as num).toDouble();

    // استخراج نوع العنصر
    String elementType = '';
    if (element.containsKey('type') && element['type'] is String) {
      elementType = element['type'] as String;
    }

    bool isImageOrSticker = elementType.contains('image') || elementType.contains('sticker');

    // تحديد ما إذا كانت الإحداثيات نسبية بشكل صريح
    // نعتمد أولاً على الخاصية isRelativeCoordinates إذا كانت موجودة
    // وإلا نحدد بناءً على قيم الأبعاد (إذا كانت <= 1 فهي نسبية)
    bool isRelative = element['isRelativeCoordinates'] as bool? ??
        (width <= 1 && height <= 1);

    // تحديد ما إذا كان العنصر من لوحة التحكم
    bool isFromAdminPanel = element['isFromAdminPanel'] as bool? ?? false;

    // طباعة معلومات العنصر للتصحيح
    debugPrint('=== تحويل عنصر للتخزين ===');
    debugPrint('النوع: $elementType');
    debugPrint('الإحداثيات الأصلية: ($x, $y), (${width}x$height)');
    debugPrint('نسبي: $isRelative, من لوحة التحكم: $isFromAdminPanel');

    // إذا كانت الإحداثيات مطلقة، نحولها إلى نسبية
    if (!isRelative) {
      // تحديد أبعاد البطاقة المستخدمة للتحويل
      final double cardWidth = isFromAdminPanel ? adminCardWidth : standardCardWidth;
      final double cardHeight = isFromAdminPanel ? adminCardHeight : standardCardHeight;

      final coordinateSystem = CoordinateSystem(
        containerWidth: cardWidth,
        containerHeight: cardHeight,
      );

      final relativePos = coordinateSystem.absoluteToRelative(x, y);
      final relativeSize = coordinateSystem.absoluteSizeToRelative(width, height);

      x = relativePos.dx;
      y = relativePos.dy;
      width = relativeSize.width;
      height = relativeSize.height;

      debugPrint('تم تحويل الإحداثيات من مطلقة إلى نسبية:');
      debugPrint('الإحداثيات بعد التحويل: ($x, $y), (${width}x$height)');
    }

    // إذا كان العنصر من لوحة التحكم، نطبق عامل التحويل
    // هذا يضمن أن العناصر تظهر بنفس النسب في كل من لوحة التحكم والتطبيق
    if (isFromAdminPanel) {
      debugPrint('تطبيق عامل التحويل بين لوحة التحكم والتطبيق:');
      debugPrint('الإحداثيات قبل التحويل: ($x, $y), (${width}x$height)');

      // تطبيق عامل التحويل على الإحداثيات والأبعاد
      // للصور والملصقات: نطبق عامل التحويل على الموقع فقط
      // للنصوص والعناصر الأخرى: نطبق عامل التحويل على الموقع والأبعاد
      x = x * widthScaleFactor;
      y = y * heightScaleFactor;

      if (!isImageOrSticker) {
        width = width * widthScaleFactor;
        height = height * heightScaleFactor;
      }

      debugPrint('الإحداثيات بعد التحويل: ($x, $y), (${width}x$height)');
    }

    // إنشاء نسخة من العنصر مع تحديث الإحداثيات
    final normalizedElement = Map<String, dynamic>.from(element);
    normalizedElement['x'] = x;
    normalizedElement['y'] = y;
    normalizedElement['width'] = width;
    normalizedElement['height'] = height;
    normalizedElement['isRelativeCoordinates'] = true;
    normalizedElement['isFromAdminPanel'] = isFromAdminPanel;

    // إذا كان العنصر من نوع النص وكان من لوحة التحكم، نطبق عامل التحويل على حجم الخط
    if (elementType.contains('text') && isFromAdminPanel && normalizedElement.containsKey('fontSize')) {
      double fontSize = (normalizedElement['fontSize'] as num).toDouble();
      fontSize = fontSize * heightScaleFactor;
      normalizedElement['fontSize'] = fontSize;
      debugPrint('تم تعديل حجم الخط من ${element['fontSize']} إلى $fontSize');
    }

    debugPrint('============================');

    return normalizedElement;
  }

  /// تحويل قائمة من العناصر من لوحة التحكم إلى التطبيق
  static List<Map<String, dynamic>> normalizeElementsForStorage(
      List<dynamic> elements) {
    return elements.map((element) {
      if (element is Map<String, dynamic>) {
        return normalizeElementForStorage(element);
      }
      return element as Map<String, dynamic>;
    }).toList();
  }

  /// تحويل إحداثيات عنصر من لوحة التحكم إلى التطبيق
  /// يستخدم عند عرض العناصر في التطبيق
  static Map<String, double> convertAdminToMobileCoordinates(
      double x, double y, double width, double height, bool isRelative, {
      bool isImageOrSticker = false,
      double? fontSize,
  }) {
    // طباعة معلومات العنصر قبل التحويل
    debugPrint('=== تحويل إحداثيات عنصر من لوحة التحكم إلى التطبيق ===');
    debugPrint('الإحداثيات قبل التحويل: ($x, $y), (${width}x$height)');
    debugPrint('isRelative: $isRelative, isImageOrSticker: $isImageOrSticker');
    debugPrint('عوامل التحويل: ($widthScaleFactor, $heightScaleFactor)');

    // إذا كانت الإحداثيات نسبية، نطبق عامل التحويل مباشرة
    if (isRelative) {
      // تطبيق عامل التحويل على الإحداثيات
      double newX = x * widthScaleFactor;
      double newY = y * heightScaleFactor;

      // تطبيق عامل التحويل على الأبعاد
      // للصور والملصقات: نحافظ على الأبعاد الأصلية
      // للنصوص والعناصر الأخرى: نطبق عامل التحويل على الأبعاد
      double newWidth, newHeight;
      if (isImageOrSticker) {
        // للصور والملصقات: نحافظ على نسبة الأبعاد الأصلية
        newWidth = width * widthScaleFactor;
        newHeight = height * heightScaleFactor;
      } else {
        // للنصوص والعناصر الأخرى: نطبق عامل التحويل على الأبعاد
        newWidth = width * widthScaleFactor;
        newHeight = height * heightScaleFactor;
      }

      // إنشاء النتيجة
      final result = {
        'x': newX,
        'y': newY,
        'width': newWidth,
        'height': newHeight,
      };

      // إضافة حجم الخط إذا كان متاحًا
      if (fontSize != null) {
        result['fontSize'] = fontSize * heightScaleFactor;
      }

      debugPrint('الإحداثيات بعد التحويل: (${result['x']}, ${result['y']}), (${result['width']}x${result['height']})');
      if (fontSize != null) {
        debugPrint('حجم الخط بعد التحويل: ${result['fontSize']}');
      }
      debugPrint('============================');

      return result;
    }
    // إذا كانت الإحداثيات مطلقة، نحولها أولاً إلى نسبية ثم نطبق عامل التحويل
    else {
      // تحويل الإحداثيات المطلقة إلى نسبية
      final coordinateSystem = CoordinateSystem(
        containerWidth: adminCardWidth,
        containerHeight: adminCardHeight,
      );

      final relativePos = coordinateSystem.absoluteToRelative(x, y);
      final relativeSize = coordinateSystem.absoluteSizeToRelative(width, height);

      debugPrint('الإحداثيات النسبية: (${relativePos.dx}, ${relativePos.dy}), (${relativeSize.width}x${relativeSize.height})');

      // تطبيق عامل التحويل على الإحداثيات النسبية
      double newX = relativePos.dx * widthScaleFactor;
      double newY = relativePos.dy * heightScaleFactor;

      // تطبيق عامل التحويل على الأبعاد
      // للصور والملصقات: نحافظ على الأبعاد الأصلية
      // للنصوص والعناصر الأخرى: نطبق عامل التحويل على الأبعاد
      double newWidth, newHeight;
      if (isImageOrSticker) {
        // للصور والملصقات: نحافظ على نسبة الأبعاد الأصلية
        newWidth = relativeSize.width * widthScaleFactor;
        newHeight = relativeSize.height * heightScaleFactor;
      } else {
        // للنصوص والعناصر الأخرى: نطبق عامل التحويل على الأبعاد
        newWidth = relativeSize.width * widthScaleFactor;
        newHeight = relativeSize.height * heightScaleFactor;
      }

      // إنشاء النتيجة
      final result = {
        'x': newX,
        'y': newY,
        'width': newWidth,
        'height': newHeight,
      };

      // إضافة حجم الخط إذا كان متاحًا
      if (fontSize != null) {
        result['fontSize'] = fontSize * heightScaleFactor;
      }

      debugPrint('الإحداثيات بعد التحويل: (${result['x']}, ${result['y']}), (${result['width']}x${result['height']})');
      if (fontSize != null) {
        debugPrint('حجم الخط بعد التحويل: ${result['fontSize']}');
      }
      debugPrint('============================');

      return result;
    }
  }
}
