// lib/core/utils/card_converter.dart

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:mashair/core/constants/card_constants.dart';
import 'package:mashair/core/models/unified_card.dart';
import 'package:mashair/core/models/unified_card_element.dart';
import 'package:mashair/features/create_card/domain/entities/card_element.dart';
import 'package:mashair/features/home/<USER>/datasources/home_remote_data_source.dart';
import 'package:uuid/uuid.dart';

/// أداة مساعدة لتحويل بين نماذج البطاقات المختلفة
class CardConverter {
  static final _uuid = Uuid();

  /// تحويل من نموذج البطاقة الموحد إلى نموذج البطاقة في التطبيق
  /// يتم التأكد من أن الإحداثيات نسبية وتطبيق عامل التحويل المناسب
  static List<CardElement> unifiedElementsToAppElements(List<UnifiedCardElement> unifiedElements) {
    return unifiedElements.map((unifiedElement) {
      // طباعة معلومات العنصر للتصحيح
      debugPrint('=== تحويل عنصر من النموذج الموحد إلى التطبيق ===');
      debugPrint('المعرف: ${unifiedElement.id}');
      debugPrint('النوع: ${unifiedElement.type}');
      debugPrint('المحتوى: ${unifiedElement.content.substring(0, math.min(50, unifiedElement.content.length))}...');
      debugPrint('الإحداثيات: (${unifiedElement.x}, ${unifiedElement.y})');
      debugPrint('الأبعاد: (${unifiedElement.width}x${unifiedElement.height})');
      debugPrint('الدوران: ${unifiedElement.rotation}');
      debugPrint('المقياس: ${unifiedElement.scale}');
      debugPrint('isRelativeCoordinates: ${unifiedElement.isRelativeCoordinates}');
      debugPrint('isFromAdminPanel: ${unifiedElement.isFromAdminPanel}');

      // تحويل نوع العنصر
      ElementType elementType;
      switch (unifiedElement.type) {
        case UnifiedElementType.text:
          elementType = ElementType.text;
          break;
        case UnifiedElementType.image:
          elementType = ElementType.image;
          break;
        case UnifiedElementType.sticker:
          elementType = ElementType.sticker;
          break;
        case UnifiedElementType.signature:
          elementType = ElementType.signature;
          break;
        case UnifiedElementType.qrCode:
          elementType = ElementType.qrCode;
          break;
      }

      // تحويل محاذاة النص
      String? textAlign;
      if (unifiedElement.textAlign != null) {
        switch (unifiedElement.textAlign) {
          case TextAlign.start:
            textAlign = 'start';
            break;
          case TextAlign.center:
            textAlign = 'center';
            break;
          case TextAlign.end:
            textAlign = 'end';
            break;
          case TextAlign.left:
            textAlign = 'left';
            break;
          case TextAlign.right:
            textAlign = 'right';
            break;
          case TextAlign.justify:
            textAlign = 'justify';
            break;
          default:
            textAlign = 'start';
        }
      }

      // تحويل لون النص
      int? colorValue;
      if (unifiedElement.fontColor != null) {
        final colorStr = unifiedElement.fontColor!;
        if (colorStr.startsWith('#')) {
          colorValue = int.parse('FF${colorStr.substring(1)}', radix: 16);
        } else {
          colorValue = int.parse('FF$colorStr', radix: 16);
        }
      }

      // نستخدم الإحداثيات النسبية كما هي
      // لأننا نريد أن تظهر العناصر بالضبط كما تم تصميمها في لوحة التحكم
      double x = unifiedElement.x;
      double y = unifiedElement.y;
      double width = unifiedElement.width;
      double height = unifiedElement.height;
      double fontSize = unifiedElement.fontSize ?? 16.0;

      // تحديد ما إذا كانت الإحداثيات نسبية (للتصحيح فقط)
      debugPrint('الإحداثيات نسبية: ${unifiedElement.isRelativeCoordinates || (width <= 1 && height <= 1)}');

      // نتأكد من أن الإحداثيات نسبية (0-1)
      if (!CardConstants.areCoordinatesRelative(width, height) || x > 1 || y > 1) {
        debugPrint('تحذير: الإحداثيات ليست نسبية، سيتم تحويلها');

        // استخدام الثوابت الموحدة للتحويل
        final converted = CardConstants.convertAbsoluteToRelative(
          x: x,
          y: y,
          width: width,
          height: height,
          isFromAdminPanel: unifiedElement.isFromAdminPanel,
        );

        x = converted['x']!;
        y = converted['y']!;
        width = converted['width']!;
        height = converted['height']!;

        debugPrint('الإحداثيات بعد التحويل: ($x, $y), (${width}x$height)');
      }

      debugPrint('استخدام الإحداثيات النسبية: ($x, $y), (${width}x$height)');

      // إنشاء عنصر التطبيق
      final appElement = CardElement(
        id: unifiedElement.id,
        type: elementType,
        content: unifiedElement.content,
        x: x,
        y: y,
        width: width,
        height: height,
        fontSize: fontSize,
        isBold: unifiedElement.isBold ?? false,
        isItalic: unifiedElement.isItalic ?? false,
        isUnderline: unifiedElement.isUnderline ?? false,
        fontFamily: unifiedElement.fontFamily,
        colorValue: colorValue,
        textAlign: textAlign,
        letterSpacing: unifiedElement.letterSpacing,
        lineHeight: unifiedElement.lineHeight,
        scale: unifiedElement.scale,
        rotation: unifiedElement.rotation,
        isRelativeCoordinates: true, // دائمًا نستخدم إحداثيات نسبية في التطبيق
        isFromAdminPanel: unifiedElement.isFromAdminPanel,
        // نقل الإحداثيات والقياسات النهائية
        finalX: unifiedElement.finalX,
        finalY: unifiedElement.finalY,
        finalWidth: unifiedElement.finalWidth,
        finalHeight: unifiedElement.finalHeight,
      );

      debugPrint('تم إنشاء عنصر التطبيق: النوع=${appElement.type}, '
          'الإحداثيات=(${appElement.x}, ${appElement.y}), '
          'الأبعاد=(${appElement.width}x${appElement.height}), '
          'isRelativeCoordinates=${appElement.isRelativeCoordinates}');
      debugPrint('============================');

      return appElement;
    }).toList();
  }

  /// تحويل من نموذج البطاقة في التطبيق إلى نموذج البطاقة الموحد
  /// يتم التأكد من أن الإحداثيات نسبية وتخزين فقط البيانات الضرورية لكل نوع من العناصر
  static List<UnifiedCardElement> appElementsToUnifiedElements(List<CardElement> appElements) {
    return appElements.map((appElement) {
      // طباعة معلومات العنصر للتصحيح
      debugPrint('=== تحويل عنصر من التطبيق إلى النموذج الموحد ===');
      debugPrint('النوع: ${appElement.type}');
      debugPrint('الإحداثيات: (${appElement.x}, ${appElement.y})');
      debugPrint('الأبعاد: (${appElement.width}x${appElement.height})');
      debugPrint('isRelativeCoordinates: ${appElement.isRelativeCoordinates}');
      debugPrint('isFromAdminPanel: ${appElement.isFromAdminPanel}');

      // تحويل نوع العنصر
      UnifiedElementType elementType;
      switch (appElement.type) {
        case ElementType.text:
          elementType = UnifiedElementType.text;
          break;
        case ElementType.image:
          elementType = UnifiedElementType.image;
          break;
        case ElementType.sticker:
          elementType = UnifiedElementType.sticker;
          break;
        case ElementType.signature:
          elementType = UnifiedElementType.signature;
          break;
        case ElementType.qrCode:
          elementType = UnifiedElementType.qrCode;
          break;
      }

      // تحويل محاذاة النص
      TextAlign? textAlign;
      if (appElement.textAlign != null) {
        switch (appElement.textAlign) {
          case 'start':
            textAlign = TextAlign.start;
            break;
          case 'center':
            textAlign = TextAlign.center;
            break;
          case 'end':
            textAlign = TextAlign.end;
            break;
          case 'left':
            textAlign = TextAlign.left;
            break;
          case 'right':
            textAlign = TextAlign.right;
            break;
          case 'justify':
            textAlign = TextAlign.justify;
            break;
          default:
            textAlign = TextAlign.start;
        }
      }

      // تحويل لون النص
      String? fontColor;
      if (appElement.colorValue != null) {
        fontColor = '#${appElement.colorValue!.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
      }

      // التأكد من أن الإحداثيات نسبية
      double x = appElement.x;
      double y = appElement.y;
      double width = appElement.width;
      double height = appElement.height;

      // تحديد ما إذا كانت الإحداثيات نسبية
      bool isRelative = appElement.isRelativeCoordinates || (width <= 1 && height <= 1);

      if (!isRelative) {
        // تحويل الإحداثيات المطلقة إلى نسبية باستخدام الثوابت الموحدة
        final converted = CardConstants.convertAbsoluteToRelative(
          x: x,
          y: y,
          width: width,
          height: height,
          isFromAdminPanel: appElement.isFromAdminPanel,
        );

        x = converted['x']!;
        y = converted['y']!;
        width = converted['width']!;
        height = converted['height']!;

        debugPrint('تم تحويل الإحداثيات من مطلقة إلى نسبية: '
            'من (${appElement.x}, ${appElement.y}, ${appElement.width}x${appElement.height}) '
            'إلى ($x, $y, ${width}x$height)');
      }

      // إنشاء عنصر موحد مع البيانات المناسبة لنوعه
      final unifiedElement = UnifiedCardElement(
        id: appElement.id,
        type: elementType,
        content: appElement.content,
        x: x,
        y: y,
        width: width,
        height: height,
        // البيانات الخاصة بالنص فقط إذا كان العنصر من نوع النص
        fontSize: elementType == UnifiedElementType.text ? appElement.fontSize : null,
        isBold: elementType == UnifiedElementType.text ? appElement.isBold : null,
        isItalic: elementType == UnifiedElementType.text ? appElement.isItalic : null,
        isUnderline: elementType == UnifiedElementType.text ? appElement.isUnderline : null,
        fontFamily: elementType == UnifiedElementType.text ? appElement.fontFamily :
                   (elementType == UnifiedElementType.signature ? appElement.fontFamily : null),
        fontColor: elementType == UnifiedElementType.text ? fontColor : null,
        textAlign: elementType == UnifiedElementType.text ? textAlign : null,
        letterSpacing: elementType == UnifiedElementType.text ? appElement.letterSpacing : null,
        lineHeight: elementType == UnifiedElementType.text ? appElement.lineHeight : null,
        // البيانات المشتركة بين جميع أنواع العناصر
        scale: appElement.scale,
        rotation: appElement.rotation,
        isRelativeCoordinates: true, // دائمًا نخزن الإحداثيات بشكل نسبي
        isFromAdminPanel: appElement.isFromAdminPanel,
        // نقل الإحداثيات والقياسات النهائية
        finalX: appElement.finalX,
        finalY: appElement.finalY,
        finalWidth: appElement.finalWidth,
        finalHeight: appElement.finalHeight,
      );

      debugPrint('تم إنشاء عنصر موحد: النوع=${unifiedElement.type}, '
          'الإحداثيات=(${unifiedElement.x}, ${unifiedElement.y}), '
          'الأبعاد=(${unifiedElement.width}x${unifiedElement.height}), '
          'isRelativeCoordinates=${unifiedElement.isRelativeCoordinates}');
      debugPrint('============================');

      return unifiedElement;
    }).toList();
  }

  /// تحويل من نموذج البطاقة في التطبيق إلى نموذج البطاقة الموحد
  static UnifiedCard greetingCardToUnifiedCard(GreetingCardModel greetingCard) {
    // تحويل عناصر البطاقة
    List<UnifiedCardElement> unifiedElements = [];
    if (greetingCard.elements.isNotEmpty) {
      for (final element in greetingCard.elements) {
        if (element is Map<String, dynamic>) {
          unifiedElements.add(UnifiedCardElement.fromMap(element));
        }
      }
    }

    return UnifiedCard(
      id: greetingCard.id,
      title: greetingCard.title,
      description: greetingCard.description,
      occasionId: greetingCard.occasion,
      imageUrl: greetingCard.imageUrl,
      popularity: greetingCard.popularity,
      visits: greetingCard.visits,
      backgroundColor: greetingCard.backgroundColor,
      elements: unifiedElements,
      createdAt: greetingCard.createdAt,
    );
  }

  /// تحويل من نموذج البطاقة الموحد إلى نموذج البطاقة في التطبيق
  static GreetingCardModel unifiedCardToGreetingCard(UnifiedCard unifiedCard) {
    // تحويل عناصر البطاقة
    List<Map<String, dynamic>> elements = unifiedCard.elements.map((e) => e.toMap()).toList();

    return GreetingCardModel(
      id: unifiedCard.id,
      title: unifiedCard.title,
      description: unifiedCard.description,
      imageUrl: unifiedCard.imageUrl,
      occasion: unifiedCard.occasionId,
      popularity: unifiedCard.popularity,
      backgroundColor: unifiedCard.backgroundColor,
      elements: elements,
      visits: unifiedCard.visits,
      createdAt: unifiedCard.createdAt,
    );
  }

  /// إنشاء معرف فريد للعنصر
  static String generateElementId() {
    return 'element_${_uuid.v4()}';
  }
}
