import 'package:flutter/material.dart';
import 'occasion_translator.dart';

/// مساعد للتعامل مع المناسبات في التطبيق
///
/// يوفر هذا الصف وظائف مساعدة للتعامل مع المناسبات، مثل ترجمة أسماء المناسبات
/// والحصول على معرفات المناسبات من أسمائها.
class OccasionHelper {
  /// ترجمة اسم المناسبة إلى اللغة الحالية
  ///
  /// [context] سياق التطبيق للحصول على اللغة الحالية
  /// [arabicName] اسم المناسبة بالعربية
  /// يرجع اسم المناسبة باللغة الحالية
  static String translateOccasionName(BuildContext context, String arabicName) {
    final currentLocale = Localizations.localeOf(context);
    return OccasionTranslator.translateOccasionName(arabicName, currentLocale);
  }

  /// ترجمة معرف المناسبة إلى اسم باللغة الحالية
  ///
  /// [context] سياق التطبيق للحصول على اللغة الحالية
  /// [occasionId] معرف المناسبة (يمكن أن يكون String أو int)
  /// يرجع اسم المناسبة باللغة الحالية
  static String translateOccasionId(BuildContext context, dynamic occasionId) {
    final currentLocale = Localizations.localeOf(context);
    return OccasionTranslator.translateOccasionId(occasionId, currentLocale);
  }

  /// الحصول على معرف المناسبة من اسمها بالعربية
  ///
  /// [arabicName] اسم المناسبة بالعربية
  /// يرجع معرف المناسبة، أو null إذا لم يتم العثور عليه
  static String? getOccasionIdFromName(String arabicName) {
    // البحث عن المعرف المقابل للاسم العربي
    for (final entry in OccasionTranslator.occasionIdToArabicName.entries) {
      if (entry.value == arabicName) {
        return entry.key;
      }
    }
    return null;
  }

  /// الحصول على اسم المناسبة بالعربية من معرفها
  ///
  /// [occasionId] معرف المناسبة (يمكن أن يكون String أو int)
  /// يرجع اسم المناسبة بالعربية، أو المعرف نفسه إذا لم يتم العثور عليه
  static String getArabicNameFromId(dynamic occasionId) {
    // تحويل معرف المناسبة إلى نص (String) إذا لم يكن كذلك
    final String safeOccasionId = occasionId.toString();

    // البحث عن اسم المناسبة بالعربية
    final arabicName =
        OccasionTranslator.occasionIdToArabicName[safeOccasionId];
    if (arabicName != null) {
      return arabicName;
    }

    // محاولة البحث عن المعرف بدون البادئة sticker_category_
    if (safeOccasionId.startsWith('sticker_category_')) {
      final shortId = safeOccasionId.replaceFirst('sticker_category_', '');
      for (final entry in OccasionTranslator.occasionIdToArabicName.entries) {
        if (entry.key.contains(shortId)) {
          return entry.value;
        }
      }
    }

    return safeOccasionId;
  }

  /// التحقق مما إذا كان اسم المناسبة يطابق معرفًا معينًا
  ///
  /// [occasionName] اسم المناسبة (بأي لغة)
  /// [occasionId] معرف المناسبة (يمكن أن يكون String أو int)
  /// [context] سياق التطبيق للحصول على اللغة الحالية
  /// يرجع true إذا كان الاسم يطابق المعرف، وfalse خلاف ذلك
  static bool isOccasionNameMatchingId(
    String occasionName,
    dynamic occasionId,
    BuildContext context,
  ) {
    // تحويل معرف المناسبة إلى نص (String) إذا لم يكن كذلك
    final String safeOccasionId = occasionId.toString();

    // الحصول على اسم المناسبة بالعربية من المعرف
    final arabicName = getArabicNameFromId(safeOccasionId);

    // الحصول على اسم المناسبة باللغة الحالية
    final translatedName = translateOccasionName(context, arabicName);

    // مقارنة الاسم المترجم مع الاسم المقدم
    return occasionName == translatedName ||
        occasionName == arabicName ||
        occasionName.toLowerCase() == translatedName.toLowerCase() ||
        occasionName.toLowerCase() == arabicName.toLowerCase();
  }
}
