// lib/core/utils/unified_element_freeze_manager.dart

import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Unified Element Freeze Manager
/// 
/// This class combines functionality from:
/// - ElementFreezeManager
/// 
/// Provides tools for freezing inactive elements to improve performance.
class UnifiedElementFreezeManager {
  // Singleton instance
  static final UnifiedElementFreezeManager _instance = UnifiedElementFreezeManager._internal();
  
  // Get singleton instance
  factory UnifiedElementFreezeManager() => _instance;
  
  // Private constructor
  UnifiedElementFreezeManager._internal();
  
  // Map to store element keys
  final Map<String, GlobalKey> _elementKeys = {};
  
  // Map to store element freeze state
  final Map<String, bool> _isFrozen = {};
  
  // Map to store frozen element images
  final Map<String, ui.Image> _frozenImages = {};
  
  /// Register an element key
  void registerElementKey(String elementId, GlobalKey key) {
    _elementKeys[elementId] = key;
  }
  
  /// Unregister an element key
  void unregisterElementKey(String elementId) {
    _elementKeys.remove(elementId);
    _isFrozen.remove(elementId);
    _frozenImages.remove(elementId);
  }
  
  /// Freeze an element
  Future<bool> freezeElement(String elementId) async {
    if (!_elementKeys.containsKey(elementId)) {
      debugPrint('Cannot freeze element $elementId: No key registered');
      return false;
    }
    
    final key = _elementKeys[elementId]!;
    final context = key.currentContext;
    
    if (context == null) {
      debugPrint('Cannot freeze element $elementId: No context available');
      return false;
    }
    
    try {
      // Get the RenderObject
      final renderObject = context.findRenderObject();
      if (renderObject == null || !renderObject.attached) {
        debugPrint('Cannot freeze element $elementId: No render object or not attached');
        return false;
      }
      
      // Convert the RenderObject to an image
      final image = await _captureRenderObject(renderObject);
      if (image == null) {
        debugPrint('Cannot freeze element $elementId: Failed to capture image');
        return false;
      }
      
      // Store the image
      _frozenImages[elementId] = image;
      _isFrozen[elementId] = true;
      
      return true;
    } catch (e) {
      debugPrint('Error freezing element $elementId: $e');
      return false;
    }
  }
  
  /// Capture a RenderObject as an image
  Future<ui.Image?> _captureRenderObject(RenderObject renderObject) async {
    if (renderObject is! RenderRepaintBoundary) {
      debugPrint('RenderObject is not a RenderRepaintBoundary');
      return null;
    }
    
    try {
      final image = await renderObject.toImage(pixelRatio: 1.0);
      return image;
    } catch (e) {
      debugPrint('Error capturing render object: $e');
      return null;
    }
  }
  
  /// Unfreeze an element
  void unfreezeElement(String elementId) {
    _isFrozen[elementId] = false;
    _frozenImages.remove(elementId);
  }
  
  /// Check if an element is frozen
  bool isElementFrozen(String elementId) {
    return _isFrozen[elementId] ?? false;
  }
  
  /// Get the frozen image of an element
  ui.Image? getFrozenElementImage(String elementId) {
    return _frozenImages[elementId];
  }
  
  /// Freeze all elements except the active one
  Future<void> freezeAllExcept(String activeElementId) async {
    for (final elementId in _elementKeys.keys) {
      if (elementId != activeElementId) {
        await freezeElement(elementId);
      } else {
        unfreezeElement(elementId);
      }
    }
  }
  
  /// Update elements freeze state
  void updateElementsFreezeState(List<dynamic> elements, String? activeElementId) {
    // Unfreeze the active element
    if (activeElementId != null) {
      unfreezeElement(activeElementId);
    }
    
    // Freeze all other elements
    for (final element in elements) {
      final elementId = element.id as String;
      if (elementId != activeElementId) {
        freezeElement(elementId);
      }
    }
  }
  
  /// Cleanup unused elements
  void cleanupUnusedElements(List<String> activeElementIds) {
    final elementsToRemove = <String>[];
    
    // Find elements that are no longer active
    for (final elementId in _elementKeys.keys) {
      if (!activeElementIds.contains(elementId)) {
        elementsToRemove.add(elementId);
      }
    }
    
    // Remove unused elements
    for (final elementId in elementsToRemove) {
      unregisterElementKey(elementId);
    }
  }
}
