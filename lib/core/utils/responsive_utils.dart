// lib/core/utils/responsive_utils.dart

import 'package:flutter/material.dart';

/// أداة مساعدة للتصميم المتجاوب
/// توفر وظائف وثوابت لتكييف واجهة المستخدم مع أحجام الشاشات المختلفة
class ResponsiveUtils {
  /// نقاط الفصل للأحجام المختلفة
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  
  /// تحديد نوع الجهاز بناءً على عرض الشاشة
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else if (width < desktopBreakpoint) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }
  
  /// الحصول على قيمة مناسبة بناءً على نوع الجهاز
  static T getValueForDeviceType<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
      case DeviceType.largeDesktop:
        return largeDesktop ?? desktop ?? tablet ?? mobile;
    }
  }
  
  /// الحصول على عدد الأعمدة المناسب بناءً على نوع الجهاز
  static int getColumnCount(BuildContext context) {
    return getValueForDeviceType(
      context: context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
      largeDesktop: 4,
    );
  }
  
  /// الحصول على حجم الخط المناسب بناءً على نوع الجهاز
  static double getFontSize(
    BuildContext context, {
    required double baseFontSize,
    double? tabletFontSizeMultiplier,
    double? desktopFontSizeMultiplier,
    double? largeFontSizeMultiplier,
  }) {
    return getValueForDeviceType(
      context: context,
      mobile: baseFontSize,
      tablet: baseFontSize * (tabletFontSizeMultiplier ?? 1.1),
      desktop: baseFontSize * (desktopFontSizeMultiplier ?? 1.2),
      largeDesktop: baseFontSize * (largeFontSizeMultiplier ?? 1.3),
    );
  }
  
  /// الحصول على حجم الأيقونة المناسب بناءً على نوع الجهاز
  static double getIconSize(
    BuildContext context, {
    required double baseIconSize,
    double? tabletIconSizeMultiplier,
    double? desktopIconSizeMultiplier,
    double? largeIconSizeMultiplier,
  }) {
    return getValueForDeviceType(
      context: context,
      mobile: baseIconSize,
      tablet: baseIconSize * (tabletIconSizeMultiplier ?? 1.1),
      desktop: baseIconSize * (desktopIconSizeMultiplier ?? 1.2),
      largeDesktop: baseIconSize * (largeIconSizeMultiplier ?? 1.3),
    );
  }
  
  /// الحصول على حجم الحشو المناسب بناءً على نوع الجهاز
  static EdgeInsetsGeometry getPadding(
    BuildContext context, {
    required EdgeInsetsGeometry basePadding,
    EdgeInsetsGeometry? tabletPadding,
    EdgeInsetsGeometry? desktopPadding,
    EdgeInsetsGeometry? largePadding,
  }) {
    return getValueForDeviceType(
      context: context,
      mobile: basePadding,
      tablet: tabletPadding,
      desktop: desktopPadding,
      largeDesktop: largePadding,
    );
  }
  
  /// الحصول على نسبة العرض إلى الارتفاع المناسبة بناءً على نوع الجهاز
  static double getAspectRatio(
    BuildContext context, {
    required double baseAspectRatio,
    double? tabletAspectRatio,
    double? desktopAspectRatio,
    double? largeAspectRatio,
  }) {
    return getValueForDeviceType(
      context: context,
      mobile: baseAspectRatio,
      tablet: tabletAspectRatio,
      desktop: desktopAspectRatio,
      largeDesktop: largeAspectRatio,
    );
  }
  
  /// تحديد ما إذا كان الجهاز في وضع أفقي
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }
  
  /// تحديد ما إذا كان الجهاز في وضع عمودي
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }
  
  /// الحصول على عرض الشاشة
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }
  
  /// الحصول على ارتفاع الشاشة
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }
  
  /// الحصول على نسبة مئوية من عرض الشاشة
  static double getWidthPercentage(BuildContext context, double percentage) {
    return getScreenWidth(context) * percentage;
  }
  
  /// الحصول على نسبة مئوية من ارتفاع الشاشة
  static double getHeightPercentage(BuildContext context, double percentage) {
    return getScreenHeight(context) * percentage;
  }
  
  /// الحصول على عرض آمن للشاشة (مع مراعاة الشقوق والنتوءات)
  static double getSafeScreenWidth(BuildContext context) {
    return getScreenWidth(context) - MediaQuery.of(context).padding.horizontal;
  }
  
  /// الحصول على ارتفاع آمن للشاشة (مع مراعاة الشقوق والنتوءات)
  static double getSafeScreenHeight(BuildContext context) {
    return getScreenHeight(context) - MediaQuery.of(context).padding.vertical;
  }
}

/// أنواع الأجهزة المدعومة
enum DeviceType {
  /// هاتف محمول (أقل من 600 بكسل)
  mobile,
  
  /// جهاز لوحي (600-900 بكسل)
  tablet,
  
  /// حاسوب مكتبي (900-1200 بكسل)
  desktop,
  
  /// حاسوب مكتبي كبير (أكثر من 1200 بكسل)
  largeDesktop,
}
