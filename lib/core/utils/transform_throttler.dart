import 'dart:async';
import 'package:flutter/material.dart';

/// معلومات التحويل
class TransformInfo {
  final Size? size;
  final Offset? offset;
  final double? rotation;
  final double? scale;
  
  const TransformInfo({
    this.size,
    this.offset,
    this.rotation,
    this.scale,
  });
  
  @override
  String toString() {
    return 'TransformInfo(size: $size, offset: $offset, rotation: $rotation, scale: $scale)';
  }
}

/// مخنق التحويلات - يحد من عدد استدعاءات دوال التحويل لتحسين الأداء
class TransformThrottler {
  /// الفاصل الزمني بين الاستدعاءات بالمللي ثانية
  final int intervalMs;
  
  /// المؤقت الحالي
  Timer? _timer;
  
  /// آخر معلومات تحويل تم تمريرها
  TransformInfo? _lastTransform;
  
  /// الدالة المراد استدعاؤها
  final ValueChanged<TransformInfo>? _callback;
  
  /// إنشاء مخنق التحويلات
  TransformThrottler({
    required this.intervalMs,
    required ValueChanged<TransformInfo>? callback,
  }) : _callback = callback;
  
  /// استدعاء الدالة مع التخنيق
  void call(TransformInfo transform) {
    _lastTransform = transform;
    
    // إذا لم يكن هناك مؤقت نشط، ابدأ واحد جديد
    if (_timer == null || !_timer!.isActive) {
      _timer = Timer(Duration(milliseconds: intervalMs), () {
        if (_lastTransform != null && _callback != null) {
          _callback!(_lastTransform!);
        }
      });
    }
  }
  
  /// استدعاء للحجم فقط
  void callSize(Size size) {
    call(TransformInfo(size: size));
  }
  
  /// استدعاء للموضع فقط
  void callOffset(Offset offset) {
    call(TransformInfo(offset: offset));
  }
  
  /// استدعاء للدوران فقط
  void callRotation(double rotation) {
    call(TransformInfo(rotation: rotation));
  }
  
  /// استدعاء للمقياس فقط
  void callScale(double scale) {
    call(TransformInfo(scale: scale));
  }
  
  /// إلغاء المؤقت والتنظيف
  void dispose() {
    _timer?.cancel();
    _timer = null;
    _lastTransform = null;
  }
  
  /// فرض تنفيذ آخر قيمة فوراً
  void flush() {
    _timer?.cancel();
    if (_lastTransform != null && _callback != null) {
      _callback!(_lastTransform!);
    }
    _timer = null;
  }
}

/// مخنق التحويلات المحسن مع دعم للتجميع
class EnhancedTransformThrottler {
  /// الفاصل الزمني بين الاستدعاءات
  final Duration interval;
  
  /// المؤقت الحالي
  Timer? _timer;
  
  /// قائمة التحويلات المتراكمة
  final List<TransformInfo> _pendingTransforms = [];
  
  /// الدالة المراد استدعاؤها للتجميع
  final ValueChanged<List<TransformInfo>>? _batchCallback;
  
  /// الدالة للتحويل الواحد
  final ValueChanged<TransformInfo>? _singleCallback;
  
  /// إنشاء مخنق التحويلات المحسن
  EnhancedTransformThrottler({
    required this.interval,
    ValueChanged<List<TransformInfo>>? batchCallback,
    ValueChanged<TransformInfo>? singleCallback,
  }) : _batchCallback = batchCallback,
       _singleCallback = singleCallback;
  
  /// إضافة تحويل جديد
  void add(TransformInfo transform) {
    _pendingTransforms.add(transform);
    
    // إذا لم يكن هناك مؤقت نشط، ابدأ واحد جديد
    if (_timer == null || !_timer!.isActive) {
      _timer = Timer(interval, _processPendingTransforms);
    }
  }
  
  /// إضافة حجم
  void addSize(Size size) {
    add(TransformInfo(size: size));
  }
  
  /// إضافة موضع
  void addOffset(Offset offset) {
    add(TransformInfo(offset: offset));
  }
  
  /// إضافة دوران
  void addRotation(double rotation) {
    add(TransformInfo(rotation: rotation));
  }
  
  /// إضافة مقياس
  void addScale(double scale) {
    add(TransformInfo(scale: scale));
  }
  
  /// معالجة التحويلات المعلقة
  void _processPendingTransforms() {
    if (_pendingTransforms.isNotEmpty) {
      // استدعاء دالة التجميع إذا كانت متوفرة
      if (_batchCallback != null) {
        _batchCallback!(List.from(_pendingTransforms));
      }
      
      // استدعاء دالة التحويل الواحد للقيمة الأخيرة
      if (_singleCallback != null && _pendingTransforms.isNotEmpty) {
        _singleCallback!(_pendingTransforms.last);
      }
      
      _pendingTransforms.clear();
    }
  }
  
  /// التنظيف
  void dispose() {
    _timer?.cancel();
    _timer = null;
    _pendingTransforms.clear();
  }
  
  /// فرض معالجة التحويلات المعلقة فوراً
  void flush() {
    _timer?.cancel();
    _processPendingTransforms();
    _timer = null;
  }
}
