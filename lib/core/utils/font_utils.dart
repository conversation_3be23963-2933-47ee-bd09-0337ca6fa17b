import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// مساعد لتطبيق الخطوط في التطبيق - نسخة مبسطة تستخدم Google Fonts
class FontUtils {
  /// تطبيق الخط المناسب على TextTheme حسب اللغة
  static TextTheme getTextTheme(BuildContext context, TextTheme baseTheme) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return isArabic
        ? GoogleFonts.cairoTextTheme(baseTheme)
        : GoogleFonts.poppinsTextTheme(baseTheme);
  }

  /// الحصول على TextStyle مع الخط المناسب حسب اللغة
  static TextStyle getTextStyle(
    BuildContext context, {
    TextStyle? style,
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
    double? letterSpacing,
  }) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    
    TextStyle baseStyle = style ?? const TextStyle();
    baseStyle = baseStyle.copyWith(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      decoration: decoration,
      letterSpacing: letterSpacing,
    );

    return isArabic
        ? GoogleFonts.cairo(textStyle: baseStyle)
        : GoogleFonts.poppins(textStyle: baseStyle);
  }

  /// الحصول على TextStyle للعناوين
  static TextStyle getHeadingStyle(
    BuildContext context, {
    double fontSize = 20,
    FontWeight fontWeight = FontWeight.bold,
    Color? color,
  }) {
    return getTextStyle(
      context,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// الحصول على TextStyle للنصوص العادية
  static TextStyle getBodyStyle(
    BuildContext context, {
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
  }) {
    return getTextStyle(
      context,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// الحصول على TextStyle للأزرار
  static TextStyle getButtonStyle(
    BuildContext context, {
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.bold,
    Color? color,
  }) {
    return getTextStyle(
      context,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }
}
