// lib/core/utils/render_optimization_service.dart

import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// خدمة تحسين الرندر للعناصر المعقدة
class RenderOptimizationService {
  static final Map<String, GlobalKey> _widgetKeys = {};
  static final Map<String, bool> _frozenWidgets = {};
  static final Map<String, Uint8List?> _frozenImages = {};

  /// تسجيل مفتاح العنصر
  static void registerWidgetKey(String widgetId, GlobalKey key) {
    _widgetKeys[widgetId] = key;
  }

  /// إلغاء تسجيل مفتاح العنصر
  static void unregisterWidgetKey(String widgetId) {
    _widgetKeys.remove(widgetId);
    _frozenWidgets.remove(widgetId);
    _frozenImages.remove(widgetId);
  }

  /// تجميد العنصر (تحويله إلى صورة)
  static Future<bool> freezeWidget(String widgetId) async {
    try {
      final key = _widgetKeys[widgetId];
      if (key?.currentContext == null) return false;

      final RenderRepaintBoundary boundary = 
          key!.currentContext!.findRenderObject() as RenderRepaintBoundary;
      
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = 
          await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData != null) {
        _frozenImages[widgetId] = byteData.buffer.asUint8List();
        _frozenWidgets[widgetId] = true;
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في تجميد العنصر: $e');
    }
    return false;
  }

  /// إلغاء تجميد العنصر
  static void unfreezeWidget(String widgetId) {
    _frozenWidgets[widgetId] = false;
    _frozenImages.remove(widgetId);
  }

  /// التحقق من حالة تجميد العنصر
  static bool isWidgetFrozen(String widgetId) {
    return _frozenWidgets[widgetId] ?? false;
  }

  /// الحصول على صورة العنصر المجمد
  static Uint8List? getFrozenImage(String widgetId) {
    return _frozenImages[widgetId];
  }

  /// تنظيف جميع البيانات
  static void clearAll() {
    _widgetKeys.clear();
    _frozenWidgets.clear();
    _frozenImages.clear();
  }
}
