// lib/core/utils/export_options.dart

import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';

/// تنسيق ملف التصدير
enum ExportFileFormat {
  /// صورة PNG
  png,

  /// صورة JPEG
  jpeg,

  /// ملف PDF
  pdf,
}

/// خيارات دقة التصدير
enum ExportResolution {
  /// دقة منخفضة (72 نقطة في البوصة)
  low(72, 'منخفضة'),

  /// دقة متوسطة (150 نقطة في البوصة)
  medium(150, 'متوسطة'),

  /// دقة عالية (300 نقطة في البوصة)
  high(300, 'عالية'),

  /// دقة عالية جدًا (600 نقطة في البوصة)
  veryHigh(600, 'عالية جدًا');

  /// قيمة الدقة بالنقاط في البوصة
  final int dpi;

  /// وصف الدقة
  final String label;

  /// إنشاء خيار دقة
  const ExportResolution(this.dpi, this.label);

  /// الحصول على نسبة البكسل
  double get pixelRatio => dpi / 96.0;
}

/// خيارات جودة التصدير
enum ExportQuality {
  /// جودة منخفضة (50%)
  low(50, 'منخفضة'),

  /// جودة متوسطة (75%)
  medium(75, 'متوسطة'),

  /// جودة عالية (90%)
  high(90, 'عالية'),

  /// جودة عالية جدًا (100%)
  veryHigh(100, 'عالية جدًا');

  /// قيمة الجودة (0-100)
  final int value;

  /// وصف الجودة
  final String label;

  /// إنشاء خيار جودة
  const ExportQuality(this.value, this.label);
}

/// خيارات حجم الورق
enum PaperSizeOption {
  /// حجم A4
  a4(PdfPageFormat.a4, 'A4'),

  /// حجم A5
  a5(PdfPageFormat.a5, 'A5'),

  /// حجم A6
  a6(PdfPageFormat.a6, 'A6'),

  /// حجم Letter
  letter(PdfPageFormat.letter, 'Letter'),

  /// حجم Legal
  legal(PdfPageFormat.legal, 'Legal'),

  /// حجم مخصص
  custom(PdfPageFormat(100, 100), 'مخصص');

  /// تنسيق الصفحة
  final PdfPageFormat pageFormat;

  /// وصف حجم الورق
  final String label;

  /// إنشاء خيار حجم ورق
  const PaperSizeOption(this.pageFormat, this.label);
}

/// خيارات التصدير
class ExportOptions {
  /// تنسيق ملف التصدير
  final ExportFileFormat fileFormat;

  /// دقة التصدير
  final ExportResolution resolution;

  /// جودة التصدير
  final ExportQuality quality;

  /// حجم الورق (لملفات PDF)
  final PaperSizeOption paperSize;

  /// ما إذا كان يجب تضمين الهوامش (لملفات PDF)
  final bool includeMargins;

  /// ما إذا كان يجب تضمين الخلفية
  final bool includeBackground;

  /// لون الخلفية
  final Color backgroundColor;

  /// حجم مخصص للورق (لملفات PDF)
  final Size? customPaperSize;

  /// إنشاء خيارات التصدير
  const ExportOptions({
    this.fileFormat = ExportFileFormat.png,
    this.resolution = ExportResolution.high,
    this.quality = ExportQuality.high,
    this.paperSize = PaperSizeOption.a4,
    this.includeMargins = true,
    this.includeBackground = true,
    this.backgroundColor = Colors.white,
    this.customPaperSize,
  });

  /// إنشاء نسخة معدلة من خيارات التصدير
  ExportOptions copyWith({
    ExportFileFormat? fileFormat,
    ExportResolution? resolution,
    ExportQuality? quality,
    PaperSizeOption? paperSize,
    bool? includeMargins,
    bool? includeBackground,
    Color? backgroundColor,
    Size? customPaperSize,
  }) {
    return ExportOptions(
      fileFormat: fileFormat ?? this.fileFormat,
      resolution: resolution ?? this.resolution,
      quality: quality ?? this.quality,
      paperSize: paperSize ?? this.paperSize,
      includeMargins: includeMargins ?? this.includeMargins,
      includeBackground: includeBackground ?? this.includeBackground,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      customPaperSize: customPaperSize ?? this.customPaperSize,
    );
  }

  /// الحصول على تنسيق الصفحة لملفات PDF
  PdfPageFormat getPdfPageFormat() {
    if (paperSize == PaperSizeOption.custom && customPaperSize != null) {
      // تحويل البكسل إلى نقاط (1 بوصة = 72 نقطة)
      final widthInPoints = customPaperSize!.width * 72 / resolution.dpi;
      final heightInPoints = customPaperSize!.height * 72 / resolution.dpi;

      return PdfPageFormat(
        widthInPoints,
        heightInPoints,
        marginAll: includeMargins ? 28.0 : 0.0,
      );
    }

    return includeMargins
        ? paperSize.pageFormat
        : paperSize.pageFormat.copyWith(
            marginLeft: 0,
            marginTop: 0,
            marginRight: 0,
            marginBottom: 0,
          );
  }
}
