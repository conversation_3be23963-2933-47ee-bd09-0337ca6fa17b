// lib/core/utils/firestore_fix.dart

import 'package:cloud_firestore/cloud_firestore.dart';

/// Extension pour corriger le problème de type nullable dans AggregateQuerySnapshot
extension AggregateQuerySnapshotFix on AggregateQuerySnapshot {
  /// Retourne le nombre d'éléments dans le résultat de la requête
  /// Cette méthode est utilisée pour contourner le problème de type nullable
  int getCount() {
    try {
      return count as int? ?? 0;
    } catch (e) {
      return 0;
    }
  }
}
