// lib/core/utils/unified_performance_monitor.dart

import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Unified Performance Monitor
///
/// This class combines functionality from:
/// - PerformanceMonitor
///
/// Provides tools for monitoring and analyzing app performance.
class UnifiedPerformanceMonitor {
  // Singleton instance
  static final UnifiedPerformanceMonitor _instance =
      UnifiedPerformanceMonitor._internal();

  // Get singleton instance
  factory UnifiedPerformanceMonitor() => _instance;

  // Private constructor
  UnifiedPerformanceMonitor._internal();

  // Whether the monitor is active
  bool _isActive = false;

  // Whether rendering diagnostics is active
  bool _isRenderingDiagnosticsActive = false;

  /// Enable performance monitoring
  void enable() {
    if (_isActive) return;

    _isActive = true;

    // Enable frame logging
    // These flags are no longer available in newer Flutter versions
    // Use Timeline events instead

    // Log frame start/end events
    developer.Timeline.startSync('PerformanceMonitoring');

    // Enable debug flags that are still available
    debugProfileBuildsEnabled = true;

    debugPrint('Performance monitoring enabled');
  }

  /// Disable performance monitoring
  void disable() {
    if (!_isActive) return;

    _isActive = false;

    // Disable frame logging
    // Stop timeline events
    developer.Timeline.finishSync();

    // Disable debug flags
    debugProfileBuildsEnabled = false;

    debugPrint('Performance monitoring disabled');
  }

  /// Enable rendering diagnostics
  void enableRenderingDiagnostics() {
    if (_isRenderingDiagnosticsActive) return;

    _isRenderingDiagnosticsActive = true;

    // Enable rendering diagnostics
    debugPaintLayerBordersEnabled = true;
    debugRepaintRainbowEnabled = true;

    debugPrint('Rendering diagnostics enabled');
  }

  /// Disable rendering diagnostics
  void disableRenderingDiagnostics() {
    if (!_isRenderingDiagnosticsActive) return;

    _isRenderingDiagnosticsActive = false;

    // Disable rendering diagnostics
    debugPaintLayerBordersEnabled = false;
    debugRepaintRainbowEnabled = false;

    debugPrint('Rendering diagnostics disabled');
  }

  /// Measure performance of an async function
  Future<T> measurePerformance<T>(
    String name,
    Future<T> Function() function,
  ) async {
    if (!_isActive) return function();

    final stopwatch = Stopwatch()..start();

    try {
      final result = await function();

      stopwatch.stop();
      debugPrint('$name took ${stopwatch.elapsedMilliseconds}ms');

      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('$name failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// Measure performance of a sync function
  T measureSyncPerformance<T>(
    String name,
    T Function() function,
  ) {
    if (!_isActive) return function();

    final stopwatch = Stopwatch()..start();

    try {
      final result = function();

      stopwatch.stop();
      debugPrint('$name took ${stopwatch.elapsedMilliseconds}ms');

      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('$name failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }

  /// Start a custom performance measurement
  Stopwatch startCustomMeasurement(String name) {
    final stopwatch = Stopwatch()..start();

    if (_isActive) {
      debugPrint('Starting measurement: $name');
    }

    return stopwatch;
  }

  /// End a custom performance measurement
  void endCustomMeasurement(String name, Stopwatch stopwatch) {
    stopwatch.stop();

    if (_isActive) {
      debugPrint('$name took ${stopwatch.elapsedMilliseconds}ms');
    }
  }

  /// Log a performance event
  void logPerformanceEvent(String name, {Map<String, dynamic>? data}) {
    if (!_isActive) return;

    developer.Timeline.instantSync(
      name,
      arguments: data,
    );

    debugPrint('Performance event: $name ${data ?? ''}');
  }

  /// Start a performance trace
  void startPerformanceTrace(String name) {
    if (!_isActive) return;

    developer.Timeline.startSync(name);
    debugPrint('Starting performance trace: $name');
  }

  /// End a performance trace
  void endPerformanceTrace(String name) {
    if (!_isActive) return;

    developer.Timeline.finishSync();
    debugPrint('Ending performance trace: $name');
  }

  /// Check if performance monitoring is active
  bool get isActive => _isActive;

  /// Check if rendering diagnostics is active
  bool get isRenderingDiagnosticsActive => _isRenderingDiagnosticsActive;
}
