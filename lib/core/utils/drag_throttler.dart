import 'dart:async';
import 'package:flutter/material.dart';

/// مخنق السحب - يحد من عدد استدعاءات دوال السحب لتحسين الأداء
class DragThrottler {
  /// الفاصل الزمني بين الاستدعاءات بالمللي ثانية
  final int intervalMs;
  
  /// المؤقت الحالي
  Timer? _timer;
  
  /// آخر قيمة تم تمريرها
  Offset? _lastValue;
  
  /// الدالة المراد استدعاؤها
  final ValueChanged<Offset>? _callback;
  
  /// إنشاء مخنق السحب
  DragThrottler({
    required this.intervalMs,
    required ValueChanged<Offset>? callback,
  }) : _callback = callback;
  
  /// استدعاء الدالة مع التخنيق
  void call(Offset value) {
    _lastValue = value;
    
    // إذا لم يكن هناك مؤقت نشط، ابدأ واحد جديد
    if (_timer == null || !_timer!.isActive) {
      _timer = Timer(Duration(milliseconds: intervalMs), () {
        if (_lastValue != null && _callback != null) {
          _callback!(_lastValue!);
        }
      });
    }
  }
  
  /// إلغاء المؤقت والتنظيف
  void dispose() {
    _timer?.cancel();
    _timer = null;
    _lastValue = null;
  }
  
  /// فرض تنفيذ آخر قيمة فوراً
  void flush() {
    _timer?.cancel();
    if (_lastValue != null && _callback != null) {
      _callback!(_lastValue!);
    }
    _timer = null;
  }
}

/// مخنق السحب المحسن مع دعم للتجميع
class EnhancedDragThrottler {
  /// الفاصل الزمني بين الاستدعاءات
  final Duration interval;
  
  /// المؤقت الحالي
  Timer? _timer;
  
  /// قائمة القيم المتراكمة
  final List<Offset> _pendingValues = [];
  
  /// الدالة المراد استدعاؤها
  final ValueChanged<List<Offset>>? _batchCallback;
  
  /// الدالة للقيمة الواحدة
  final ValueChanged<Offset>? _singleCallback;
  
  /// إنشاء مخنق السحب المحسن
  EnhancedDragThrottler({
    required this.interval,
    ValueChanged<List<Offset>>? batchCallback,
    ValueChanged<Offset>? singleCallback,
  }) : _batchCallback = batchCallback,
       _singleCallback = singleCallback;
  
  /// إضافة قيمة جديدة
  void add(Offset value) {
    _pendingValues.add(value);
    
    // إذا لم يكن هناك مؤقت نشط، ابدأ واحد جديد
    if (_timer == null || !_timer!.isActive) {
      _timer = Timer(interval, _processPendingValues);
    }
  }
  
  /// معالجة القيم المعلقة
  void _processPendingValues() {
    if (_pendingValues.isNotEmpty) {
      // استدعاء دالة التجميع إذا كانت متوفرة
      if (_batchCallback != null) {
        _batchCallback!(List.from(_pendingValues));
      }
      
      // استدعاء دالة القيمة الواحدة للقيمة الأخيرة
      if (_singleCallback != null && _pendingValues.isNotEmpty) {
        _singleCallback!(_pendingValues.last);
      }
      
      _pendingValues.clear();
    }
  }
  
  /// التنظيف
  void dispose() {
    _timer?.cancel();
    _timer = null;
    _pendingValues.clear();
  }
  
  /// فرض معالجة القيم المعلقة فوراً
  void flush() {
    _timer?.cancel();
    _processPendingValues();
    _timer = null;
  }
}
