import 'package:intl/intl.dart';

/// Utility class for formatting dates and times.
///
/// This class provides static methods to format DateTime objects into
/// standardized string representations for display in the UI.
class DateFormatter {
  /// Formats a DateTime into a standard date string (day/month/year).
  ///
  /// Example output: "25/12/2023"
  ///
  /// @param date The DateTime to format
  /// @return A formatted date string in dd/MM/yyyy format
  static String formatDate(DateTime date) {
    final DateFormat formatter = DateFormat('dd/MM/yyyy');
    return formatter.format(date);
  }

  /// Formats a DateTime into a time string (hour:minute).
  ///
  /// Example output: "14:30"
  ///
  /// @param dateTime The DateTime to format
  /// @return A formatted time string in 24-hour HH:mm format
  static String formatTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('HH:mm');
    return formatter.format(dateTime);
  }

  /// Formats a DateTime into a combined date and time string.
  ///
  /// Example output: "25/12/2023 14:30"
  ///
  /// @param dateTime The DateTime to format
  /// @return A formatted date and time string in dd/MM/yyyy HH:mm format
  static String formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('dd/MM/yyyy HH:mm');
    return formatter.format(dateTime);
  }
}
