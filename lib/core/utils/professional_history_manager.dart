import 'dart:convert';
import 'package:flutter/material.dart';

/// مدير التاريخ الاحترافي للتراجع والإعادة
class ProfessionalHistoryManager<T> {
  final List<HistoryState<T>> _history = [];
  int _currentIndex = -1;
  final int maxHistorySize;
  final Function(T) stateSerializer;
  final Function(dynamic) stateDeserializer;
  
  ProfessionalHistoryManager({
    this.maxHistorySize = 50,
    required this.stateSerializer,
    required this.stateDeserializer,
  });
  
  /// حفظ حالة جديدة
  void saveState(T state, String description) {
    // إزالة الحالات التي تأتي بعد الحالة الحالية (في حالة التراجع ثم إجراء عملية جديدة)
    if (_currentIndex < _history.length - 1) {
      _history.removeRange(_currentIndex + 1, _history.length);
    }
    
    // إضافة الحالة الجديدة
    final historyState = HistoryState<T>(
      state: state,
      description: description,
      timestamp: DateTime.now(),
      serializedData: stateSerializer(state),
    );
    
    _history.add(historyState);
    _currentIndex++;
    
    // الحفاظ على الحد الأقصى لحجم التاريخ
    if (_history.length > maxHistorySize) {
      _history.removeAt(0);
      _currentIndex--;
    }
    
    debugPrint('History: Saved state "$description" (${_currentIndex + 1}/${_history.length})');
  }
  
  /// التراجع إلى الحالة السابقة
  T? undo() {
    if (canUndo()) {
      _currentIndex--;
      final state = _history[_currentIndex];
      debugPrint('History: Undo to "${state.description}" (${_currentIndex + 1}/${_history.length})');
      return stateDeserializer(state.serializedData);
    }
    return null;
  }
  
  /// الإعادة إلى الحالة التالية
  T? redo() {
    if (canRedo()) {
      _currentIndex++;
      final state = _history[_currentIndex];
      debugPrint('History: Redo to "${state.description}" (${_currentIndex + 1}/${_history.length})');
      return stateDeserializer(state.serializedData);
    }
    return null;
  }
  
  /// التحقق من إمكانية التراجع
  bool canUndo() => _currentIndex > 0;
  
  /// التحقق من إمكانية الإعادة
  bool canRedo() => _currentIndex < _history.length - 1;
  
  /// الحصول على الحالة الحالية
  T? getCurrentState() {
    if (_currentIndex >= 0 && _currentIndex < _history.length) {
      return stateDeserializer(_history[_currentIndex].serializedData);
    }
    return null;
  }
  
  /// الحصول على وصف الحالة الحالية
  String? getCurrentDescription() {
    if (_currentIndex >= 0 && _currentIndex < _history.length) {
      return _history[_currentIndex].description;
    }
    return null;
  }
  
  /// الحصول على قائمة بجميع الحالات
  List<HistoryState<T>> getHistory() => List.unmodifiable(_history);
  
  /// مسح التاريخ
  void clear() {
    _history.clear();
    _currentIndex = -1;
    debugPrint('History: Cleared all states');
  }
  
  /// الحصول على معلومات التاريخ
  HistoryInfo getHistoryInfo() {
    return HistoryInfo(
      totalStates: _history.length,
      currentIndex: _currentIndex,
      canUndo: canUndo(),
      canRedo: canRedo(),
      currentDescription: getCurrentDescription(),
    );
  }
}

/// حالة في التاريخ
class HistoryState<T> {
  final T state;
  final String description;
  final DateTime timestamp;
  final dynamic serializedData;
  
  const HistoryState({
    required this.state,
    required this.description,
    required this.timestamp,
    required this.serializedData,
  });
}

/// معلومات التاريخ
class HistoryInfo {
  final int totalStates;
  final int currentIndex;
  final bool canUndo;
  final bool canRedo;
  final String? currentDescription;
  
  const HistoryInfo({
    required this.totalStates,
    required this.currentIndex,
    required this.canUndo,
    required this.canRedo,
    this.currentDescription,
  });
}

/// مدير تاريخ البطاقة المخصص
class CardHistoryManager extends ProfessionalHistoryManager<Map<String, dynamic>> {
  CardHistoryManager() : super(
    maxHistorySize: 50,
    stateSerializer: (state) => jsonEncode(state),
    stateDeserializer: (data) => jsonDecode(data as String),
  );
  
  /// حفظ حالة إضافة عنصر
  void saveAddElementState(Map<String, dynamic> cardState, String elementType) {
    saveState(cardState, 'إضافة $elementType');
  }
  
  /// حفظ حالة حذف عنصر
  void saveDeleteElementState(Map<String, dynamic> cardState, String elementType) {
    saveState(cardState, 'حذف $elementType');
  }
  
  /// حفظ حالة تحريك عنصر
  void saveMoveElementState(Map<String, dynamic> cardState, String elementId) {
    saveState(cardState, 'تحريك عنصر');
  }
  
  /// حفظ حالة تغيير حجم عنصر
  void saveResizeElementState(Map<String, dynamic> cardState, String elementId) {
    saveState(cardState, 'تغيير حجم عنصر');
  }
  
  /// حفظ حالة دوران عنصر
  void saveRotateElementState(Map<String, dynamic> cardState, String elementId) {
    saveState(cardState, 'دوران عنصر');
  }
  
  /// حفظ حالة تعديل نص
  void saveTextEditState(Map<String, dynamic> cardState, String text) {
    saveState(cardState, 'تعديل النص');
  }
  
  /// حفظ حالة تغيير خصائص العنصر
  void savePropertyChangeState(Map<String, dynamic> cardState, String property) {
    saveState(cardState, 'تغيير $property');
  }
}

/// أداة التحكم في التاريخ
class HistoryControlWidget extends StatelessWidget {
  final CardHistoryManager historyManager;
  final VoidCallback? onUndo;
  final VoidCallback? onRedo;
  final Color primaryColor;
  final Color disabledColor;
  
  const HistoryControlWidget({
    Key? key,
    required this.historyManager,
    this.onUndo,
    this.onRedo,
    this.primaryColor = Colors.blue,
    this.disabledColor = Colors.grey,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<HistoryInfo>(
      stream: _createHistoryStream(),
      builder: (context, snapshot) {
        final info = snapshot.data ?? HistoryInfo(
          totalStates: 0,
          currentIndex: -1,
          canUndo: false,
          canRedo: false,
        );
        
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // زر التراجع
            IconButton(
              onPressed: info.canUndo ? onUndo : null,
              icon: Icon(Icons.undo),
              color: info.canUndo ? primaryColor : disabledColor,
              tooltip: 'تراجع${info.canUndo && info.currentDescription != null ? ' - ${info.currentDescription}' : ''}',
            ),
            
            // زر الإعادة
            IconButton(
              onPressed: info.canRedo ? onRedo : null,
              icon: Icon(Icons.redo),
              color: info.canRedo ? primaryColor : disabledColor,
              tooltip: 'إعادة',
            ),
            
            // عداد الحالات
            if (info.totalStates > 0)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  '${info.currentIndex + 1}/${info.totalStates}',
                  style: TextStyle(
                    fontSize: 12,
                    color: primaryColor,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
  
  Stream<HistoryInfo> _createHistoryStream() {
    // في التطبيق الحقيقي، يمكن استخدام StreamController
    // هنا نستخدم Stream.periodic كمثال
    return Stream.periodic(
      const Duration(milliseconds: 100),
      (_) => historyManager.getHistoryInfo(),
    ).distinct();
  }
}

/// مدير الاختصارات للتاريخ
class HistoryShortcutManager {
  static const Map<String, String> shortcuts = {
    'Ctrl+Z': 'تراجع',
    'Ctrl+Y': 'إعادة',
    'Ctrl+Shift+Z': 'إعادة',
  };
  
  static void handleKeyEvent(
    KeyEvent event,
    CardHistoryManager historyManager,
    Function(Map<String, dynamic>) onStateRestore,
  ) {
    if (event is KeyDownEvent) {
      final isCtrlPressed = event.logicalKey == LogicalKeyboardKey.controlLeft ||
                           event.logicalKey == LogicalKeyboardKey.controlRight;
      
      if (isCtrlPressed) {
        if (event.logicalKey == LogicalKeyboardKey.keyZ) {
          if (event.isShiftPressed) {
            // Ctrl+Shift+Z = إعادة
            final state = historyManager.redo();
            if (state != null) {
              onStateRestore(state);
            }
          } else {
            // Ctrl+Z = تراجع
            final state = historyManager.undo();
            if (state != null) {
              onStateRestore(state);
            }
          }
        } else if (event.logicalKey == LogicalKeyboardKey.keyY) {
          // Ctrl+Y = إعادة
          final state = historyManager.redo();
          if (state != null) {
            onStateRestore(state);
          }
        }
      }
    }
  }
}
