import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';

/// محسن الأداء الاحترافي للوصول إلى 60 FPS
class ProfessionalPerformanceOptimizer {
  static const int targetFPS = 60;
  static const Duration frameTime = Duration(microseconds: 16667); // 1/60 second
  static const Duration throttleInterval = Duration(milliseconds: 16);
  
  DateTime _lastUpdate = DateTime.now();
  Timer? _throttleTimer;
  
  /// التحقق من إمكانية التحديث للحفاظ على 60 FPS
  bool shouldUpdate() {
    final now = DateTime.now();
    if (now.difference(_lastUpdate) >= frameTime) {
      _lastUpdate = now;
      return true;
    }
    return false;
  }
  
  /// تطبيق throttling للتحديثات
  void throttledUpdate(VoidCallback callback) {
    _throttleTimer?.cancel();
    _throttleTimer = Timer(throttleInterval, callback);
  }
  
  /// تنظيف الموارد
  void dispose() {
    _throttleTimer?.cancel();
  }
}

/// مدير ذاكرة التخزين المؤقت للتحويلات
class TransformCacheManager {
  static final Map<String, Matrix4> _transformCache = {};
  static const int maxCacheSize = 100;
  
  /// الحصول على تحويل مخزن مؤقتاً
  static Matrix4? getCachedTransform(String key) {
    return _transformCache[key];
  }
  
  /// حفظ تحويل في الذاكرة المؤقتة
  static void cacheTransform(String key, Matrix4 transform) {
    if (_transformCache.length >= maxCacheSize) {
      // إزالة أقدم عنصر
      final firstKey = _transformCache.keys.first;
      _transformCache.remove(firstKey);
    }
    _transformCache[key] = Matrix4.copy(transform);
  }
  
  /// مسح الذاكرة المؤقتة
  static void clearCache() {
    _transformCache.clear();
  }
  
  /// إزالة تحويل محدد من الذاكرة المؤقتة
  static void removeCachedTransform(String key) {
    _transformCache.remove(key);
  }
}

/// مُحسِّن الرسوميات للأداء العالي
class GraphicsOptimizer {
  /// إنشاء Paint محسن للأداء
  static Paint createOptimizedPaint({
    required Color color,
    PaintingStyle style = PaintingStyle.fill,
    double strokeWidth = 1.0,
    bool isAntiAlias = true,
  }) {
    return Paint()
      ..color = color
      ..style = style
      ..strokeWidth = strokeWidth
      ..isAntiAlias = isAntiAlias;
  }
  
  /// إنشاء Shadow محسن
  static List<BoxShadow> createOptimizedShadow({
    Color color = Colors.black26,
    double blurRadius = 2.0,
    Offset offset = const Offset(0, 1),
  }) {
    return [
      BoxShadow(
        color: color,
        blurRadius: blurRadius,
        offset: offset,
      ),
    ];
  }
}

/// مدير الرسوم المتحركة السلسة
class SmoothAnimationManager {
  static const Duration defaultDuration = Duration(milliseconds: 200);
  static const Curve defaultCurve = Curves.easeOutCubic;
  
  /// إنشاء رسم متحرك سلس للتحويل
  static AnimationController createSmoothTransformAnimation(
    TickerProvider vsync, {
    Duration duration = defaultDuration,
  }) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
    );
  }
  
  /// إنشاء رسم متحرك للتلاشي
  static Animation<double> createFadeAnimation(
    AnimationController controller, {
    Curve curve = defaultCurve,
  }) {
    return Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: curve,
    ));
  }
  
  /// إنشاء رسم متحرك للحجم
  static Animation<double> createScaleAnimation(
    AnimationController controller, {
    double begin = 0.8,
    double end = 1.0,
    Curve curve = defaultCurve,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: curve,
    ));
  }
}

/// مُحسِّن الذاكرة للعناصر
class MemoryOptimizer {
  static final Map<String, dynamic> _memoryPool = {};
  
  /// إعادة استخدام الكائنات لتوفير الذاكرة
  static T getOrCreate<T>(String key, T Function() creator) {
    if (_memoryPool.containsKey(key)) {
      return _memoryPool[key] as T;
    }
    final object = creator();
    _memoryPool[key] = object;
    return object;
  }
  
  /// تنظيف الذاكرة
  static void cleanup() {
    _memoryPool.clear();
  }
}

/// مدير الأداء العام
class PerformanceManager {
  static final ProfessionalPerformanceOptimizer _optimizer = 
      ProfessionalPerformanceOptimizer();
  
  static bool shouldUpdate() => _optimizer.shouldUpdate();
  static void throttledUpdate(VoidCallback callback) => 
      _optimizer.throttledUpdate(callback);
  static void dispose() => _optimizer.dispose();
  
  /// قياس أداء العملية
  static Future<T> measurePerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      debugPrint('$operationName took ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint('$operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }
}
