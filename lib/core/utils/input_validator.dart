/// Utility class for validating user input.
///
/// This class provides static methods to validate different types of user input
/// such as email addresses, passwords, and phone numbers.
class InputValidator {
  /// Validates if the provided string is a valid email address.
  ///
  /// Uses a simple regex pattern to check if the email has the basic format:
  /// <EMAIL>
  ///
  /// @param email The email address to validate
  /// @return true if the email is valid, false otherwise
  static bool isValidEmail(String email) {
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    return emailRegex.hasMatch(email);
  }

  /// Validates if the provided string is a valid password.
  ///
  /// Currently checks if the password is at least 6 characters long.
  /// This can be extended with more complex validation rules as needed.
  ///
  /// @param password The password to validate
  /// @return true if the password is valid, false otherwise
  static bool isValidPassword(String password) {
    return password.length >= 6;
  }

  /// Validates if the provided string is a valid phone number.
  ///
  /// Checks if the phone number:
  /// 1. Contains only digits
  /// 2. Is at least 9 digits long
  ///
  /// @param phone The phone number to validate
  /// @return true if the phone number is valid, false otherwise
  static bool isValidPhoneNumber(String phone) {
    final phoneRegex = RegExp(r'^[0-9]+$');
    return phoneRegex.hasMatch(phone) && phone.length >= 9;
  }

  // Additional validation methods can be added here as needed:
  // - isValidName()
  // - isValidAddress()
  // - isValidCreditCard()
  // etc.
}
