// lib/core/utils/unified_image_cache_manager.dart

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

/// Unified Image Cache Manager
/// 
/// This class combines functionality from:
/// - ImageCacheManager
/// - PerformanceUtils (image caching parts)
/// - OptimizedImage (caching parts)
/// 
/// Provides tools for optimizing image loading and caching.
class UnifiedImageCacheManager {
  // Singleton instance
  static final UnifiedImageCacheManager _instance = UnifiedImageCacheManager._internal();
  
  // Get singleton instance
  factory UnifiedImageCacheManager() => _instance;
  
  // Private constructor
  UnifiedImageCacheManager._internal();
  
  // Custom cache manager
  final CacheManager _cacheManager = CacheManager(
    Config(
      'unifiedImageCache',
      stalePeriod: const Duration(days: 14),
      maxNrOfCacheObjects: 200,
      repo: JsonCacheInfoRepository(databaseName: 'unifiedImageCache'),
      fileService: HttpFileService(),
    ),
  );
  
  // Get the cache manager
  CacheManager get cacheManager => _cacheManager;
  
  /// Download and cache an image
  Future<File?> downloadAndCacheImage(String url) async {
    try {
      final fileInfo = await _cacheManager.downloadFile(url);
      return fileInfo.file;
    } catch (e) {
      debugPrint('Error downloading image: $e');
      return null;
    }
  }
  
  /// Check if an image is cached
  Future<bool> isImageCached(String url) async {
    final fileInfo = await _cacheManager.getFileFromCache(url);
    return fileInfo != null;
  }
  
  /// Get a cached image
  Future<File?> getCachedImage(String url) async {
    final fileInfo = await _cacheManager.getFileFromCache(url);
    return fileInfo?.file;
  }
  
  /// Clear the cache
  Future<void> clearCache() async {
    await _cacheManager.emptyCache();
    
    // Clear temporary files
    if (!kIsWeb) {
      try {
        final tempDir = await getTemporaryDirectory();
        if (await tempDir.exists()) {
          await for (final entity in tempDir.list()) {
            if (entity is File) {
              await entity.delete();
            }
          }
        }
      } catch (e) {
        debugPrint('Error clearing temporary files: $e');
      }
    }
  }
  
  /// Compress and cache an image
  Future<File?> compressAndCacheImage(
    File file, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      // Check if we're on web
      if (kIsWeb) {
        debugPrint('Web platform detected, returning original file');
        return file; // On web, return the original file without compression
      }

      // Get a temporary directory
      final tempDir = await getTemporaryDirectory();
      final fileName = 'compressed_${const Uuid().v4()}.jpg';
      final targetPath = path.join(tempDir.path, fileName);

      // Compress the image
      final result = await FlutterImageCompress.compressAndGetFile(
        file.absolute.path,
        targetPath,
        quality: quality,
        minWidth: maxWidth ?? 1024,
        minHeight: maxHeight ?? 1024,
      );

      return result != null ? File(result.path) : null;
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return null;
    }
  }
  
  /// Compress image bytes
  Future<Uint8List?> compressImageBytes(
    Uint8List bytes, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      // Compress image bytes
      final result = await FlutterImageCompress.compressWithList(
        bytes,
        quality: quality,
        minWidth: maxWidth ?? 1024,
        minHeight: maxHeight ?? 1024,
      );

      return result;
    } catch (e) {
      debugPrint('Error compressing image bytes: $e');
      return null;
    }
  }
  
  /// Preload an image
  Future<void> preloadImage(String url) async {
    if (url.startsWith('http')) {
      await downloadAndCacheImage(url);
    } else if (url.startsWith('assets/')) {
      // Preload asset image
      try {
        final context = NavigationService.navigatorKey.currentContext;
        if (context != null) {
          await precacheImage(AssetImage(url), context);
        }
      } catch (e) {
        debugPrint('Error preloading asset image: $e');
      }
    }
  }
  
  /// Preload multiple images
  Future<void> preloadImages(List<String> urls) async {
    for (final url in urls) {
      await preloadImage(url);
    }
  }
  
  /// Generate a BlurHash for an image
  Future<String> generateBlurHash(String imagePath) async {
    try {
      // Check if we're on web
      if (kIsWeb) {
        debugPrint('Web platform detected, returning default BlurHash');
        return 'LGF5]+Yk^6#M@-5c,1J5@[or[Q6.'; // Default value for web
      }

      final file = File(imagePath);
      if (!await file.exists()) {
        return ''; // Return empty string if file doesn't exist
      }

      // Read image as bytes
      final bytes = await file.readAsBytes();

      // Use compute to move heavy operation to a separate isolate
      final blurHash = await compute(_encodeBlurHash, bytes);

      debugPrint('BlurHash generated: $blurHash');
      return blurHash;
    } catch (e) {
      debugPrint('Error generating BlurHash: $e');
      return '';
    }
  }

  /// Encode BlurHash (executed in a separate isolate)
  static Future<String> _encodeBlurHash(Uint8List bytes) async {
    // This function needs a specific implementation for encoding BlurHash
    // Can use an external library or implement the encoding algorithm manually

    // This is just a simple example, in a real app should use a specialized library
    return 'LGF5]+Yk^6#M@-5c,1J5@[or[Q6.';
  }
}

/// Navigation Service
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}
