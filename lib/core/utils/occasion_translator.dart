import 'package:flutter/material.dart';

/// مترجم أسماء المناسبات
///
/// هذا الصف يوفر ترجمات لأسماء المناسبات المخزنة بالعربية في قاعدة البيانات
/// إلى اللغات المختلفة المدعومة في التطبيق.
class OccasionTranslator {
  /// ترجمات أسماء المناسبات لكل لغة
  ///
  /// المفتاح الخارجي هو رمز اللغة (ar, en, es, fr, de)
  /// المفتاح الداخلي هو اسم المناسبة بالعربية
  /// القيمة هي اسم المناسبة باللغة المقابلة
  static const Map<String, Map<String, String>> _translations = {
    // العربية (الأصل)
    'ar': {
      'غالية وجت': 'غالية وجت',
      'ذكرى غالية': 'ذكرى غالية',
      'غلا العمر': 'غلا العمر',
      'أبوي الغالي': 'أبوي الغالي',
      'أمي الحبيبة': 'أمي الحبيبة',
      'سنة حلوة': 'سنة حلوة',
      'نور البيت': 'نور البيت',
      'كفو دايم': 'كفو دايم',
      'مبروك التخرج': 'مبروك التخرج',
      'بداية جديدة': 'بداية جديدة',
      'منزل مبارك': 'منزل مبارك',
      'أجر وشفاء': 'أجر وشفاء',
      'عيد الفطر': 'عيد الفطر',
      'عيد الأضحى': 'عيد الأضحى',
      'رمضان كريم': 'رمضان كريم',
      'جمعة مباركة': 'جمعة مباركة',
      'مولود جديد': 'مولود جديد',
    },

    // الإنجليزية
    'en': {
      'غالية وجت': 'Wedding',
      'ذكرى غالية': 'Anniversary',
      'غلا العمر': 'Love',
      'أبوي الغالي': 'Father\'s Day',
      'أمي الحبيبة': 'Mother\'s Day',
      'سنة حلوة': 'Birthday',
      'نور البيت': 'Baby',
      'كفو دايم': 'Congratulations',
      'مبروك التخرج': 'Graduation',
      'بداية جديدة': 'New Beginning',
      'منزل مبارك': 'New Home',
      'أجر وشفاء': 'Get Well Soon',
      'عيد الفطر': 'Eid Al-Fitr',
      'عيد الأضحى': 'Eid Al-Adha',
      'رمضان كريم': 'Ramadan Kareem',
      'جمعة مباركة': 'Blessed Friday',
      'مولود جديد': 'New Baby',
    },

    // الإسبانية
    'es': {
      'غالية وجت': 'Boda',
      'ذكرى غالية': 'Aniversario',
      'غلا العمر': 'Amor',
      'أبوي الغالي': 'Día del Padre',
      'أمي الحبيبة': 'Día de la Madre',
      'سنة حلوة': 'Cumpleaños',
      'نور البيت': 'Bebé',
      'كفو دايم': 'Felicitaciones',
      'مبروك التخرج': 'Graduación',
      'بداية جديدة': 'Nuevo Comienzo',
      'منزل مبارك': 'Nuevo Hogar',
      'أجر وشفاء': 'Recupérate Pronto',
      'عيد الفطر': 'Eid Al-Fitr',
      'عيد الأضحى': 'Eid Al-Adha',
      'رمضان كريم': 'Ramadán Kareem',
      'جمعة مباركة': 'Viernes Bendito',
      'مولود جديد': 'Recién Nacido',
    },

    // الفرنسية
    'fr': {
      'غالية وجت': 'Mariage',
      'ذكرى غالية': 'Anniversaire',
      'غلا العمر': 'Amour',
      'أبوي الغالي': 'Fête des Pères',
      'أمي الحبيبة': 'Fête des Mères',
      'سنة حلوة': 'Anniversaire',
      'نور البيت': 'Bébé',
      'كفو دايم': 'Félicitations',
      'مبروك التخرج': 'Remise de Diplôme',
      'بداية جديدة': 'Nouveau Départ',
      'منزل مبارك': 'Nouvelle Maison',
      'أجر وشفاء': 'Prompt Rétablissement',
      'عيد الفطر': 'Aïd al-Fitr',
      'عيد الأضحى': 'Aïd al-Adha',
      'رمضان كريم': 'Ramadan Kareem',
      'جمعة مباركة': 'Vendredi Béni',
      'مولود جديد': 'Nouveau-né',
    },

    // الألمانية
    'de': {
      'غالية وجت': 'Hochzeit',
      'ذكرى غالية': 'Jahrestag',
      'غلا العمر': 'Liebe',
      'أبوي الغالي': 'Vatertag',
      'أمي الحبيبة': 'Muttertag',
      'سنة حلوة': 'Geburtstag',
      'نور البيت': 'Baby',
      'كفو دايم': 'Glückwünsche',
      'مبروك التخرج': 'Abschluss',
      'بداية جديدة': 'Neuanfang',
      'منزل مبارك': 'Neues Zuhause',
      'أجر وشفاء': 'Gute Besserung',
      'عيد الفطر': 'Eid al-Fitr',
      'عيد الأضحى': 'Eid al-Adha',
      'رمضان كريم': 'Ramadan Kareem',
      'جمعة مباركة': 'Gesegneter Freitag',
      'مولود جديد': 'Neugeborenes',
    },
  };

  /// ترجمة معرفات المناسبات إلى أسماء بالعربية
  static const Map<String, String> occasionIdToArabicName = {
    'sticker_category_wedding': 'غالية وجت',
    'sticker_category_love': 'ذكرى غالية',
    'sticker_category_mother': 'أمي الحبيبة',
    'sticker_category_father': 'أبوي الغالي',
    'sticker_category_birthday': 'سنة حلوة',
    'sticker_category_baby': 'نور البيت',
    'sticker_category_congrats': 'كفو دايم',
    'sticker_category_graduation': 'مبروك التخرج',
    'sticker_category_newbeginning': 'بداية جديدة',
    'sticker_category_newhome': 'منزل مبارك',
    'sticker_category_getwellsoon': 'أجر وشفاء',
    'sticker_category_eid': 'عيد الفطر',
    'sticker_category_adha': 'عيد الأضحى',
    'sticker_category_ramadan': 'رمضان كريم',
    'sticker_category_friday': 'جمعة مباركة',
    'sticker_category_newbaby': 'مولود جديد',
  };

  /// ترجمة اسم المناسبة من العربية إلى اللغة المحددة
  ///
  /// [arabicName] اسم المناسبة بالعربية
  /// [locale] اللغة المطلوبة
  /// يرجع اسم المناسبة باللغة المطلوبة، أو الاسم العربي إذا لم تتوفر ترجمة
  static String translateOccasionName(String arabicName, Locale locale) {
    // إذا كانت اللغة هي العربية، نرجع الاسم كما هو
    if (locale.languageCode == 'ar') {
      return arabicName;
    }

    // نحاول الحصول على الترجمة
    final languageTranslations = _translations[locale.languageCode];
    if (languageTranslations != null) {
      final translation = languageTranslations[arabicName];
      if (translation != null) {
        return translation;
      }
    }

    // إذا لم تتوفر ترجمة، نرجع الاسم العربي
    return arabicName;
  }

  /// ترجمة معرف المناسبة إلى اسم باللغة المحددة
  ///
  /// [occasionId] معرف المناسبة (يمكن أن يكون String أو int)
  /// [locale] اللغة المطلوبة
  /// يرجع اسم المناسبة باللغة المطلوبة، أو معرف المناسبة إذا لم يتم العثور عليه
  static String translateOccasionId(dynamic occasionId, Locale locale) {
    // تحويل معرف المناسبة إلى نص (String) إذا لم يكن كذلك
    final String safeOccasionId = occasionId.toString();

    // نحصل على اسم المناسبة بالعربية
    final arabicName = occasionIdToArabicName[safeOccasionId];
    if (arabicName == null) {
      // محاولة البحث عن المعرف بدون البادئة sticker_category_
      if (safeOccasionId.startsWith('sticker_category_')) {
        final shortId = safeOccasionId.replaceFirst('sticker_category_', '');
        for (final entry in occasionIdToArabicName.entries) {
          if (entry.key.contains(shortId)) {
            return translateOccasionName(entry.value, locale);
          }
        }
      }

      return safeOccasionId; // نرجع المعرف إذا لم نجد اسمًا عربيًا
    }

    // نترجم الاسم العربي إلى اللغة المطلوبة
    return translateOccasionName(arabicName, locale);
  }
}
