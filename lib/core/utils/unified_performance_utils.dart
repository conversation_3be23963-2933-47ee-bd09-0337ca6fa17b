// lib/core/utils/unified_performance_utils.dart

import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_blurhash/flutter_blurhash.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';

import '../widgets/optimized/optimized_image.dart';
import '../widgets/optimized/optimized_list.dart';
import '../widgets/optimized/optimized_grid.dart';
import '../widgets/optimized/optimized_widget.dart';
import 'unified_image_cache_manager.dart';

/// Unified Performance Utilities
///
/// This class combines functionality from:
/// - PerformanceUtils
/// - EnhancedPerformanceUtils
/// - UIPerformanceHelper
///
/// Provides optimized widgets and utilities for improving app performance.
class UnifiedPerformanceUtils {
  // Singleton instance
  static final UnifiedPerformanceUtils _instance =
      UnifiedPerformanceUtils._internal();

  // Get singleton instance
  factory UnifiedPerformanceUtils() => _instance;

  // Private constructor
  UnifiedPerformanceUtils._internal();

  // Image cache manager
  static final _imageCacheManager = UnifiedImageCacheManager();

  // Custom cache manager for network images
  static final _customCacheManager = CacheManager(
    Config(
      'unifiedImageCache',
      stalePeriod: const Duration(days: 14),
      maxNrOfCacheObjects: 200,
      repo: JsonCacheInfoRepository(databaseName: 'unifiedImageCache'),
      fileService: HttpFileService(),
    ),
  );

  /// Get the custom cache manager
  static CacheManager get cacheManager => _customCacheManager;

  //
  // IMAGE OPTIMIZATION METHODS
  //

  /// Optimized network image with advanced features
  static Widget optimizedNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String? blurHash,
    bool useProgressiveLoading = true,
    bool freezeWhenInactive = false,
    bool isActive = true,
  }) {
    return OptimizedImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      blurHash: blurHash ?? 'LEHV6nWB2yk8pyo0adR*.7kCMdnj',
      useProgressiveLoading: useProgressiveLoading,
      freezeWhenInactive: freezeWhenInactive,
      isActive: isActive,
    );
  }

  /// Optimized local image
  static Widget optimizedLocalImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool freezeWhenInactive = false,
    bool isActive = true,
  }) {
    return OptimizedImage(
      imageUrl: imagePath,
      width: width,
      height: height,
      fit: fit,
      freezeWhenInactive: freezeWhenInactive,
      isActive: isActive,
    );
  }

  /// Optimized asset image
  static Widget optimizedAssetImage({
    required String assetPath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool freezeWhenInactive = false,
    bool isActive = true,
  }) {
    return OptimizedImage(
      imageUrl: assetPath,
      width: width,
      height: height,
      fit: fit,
      freezeWhenInactive: freezeWhenInactive,
      isActive: isActive,
    );
  }

  /// Basic network image with caching
  static Widget basicNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String placeholder = 'LEHV6nWB2yk8pyo0adR*.7kCMdnj',
  }) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: _customCacheManager,
      placeholder: (context, url) => BlurHash(hash: placeholder),
      errorWidget: (context, url, error) => const Icon(Icons.error),
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
    );
  }

  //
  // LIST AND GRID OPTIMIZATION METHODS
  //

  /// Optimized list view
  static Widget optimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
    ScrollController? controller,
    bool freezeInvisibleItems = true,
  }) {
    return OptimizedList(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: padding,
      controller: controller,
      freezeInvisibleItems: freezeInvisibleItems,
    );
  }

  /// Optimized grid view
  static Widget optimizedGridView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    required int crossAxisCount,
    double childAspectRatio = 1.0,
    double crossAxisSpacing = 0.0,
    double mainAxisSpacing = 0.0,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
    ScrollController? controller,
    bool freezeInvisibleItems = true,
  }) {
    return OptimizedGrid(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      crossAxisCount: crossAxisCount,
      childAspectRatio: childAspectRatio,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: padding,
      controller: controller,
      freezeInvisibleItems: freezeInvisibleItems,
    );
  }

  //
  // WIDGET OPTIMIZATION METHODS
  //

  /// Wrap a widget with RepaintBoundary
  static Widget withRepaintBoundary({
    required Widget child,
    String? id,
    bool freezeWhenInactive = false,
    bool isActive = true,
  }) {
    return OptimizedWidget(
      id: id,
      freezeWhenInactive: freezeWhenInactive,
      isActive: isActive,
      child: child,
    );
  }

  /// Optimize text widget
  static Widget optimizeText({
    required Widget child,
    bool addRepaintBoundary = true,
    bool useExactSize = true,
    Size? size,
  }) {
    Widget optimizedWidget = child;

    // Add RepaintBoundary to reduce repainting
    if (addRepaintBoundary) {
      optimizedWidget = RepaintBoundary(child: optimizedWidget);
    }

    // Use exact size for text to avoid recalculation
    if (useExactSize && size != null) {
      optimizedWidget = SizedBox(
        width: size.width,
        height: size.height,
        child: optimizedWidget,
      );
    }

    return optimizedWidget;
  }

  //
  // IMAGE PROCESSING METHODS
  //

  /// Compress and cache an image
  static Future<File?> compressAndCacheImage(
    File file, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    return _imageCacheManager.compressAndCacheImage(
      file,
      quality: quality,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
    );
  }

  /// Compress image bytes
  static Future<Uint8List?> compressImageBytes(
    Uint8List bytes, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      // Compress image bytes
      final result = await FlutterImageCompress.compressWithList(
        bytes,
        quality: quality,
        minWidth: maxWidth ?? 1024,
        minHeight: maxHeight ?? 1024,
      );

      return result;
    } catch (e) {
      debugPrint('Error compressing image bytes: $e');
      return null;
    }
  }

  //
  // CACHE MANAGEMENT METHODS
  //

  /// Preload an image
  static Future<void> preloadImage(String imageUrl) async {
    await _imageCacheManager.preloadImage(imageUrl);
  }

  /// Preload multiple images
  static Future<void> preloadImages(List<String> imageUrls) async {
    await _imageCacheManager.preloadImages(imageUrls);
  }

  /// Clear image cache
  static Future<void> clearCache() async {
    await _imageCacheManager.clearCache();
    await _customCacheManager.emptyCache();

    // Clear temporary files
    if (!kIsWeb) {
      final tempDir = await getTemporaryDirectory();
      if (await tempDir.exists()) {
        try {
          await for (final entity in tempDir.list()) {
            if (entity is File) {
              await entity.delete();
            }
          }
        } catch (e) {
          debugPrint('Error clearing temp files: $e');
        }
      }
    }
  }

  //
  // RENDERING DIAGNOSTICS METHODS
  //

  /// Enable rendering diagnostics
  static void enableRenderingDiagnostics() {
    debugPaintLayerBordersEnabled = true;
    debugRepaintRainbowEnabled = true;
  }

  /// Disable rendering diagnostics
  static void disableRenderingDiagnostics() {
    debugPaintLayerBordersEnabled = false;
    debugRepaintRainbowEnabled = false;
  }
}
