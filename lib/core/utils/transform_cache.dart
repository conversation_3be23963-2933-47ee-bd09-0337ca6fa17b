import 'package:flutter/material.dart';

/// ذاكرة التخزين المؤقت للتحويلات
class TransformCache {
  /// خريطة التحويلات المحفوظة
  final Map<String, Matrix4> _transformCache = {};
  
  /// خريطة الأحجام المحفوظة
  final Map<String, Size> _sizeCache = {};
  
  /// خريطة المواضع المحفوظة
  final Map<String, Offset> _offsetCache = {};
  
  /// خريطة زوايا الدوران المحفوظة
  final Map<String, double> _rotationCache = {};
  
  /// خريطة المقاييس المحفوظة
  final Map<String, double> _scaleCache = {};
  
  /// الحد الأقصى لعدد العناصر في الذاكرة المؤقتة
  final int maxCacheSize;
  
  /// قائمة ترتيب الاستخدام (LRU)
  final List<String> _usageOrder = [];
  
  /// إنشاء ذاكرة التخزين المؤقت
  TransformCache({this.maxCacheSize = 100});
  
  /// حفظ تحويل في الذاكرة المؤقتة
  void cacheTransform(String key, Matrix4 transform) {
    _updateUsage(key);
    _transformCache[key] = Matrix4.copy(transform);
    _enforceMaxSize();
  }
  
  /// استرجاع تحويل من الذاكرة المؤقتة
  Matrix4? getTransform(String key) {
    if (_transformCache.containsKey(key)) {
      _updateUsage(key);
      return Matrix4.copy(_transformCache[key]!);
    }
    return null;
  }
  
  /// حفظ حجم في الذاكرة المؤقتة
  void cacheSize(String key, Size size) {
    _updateUsage(key);
    _sizeCache[key] = size;
    _enforceMaxSize();
  }
  
  /// استرجاع حجم من الذاكرة المؤقتة
  Size? getSize(String key) {
    if (_sizeCache.containsKey(key)) {
      _updateUsage(key);
      return _sizeCache[key];
    }
    return null;
  }
  
  /// حفظ موضع في الذاكرة المؤقتة
  void cacheOffset(String key, Offset offset) {
    _updateUsage(key);
    _offsetCache[key] = offset;
    _enforceMaxSize();
  }
  
  /// استرجاع موضع من الذاكرة المؤقتة
  Offset? getOffset(String key) {
    if (_offsetCache.containsKey(key)) {
      _updateUsage(key);
      return _offsetCache[key];
    }
    return null;
  }
  
  /// حفظ زاوية دوران في الذاكرة المؤقتة
  void cacheRotation(String key, double rotation) {
    _updateUsage(key);
    _rotationCache[key] = rotation;
    _enforceMaxSize();
  }
  
  /// استرجاع زاوية دوران من الذاكرة المؤقتة
  double? getRotation(String key) {
    if (_rotationCache.containsKey(key)) {
      _updateUsage(key);
      return _rotationCache[key];
    }
    return null;
  }
  
  /// حفظ مقياس في الذاكرة المؤقتة
  void cacheScale(String key, double scale) {
    _updateUsage(key);
    _scaleCache[key] = scale;
    _enforceMaxSize();
  }
  
  /// استرجاع مقياس من الذاكرة المؤقتة
  double? getScale(String key) {
    if (_scaleCache.containsKey(key)) {
      _updateUsage(key);
      return _scaleCache[key];
    }
    return null;
  }
  
  /// إنشاء تحويل مركب
  Matrix4 createCompositeTransform({
    required Offset offset,
    required double rotation,
    required double scale,
    Offset? anchor,
  }) {
    final String key = 'composite_${offset.dx}_${offset.dy}_${rotation}_$scale';
    
    // التحقق من الذاكرة المؤقتة أولاً
    final cached = getTransform(key);
    if (cached != null) {
      return cached;
    }
    
    // إنشاء التحويل الجديد
    final transform = Matrix4.identity();
    
    // تطبيق الترجمة
    transform.translate(offset.dx, offset.dy);
    
    // تطبيق الدوران حول النقطة المرجعية
    if (anchor != null) {
      transform.translate(anchor.dx, anchor.dy);
      transform.rotateZ(rotation);
      transform.translate(-anchor.dx, -anchor.dy);
    } else {
      transform.rotateZ(rotation);
    }
    
    // تطبيق المقياس
    transform.scale(scale, scale);
    
    // حفظ في الذاكرة المؤقتة
    cacheTransform(key, transform);
    
    return transform;
  }
  
  /// تحديث ترتيب الاستخدام
  void _updateUsage(String key) {
    _usageOrder.remove(key);
    _usageOrder.add(key);
  }
  
  /// فرض الحد الأقصى للحجم
  void _enforceMaxSize() {
    while (_usageOrder.length > maxCacheSize) {
      final oldestKey = _usageOrder.removeAt(0);
      _transformCache.remove(oldestKey);
      _sizeCache.remove(oldestKey);
      _offsetCache.remove(oldestKey);
      _rotationCache.remove(oldestKey);
      _scaleCache.remove(oldestKey);
    }
  }
  
  /// مسح عنصر من الذاكرة المؤقتة
  void remove(String key) {
    _usageOrder.remove(key);
    _transformCache.remove(key);
    _sizeCache.remove(key);
    _offsetCache.remove(key);
    _rotationCache.remove(key);
    _scaleCache.remove(key);
  }
  
  /// مسح جميع العناصر
  void clear() {
    _usageOrder.clear();
    _transformCache.clear();
    _sizeCache.clear();
    _offsetCache.clear();
    _rotationCache.clear();
    _scaleCache.clear();
  }
  
  /// الحصول على إحصائيات الذاكرة المؤقتة
  Map<String, int> getStats() {
    return {
      'transforms': _transformCache.length,
      'sizes': _sizeCache.length,
      'offsets': _offsetCache.length,
      'rotations': _rotationCache.length,
      'scales': _scaleCache.length,
      'total': _usageOrder.length,
    };
  }

  /// مسح ذاكرة التخزين المؤقت لعنصر معين
  void clearCacheForElement(String elementId) {
    final keysToRemove = <String>[];

    // البحث عن جميع المفاتيح التي تبدأ بـ elementId
    for (final key in _usageOrder) {
      if (key.startsWith(elementId)) {
        keysToRemove.add(key);
      }
    }

    // إزالة المفاتيح المطابقة
    for (final key in keysToRemove) {
      remove(key);
    }
  }
}
