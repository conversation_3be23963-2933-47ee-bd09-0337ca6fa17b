import 'package:flutter/material.dart';
import 'dart:math' as math;

/// نظام الالتصاق الاحترافي
class ProfessionalSnappingSystem {
  static const double defaultSnapThreshold = 8.0;
  static const double gridSize = 10.0;
  static const double rulerSnapThreshold = 5.0;
  
  /// الالتصاق بالشبكة
  static Offset snapToGrid(
    Offset position, {
    double gridSize = ProfessionalSnappingSystem.gridSize,
    double threshold = defaultSnapThreshold,
  }) {
    final snappedX = (position.dx / gridSize).round() * gridSize;
    final snappedY = (position.dy / gridSize).round() * gridSize;
    
    final deltaX = (position.dx - snappedX).abs();
    final deltaY = (position.dy - snappedY).abs();
    
    return Offset(
      deltaX <= threshold ? snappedX : position.dx,
      deltaY <= threshold ? snappedY : position.dy,
    );
  }
  
  /// الالتصاق بالعناصر الأخرى
  static SnapResult snapToElements(
    Rect currentRect,
    List<Rect> otherRects, {
    double threshold = defaultSnapThreshold,
  }) {
    Offset snapOffset = Offset.zero;
    List<SnapLine> snapLines = [];
    
    for (final otherRect in otherRects) {
      // الالتصاق الأفقي
      final horizontalSnap = _snapHorizontally(currentRect, otherRect, threshold);
      if (horizontalSnap.snapped) {
        snapOffset = Offset(horizontalSnap.offset, snapOffset.dy);
        snapLines.addAll(horizontalSnap.lines);
      }
      
      // الالتصاق العمودي
      final verticalSnap = _snapVertically(currentRect, otherRect, threshold);
      if (verticalSnap.snapped) {
        snapOffset = Offset(snapOffset.dx, verticalSnap.offset);
        snapLines.addAll(verticalSnap.lines);
      }
    }
    
    return SnapResult(
      offset: snapOffset,
      snapLines: snapLines,
      snapped: snapOffset != Offset.zero,
    );
  }
  
  /// الالتصاق بحدود الحاوية
  static Offset snapToContainer(
    Rect elementRect,
    Rect containerRect, {
    double threshold = defaultSnapThreshold,
  }) {
    double deltaX = 0;
    double deltaY = 0;
    
    // الالتصاق بالحدود اليسرى واليمنى
    if ((elementRect.left - containerRect.left).abs() <= threshold) {
      deltaX = containerRect.left - elementRect.left;
    } else if ((elementRect.right - containerRect.right).abs() <= threshold) {
      deltaX = containerRect.right - elementRect.right;
    }
    
    // الالتصاق بالحدود العلوية والسفلية
    if ((elementRect.top - containerRect.top).abs() <= threshold) {
      deltaY = containerRect.top - elementRect.top;
    } else if ((elementRect.bottom - containerRect.bottom).abs() <= threshold) {
      deltaY = containerRect.bottom - elementRect.bottom;
    }
    
    return Offset(deltaX, deltaY);
  }
  
  /// الالتصاق بالمركز
  static Offset snapToCenter(
    Rect elementRect,
    Rect containerRect, {
    double threshold = defaultSnapThreshold,
  }) {
    final elementCenter = elementRect.center;
    final containerCenter = containerRect.center;
    
    double deltaX = 0;
    double deltaY = 0;
    
    // الالتصاق بالمركز الأفقي
    if ((elementCenter.dx - containerCenter.dx).abs() <= threshold) {
      deltaX = containerCenter.dx - elementCenter.dx;
    }
    
    // الالتصاق بالمركز العمودي
    if ((elementCenter.dy - containerCenter.dy).abs() <= threshold) {
      deltaY = containerCenter.dy - elementCenter.dy;
    }
    
    return Offset(deltaX, deltaY);
  }
  
  static _SnapData _snapHorizontally(Rect current, Rect other, double threshold) {
    List<SnapLine> lines = [];
    double offset = 0;
    bool snapped = false;
    
    // محاذاة اليسار
    if ((current.left - other.left).abs() <= threshold) {
      offset = other.left - current.left;
      snapped = true;
      lines.add(SnapLine(
        start: Offset(other.left, math.min(current.top, other.top)),
        end: Offset(other.left, math.max(current.bottom, other.bottom)),
        type: SnapLineType.vertical,
      ));
    }
    // محاذاة اليمين
    else if ((current.right - other.right).abs() <= threshold) {
      offset = other.right - current.right;
      snapped = true;
      lines.add(SnapLine(
        start: Offset(other.right, math.min(current.top, other.top)),
        end: Offset(other.right, math.max(current.bottom, other.bottom)),
        type: SnapLineType.vertical,
      ));
    }
    // محاذاة المركز الأفقي
    else if ((current.center.dx - other.center.dx).abs() <= threshold) {
      offset = other.center.dx - current.center.dx;
      snapped = true;
      lines.add(SnapLine(
        start: Offset(other.center.dx, math.min(current.top, other.top)),
        end: Offset(other.center.dx, math.max(current.bottom, other.bottom)),
        type: SnapLineType.vertical,
      ));
    }
    
    return _SnapData(offset: offset, snapped: snapped, lines: lines);
  }
  
  static _SnapData _snapVertically(Rect current, Rect other, double threshold) {
    List<SnapLine> lines = [];
    double offset = 0;
    bool snapped = false;
    
    // محاذاة الأعلى
    if ((current.top - other.top).abs() <= threshold) {
      offset = other.top - current.top;
      snapped = true;
      lines.add(SnapLine(
        start: Offset(math.min(current.left, other.left), other.top),
        end: Offset(math.max(current.right, other.right), other.top),
        type: SnapLineType.horizontal,
      ));
    }
    // محاذاة الأسفل
    else if ((current.bottom - other.bottom).abs() <= threshold) {
      offset = other.bottom - current.bottom;
      snapped = true;
      lines.add(SnapLine(
        start: Offset(math.min(current.left, other.left), other.bottom),
        end: Offset(math.max(current.right, other.right), other.bottom),
        type: SnapLineType.horizontal,
      ));
    }
    // محاذاة المركز العمودي
    else if ((current.center.dy - other.center.dy).abs() <= threshold) {
      offset = other.center.dy - current.center.dy;
      snapped = true;
      lines.add(SnapLine(
        start: Offset(math.min(current.left, other.left), other.center.dy),
        end: Offset(math.max(current.right, other.right), other.center.dy),
        type: SnapLineType.horizontal,
      ));
    }
    
    return _SnapData(offset: offset, snapped: snapped, lines: lines);
  }
}

/// نتيجة عملية الالتصاق
class SnapResult {
  final Offset offset;
  final List<SnapLine> snapLines;
  final bool snapped;
  
  const SnapResult({
    required this.offset,
    required this.snapLines,
    required this.snapped,
  });
}

/// خط الالتصاق
class SnapLine {
  final Offset start;
  final Offset end;
  final SnapLineType type;
  final Color color;
  final double strokeWidth;
  
  const SnapLine({
    required this.start,
    required this.end,
    required this.type,
    this.color = Colors.blue,
    this.strokeWidth = 1.0,
  });
}

/// نوع خط الالتصاق
enum SnapLineType {
  horizontal,
  vertical,
}

/// بيانات الالتصاق الداخلية
class _SnapData {
  final double offset;
  final bool snapped;
  final List<SnapLine> lines;
  
  const _SnapData({
    required this.offset,
    required this.snapped,
    required this.lines,
  });
}

/// رسام خطوط الالتصاق
class SnapLinePainter extends CustomPainter {
  final List<SnapLine> snapLines;
  
  const SnapLinePainter({required this.snapLines});
  
  @override
  void paint(Canvas canvas, Size size) {
    for (final line in snapLines) {
      final paint = Paint()
        ..color = line.color
        ..strokeWidth = line.strokeWidth
        ..style = PaintingStyle.stroke;
      
      canvas.drawLine(line.start, line.end, paint);
    }
  }
  
  @override
  bool shouldRepaint(covariant SnapLinePainter oldDelegate) {
    return snapLines != oldDelegate.snapLines;
  }
}

/// شبكة الخلفية
class GridPainter extends CustomPainter {
  final double gridSize;
  final Color gridColor;
  final double strokeWidth;
  final bool showGrid;
  
  const GridPainter({
    this.gridSize = 10.0,
    this.gridColor = Colors.grey,
    this.strokeWidth = 0.5,
    this.showGrid = true,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (!showGrid) return;
    
    final paint = Paint()
      ..color = gridColor.withOpacity(0.3)
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;
    
    // رسم الخطوط العمودية
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
    
    // رسم الخطوط الأفقية
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }
  
  @override
  bool shouldRepaint(covariant GridPainter oldDelegate) {
    return gridSize != oldDelegate.gridSize ||
           gridColor != oldDelegate.gridColor ||
           strokeWidth != oldDelegate.strokeWidth ||
           showGrid != oldDelegate.showGrid;
  }
}
