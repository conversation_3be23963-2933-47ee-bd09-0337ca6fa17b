// lib/core/presentation/widgets/loading_indicator.dart

import 'package:flutter/material.dart';

/// مؤشر تحميل
class LoadingIndicator extends StatelessWidget {
  /// إنشاء مؤشر تحميل
  const LoadingIndicator({
    super.key,
    this.color,
    this.size = 24.0,
    this.strokeWidth = 4.0,
  });

  /// لون المؤشر
  final Color? color;
  
  /// حجم المؤشر
  final double size;
  
  /// عرض الخط
  final double strokeWidth;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: color != null
            ? AlwaysStoppedAnimation<Color>(color!)
            : AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
      ),
    );
  }
}
