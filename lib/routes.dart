import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// استيراد التأثيرات الانتقالية المخصصة
import 'core/routes/page_transition.dart';
// استيراد باقي الصفحات والبلوكات...
import 'core/widgets/splash_page.dart';
import 'features/auth/presentation/pages/login_page.dart';
import 'features/auth/presentation/pages/register_page.dart';
import 'features/home/<USER>/blocs/home/<USER>';
import 'features/home/<USER>/pages/new_home_page.dart';
import 'features/create_card/presentation/bloc/card_editor_bloc.dart';
import 'features/create_card/presentation/bloc/card_editor_event.dart';
import 'features/create_card/presentation/pages/card_editor_page.dart';
import 'features/create_card/domain/entities/card_element.dart';
import 'features/ai/presentation/blocs/ai_bloc.dart';
import 'features/suggestions/presentation/pages/modern_suggestions_page.dart';
import 'features/suggestions/presentation/pages/user_suggestions_page.dart';
// استيراد Bloc و Page للإشعارات
import 'features/notifications/presentation/bloc/notifications_bloc.dart';
import 'features/notifications/presentation/pages/notifications_page.dart';
import 'features/profile/presentation/pages/profile_page.dart';
import 'features/settings/presentation/blocs/settings/settings_bloc.dart';
import 'features/settings/presentation/pages/enhanced_settings_page.dart';
// Suggestions removed
// استيراد Bloc و Page لتوليد الصور بالذكاء الاصطناعي
import 'core/widgets/layout/example_page.dart';
import 'features/user_guide/presentation/pages/modern_user_guide_page.dart';

import 'features/cards_history/presentation/pages/my_cards_page.dart';
import 'features/cards/presentation/pages/enhanced_cards_list_page.dart';
import 'injection_container.dart' show sl;

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String home = '/home';
  static const String profile = '/profile';
  static const String suggestions = '/suggestions';
  static const String mySuggestions = '/my-suggestions';
  static const String cardsList = '/cards_list';
  static const String createCard = '/createCard';
  static const String greetingsIslandEditor = '/greetings-island-editor';
  static const String notifications = '/notifications'; // مسار الإشعارات
  static const String settings = '/settings'; // مسار الإعدادات
  static const String aiImageGenerator =
      '/ai-image-generator'; // مسار توليد الصور بالذكاء الاصطناعي
  static const String responsiveExample =
      '/responsive-example'; // مثال على التصميم المتجاوب
  static const String userGuide = '/user-guide'; // مسار دليل المستخدم
  static const String cardsHistory = '/cards-history'; // مسار تاريخ البطاقات
}

Route<dynamic> generateRoute(RouteSettings settings) {
  switch (settings.name) {
    case AppRoutes.splash:
      return PageTransition(
        type: PageTransitionType.fade,
        duration: const Duration(milliseconds: 800),
        page: const SplashPage(),
        settings: settings,
      );

    case AppRoutes.login:
      return PageTransition(
        type: PageTransitionType.fadeWithScaleAndRotation,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOutQuint,
        page: const LoginPage(),
        settings: settings,
      );

    case AppRoutes.register:
      return PageTransition(
        type: PageTransitionType.rightToLeftWithFade,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeOut,
        page: const RegisterPage(),
        settings: settings,
      );

    case AppRoutes.home:
      return PageTransition(
        type: PageTransitionType.fade,
        duration: const Duration(milliseconds: 300),
        page: MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: sl<HomeBloc>(),
            ),
            BlocProvider.value(
              value: sl<NotificationsBloc>(),
            ),
          ],
          child: const NewHomePage(),
        ),
        settings: settings,
      );

    case AppRoutes.profile:
      final args = settings.arguments;
      if (args is String) {
        return PageTransition(
          type: PageTransitionType.bottomToTop,
          duration: const Duration(milliseconds: 350),
          curve: Curves.easeOutCubic,
          page: ProfilePage(uid: args),
          settings: settings,
        );
      }
      return _errorRoute("No uid found in arguments for ProfilePage");

    case AppRoutes.suggestions:
      return PageTransition(
        type: PageTransitionType.rightToLeft,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        page: const ModernSuggestionsPage(),
        settings: settings,
      );

    case AppRoutes.mySuggestions:
      return PageTransition(
        type: PageTransitionType.rightToLeft,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        page: const UserSuggestionsPage(),
        settings: settings,
      );

    case AppRoutes.cardsList:
      // تحديد معرف المناسبة واسمها من الوسائط
      final args = settings.arguments is Map<String, dynamic>
          ? (settings.arguments as Map<String, dynamic>)
          : <String, dynamic>{};

      // التأكد من تحويل معرف المناسبة إلى String بغض النظر عن نوعه
      dynamic rawOccasionId = args['occasionId'];
      String? occasionId;

      if (rawOccasionId != null) {
        // تحويل صريح إلى String لضمان اتساق نوع البيانات
        occasionId = rawOccasionId.toString();
      }

      final occasionName = args['occasionName'] as String?;

      debugPrint(
          'توجيه إلى صفحة البطاقات: معرف المناسبة = $occasionId، اسم المناسبة = $occasionName');

      return PageTransition(
        type: PageTransitionType.rightToLeft,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        page: EnhancedCardsListPage(
          occasionId: occasionId,
          occasionName: occasionName,
        ),
        settings: settings,
      );

    case AppRoutes.createCard:
      // تحديد ما إذا كان هناك بطاقة للتحرير أو إنشاء بطاقة جديدة
      final args = settings.arguments is Map<String, dynamic>
          ? (settings.arguments as Map<String, dynamic>)
          : <String, dynamic>{};

      final card = args['card'];
      final cardId =
          args['cardId'] as String?; // معرف البطاقة للاسترجاع من Firebase
      final isAiMode = args['isAiMode'] ?? false;

      // التحقق من نوع البطاقة المرسلة
      List<CardElement>? initialElements;
      Color? initialBackgroundColor;

      // إذا كانت البطاقة من نوع PopularCardEntity، فلا تحتوي على عناصر
      // وسنستخدم قائمة فارغة من العناصر
      if (card != null) {
        try {
          initialElements = card.elements;
          initialBackgroundColor = card.backgroundColor;
        } catch (e) {
          // إذا لم تكن البطاقة تحتوي على خاصية elements، فسنستخدم قائمة فارغة
          initialElements = null;
          initialBackgroundColor = Colors.white;
        }
      }

      return PageTransition(
        type: PageTransitionType.scale,
        alignment: Alignment.center,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeOutQuad,
        page: MultiBlocProvider(
          providers: [
            BlocProvider<CardEditorBloc>(
              create: (_) => sl<CardEditorBloc>()..add(LoadElementsEvent()),
            ),
            BlocProvider<AiBloc>(
              create: (_) => sl<AiBloc>(),
            ),
          ],
          child: CardEditorPage(
            initialElements: initialElements,
            initialBackgroundColor: initialBackgroundColor ?? Colors.white,
            isAiMode: isAiMode,
            cardId: cardId, // تمرير معرف البطاقة للاسترجاع من Firebase
          ),
        ),
        settings: settings,
      );

    case AppRoutes.greetingsIslandEditor:
      // تحديد ما إذا كان هناك بطاقة للتحرير أو إنشاء بطاقة جديدة
      final args = settings.arguments is Map<String, dynamic>
          ? (settings.arguments as Map<String, dynamic>)
          : <String, dynamic>{};

      final card = args['card'];
      final cardId =
          args['cardId'] as String?; // معرف البطاقة للاسترجاع من Firebase
      final isAiMode = args['isAiMode'] ?? false;

      return PageTransition(
        type: PageTransitionType.scale,
        alignment: Alignment.center,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeOutQuad,
        page: MultiBlocProvider(
          providers: [
            BlocProvider<CardEditorBloc>(
              create: (_) => sl<CardEditorBloc>()..add(LoadElementsEvent()),
            ),
            BlocProvider<AiBloc>(
              create: (_) => sl<AiBloc>(),
            ),
          ],
          child: CardEditorPage(
            initialElements: card?.elements,
            initialBackgroundColor: card?.backgroundColor ?? Colors.white,
            isAiMode: isAiMode,
            cardId: cardId, // تمرير معرف البطاقة للاسترجاع من Firebase
          ),
        ),
        settings: settings,
      );

    case AppRoutes.notifications:
      return PageTransition(
        type: PageTransitionType.rightToLeftWithFade,
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeOut,
        page: BlocProvider.value(
          value: sl<NotificationsBloc>(),
          child: const NotificationsPage(),
        ),
        settings: settings,
      );

    case AppRoutes.settings:
      return PageTransition(
        type: PageTransitionType.bottomToTop,
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeOutCubic,
        page: BlocProvider.value(
          value: sl<SettingsBloc>(),
          child: const EnhancedSettingsPage(),
        ),
        settings: settings,
      );

    case AppRoutes.responsiveExample:
      return PageTransition(
        type: PageTransitionType.rightToLeft,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        page: const ResponsiveDesignExamplePage(),
        settings: settings,
      );

    case AppRoutes.userGuide:
      return PageTransition(
        type: PageTransitionType.rightToLeft,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        page: const ModernUserGuidePage(),
        settings: settings,
      );

    case AppRoutes.cardsHistory:
      return PageTransition(
        type: PageTransitionType.rightToLeft,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        page: const MyCardsPage(),
        settings: settings,
      );

    default:
      return _errorRoute("No route defined for ${settings.name}");
  }
}

Route<dynamic> _errorRoute(String message) {
  return PageTransition(
    type: PageTransitionType.fade,
    duration: const Duration(milliseconds: 300),
    page: Scaffold(
      appBar: AppBar(title: const Text('خطأ في التوجيه')),
      body: Center(child: Text(message)),
    ),
  );
}
