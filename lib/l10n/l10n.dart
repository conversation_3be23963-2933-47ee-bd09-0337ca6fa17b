import 'package:flutter/material.dart';

/// L10n class that provides localization utilities
class L10n {
  /// Private constructor to prevent instantiation
  L10n._();

  /// List of all supported locales in the application
  static const List<Locale> all = [
    Locale('ar'), // Arabic
    Locale('de'), // German
    Locale('en'), // English
    Locale('es'), // Spanish
    Locale('fr'), // French
  ];
}
