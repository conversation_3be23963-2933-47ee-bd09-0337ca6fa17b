{"@@locale": "en", "appName": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "welcome": "Welcome to <PERSON><PERSON><PERSON>!", "errorOccurred": "An unexpected error occurred", "tryAgain": "Try Again", "loading": "Loading...", "email": "Email", "password": "Password", "name": "Name", "phoneNumber": "Phone Number", "confirmPassword": "Confirm Password", "continueWithGoogle": "Continue with Google", "continueWithFacebook": "Continue with Facebook", "language": "Language", "arabic": "Arabic", "english": "English", "whatsappWelcomeMessage": "Hello, I would like to inquire about the Mashair app", "appropriateWords": "Appropriate Words", "expressBeautifulFeelings": "To Express Beautiful Feelings", "newText": "New Text", "addQrCode": "Add QR Code", "enterLink": "Enter Link", "preview": "Preview", "errorGeneratingQr": "Error generating QR code", "qrCodeAddedSuccessfully": "QR Code added successfully", "pleaseEnterValidUrl": "Please enter a valid URL", "drawSignature": "Draw Signature", "textSignature": "Text Signature", "clear": "Clear", "yourSignature": "Your Signature", "typeYourSignature": "Type your signature here", "selectFont": "Select Font", "pleaseDrawSignature": "Please draw signature first", "pleaseEnterSignatureText": "Please enter signature text", "profile": "Profile", "home": "Home", "settings": "Settings", "notifications": "Notifications", "greetingCards": "Greeting Cards", "popularCards": "Popular Cards", "occasionCards": "Occasion Cards", "registerHint": "Don't have an account? Create a new one", "loginHint": "Already have an account?", "createCard": "Create Card", "title": "Title", "occasionId": "Occasion ID", "successCreateCard": "Card created successfully!", "cards": "Cards", "banners": "Banners", "darkMode": "Dark Mode", "selectLanguage": "Select Language", "spanish": "Spanish", "french": "French", "german": "German", "languageChanged": "Language Changed", "notificationsSettings": "Notifications Settings", "enableNotifications": "Enable Notifications", "disableNotifications": "Disable Notifications", "notificationsEnabled": "Notifications Enabled", "notificationsDisabled": "Notifications Disabled", "markAsRead": "<PERSON> as read", "markAllAsRead": "Mark all as read", "deleteAll": "Delete all", "notificationMarkedAsRead": "Notification marked as read", "allNotificationsMarkedAsRead": "All notifications marked as read", "allNotificationsDeleted": "All notifications deleted", "notificationDeleted": "Notification deleted", "noNotifications": "No Notifications", "loadingNotifications": "Loading Notifications...", "newNotifications": "New Notifications", "allOccasions": "All Occasions", "eidAlFitr": "<PERSON><PERSON>", "eidAlAdha": "<PERSON><PERSON>", "ramadan": "<PERSON><PERSON>", "birthday": "Birthday", "wedding": "Wedding", "graduation": "Graduation", "mothersDay": "Mother's Day", "fathersDay": "Father's Day", "valentinesDay": "Valentine's Day", "newYear": "New Year", "anniversary": "Anniversary", "babyShower": "Baby Shower", "congratulations": "Congratulations", "newHome": "New Home", "getWell": "Get Well Soon", "newBeginning": "New Beginning", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "share": "Share", "send": "Send", "search": "Search", "yes": "Yes", "no": "No", "ok": "OK", "version": "Version", "buildNumber": "Build Number", "aboutApp": "About App", "privacyPolicy": "Privacy Policy", "termsAndConditions": "Terms and Conditions", "contactUs": "Contact Us", "rateApp": "Rate App", "shareApp": "Share App", "copyright": "All rights reserved to\ncraft solutions © 2025", "pleaseLogin": "Please Login", "appearance": "Appearance", "enableDarkMode": "Enable Dark Mode", "cardSettings": "Card Settings", "account": "Account", "receiveNotifications": "Receive Notifications", "deleteAllNotifications": "Delete All Notifications", "featureComingSoon": "This feature is coming soon", "homePage": "Home Page", "createNewCard": "Create New Card", "aiAssistant": "AI Assistant", "createWithAi": "Create a card with AI assistance", "error": "Error", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loggingOut": "Logging out...", "logoutConfirmation": "Are you sure you want to log out?", "appDescription": "Mashair app provides ready-made phrases and allows you to create your own expressions or let Mashair AI write them for you, suitable for flower and gift shops or even for personal dedications to family and friends", "defaultCardSize": "Default <PERSON> Si<PERSON>", "defaultSizeForNewCards": "<PERSON><PERSON><PERSON> for New Cards", "small": "Small", "medium": "Medium", "large": "Large", "loginWithSocial": "Or login with", "noAccount": "Don't have an account? Register with us", "touchHearts": "Make your words touch hearts and immortalize beautiful moments.", "subscribeNow": "Subscribe now and start sending the most beautiful feelings", "addText": "Add Text", "addSticker": "<PERSON><PERSON>", "addImage": "Add Image", "cardBackgroundColor": "Card Background Color", "addSongLink": "Add Song Link", "addSignature": "Add Signature", "saveDraft": "Save Draft", "undo": "Undo", "redo": "Redo", "suggestions": "Suggestions", "viaEmail": "Via Email", "visitWebsite": "Visit Our Website", "whatsappMessage": "Message Us on WhatsApp", "confirm": "Confirm", "logoutConfirm": "Are you sure you want to log out?", "allRightsReserved": "All rights reserved to\ncraft solutions © 2025", "emailError": "Cannot open email application", "websiteError": "Cannot open website", "whatsappError": "Cannot open WhatsApp", "editCard": "Edit Card", "cardDetails": "Card Details", "cardPreview": "Card Preview", "shareCard": "Share Card", "downloadCard": "Download Card", "printCard": "Print Card", "deleteCard": "Delete Card", "confirmDelete": "Are you sure you want to delete this card?", "cardDeleted": "Card deleted successfully", "cardSaved": "Card saved successfully", "cardShared": "Card shared successfully", "cardDownloaded": "Card downloaded successfully", "next": "Next", "back": "Back", "addElement": "Add Element", "drafts": "Drafts", "aiPrompt": "Tell me the occasion and who you're giving it to, and I'll write you the most beautiful dedication!", "aiError": "AI Error: {message}", "sendSuggestion": "Send Suggestion", "appVersion": "App Version", "versionNumber": "Version {version} (Build: {buildNumber})", "personalInfo": "Personal Information", "updateProfile": "Update Profile", "profileUpdated": "Profile Updated", "yourSuggestions": "Your Suggestions Matter", "suggestionsDescription": "We value your feedback and suggestions to improve our services", "suggestionTitle": "Suggestion Title", "suggestionDetails": "Suggestion Details", "suggestionSent": "Your suggestion has been sent successfully", "thankYou": "Thank You", "thankYouMessage": "Thank you for your message", "mySuggestions": "My Suggestions", "noSuggestionsYet": "No Suggestions Yet", "addSuggestion": "Add Suggestion", "username": "Username", "fieldRequired": "This field is required", "createWithAI": "Create Card with AI", "createYourCard": "Create Your Own Card", "mostPopularCards": "Most Popular Cards", "yourOpinionMatters": "Your Opinion Matters", "contactInfo": "Contact Information", "yourMessage": "Your Message", "writeYourSuggestionHere": "Write your suggestion here...", "now": "Now", "minutesAgo": "{minutes} minutes ago", "@minutesAgo": {"placeholders": {"minutes": {"type": "int"}}}, "hoursAgo": "{hours} hours ago", "@hoursAgo": {"placeholders": {"hours": {"type": "int"}}}, "daysAgo": "{days} days ago", "@daysAgo": {"placeholders": {"days": {"type": "int"}}}, "exportPreview": "Export Preview", "printPreview": "Print Preview", "print": "Print", "saveAsPdf": "Save as PDF", "preparing": "Preparing...", "exportFailed": "Export failed", "saveFailed": "File save failed", "exportSuccess": "Export successful", "view": "View", "exportError": "Export error", "retry": "Retry", "noPreview": "No preview available", "exporting": "Exporting...", "stickerKey": "<PERSON>er Key", "previewFailed": "Preview loading failed", "export": "Export", "cardEditor": "Card Editor", "exportAsImage": "Export as Image", "clearAll": "Clear All", "clearAllConfirmation": "Are you sure you want to clear all elements?", "enterPrompt": "Please enter a prompt for text generation", "generate": "Generate", "generatedText": "Generated Text", "addToCard": "Add to Card", "chooseSticker": "<PERSON><PERSON>", "chooseColor": "Choose Color", "apply": "Apply", "enterSongLink": "Enter Song Link", "add": "Add", "shareAsImage": "Share as Image", "shareAsPdf": "Share as PDF", "imageSaved": "Image Saved", "pdfSaved": "PDF Saved", "fileFormat": "File Format", "resolution": "Resolution", "quality": "Quality", "paperSize": "Paper Size", "includeMargins": "Include <PERSON><PERSON>", "includeBackground": "Include Background", "backgroundColor": "Background Color", "selectColor": "Select Color", "select": "Select", "cardsHistory": "Cards History", "templatesGallery": "Templates Gallery", "userGuide": "User Guide", "noData": "No Data", "cardsForYou": "Cards for You", "card": "Card", "aiCardCreator": "AI Card Creator", "aiCardDescription": "Use artificial intelligence to create distinctive cards", "occasions": "Occasions", "viewAll": "View All", "noOccasions": "No Occasions", "noPopularCards": "No Popular Cards", "gettingStarted": "Getting Started", "aiFeatures": "AI Features", "sharingAndExporting": "Sharing and Exporting", "tips": "Tips and Tricks", "introduction": "Introduction", "appIntroduction": "App Introduction", "navigation": "Navigation", "homeScreen": "Home Screen", "homeScreenDesc": "Home Screen Description", "sideMenu": "Side Menu", "sideMenuDesc": "Side Menu Description", "createNewCardDesc": "Create New Card Description", "editorOverview": "Editor Overview", "editorOverviewDesc": "Editor Overview Description", "addingElements": "Adding Elements", "addingText": "Adding Text", "addingTextDesc": "Adding Text Description", "addingImages": "Adding Images", "addingImagesDesc": "Adding Images Description", "addingShapes": "Adding <PERSON>", "addingShapesDesc": "Adding Shapes Description", "addingStickers": "Adding Stickers", "addingStickersDesc": "Adding Stickers Description", "editingElements": "Editing Elements", "selectingElements": "Selecting Elements", "selectingElementsDesc": "Selecting Elements Description", "movingElements": "Moving Elements", "movingElementsDesc": "Moving Elements Description", "resizingElements": "Resizing Elements", "resizingElementsDesc": "Resizing Elements Description", "rotatingElements": "Rotating Elements", "rotatingElementsDesc": "Rotating Elements Description", "deletingElements": "Deleting Elements", "deletingElementsDesc": "Deleting Elements Description", "stylingElements": "Styling Elements", "stylingElementsDesc": "Styling Elements Description", "textFormatting": "Text Formatting", "textFormattingDesc": "Text Formatting Description", "imageFilters": "Image Filters", "imageFiltersDesc": "Image Filters Description", "shapeStyles": "<PERSON><PERSON><PERSON>", "shapeStylesDesc": "<PERSON><PERSON>pe Styles Description", "alignmentAndDistribution": "Alignment and Distribution", "alignmentAndDistributionDesc": "Alignment and Distribution Description", "alignLeft": "Align Left", "alignCenter": "Align Center", "alignRight": "Align Right", "alignTop": "Align Top", "alignMiddle": "Align Middle", "alignBottom": "Align Bottom", "distributeHorizontally": "Distribute Horizontally", "distributeVertically": "Distribute Vertically", "groupingElements": "Grouping Elements", "groupingElementsDesc": "Grouping Elements Description", "groupElements": "Group Elements", "ungroupElements": "Ungroup Elements", "layerManagement": "Layer Management", "layerManagementDesc": "Layer Management Description", "bringToFront": "Bring to Front", "sendToBack": "Send to Back", "bringForward": "Bring Forward", "sendBackward": "Send Backward", "undoRedo": "Undo/Redo", "undoRedoDesc": "Undo/Redo Description", "savingCards": "Saving Cards", "savingCardsDesc": "Saving Cards Description", "saveCard": "Save Card", "autoSave": "Auto Save", "exportingCards": "Exporting Cards", "exportingCardsDesc": "Exporting Cards Description", "exportAsPdf": "Export as PDF", "exportOptions": "Export Options", "sharingCards": "Sharing Cards", "sharingCardsDesc": "Sharing Cards Description", "shareViaWhatsapp": "Share via WhatsApp", "shareViaEmail": "Share via Email", "shareViaSocialMedia": "Share via Social Media", "printingCards": "Printing Cards", "printingCardsDesc": "Printing Cards Description", "printerSettings": "Printer <PERSON>s", "aiTextGeneration": "AI Text Generation", "aiTextGenerationDesc": "AI Text Generation Description", "aiImageGeneration": "AI Image Generation", "aiImageGenerationDesc": "AI Image Generation Description", "aiCardSuggestions": "AI Card Suggestions", "aiCardSuggestionsDesc": "AI Card Suggestions Description", "aiPromptTips": "AI Prompt Tips", "aiPromptTipsDesc": "AI Prompt Tips Description", "keyboardShortcuts": "Keyboard Shortcuts", "keyboardShortcutsDesc": "Keyboard Shortcuts Description", "copy": "Copy", "paste": "Paste", "cut": "Cut", "selectAll": "Select All", "group": "Group", "ungroup": "Ungroup", "filter": "Filter", "noTemplatesFound": "No Templates Found", "all": "All", "searchTemplates": "Search Templates", "noCardsHistory": "No Cards History", "createCardToSeeHistory": "Create cards to see them here", "today": "Today", "yesterday": "Yesterday", "featured": "Featured", "filters": "Filters", "reset": "Reset", "showOnly": "Show Only", "featuredTemplates": "Featured Templates", "freeTemplates": "Free Templates", "sortBy": "Sort By", "newest": "Newest", "oldest": "Oldest", "popular": "Popular", "alphabetical": "Alphabetical", "created": "Created", "edited": "Edited", "saved": "Saved", "exported": "Exported", "shared": "Shared", "printed": "Printed", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "quickRanges": "Quick Ranges", "lastWeek": "Last Week", "lastMonth": "Last Month", "last3Months": "Last 3 Months", "allTime": "All Time", "addShape": "<PERSON>d <PERSON>", "pleaseAgreeToTerms": "Please agree to the terms and conditions", "agreeToThe": "I agree to the", "chooseBackgroundColor": "Choose Background Color", "myCards": "My Cards", "refresh": "Refresh", "filterCards": "Filter Cards", "searchForCard": "Search for a card...", "noSavedCards": "No Saved Cards", "createCardToShowHere": "Create a card and save it to appear here", "filterCardsTitle": "Filter Cards", "searchForCardHint": "Search for a card...", "occasion": "Occasion", "newestFirst": "Newest First", "oldestFirst": "Oldest First", "mostPopular": "Most Popular", "searchSuggestions": "Search Suggestions", "searchInNotifications": "Search in notifications...", "unread": "Unread", "read": "Read", "changingFonts": "Changing Fonts", "changingFontsDesc": "You can easily change the font type, size, and style through the text formatting menu", "changingColors": "Changing Colors", "changingColorsDesc": "You can change the color of text, background, and other elements through the color picker tool", "textEffects": "Text Effects", "textEffectsDesc": "You can add various effects to text such as shadow, glow, reflection, and color gradient", "aiCardCreation": "AI Card Creation", "aiCardCreationDesc": "You can create complete cards using artificial intelligence by describing what you want", "accessingAiText": "Accessing Text Generation Feature", "accessingAiTextDesc": "You can access the text generation feature by clicking on the 'Add Text' button and then selecting 'Generate Text with AI'", "writingPrompt": "Writing the Description", "writingPromptDesc": "Write a clear description of the text you want, such as 'Write a birthday greeting for a close friend'", "usingGeneratedText": "Using Generated Text", "usingGeneratedTextDesc": "After generating the text, you can edit it or add it directly to the card", "accessingAiImage": "Accessing Image Generation Feature", "accessingAiImageDesc": "You can access the image generation feature by clicking on the 'Add Image' button and then selecting 'Generate Image with AI'", "describingImage": "Describing the Image", "describingImageDesc": "Write a detailed description of the image you want, such as 'A picture of a red bouquet of flowers with a blue background'", "customizingImage": "Customizing the Image", "customizingImageDesc": "You can customize the generated image by adjusting settings such as style, colors, and size", "usingGeneratedImage": "Using Generated Image", "usingGeneratedImageDesc": "After generating the image, you can add it directly to the card and adjust its size and position", "savingDraft": "Saving as Draft", "savingDraftDesc": "You can save the card as a draft to return to it and complete it later", "savingFinal": "Final Save", "savingFinalDesc": "When you finish designing the card, you can save it in its final form", "exportAsImageDesc": "You can export the card as an image in PNG or JPEG format with high quality", "exportAsPdfDesc": "You can export the card as a PDF file for printing or sharing", "exportOptionsDesc": "You can customize export options such as resolution, quality, and paper size", "sharingDirectly": "Direct Sharing", "sharingDirectlyDesc": "You can share the card directly with contacts through the app", "sharingSocial": "Sharing on Social Media", "sharingSocialDesc": "You can share the card on various social media platforms", "sharingWhatsapp": "Sharing via WhatsApp", "sharingWhatsappDesc": "You can share the card directly through WhatsApp", "printing": "Printing Cards", "accessingPrint": "Accessing Print Feature", "accessingPrintDesc": "You can access the print feature by clicking on the 'Print' button in the export menu", "printOptions": "Print Options", "printOptionsDesc": "You can customize print options such as paper size, margins, and quality", "printPreviewDesc": "You can preview the card before printing to ensure it appears as desired", "designTips": "Design Tips", "colorScheme": "Color Scheme", "colorSchemeDesc": "Use a harmonious color palette to create an attractive and cohesive design", "typography": "Typography", "typographyDesc": "Use readable fonts and consistency between different text sizes", "balance": "Balance", "balanceDesc": "Maintain balance of elements in the card to create a harmonious design", "performanceTips": "Performance Tips", "imageOptimization": "Image Optimization", "imageOptimizationDesc": "Use appropriately sized images to improve app performance and loading speed", "savingRegularly": "Saving Regularly", "savingRegularlyDesc": "Save your work regularly to avoid losing changes in case of any issues", "saveMyCard": "Save My Card", "saveMyCardDesc": "Save as high quality image in my cards", "saveAsImage": "Save as Image", "changeColor": "Change Color", "generateText": "Generate Text", "signature": "Signature", "signatureText": "Signature Text", "clearSignature": "Clear Signature", "signatureAdded": "Signature Added", "colorUpdated": "Color Updated", "pdfError": "Error creating PDF", "pdfNotGenerated": "PDF not generated yet. Please wait.", "pdfSavedTo": "PDF saved to", "errorSavingPdf": "Error saving PDF", "noElementsToSave": "No elements to save", "saveDraftQuestion": "Save as Draft?", "saveDraftMessage": "Do you want to save this card as a draft to continue editing later?", "dontSave": "Don't Save", "errorSavingImage": "Error saving image", "signaturePreview": "Signature Preview", "sampleSignature": "Sample Signature", "editText": "Edit Text"}