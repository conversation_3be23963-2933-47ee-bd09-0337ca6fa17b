import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';

import 'core/constants/app_strings.dart';
import 'core/providers/language_provider.dart';
import 'core/services/navigation_service.dart';
import 'core/theme/unified_theme.dart';
import 'core/utils/font_utils.dart';
import 'features/auth/presentation/blocs/auth/auth_bloc.dart';
import 'features/auth/presentation/blocs/auth/auth_event.dart';
import 'features/auth/presentation/blocs/auth/auth_state.dart';
import 'features/settings/presentation/blocs/settings/settings_bloc.dart';
import 'features/settings/presentation/blocs/settings/settings_event.dart';
import 'features/settings/presentation/blocs/settings/settings_state.dart';
import 'injection_container.dart' as di; // For accessing sl<AuthBloc>()
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'l10n/l10n.dart';
import 'routes.dart';

/// Main application widget that sets up the app's theme, localization, and routing.
class MyApp extends StatelessWidget {
  /// Navigator key for accessing the navigator state from outside the build context
  final GlobalKey<NavigatorState> navigatorKey;

  /// Creates a new app instance with the required navigator key
  const MyApp({super.key, required this.navigatorKey});

  @override
  Widget build(BuildContext context) {
    // Create a LanguageProvider instance
    final languageProvider = LanguageProvider();
    // Initialize the language provider
    languageProvider.init();

    // Wrap the app with ChangeNotifierProvider for language management
    return ChangeNotifierProvider(
      create: (_) => languageProvider,
      child: MultiBlocProvider(
        providers: [
          // Auth BLoC provider - تحميل فوري للتحقق من حالة تسجيل الدخول
          BlocProvider<AuthBloc>(
            create: (_) => di.sl<AuthBloc>()..add(CheckAuthStatusEvent()),
            lazy:
                false, // تحميل فوري للتحقق من حالة تسجيل الدخول عند بدء التطبيق
          ),
          // Settings BLoC provider - تحميل فوري لأنه مطلوب للتهيئة
          BlocProvider<SettingsBloc>(
            create: (_) => di.sl<SettingsBloc>()..add(LoadSettingsEvent()),
            lazy: false,
          ),
        ],
        child: _AppContent(),
      ),
    );
  }
}

/// محتوى التطبيق المحسن لتقليل إعادة البناء
class _AppContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, _) {
        return BlocBuilder<SettingsBloc, SettingsState>(
          buildWhen: (previous, current) {
            // إعادة البناء فقط عند تغيير الإعدادات الفعلية
            return current is SettingsLoaded || current is SettingsError;
          },
          builder: (context, state) {
              // لا نحتاج لمتغير الإعدادات لأننا نستخدم ثيم ثابت
              // تم إزالة التحديث التلقائي للغة لتجنب التداخل مع زر تغيير اللغة
              // سيتم التعامل مع تغيير اللغة من خلال LanguageProvider مباشرة

              return BlocListener<AuthBloc, AuthState>(
                listener: (context, authState) {
                  // التعامل مع تغييرات حالة المصادقة
                  debugPrint('AuthBloc state changed: ${authState.runtimeType}');

                  if (authState is AuthLoggedOut) {
                    debugPrint('User logged out, navigating to login page');
                    // التوجيه لصفحة تسجيل الدخول عند تسجيل الخروج
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      final navigationService = di.sl<NavigationService>();
                      debugPrint('Using NavigationService to navigate to login');
                      debugPrint('NavigatorKey current state: ${navigationService.navigatorKey.currentState}');
                      debugPrint('Current context mounted: ${context.mounted}');

                      try {
                        // جرب NavigationService أولاً
                        if (navigationService.navigatorKey.currentState != null) {
                          debugPrint('Using NavigationService for navigation');
                          navigationService.navigateToAndRemoveUntil(AppRoutes.login);
                        } else {
                          // إذا فشل، استخدم Navigator العادي
                          debugPrint('NavigationService failed, using regular Navigator');
                          if (context.mounted) {
                            Navigator.of(context).pushNamedAndRemoveUntil(
                              AppRoutes.login,
                              (route) => false,
                            );
                          }
                        }
                        debugPrint('Navigation to login completed successfully');
                      } catch (e) {
                        debugPrint('Navigation error: $e');
                        // محاولة أخيرة باستخدام Navigator العادي
                        try {
                          if (context.mounted) {
                            Navigator.of(context).pushNamedAndRemoveUntil(
                              AppRoutes.login,
                              (route) => false,
                            );
                          }
                        } catch (e2) {
                          debugPrint('Fallback navigation also failed: $e2');
                        }
                      }
                    });
                  }
                },
                child: MaterialApp(
                  debugShowCheckedModeBanner: false,
                  title: AppStrings.appName,

                // Localization settings
                locale: languageProvider.locale,
                supportedLocales: L10n.all,
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                localeResolutionCallback: (locale, supportedLocales) {
                  if (locale != null) {
                    for (var supportedLocale in supportedLocales) {
                      if (supportedLocale.languageCode == locale.languageCode) {
                        return supportedLocale;
                      }
                    }
                  }
                  return supportedLocales.first; // Default to Arabic
                },

                // Dynamic theme based on settings
                theme: UnifiedTheme.getLightTheme(isArabic: languageProvider.isRtl),
                darkTheme: UnifiedTheme.getDarkTheme(isArabic: languageProvider.isRtl),
                themeMode: state is SettingsLoaded && state.settings.isDarkModeEnabled
                    ? ThemeMode.dark
                    : ThemeMode.light,

                // Set text direction based on language and apply fonts
                builder: (context, child) {
                  // تطبيق الخط المناسب حسب اللغة
                  final isArabic = languageProvider.isRtl;

                  // تطبيق الخط المناسب على جميع النصوص
                  final textTheme = Theme.of(context).textTheme;
                  final customTextTheme =
                      FontUtils.getTextTheme(context, textTheme);

                  // تطبيق الثيم المخصص مع الخط المناسب
                  return Theme(
                    data: Theme.of(context).copyWith(
                      textTheme: customTextTheme,
                      // تطبيق الخط على AppBarTheme
                      appBarTheme: Theme.of(context).appBarTheme.copyWith(
                            titleTextStyle: FontUtils.getHeadingStyle(
                              context,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                      // تطبيق الخط على ButtonTheme
                      textButtonTheme: TextButtonThemeData(
                        style: TextButton.styleFrom(
                          textStyle: FontUtils.getButtonStyle(context),
                        ),
                      ),
                    ),
                    child: Directionality(
                      textDirection:
                          isArabic ? TextDirection.rtl : TextDirection.ltr,
                      child: child!,
                    ),
                  );
                },

                  // Navigation management (routes.dart)
                  onGenerateRoute: generateRoute,
                  initialRoute: AppRoutes.splash,
                  navigatorKey: di.sl<NavigationService>().navigatorKey,
                ),
              );
            },
          );
        },
    );
  }
}
